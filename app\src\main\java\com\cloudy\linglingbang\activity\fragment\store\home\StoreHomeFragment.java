package com.cloudy.linglingbang.activity.fragment.store.home;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseScrollTabViewPagerFragment;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.fragment.mynew.util.SelfUserInfoLoader;
import com.cloudy.linglingbang.activity.fragment.store.home.model.StoreHomeConstant;
import com.cloudy.linglingbang.activity.fragment.store.rights.VipRightsFragment;
import com.cloudy.linglingbang.activity.fragment.store.service.ServiceFragment;
import com.cloudy.linglingbang.activity.fragment.store.youpin.ReFitFragment;
import com.cloudy.linglingbang.activity.fragment.store.youpin.SuperiorProductFragment;
import com.cloudy.linglingbang.activity.fragment.store.youpin.search.SearchActivity;
import com.cloudy.linglingbang.activity.store.commodity.ShoppingCartActivity;
import com.cloudy.linglingbang.app.receiver.UserInfoChangeReceiver;
import com.cloudy.linglingbang.app.receiver.UserInfoChangedHelper;
import com.cloudy.linglingbang.app.util.NotchScreenUtils;
import com.cloudy.linglingbang.app.util.StatusBarUtils;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.util.ViewHolderUtils;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.util.share.L00ShareUtils;
import com.cloudy.linglingbang.app.widget.BaseOnClickListener;
import com.cloudy.linglingbang.model.SourceModel;
import com.cloudy.linglingbang.model.store.ecology.CartInfo;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.viewpager.widget.PagerAdapter;
import butterknife.BindView;
import butterknife.OnClick;

/**
 * 商城首页
 *
 * <AUTHOR>
 * @date 2018/10/10
 */
public class StoreHomeFragment extends BaseScrollTabViewPagerFragment<Fragment> {

    /**
     * 用于控制是否展示分享以及购物车数量
     */
    private UserInfoChangedHelper mUserInfoChangedHelper;
    /**
     * 整车
     */
    public static final int TYPE_CAR = 99999999;
    public static final int TYPE_COMMODITY = 0;

    /**
     * 购物车数量
     */
    private TextView mTvCartCount;
    /**
     * 购物车
     */
    private View mFlShopping;

    /**
     * 优品分销
     */
    private View mViewShare;

    @BindView(R.id.tv_store_search)
    TextView mTvStoreSearch;

    @BindView(R.id.iv_back)
    ImageView mIvBack;

    private String mFromType;

    private int mPosition;

    public static Fragment newInstance() {
        return newInstance(-1);
    }

    public static Fragment newInstance(int type) {
        return IntentUtils.setFragmentIntArgument(new StoreHomeFragment(), type);
    }

    public static Fragment newInstance(String fromType) {
        Fragment fragment = new StoreHomeFragment();
        Bundle bundle = new Bundle();
        bundle.putString("fromType", fromType);
        fragment.setArguments(bundle);
        return fragment;
    }

    /**
     * 打开指定位置
     */
    public static Fragment newInstance(String fromType, int position) {
        Fragment fragment = new StoreHomeFragment();
        Bundle bundle = new Bundle();
        bundle.putString("fromType", fromType);
        bundle.putInt(IntentUtils.INTENT_EXTRA_FROM, position);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_store_home;
    }

    @Override
    protected void initViews() {
        super.initViews();
        mFromType = getArguments().getString("fromType");
        mPosition = getArguments().getInt("fromType", -1);
        if (TextUtils.isEmpty(mFromType)) {
            mIvBack.setVisibility(View.GONE);
            //设置状态栏的高度
            int statusHeight = Math.max(StatusBarUtils.getStatusBarHeight(mRootView.getContext()), NotchScreenUtils.getNotchSafeWH()[1]);
            mRootView.setPadding(mRootView.getPaddingLeft(),
                    statusHeight,
                    mRootView.getPaddingRight(),
                    mRootView.getPaddingBottom());
        } else {
            mIvBack.setVisibility(View.VISIBLE);
        }
        mTvStoreSearch.setText("潮改焕新尽在五菱汽车");
        tabs.setShouldExpand(true);
        mFlShopping = mRootView.findViewById(R.id.fl_cart);
        int defaultItemIndex = getDefaultItemIndex();
        mViewPager.setCurrentItem(defaultItemIndex);
        //由于子级使用了 FragmentStatePagerAdapter，如果父级将 Fragment destroy，则在 restoreState 时会出错
        //因为子级使用了懒加载，所以 setOffscreenPageLimit 也可以接受
        mViewPager.setOffscreenPageLimit(mData.size() - 1);
        SensorsUtils.sensorsClickBtn("点击" + titles[defaultItemIndex], "好物" + titles[defaultItemIndex] + "页", "好物" + titles[defaultItemIndex]);
        mTvCartCount = mRootView.findViewById(R.id.tv_cart_count);
        //点击购物车
        ViewHolderUtils.setOnClickListener(new BaseOnClickListener(true) {
            @Override
            public void onClick(Context context) {
                super.onClick(context);
                SensorsUtils.sensorsClickBtn("购物车", "好物");
                IntentUtils.startActivity(context, ShoppingCartActivity.class);
            }
        }, mRootView.findViewById(R.id.fl_cart));
        //分享
        mViewShare = mRootView.findViewById(R.id.iv_share);
        ViewHolderUtils.setOnClickListener(new BaseOnClickListener(true) {
            @Override
            public void onClick(Context context) {
                super.onClick(context);
                onClickShare(context);
            }
        }, mViewShare);
        initShare();
        //初始化状态
        initCart();

        //刷新购物车数量
        SelfUserInfoLoader.getInstance().updateCartCountAndDispatch(getContext());
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (mUserInfoChangedHelper == null) {
            mUserInfoChangedHelper = new UserInfoChangedHelper(new UserInfoChangeReceiver() {
                @Override
                protected void onUpdateBaseInfo() {
                    super.onUpdateBaseInfo();
                    initShare();
                    for (Fragment datum : mData) {
                        if (datum instanceof SuperiorProductFragment) {
                            ((SuperiorProductFragment) datum).userChange();
                        }
                    }
                }

                @Override
                protected void onUpdateCartInfo() {
                    super.onUpdateCartInfo();
                    initCart();
                }

                @Override
                protected void onClearUser() {
                    super.onClearUser();
                    initCart();
                }
            });
        }
        mUserInfoChangedHelper.register(getContext());
    }

    @OnClick(R.id.search_go_btn)
    public void onViewClicked() {
        SensorsUtils.sensorsClickBtn("点击搜索", "好物", "搜索");
        IntentUtils.startActivity(getActivity(), SearchActivity.class, new SensorsUtils.StoreHomeAnchor("优品-搜索", "优品-搜索", SourceModel.POSITION_TYPE.SEARCH_YOUPIN_TYPE, SourceModel.POSITION_TYPE.SEARCH_YOUPIN_VALUE));
    }

    /**
     * 顶部tab选中时候的埋点事件
     */
    @Override
    protected void onItemSelected(int position) {
        super.onItemSelected(position);
        SensorsUtils.sensorsClickBtn("点击" + titles[position], "好物", "好物");

    }

    /**
     * 获取默认的 index，可用于指定打开时跳转到某个 Fragment
     */
    private int getDefaultItemIndex() {
        int argument = IntentUtils.getFragmentIntArgument(this);
        if (argument < 0) {
            argument = 0;
        }
        return argument;
    }

    @Override
    protected PagerAdapter createViewPagerAdapter(final List<Fragment> data, final String[] titles) {
        return new FragmentStatePagerAdapter(getChildFragmentManager()) {
            @Override
            public Fragment getItem(int position) {
                return data.get(position);
            }

            @Override
            public int getCount() {
                return data.size();
            }

            @Nullable
            @Override
            public CharSequence getPageTitle(int position) {
                return titles[position];
            }
        };
    }

    @Override
    protected List<Fragment> createAdapterData() {
        List<Fragment> fragmentList = new ArrayList<>();
        fragmentList.add(ReFitFragment.newInstance());
        fragmentList.add(SuperiorProductFragment.newInstance(StoreHomeConstant.PAGE_CODE_LIFE_GOODS, "生活好物", false));
        fragmentList.add(new ServiceFragment());
        fragmentList.add(VipRightsFragment.newInstance());
        return fragmentList;
    }

    @Override
    protected String[] getTitles() {
        return getResources().getStringArray(R.array.store_home_tab_title_array);
    }


    /**
     * 初始化购物车，在初始化时、用户购物车信息变化时调用
     */
    private void initCart() {
        if (mTvCartCount == null) {
            return;
        }
        if (UserUtils.hasLogin()) {
            //已登录
            CartInfo cartInfo = SelfUserInfoLoader.getInstance().getCartInfo();
            if (cartInfo.getCount() > 0) {
                mTvCartCount.setVisibility(View.VISIBLE);
                mTvCartCount.setText(String.valueOf(cartInfo.getCount()));
            } else {
                mTvCartCount.setVisibility(View.GONE);
            }
        } else {
            //未登录
            mTvCartCount.setVisibility(View.GONE);
        }
    }

    /**
     * 初始化分享，在初始化时、 setUserVisibleHint 时、用户状态变化时调用
     */
    private void initShare() {
        if (mViewShare == null) {
            return;
        }
        if (getUserVisibleHint()) {
            if (UserUtils.canShareStoreCommodity()) {
                mViewShare.setVisibility(View.GONE);
            } else {
                mViewShare.setVisibility(View.GONE);
            }
        } else {
            mViewShare.setVisibility(View.GONE);
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        initShare();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mUserInfoChangedHelper != null) {
            mUserInfoChangedHelper.unregister(getContext());
        }
    }

    /**
     * 点击分享
     */
    private void onClickShare(Context context) {
        if (UserUtils.canShareStoreCommodity()) {
            L00ShareUtils.getInstance().start(context);
        }
    }

    @OnClick(R.id.iv_back)
    void clickBack() {
        getActivity().finish();
    }
}
