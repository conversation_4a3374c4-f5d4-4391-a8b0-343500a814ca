package com.cloudy.linglingbang.activity.community.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.span.UserIdentitySpanUtils;
import com.cloudy.linglingbang.app.widget.banner.AdImageView;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.user.User;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/8/22
 */
public class PostSelectUserAdapter extends BaseRecyclerViewAdapter<User> {
    private final List<User> mSelector;

    public void setSelector(List<User> selector) {
        if (selector == null || selector.isEmpty()) {
            return;
        }
        mSelector.addAll(selector);
        notifyDataSetChanged();
        if (mOnUserSelectorListener != null) {
            mOnUserSelectorListener.UserChange(mSelector, null);
        }
    }

    public PostSelectUserAdapter(Context context, List<User> data) {
        super(context, data);
        mSelector = new ArrayList<>();
    }

    @Override
    protected BaseRecyclerViewHolder<User> createViewHolder(View itemView) {
        return new UserViewHolder(itemView);
    }

    @Override
    protected int getItemLayoutRes(int viewType) {
        return R.layout.item_post_select_user;
    }

    OnUserSelectorListener mOnUserSelectorListener;

    public void setOnUserSelectorListener(OnUserSelectorListener onUserSelectorListener) {
        mOnUserSelectorListener = onUserSelectorListener;
    }

    public interface OnUserSelectorListener {
        void UserChange(List<User> mSelector, User user);
    }

    class UserViewHolder extends BaseRecyclerViewHolder<User> {
        AdImageView mHeaderImg;
        ImageView mIvSelect;
        TextView mTvNickName;
        User mUser;

        public UserViewHolder(View itemView) {
            super(itemView);
            mHeaderImg = itemView.findViewById(R.id.iv_head);
            mTvNickName = itemView.findViewById(R.id.tv_nickname);
            mIvSelect = itemView.findViewById(R.id.iv_select);
            mIvSelect.setOnClickListener(v -> {
                boolean isSelector = false;
                for (User user1 : mSelector) {
                    if (Objects.equals(user1.getUserIdStr(), mUser.getUserIdStr())) {
                        isSelector = true;
                        break;
                    }
                }
                int size = mSelector.size();
                if (size >= 50 && !isSelector) {
                    ToastUtil.showMessage(itemView.getContext(), "最多同时@50个人～");
                    return;
                }
                if (isSelector) {
                    mIvSelect.setImageResource(R.drawable.bg_circle_stroke_1_5dp_3d383a40);
                    removeUserByIdStr(mUser.getUserIdStr());
                } else {
                    mIvSelect.setImageResource(R.drawable.ecological_button_checked);
                    removeUserByIdStr(mUser.getUserIdStr());
                    mSelector.add(mUser);
                }
                if (mOnUserSelectorListener != null) {
                    mOnUserSelectorListener.UserChange(mSelector, mUser);
                }
            });
        }

        private void removeUserByIdStr(String idStr) {
            if (TextUtils.isEmpty(idStr)) {
                return;
            }
            int temp = -1;
            for (int i = 0; i < mSelector.size(); i++) {
                if (idStr.equals(mSelector.get(i).getUserIdStr())) {
                    temp = i;
                    break;
                }
            }
            if (temp >= 0) {
                mSelector.remove(temp);
                removeUserByIdStr(idStr);
            }
        }

        @Override
        public void bindTo(User user, int position) {
            super.bindTo(user, position);
            mUser = user;
            ImageLoad.LoadUtils.loadAvatar(itemView.getContext(), mHeaderImg, user.getPhoto());
            //昵称
            mTvNickName.setText(user.getNickname());
            UserIdentitySpanUtils.setUserNameAndIdentity(mTvNickName, user, 0);
            int imgRes = R.drawable.bg_circle_stroke_1_5dp_3d383a40;
            for (User user1 : mSelector) {
                if (Objects.equals(user1.getUserIdStr(), user.getUserIdStr())) {
                    imgRes = R.drawable.ecological_button_checked;
                    break;
                }
            }
            mIvSelect.setImageResource(imgRes);
        }
    }
}
