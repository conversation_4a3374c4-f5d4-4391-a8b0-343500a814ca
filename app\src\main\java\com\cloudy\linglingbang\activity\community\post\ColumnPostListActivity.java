package com.cloudy.linglingbang.activity.community.post;

import android.text.TextUtils;

import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.activity.community.common.BasePostAdapter;
import com.cloudy.linglingbang.model.community.HotType;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;

import java.util.List;

import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 栏目中的帖子列表，以帖子的形式展示
 *
 * <AUTHOR>
 * @date 2017/7/21
 * @see com.cloudy.linglingbang.activity.fragment.homePage.ColumnListActivity
 */
public class ColumnPostListActivity extends BaseRecyclerViewRefreshActivity<PostCard> {
    private long mColumnId;

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<PostCard> list) {
        return new BasePostAdapter(this, list);
    }

    @Override
    public Observable<BaseResponse<List<PostCard>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return service2.getColumnPostList(mColumnId, pageNo, pageSize);
    }

    @Override
    protected void initialize() {
        super.initialize();
        HotType hotType = (HotType) getIntentExtra(null);
        if (hotType == null || TextUtils.isEmpty(hotType.getColumnId())) {
            onIntentExtraError();
        } else {
            try {
                mColumnId = Long.parseLong(hotType.getColumnId());
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
            if (mColumnId == 0) {
                onIntentExtraError();
            } else {
                //columnId正常，走正常逻辑
                setLeftTitle(hotType.getColumnName());
            }
        }
    }
}
