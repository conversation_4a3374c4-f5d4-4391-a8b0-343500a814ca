package com.cloudy.linglingbang.model.request.retrofit2;

import com.bangcle.comapiprotect.CheckCodeUtil;
import com.cloudy.linglingbang.BaseInstrumentedTest;
import com.cloudy.linglingbang.app.log.LogUtils;

/**
 * 加解密
 *
 * <AUTHOR>
 * @date 2019/3/18
 */
public class CheckCodeUtilsTest extends BaseInstrumentedTest {
    @Override
    public void test() {
        super.test();
        CheckCodeUtil checkCodeUtil = CheckCodeUtil.getInstance(getContext());
        String source = "test=1";
        String encodeResult = checkCodeUtil.checkcode(source, CheckCodeUtil.DATA_TYPE_OTHER);
        LogUtils.d("加密 %1$s \n结果为 %2$s", source, encodeResult);
        String decodeResult = checkCodeUtil.decheckcode(encodeResult);
        LogUtils.d("解密 %1$s \n结果为 %2$s", encodeResult, decodeResult);

//        String invalidEncodeResult = encodeResult.substring(0, encodeResult.length() - 5);
//        try {
//            decodeResult = checkCodeUtil.decheckcode(invalidEncodeResult);
//            LogUtils.d("解密 %1$s \n结果为 %2$s", invalidEncodeResult, decodeResult);
//        } catch (Throwable e) {
//            e.printStackTrace();
//        }
    }
}