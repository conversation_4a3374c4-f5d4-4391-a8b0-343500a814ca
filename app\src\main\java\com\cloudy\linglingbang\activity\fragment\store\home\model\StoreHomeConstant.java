package com.cloudy.linglingbang.activity.fragment.store.home.model;

/**
 * 商城首页的部分常量
 *
 * <AUTHOR>
 * @date 2018/10/22
 */
public class StoreHomeConstant {
    /**
     * 推荐
     */
    public static final String PAGE_CODE_RECOMMEND = "recommend_new";
    /**
     * 优品
     */
    public static final String PAGE_CODE_COMMODITY = "commodity_new";
    /**
     * 生活好货
     */
    public static final String PAGE_CODE_LIFE_GOODS = "lifeGoods_new";

    /**
     * 更多商品
     */
    public static final String PAGE_CODE_MORE_COMMODITY = "moreCommodity_new";
    /**
     * 尊享权益- 混合支付
     */
    public static final String PAGE_CODE_VIP_RIGHTS = "vipRights_mixed_new";
    /**
     * 整车
     */
    public static final String PAGE_CODE_CAR = "vehicle_new";
    /**
     * 服务产品
     */
    public static final String PAGE_CODE_SERVICE_GOODS = "service_good_new";
    /**
     * 改装推荐
     */
    public static final String PAGE_CODE_REMOULD_RECOMMEND = "remould_recommend_new";
    /**
     * 服务-推荐
     */
    public static final String PAGE_CODE_GOODS_SERVICE = "goods_service_recommend_new";

    /**
     * 好物-首页
     */
    public static final String PAGE_CODE_HOME_GOODS = "shoppingMall";

    /**
     * 通过解析 pageCode 返回 pageName，只有定义的几个常量可以转
     */
    public static String getPageNameByPageCode(String pageCode) {
        switch (pageCode) {
            case StoreHomeConstant.PAGE_CODE_RECOMMEND:
                return "推荐";
            case StoreHomeConstant.PAGE_CODE_COMMODITY:
                return "菱菱优品";
            case StoreHomeConstant.PAGE_CODE_CAR:
                return "车";
            case StoreHomeConstant.PAGE_CODE_SERVICE_GOODS:
                return "服务产品";
            default:
                return null;
        }
    }
}
