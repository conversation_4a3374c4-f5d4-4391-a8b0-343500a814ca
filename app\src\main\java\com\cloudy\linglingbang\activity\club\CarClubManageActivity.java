package com.cloudy.linglingbang.activity.club;

import android.app.Activity;
import android.content.Intent;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.club.activity.PublishCarClubActivityActivity;
import com.cloudy.linglingbang.app.widget.item.MyInfoItem;
import com.cloudy.linglingbang.model.channel.Channel;
import com.umeng.analytics.MobclickAgent;

import java.io.Serializable;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 车友会管理页面
 *
 * <AUTHOR>
 * @date 2017/11/16
 */
public class CarClubManageActivity extends BaseActivity {
    public final static String EXTRA_UNREAD_COUNT = "unReadCount";
    public final static String EXTRA_CHANNEL_ID = "channelId";
    public final static String EXTRA_ROLE_ID = "roleId";

    private IntentExtra mIntentExtra;

    /**
     * 车友会id
     */
    private Long mChannelId;
    /**
     * 车友会未读消息数
     */
    private int mUnReadCount;
    /**
     * 车友会角色
     */
    private int mRole;
    /**
     * 车友会头像
     */
    private String mChannelFavicon;
    /**
     * 车友会名称
     */
    private String mChannelName;

    /**
     * 会员申请
     */
    @BindView(R.id.item_member_apply)
    MyInfoItem mItemMemberApply;

    /**
     * 任命副会长
     */
    @BindView(R.id.item_appoint_vice_chairman)
    MyInfoItem mItemAppointViceChairman;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_car_club_manage);
    }

    @Override
    protected void initialize() {
        setMiddleTitle(getString(R.string.club_manage));
        mIntentExtra = (IntentExtra) getIntentExtra(null);
        if (mIntentExtra == null) {
            onIntentExtraError();
            return;
        }
        mChannelId = mIntentExtra.getChannelId();
        mUnReadCount = mIntentExtra.getUnReadMsgCount();
        mRole = mIntentExtra.getUserRoleId();
        mChannelFavicon = mIntentExtra.getHeaderUrl();
        mChannelName = mIntentExtra.getChannelName();
        //判断小红点
        if (mUnReadCount > 0) {
            mItemMemberApply.setRedPointVisible(true);
        } else {
            mItemMemberApply.setRedPointVisible(false);
        }
        //判断是不是会长,如果是会长，显示任命副会长选项
        if (mRole == Channel.RoleType.PRESIDENT) {
            mItemAppointViceChairman.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 点击资料编辑
     */
    @OnClick(R.id.item_data_edit)
    protected void onClickDataEdit(View view) {
        //添加友盟统计
        MobclickAgent.onEvent(this, "312");
        ClubInfoEditActivity.startActivity(this, mChannelFavicon, mChannelName, mChannelId);
    }

    /**
     * 点击发起活动
     */
    @OnClick(R.id.item_add_activity)
    protected void onClickAddActivity(View view) {
        //添加友盟统计
        MobclickAgent.onEvent(this, "313");
        IntentUtils.startActivity(this, PublishCarClubActivityActivity.class, mChannelId);
    }

    /**
     * 点击会员申请
     */
    @OnClick(R.id.item_member_apply)
    protected void onClickMemberApply(View view) {
        //添加友盟统计
        MobclickAgent.onEvent(this, "314");
        //先将未读数置为0,取消小红点
        //后来服务器又改为小红点只有全部处理完了再消失，先不处理，防止再改回来
//        mUnReadCount = 0;
//        mItemMemberApply.setRedPointVisible(false);
        IntentUtils.startActivityForResult(this, ClubApplyListActivity.class, CarClubDetailActivity.REQUEST_REFRESH_POINT, mChannelId);
    }

    /**
     * 点击任命副会长
     *
     * @param view
     */
    @OnClick(R.id.item_appoint_vice_chairman)
    protected void onClickAppoint(View view) {
        //添加友盟统计
        MobclickAgent.onEvent(this, "315");
        IntentUtils.startActivity(this, AppointViceChairmanActivity.class, mChannelId);

    }

    @Override
    protected void onBack() {
        //点击返回，判断是否点击了会员申请，清空了页面
        Intent intent = new Intent();
        intent.putExtra(EXTRA_UNREAD_COUNT, mUnReadCount);
        setResult(Activity.RESULT_OK, intent);
        finish();
    }

    public static void startActivity(Activity context, Channel channel, int requestCode) {
        try {
            Intent intent = new Intent(context, CarClubManageActivity.class);
            IntentExtra intentExtra = new IntentExtra(Long.valueOf(channel.getChannelId()), channel.getChannelName(), channel.getUnReadMsgCount(), channel.getUserRoleId(), channel.getChannelFavicon());
            intent.putExtras(IntentUtils.createBundle(intentExtra));
            context.startActivityForResult(intent, requestCode);
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            if (requestCode == CarClubDetailActivity.REQUEST_REFRESH_POINT) {
                mUnReadCount = data.getIntExtra(CarClubManageActivity.EXTRA_UNREAD_COUNT, 0);
                if (mUnReadCount == 0) {
                    mItemMemberApply.setRedPointVisible(false);
                }
            }
        }
    }

    /**
     * 传递channel的参数
     */
    public static class IntentExtra implements Serializable {
        private Long mChannelId;
        private String mChannelName;
        private int mUnReadMsgCount;
        private int mUserRoleId;
        private String mHeaderUrl;

        public IntentExtra(Long channelId, String channelName, int unReadMsgCount, int userRoleId, String headerUrl) {
            mChannelId = channelId;
            mChannelName = channelName;
            mUnReadMsgCount = unReadMsgCount;
            mUserRoleId = userRoleId;
            mHeaderUrl = headerUrl;
        }

        public Long getChannelId() {
            return mChannelId;
        }

        public void setChannelId(Long channelId) {
            mChannelId = channelId;
        }

        public String getChannelName() {
            return mChannelName;
        }

        public void setChannelName(String channelName) {
            mChannelName = channelName;
        }

        public int getUnReadMsgCount() {
            return mUnReadMsgCount;
        }

        public void setUnReadMsgCount(int unReadMsgCount) {
            mUnReadMsgCount = unReadMsgCount;
        }

        public int getUserRoleId() {
            return mUserRoleId;
        }

        public void setUserRoleId(int userRoleId) {
            mUserRoleId = userRoleId;
        }

        public String getHeaderUrl() {
            return mHeaderUrl;
        }

        public void setHeaderUrl(String headerUrl) {
            mHeaderUrl = headerUrl;
        }
    }

}
