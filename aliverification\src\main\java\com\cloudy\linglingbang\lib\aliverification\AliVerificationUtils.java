package com.cloudy.linglingbang.lib.aliverification;

import android.content.Context;

import com.alibaba.verificationsdk.ui.IActivityCallback;
import com.alibaba.verificationsdk.ui.VerifyActivity;
import com.alibaba.verificationsdk.ui.VerifyType;

/**
 * 阿里校验
 *
 * <AUTHOR>
 * @date 2018/11/28
 */
public class AliVerificationUtils {
    public static void startSimpleVerifyUI(Context context, IActivityCallback callback) {
        //这里如果不初始化，sessionId 能否校验通过
        VerifyActivity.startSimpleVerifyUI(context, VerifyType.NOCAPTCHA, "0335", null, callback);
    }
}
