package com.cloudy.linglingbang.activity.fragment.store.home;

import android.content.Context;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.util.TypedValue;
import android.view.View;
import android.widget.HorizontalScrollView;
import android.widget.RadioButton;
import android.widget.RadioGroup;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.fragment.homePage.lab.LingLabAdapter;
import com.cloudy.linglingbang.model.community.HotType;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.NormalSubscriber;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;
import butterknife.BindView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 栏目详情页
 *
 * <AUTHOR>
 * @date 22/12/22
 */
public class StorePostColumnsListActivity extends BaseRecyclerViewRefreshActivity<Object> {

    @BindView(R.id.radio_group_label)
    RadioGroup mRadioGroupLabel;

    @BindView(R.id.horizontal_scroll_view)
    HorizontalScrollView mHorizontalScrollView;

    public static void startActivity(Context context, String title, int columnId) {
        Intent intent = new Intent(context, StorePostColumnsListActivity.class);
        intent.putExtra("columnId", columnId);
        intent.putExtra("title", title);
        context.startActivity(intent);
    }

    private boolean refresh_flag_is_change = false;
    private int refresh_flag = -1;

    /**
     * 栏目位置
     */
    private final int LING_LAB_POSITION = 20;

    Object mTopModel;

    private LingLabAdapter mLingLabAdapter;

    private int mColumnId = -1;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.fragment_ling_lab);
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<Object> list) {
        //list.add(0, mTopModel);
        mLingLabAdapter = new LingLabAdapter(this, list);
        return mLingLabAdapter;
    }

    @Override
    protected void initialize() {
        super.initialize();
        mColumnId = getIntent().getIntExtra("columnId", mColumnId);
        String title = getIntent().getStringExtra("title");
        findViewById(R.id.title_layout).setVisibility(View.VISIBLE);
        setMiddleTitle("种草推荐购");
        initLabel();
        requestLabel();
    }

    private void initLabel() {
        mRadioGroupLabel.setOnCheckedChangeListener((group, checkedId) -> {
            RadioButton radioButton = mRadioGroupLabel.findViewById(checkedId);
            mColumnId = (int) radioButton.getTag();
            refresh_flag_is_change = true;
            getRefreshController().manualRefresh();
        });
    }

    private void requestLabel() {
        L00bangRequestManager2.getServiceInstance()
                .getColumnsByPosition(LING_LAB_POSITION)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new NormalSubscriber<List<HotType>>(getContext()) {
                    @Override
                    public void onSuccess(List<HotType> hotTypes) {
                        super.onSuccess(hotTypes);
                        if (hotTypes != null && hotTypes.size() > 0) {
                            mColumnId = Integer.parseInt(hotTypes.get(0).getColumnId());
//                            LingLabFragment.this.refresh();
                            addColumnList(hotTypes);
                        }

                    }

                });
    }

    private void addColumnList(List<HotType> hotTypes) {
        //先清空
        RadioButton radioButton1 = mRadioGroupLabel.findViewWithTag(mColumnId);
        if (radioButton1 != null) {
            radioButton1.setChecked(false);
        }
        //先移除
        mRadioGroupLabel.removeAllViews();
        for (HotType hotType : hotTypes) {
            RadioButton radioButton = new RadioButton(getContext());
            radioButton.setText(hotType.getColumnName());
            radioButton.setButtonDrawable(null);
            radioButton.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimension(R.dimen.activity_set_text_24));
            ColorStateList colorStateList =
                    getResources().getColorStateList(R.color.selector_ling_lab_color);
            radioButton.setTextColor(colorStateList);
            radioButton.setBackground(AppCompatResources.getDrawable(getContext(), R.drawable.select_ling_lab_label));
            radioButton.setPadding(getContext().getResources().getDimensionPixelSize(R.dimen.normal_20), 0, getContext().getResources().getDimensionPixelSize(R.dimen.normal_20), 0);
            radioButton.setTag(Integer.parseInt(hotType.getColumnId()));
            mRadioGroupLabel.addView(radioButton);
            RadioGroup.LayoutParams lp = new RadioGroup.LayoutParams(radioButton.getLayoutParams());
            lp.setMargins(getContext().getResources().getDimensionPixelSize(R.dimen.normal_12), 0, getContext().getResources().getDimensionPixelSize(R.dimen.normal_12), 0);
            radioButton.setLayoutParams(lp);
            if (Integer.parseInt(hotType.getColumnId()) == mColumnId) {
                radioButton.setChecked(true);
            }
        }
        mHorizontalScrollView.scrollTo(0, 0);

    }

    @Override
    public RefreshController<Object> createRefreshController() {
        return new RefreshController<Object>(this) {

            /**
             * 标识是不是从顶部下拉，因为下拉也会加载更多，所以不能从pageNo判断，否则会出现刷新异常
             */
            private boolean isRefreshMore = true;

            @Override
            protected boolean loadDataAfterInitViews() {
                return false;
            }

            @Override
            protected RecyclerView.LayoutManager createLayoutManager(Context context) {
                StaggeredGridLayoutManager layoutManager = new StaggeredGridLayoutManager(2, StaggeredGridLayoutManager.VERTICAL);
                //防止 item 交换位置
                layoutManager.setGapStrategy(StaggeredGridLayoutManager.GAP_HANDLING_NONE);
                return layoutManager;
            }

            @Override
            public void initViews(View rootView) {
                super.initViews(rootView);
                int px16 = rootView.getResources().getDimensionPixelSize(R.dimen.normal_16);
                recyclerView.setPadding(px16, 0, 0, 0);
                recyclerView.setBackgroundColor(Color.parseColor("#f8f8f8"));
                //防止顶部空白
                recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
                    @Override
                    public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                        RecyclerView.LayoutManager layoutManager;
                        if (newState == RecyclerView.SCROLL_STATE_IDLE && (layoutManager = recyclerView.getLayoutManager()) instanceof StaggeredGridLayoutManager) {
                            StaggeredGridLayoutManager staggeredGridLayoutManager = (StaggeredGridLayoutManager) layoutManager;
                            int[] first = new int[2];
                            staggeredGridLayoutManager.findFirstCompletelyVisibleItemPositions(first);
                            if (first[0] <= 1 || first[1] <= 1) {
                                staggeredGridLayoutManager.invalidateSpanAssignments();
                            }
                        }
//                        ivBackToTop.setVisibility(recyclerView.computeVerticalScrollOffset() > 200 ? View.VISIBLE : View.GONE);
                    }
                });
            }

            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return null;
            }

            @Override
            protected void onLoadSuccess(int loadPage, List<Object> list, int loadType) {
                if (isRefreshMore) {
                    if (mData != null) {
                        mData.clear();
                    }
                }
                if ((loadPage == 1 || isRefreshMore) && mTopModel != null && !list.contains(mTopModel)) {
                    list.add(0, mTopModel);
                }
                super.onLoadSuccess(loadPage, list, loadType);
                isRefreshMore = false;
            }

            @Override
            public void onRefresh() {
                if (mColumnId <= 0) {
                    onRefreshComplete();
                    return;
                }
                isRefreshMore = true;
                //如果变化了，将pageNo置为0
                if (refresh_flag_is_change) {
                    mPageNo = 0;
                    //当有变化刷新时候从第0页开始，同时将值置位false
                    refresh_flag_is_change = false;
                    super.onRefresh();
                } else {
                    //如果还可以加载更多，那就继续加载下一页
                    if (getRefreshController().getSwipeToLoadLayout().isLoadMoreEnabled()) {
                        onLoadMore();
                    } else {//否则加载第一页
                        super.onRefresh();
                    }
                }
//                getSearchBoxRecommendList();
            }

            @Override
            protected void onLoadMoreComplete(int size) {
                if (isRefreshMore) {
                    adapter.notifyDataSetChanged();
                    swipeToLoadLayout.setRefreshing(false);
                } else {
                    adapter.notifyItemRangeInserted(getData().size() - size, size);
                    swipeToLoadLayout.setLoadingMore(false, true);
                }
            }

        };
    }

    @Override
    public Observable<BaseResponse<List<Object>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return service2.getPostsByColumnId(pageNo, pageSize, mColumnId).map(baseResponse -> {
            //取本地记录的值，如果没有保存一次
            if (baseResponse.getData().getEndPageStatus() == 1) {
                setRefreshNextTime(baseResponse);
            } else if (refresh_flag >= 0) {
                //如果有值，拿本地记录的值和当前返回的值作比较
                //如果不同，记录下有变化，同时更新标志位数值
                if (refresh_flag != baseResponse.getData().getRefreshFlag()) {
                    setRefreshNextTime(baseResponse);
                }
            } else {
                refresh_flag = baseResponse.getData().getRefreshFlag();
            }

            List<Object> list = new ArrayList<>(baseResponse.getData().getPosts());
            return baseResponse.cloneWithData(list);
        });
    }

    private void setRefreshNextTime(BaseResponse<HotType> baseResponse) {
        refresh_flag_is_change = true;
        refresh_flag = baseResponse.getData().getRefreshFlag();
    }

//    @Override
//    public void onStart() {
//        super.onStart();
//        SensorsUtils.sensorsViewStart(FinalSensors.BROWSE_HOME_LING_LAB);
//    }
//
//    @Override
//    public void onStop() {
//        super.onStop();
//        SensorsUtils.sensorsViewEndNew(FinalSensors.HOME_LING_LAB_EVENT_PAGE, FinalSensors.BROWSE_HOME_LING_LAB);
//    }
//
//    @Override
//    public void onHiddenChanged(boolean hidden) {
//        super.onHiddenChanged(hidden);
//        if (hidden) {
//            SensorsUtils.sensorsViewEndNew(FinalSensors.HOME_LING_LAB_EVENT_PAGE, FinalSensors.BROWSE_HOME_LING_LAB);
//        } else {
//            SensorsUtils.sensorsViewStart(FinalSensors.BROWSE_HOME_LING_LAB);
//        }
//    }

//    @Override
//    public void onClick(View v) {
//        if (v.getId() == R.id.iv_back_to_top) {
//            getRefreshController().getRecyclerView().scrollToPosition(0);
//            ivBackToTop.setVisibility(View.GONE);
//        }
//    }
}
