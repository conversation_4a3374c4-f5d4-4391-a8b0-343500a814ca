package com.cloudy.linglingbang.activity.community.post.shortVideo;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.View;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.community.post.CommentReplyAdapter;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.widget.NoScrollListView;
import com.cloudy.linglingbang.app.widget.PraiseCountTextView;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.Comment;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.NormalSubscriber;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 视频贴评论的ViewHolder
 *
 * <AUTHOR>
 * @date 2018/11/19
 */
public class ShortVideoCommentViewHolder extends BaseRecyclerViewHolder<Comment> {
    private Context mContext;
    private ShortVideoCommentReplyAdapter.OnReplyClickListener mReplyClickListener;
    private BaseRecyclerViewAdapter<Comment> mCommentAdapter;
    /**
     * 点赞数
     */
    @BindView(R.id.tv_praise_count)
    PraiseCountTextView mTvPraiseCount;

    /**
     * 头像
     */
    @BindView(R.id.iv_header)
    ImageView mIvHeader;

    /**
     * 昵称
     */
    @BindView(R.id.tv_nickname)
    TextView mTvNickname;

    /**
     * 评论内容
     */
    @BindView(R.id.tv_comment_content)
    TextView mTvCommentContent;

    /**
     * 回复的回复
     */
    @BindView(R.id.lv_comment_reply)
    NoScrollListView mLvCommentReply;

    @BindView(R.id.iv_big_v)
    ImageView mVipView;

    public ShortVideoCommentViewHolder(ShortVideoPostCommentAdapter adapter, View itemView) {
        super(itemView);
        mCommentAdapter = adapter;
    }

    //设置点击回复的监听
    public void setOnReplyClickListener(ShortVideoCommentReplyAdapter.OnReplyClickListener listener) {
        this.mReplyClickListener = listener;
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        ButterKnife.bind(this, itemView);
        mContext = itemView.getContext();
    }

    @Override
    public void bindTo(final Comment comment, int position) {
        super.bindTo(comment, position);
        //防止滚动不到顶端，设置移除焦点
        mLvCommentReply.setFocusableInTouchMode(false); //设置不需要焦点
        mLvCommentReply.requestFocus(); //设置焦点不需要
        if (comment == null) {
            return;
        }
        setPraiseCount(comment);
        if (!TextUtils.isEmpty(comment.getCommentContent())) {
            mTvCommentContent.setText(comment.getCommentContent());
        }
        setPraiseState(comment);
        //显示回复的回复
        List<Comment> subComments = comment.getSubWebComments();
        if (subComments == null || subComments.size() == 0) {
            mLvCommentReply.setVisibility(View.GONE);
        } else if (subComments != null && subComments.size() > 0) {
            mLvCommentReply.setVisibility(View.VISIBLE);
            CommentReplyAdapter commentReplyAdapter = new ShortVideoCommentReplyAdapter(mContext, comment.getSubWebComments(), position);
            mLvCommentReply.setAdapter(commentReplyAdapter);
            commentReplyAdapter.notifyDataSetChanged();

            ((ShortVideoCommentReplyAdapter) commentReplyAdapter).setOnReplyClickListener(new ShortVideoCommentReplyAdapter.OnReplyClickListener() {
                @Override
                public void onReplyClick(Comment comment, int position) {
                    if (mReplyClickListener != null) {
                        mReplyClickListener.onReplyClick(comment, position);
                    }
                }
            });
        }

        //用户相关
        if (comment.getUser() != null) {
            new ImageLoad(mContext, mIvHeader, AppUtil.getImageUrlBySize(comment.getUser().getPhoto(), AppUtil._120X120), ImageLoad.LoadMode.URL)
                    .setPlaceholder(R.drawable.user_head_default_120x120)//设置占位图
                    .setCircle(true)
                    .load();//执行加载图片
            if (!TextUtils.isEmpty(comment.getUser().getNickname())) {
                mTvNickname.setText(comment.getUser().getNickname());
            }
            //是否显示大V
            mVipView.setVisibility(UserUtils.isBigV(comment.getUser()) ? View.VISIBLE : View.GONE);
        }
        //设置评论内容的点击事件
        mTvCommentContent.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                int position = getAdapterPosition();
                if (mReplyClickListener != null) {
                    mReplyClickListener.onReplyClick(comment, position);
                }
            }
        });

        //赞
        mTvPraiseCount.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!AppUtil.checkLogin(mContext, AppUtil.RegisterChannel.CHANNEL_POST)) {
                    return;
                }
                int itemPosition = getAdapterPosition();
                if (itemPosition == -1) {
                    return;
                }
                if (comment == null || comment.getCommentId() == null) {
                    return;
                }
                if (comment.getIsPraise() == 1) {
                    ToastUtil.showMessage(mContext, "您已经赞过了");
                } else {
                    //先成功，失败后复原
                    //显示点赞数
                    Drawable drawableTop = comment.getIsPraise() == 1 ? mContext.getResources().getDrawable(R.drawable.ic_video_comment_praise) : mContext.getResources().getDrawable(R.drawable.ic_video_comment_un_praise);
                    mTvPraiseCount.setCompoundDrawables(null, drawableTop, null, null);

                    mTvPraiseCount.setTextColor(mContext.getResources().getColor(R.color.color_ff5252));
                    mTvPraiseCount.addCount((int) (comment.getPraiseCount()));
                    comment.setPraiseCount(comment.getPraiseCount() + 1);
                    comment.setIsPraise(1);
                    //不调用notify方法，只更新局部的view
//                    mCommentAdapter.notifyItemChanged(getAdapterPosition());
                    if (comment.getIsPraise() == 1) {
                        mTvPraiseCount.setCompoundDrawablesWithIntrinsicBounds(null, mContext.getResources().getDrawable(R.drawable.ic_video_comment_praise), null, null);
                        //点赞动画
                        mTvPraiseCount.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.post_praise_anim));
                    } else {
                        mTvPraiseCount.setCompoundDrawablesWithIntrinsicBounds(null, mContext.getResources().getDrawable(R.drawable.ic_video_comment_un_praise), null, null);
                    }
                    L00bangRequestManager2
                            .getServiceInstance()
                            .praiseComment(comment.getCommentId())
                            .compose(L00bangRequestManager2.<String>setSchedulers())
                            .subscribe(new NormalSubscriber<String>(mContext) {
                                @Override
                                public void onSuccess(String s) {
                                    super.onSuccess(s);
                                    //点赞完成后，更新点赞，主要是置顶的评论
                                    updatePraiseStatus(getAdapterPosition());
                                }

                                @Override
                                public void onFailure(Throwable e) {
                                    super.onFailure(e);
                                    final Comment comment = mCommentAdapter.getData().get(getAdapterPosition());
                                    comment.setPraiseCount(comment.getPraiseCount() - 1);
                                    comment.setIsPraise(0);
//                                    mCommentAdapter.notifyItemChanged(getAdapterPosition());
                                    /*if (comment.getPraiseCount() == 0) {
                                        tv_praise_count.setText("点赞");//回复数
                                    } else {*/
//                                        延迟执行,防止+1的动画没有完成，又被刷新了数字
                                    mTvPraiseCount.postDelayed(new Runnable() {
                                        @Override
                                        public void run() {
                                            setPraiseCount(comment);
                                        }
                                    }, 500);

                                    /*}*/
                                    setPraiseState(comment);
                                }
                            });
                }
            }

            /**
             * 更新点赞的状态
             * 当前 position 的已经改变了，只需要处理别的
             */
            private void updatePraiseStatus(int position) {
                List<Comment> commentList = mCommentAdapter.getData();
                if (commentList != null && position < commentList.size()) {
                    Comment anchorComment = commentList.get(position);
                    if (anchorComment != null) {
                        Long anchorCommentId = anchorComment.getCommentId();
                        if (anchorCommentId != null && anchorCommentId != 0) {
                            //遍历
                            for (int i = 0; i < commentList.size(); i++) {
                                if (i == position) {
                                    //不处理当前
                                    continue;
                                }
                                Comment comment = commentList.get(i);
                                if (comment != null) {
                                    Long commentId = comment.getCommentId();
                                    if (commentId != null && commentId.equals(anchorCommentId)) {
                                        comment.setPraiseCount(comment.getPraiseCount() + 1);
                                        comment.setIsPraise(1);
                                        mAdapter.notifyItemChanged(i);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * 设置点赞状态
     */
    protected void setPraiseState(Comment comment) {
        Drawable drawableTop;
        if (comment.getIsPraise() == 1) {
            drawableTop = mContext.getResources().getDrawable(R.drawable.ic_video_comment_praise);
            mTvPraiseCount.setTextColor(mContext.getResources().getColor(R.color.color_ff5252));
        } else {
            drawableTop = mContext.getResources().getDrawable(R.drawable.ic_video_comment_un_praise);
            mTvPraiseCount.setTextColor(mContext.getResources().getColor(R.color.color_9b9b9b));
        }
        mTvPraiseCount.setCompoundDrawablesWithIntrinsicBounds(null, drawableTop, null, null);
    }

    private void setPraiseCount(Comment comment) {
        if (comment.getPraiseCount() == 0) {
            mTvPraiseCount.setText(R.string.post_praise);
        } else {
            mTvPraiseCount.setText(AppUtil.getCommentDesc(comment.getPraiseCount()));
        }
    }
}
