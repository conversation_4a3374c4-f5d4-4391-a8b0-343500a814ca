package com.cloudy.linglingbang.activity.fragment.store.youpin;

import android.content.Context;

import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.model.SourceModel;
import com.cloudy.linglingbang.model.server.Ad.AdJumpUtil2;
import com.cloudy.linglingbang.model.store.home.StoreLayoutElement;

/**
 * 优品调整页面工具
 *
 * <AUTHOR>
 * @date 2020/5/10
 */
public class StoreHomeJumpPageUtils {

    /***
     *根据组件跳转
     */
    public static void goToPageByElement(Context context, StoreLayoutElement layoutElement, SensorsUtils.StoreHomeAnchor storeHomeAnchor) {

        if (layoutElement != null) {
            int linkType = layoutElement.getLinkType();
            SourceModel sourceModel = new SourceModel(SourceModel.POSITION_TYPE.COMPONENTS_TYPE, "组件");
            if (storeHomeAnchor != null) {
                if (!storeHomeAnchor.getOrderSourceType().isEmpty()) {
                    sourceModel = new SourceModel(storeHomeAnchor.getOrderSourceType(), storeHomeAnchor.getOrderSource());
                } else {
                    sourceModel = new SourceModel(SourceModel.POSITION_TYPE.COMPONENTS_TYPE, storeHomeAnchor.getPageCode());
                }
                AdJumpUtil2.goToActivity(context, linkType, layoutElement.getLinkUrl(), storeHomeAnchor.getPageName(), storeHomeAnchor.getPageCode(), sourceModel);
            } else {
                AdJumpUtil2.goToActivity(context, linkType, layoutElement.getLinkUrl(), "组件", "组件", sourceModel);
            }

            //组件的新埋点
            String componentName = "";
            String commodityCategoryStr = "";
            if (layoutElement.getProductVo() != null && layoutElement.getProductVo().getProductName() != null) {
                componentName = layoutElement.getProductVo().getProductName();

                commodityCategoryStr = layoutElement.getProductVo().getCommodityCategoryStr();
            } else {
                if (layoutElement.getTitle() != null) {
                    componentName = layoutElement.getTitle();
                }
            }
            SensorsUtils.sensorsClickComponent("点击(" + commodityCategoryStr + ")-(" + componentName + ")", componentName, storeHomeAnchor);
        }
    }
}
