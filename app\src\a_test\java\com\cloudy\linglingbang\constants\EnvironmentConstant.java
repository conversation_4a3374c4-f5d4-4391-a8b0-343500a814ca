package com.cloudy.linglingbang.constants;

import com.qinggan.mobile.tsp.remote.RemoteManager;

/**
 * 区分环境配置的常量（如果不同的环境需要不同的常量配置，放在此文件中）
 * test1 测试一环境
 *
 * <AUTHOR>
 * @date 2018/2/2
 */
public class EnvironmentConstant {
    //埋点地址
    public static final String SA_SERVER_URL = "https://kgw.baojunev.com/outer/test/logif/api/log/appBuryingPoint?project=app-llb&token=token1";//测试;

    /**
     * API地址
     */
    public static final String BASE_API_HOST_URL = "https://api-test.00bang.cn/";
    public static final String BASE_API_URL = "https://api-test.00bang.cn/llb/";
    /** API-third地址 */
    public static final String BASE_API_THIRD_URL = "https://api-test.00bang.cn/third/";
    /**
     * 广告埋点API地址
     */
    public static final String BASE_SENSOR_AD_API_URL = "https://event-test.00bang.cn/event/";

    public static final String BASE_SENSORS_URL = "https://anchor-test.00bang.cn/";

    /**
     * dealer API地址（目前只有test1环境）
     */
    public static final String BASE_DEALER_API_URL = "https://api-test.00bang.cn/dealer/";
    /**
     * dealer API地址 忽略梆梆安全
     */
    public static final String BASE_DEALER_API_URL1 = "https://hapi-test.00bang.cn/dealer/";

    public static final String BASE_OPEN_API_URL = "https://openapi-test.00bang.cn/";

    /**
     * 新宝骏的域名
     */
    public static final String BASE_BAOJUN_OPEN_API_URL = "https://openapi-test.baojun.net/";

    /**
     * H5页面地址
     */
    public static final String BASE_WEB_HOST_URL = "https://m-test.00bang.cn";
    public static final String BASE_WEB_URL = "https://m-test.00bang.cn/llb";

    /** 五菱的H5页面地址 */
    public static final String SGMW_BASE_WEB_URL = "https://m-test.baojun.net/lingClub";

    /**
     * H5页面地址WX
     */
    public static final String BASE_WX_WEB_URL = "https://m-test.00bang.cn/wx";

    /** H5经销商base_url配置 */
//    public static final String BASE_WEB_URL_DMS = "https://dms-m-dev.00bang.cn/dms";
    public static final String BASE_WEB_URL_DMS = "https://m-test.00bang.cn/llb";

    /** 上传资源文件的url配置 */
    public static final String BASE_RES_URL = "https://uf-test.00bang.cn/";
    public static final String BASE_RES_URL_NEW = "https://fs-test.00bang.cn/";

    /** 宝骏res服务器上传地址 */
    public static final String BAOJUN_RES_URL = "https://file-test.baojun.net/file/secret/upload";

    /** 宝骏res服务器上传地址-主域名 */
    public static final String BAOJUN_BASE_RES_URL = "https://file-test.baojun.net/";

    /** 微吼用户登录名前缀 */
    public static final String VHALL_PRE_USERNAME = "test_";

    /** 新车专区(宝骏530专区)的默认channelId （注意：因为在test2中测试，未设置该项） */
    public static final long NEW_VEHICLE_ZONE_CHANNEL_ID = 1146;

    /** 专区默认车型id （注意：因为在test2中测试，未设置该项） */
    public static final String NEW_VEHICLE_ZONE_CAR_TYPE_ID = "49";

    /** 专区默认的栏目id （注意：因为在test2中测试，未设置该项） */
    public static final Long[] NEW_VEHICLE_ZONE_COLUMN_ID_ARRAY = {49L, 50L, 51L};

    /** 微吼相关key值 */
    public static final String VHALL_APPKEY = "0dcbc63026bb57372ef7dd0ca1d00c92";
    public static final String VHALL_APPSECRETKEY = "cce04d9acf51cd5c9487f14e50ecc4e5";

    /** 首页fragment 4S店地址 */
    public static String FRAGMENT_4S_URL = "http://dealer.m-devnew.00bang.cn/dealer/store.html";

    /** 微信分享 **/
    public static final String WX_APP_ID = "wx0c4d84a74a0b8f39";
    public static final String WX_APP_SECRET = "6cd93000c503930d1e90761b0e22d26d";

    /** 微信支付域名 **/
    public static final String WX_PAY_REFERER = "https://sgmwsales.com";

    //登录相关
    /**
     * 客户端ID  固定值
     */
    public static final String LOGIN_CLIENT_ID = "2019041810222516127";
    /**
     * 客户端秘钥 固定值
     */
    public static final String LOGIN_CLIENT_SECRET = "7583a5935c7a48434da5a72ce9ba7721";
    /**
     * 所有请求计算签名的盐值
     */
    public static final String REQUEST_SALT = "70ca2a1e49ada8aca5f1b94c77df3ba0";
    /**
     * 使用白盒秘钥 AES加密的key
     */
    public static final String AES_EKEY = "e4806fe0fcb2712609524502f3222f1784fa63c45fcb91088d28b571db120aad982152ed55b43d91e3343464547921e97391404df9934e1714815159aa6ec1278c258bf809d6c796557d49020cbf11b398af2b2c341723d5817e26cfb2dc9f644fbd1afad4e80fd0b78091e7fea1b5aff6e0bb5351631829ed559f18e4b915d43e5e20aa112cac785d2909ad0677d8e2cdfb9f2cdf6d35abcf12ceeb5332cc114c41ff99470be601fc3b636e541d8aac1881b13b6006468507624f5abc5bdd7e9b99a32c7d661ecf2e640b9743f1a86c67a4c08606cb9503e2fb0020b8640ce0a1a3ace1588b106afba7837ca3bbe31bde9586ef";

    /**
     * 使用白盒秘钥 AES解密的key
     */
    public static final String AES_DKEY = "0b3e1a0e9b461cb24de525bda52841287b2af46251b0e5797ce32809be954554440a82995eb3272718dbf915080d07db4e54712a3f1a6a5bdaae20aaae8222021502788733f89a9862efb70aceaa70815c71476ea1f0f23782d2e0e225c8224e4142cf8a6cedabdfe83ae9e7a988db10b81230fcca8e083ab4d081e054ea0f69092c6bfb7d02feb7eae0dc216323745b951890be2e9f19f521f37f7092312687e545b62c2bff81174d51cafe37e2ca6b0bbf624669c3f13429f4dce44b1358ff72def522b55be778d4c9055e82faeacc777416fa9883e15992201d6519ebda76d2339ae72c88aa82359bb1010ac254c9238e8221";

    /**
     * 闪验的app_id
     */
    public static final String SY_APP_ID = "FGd9aQ26";

    /** 电子手册地址 */
    public static final String ELECTRONIC_MANUAL = "https://manual.1pi.cn/index/baojun";

    /** 在线客服聊天页面 */
    public static final String MM_CONSULTANT_SERVICE = "https://sgmw.web.sti-uat.com:10006/Home/chatMobile";

    /** 在线客服首页 */
    public static final String MM_CONSULTANT_SERVICE_HOME = "https://sgmw.web.sti-uat.com:10006/Home/chatHome";

    /** 新在线客服 */
    public static final String MM_CONSULTANT_SERVICE_URL = "https://cac-test.sgmwsales.com:1443/am-mcs-web/h5/MS/2020010610017712/indexApp.html";

    /**
     * 1. LiteAVSDK Licence。 用于直播推流鉴权。
     * <p>
     * 获取License，请参考官网指引 https://cloud.tencent.com/document/product/454/34750
     */
    public static final String LICENCE_URL = "http://license.vod2.myqcloud.com/license/v1/37fa4db3b61eeccd5f8c83e5438f8297/TXLiveSDK.licence";
    public static final String LICENCE_KEY = "3ff57f3beef34b42cb6e6e350de7d8c8";

    //设置博泰环境
    public static final int PATEO_SDK_ENVIRONMENT = RemoteManager.IP_PERF;

    /** 友盟key */
    public static final String UMENG_KEY = "5ce7bff74ca357a02f00067f";

    /**
     * 统一车控-补电须知
     */
    public static final String UNIFY_POWER_UP_URL = "https://m-pre.baojun.net/lingClub/agreement/user-agreement";

    /**
     * 扫码绑车-扫码结果
     */
    public static final String SCAN_RESULT_URL = "https://test-cdn-m.baojun.net/share/code";

    /**
     * 扫码绑车-跳转链接
     */
    public static final String SCAN_JUMP_URL = "https://m-test.baojun.net/lingClub/love-car/bindcar/";
}
