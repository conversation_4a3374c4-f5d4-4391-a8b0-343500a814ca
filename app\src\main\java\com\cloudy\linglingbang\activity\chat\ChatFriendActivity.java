package com.cloudy.linglingbang.activity.chat;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import com.cloudy.linglingbang.ApplicationLLB;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshFragment;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.my.MyFansActivity;
import com.cloudy.linglingbang.adapter.chat.FriendListAdapter;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.user.User;
import com.umeng.analytics.MobclickAgent;

import java.util.List;

import androidx.recyclerview.widget.RecyclerView;
import butterknife.OnClick;
import io.reactivex.rxjava3.core.Observable;

/**
 * 我的好友页
 * Created by hanfei on 16/10/8.
 */

public class ChatFriendActivity extends BaseActivity {

    @Override
    public void loadViewLayout() {
        setContentView(R.layout.activity_chat_friend);
    }

    @Override
    public void initialize() {
        super.mToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                setResult(Activity.RESULT_OK);
                finish();
            }
        });
    }

    @Override
    protected void onStart() {
        super.onStart();
        if (!ApplicationLLB.isLogin()) {
            return;
        }
    }

    /**
     * 点击通讯录
     *
     * @param view
     */
    @OnClick(R.id.ll_add_friend)
    public void addFriendOnclick(View view) {
        //友盟统计
        MobclickAgent.onEvent(this, "31");
        Intent intent = new Intent();
        intent.setClass(ChatFriendActivity.this, ContactsFriendActivity.class);
        startActivity(intent);
    }

    /**
     * 附近的人
     *
     * @param view
     */
    @OnClick(R.id.ll_friend_around)
    public void friendAroundOnclick(View view) {
        //友盟统计
        MobclickAgent.onEvent(this, "34");
        Intent intent = new Intent();
        intent.setClass(ChatFriendActivity.this, NearbyFriendActivity.class);
        startActivity(intent);
    }

    @Override
    public void onBackPressed() {
        setResult(Activity.RESULT_OK);
        super.finish();
    }

    public static class UserListFragment extends BaseRecyclerViewRefreshFragment<User> {

        private static final String INTENT_EXTRA = "intent_extra";

        private MyFansActivity.IntentExtra mIntentExtra;

        @Override
        public void onAttach(Activity activity) {
            if (getArguments() != null) {
                mIntentExtra = (MyFansActivity.IntentExtra) getArguments().getSerializable(INTENT_EXTRA);
                if (mIntentExtra == null) {
                    return;
                }
            } else {
                //我的关注页面进入
                mIntentExtra = new MyFansActivity.IntentExtra(null, MyFansActivity.IntentExtra.RELATION_TYPE_ATTENTION);
            }
            super.onAttach(activity);
        }

        @Override
        public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(final List<User> list) {
            FriendListAdapter adapter = new FriendListAdapter(getActivity(), list);
            adapter.setAttentionType(mIntentExtra != null ? mIntentExtra.getType() : -1);
            adapter.setOnItemClickListener(new BaseRecyclerViewHolder.OnItemClickListener() {
                @Override
                public void onItemClick(View itemView, int position) {
                    JumpPageUtil.goToPersonPage(getActivity(), list.get(position));
                }
            });
            return adapter;
        }

        @Override
        public RefreshController<User> createRefreshController() {
            String emptyString;
            if (mIntentExtra.getType() == MyFansActivity.IntentExtra.RELATION_TYPE_ATTENTION) {
                emptyString = getContext().getString(R.string.empty_attention);
            } else {
                emptyString = getContext().getString(R.string.empty_fans);
            }
            return super.createRefreshController().setEmptyString(emptyString).setEmptyImageResId(R.drawable.ic_message_empty_cry);
        }

        public static UserListFragment newInstance(MyFansActivity.IntentExtra intentExtra) {
            Bundle bundle = new Bundle();
            bundle.putSerializable(INTENT_EXTRA, intentExtra);
            UserListFragment fragment = new UserListFragment();
            fragment.setArguments(bundle);
            return fragment;
        }

        @Override
        public Observable<BaseResponse<List<User>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
            if (mIntentExtra.getSelfType() == MyFansActivity.IntentExtra.SELF_EXTRA_SELF) {
                return service2.getMyFriends(mIntentExtra.getType(), pageNo, pageSize);
            } else {
                return service2.getOtherUserFriends(mIntentExtra.getUserIdStr(), mIntentExtra.getType(), pageNo, pageSize);
            }
        }
    }

}
