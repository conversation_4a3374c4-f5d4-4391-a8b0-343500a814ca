package com.cloudy.linglingbang.activity.store.commodity.adapter.holder

import android.view.View
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder
import com.cloudy.linglingbang.model.store.commodity.CenterCommodity

/**
 * <AUTHOR>
 * @date 2022/9/30
 */
open class BaseCommodityHolder<MODEL>(itemView: View?) : BaseRecyclerViewHolder<MODEL>(itemView) {
    var mCenterCommodity: CenterCommodity? = null
}