package com.cloudy.linglingbang.activity.fragment.store.youpin;

import android.os.Bundle;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.fragment.mynew.util.SelfUserInfoLoader;
import com.cloudy.linglingbang.activity.fragment.store.home.StoreHomeTabFragment;
import com.cloudy.linglingbang.activity.fragment.store.home.model.StoreHomeConstant;
import com.cloudy.linglingbang.activity.fragment.store.rights.VipRightsAdapter;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.constants.FinalSensors;
import com.cloudy.linglingbang.model.server.Ad.Ad2;
import com.cloudy.linglingbang.model.user.User;
import com.cloudy.linglingbang.model.wrapper.EmptyInfo;

import java.util.List;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 生活-尊享权益-尊享权益
 *
 * <AUTHOR>
 * @date 2020/5/6
 */
public class SuperiorProductFragment extends StoreHomeTabFragment {
    public static final String EXTRA_NEED_LOAD_LAZY = "need_load_lazy";
    public static final String EXTRA_PAGE = "from_page";

    /**
     * 记录用户id
     */
    private String userIdStr;
    private String page;
    private String code;

    public static Fragment newInstance(String code, boolean needLazy) {
        return newInstance(code, null, needLazy);
    }

    public static Fragment newInstance(String code, String page, boolean needLazy) {
        SuperiorProductFragment fragment = new SuperiorProductFragment();
        Bundle bundle = new Bundle();
        bundle.putString(IntentUtils.INTENT_EXTRA_COMMON, code);
        bundle.putString(SuperiorProductFragment.EXTRA_PAGE, page);
        bundle.putBoolean(SuperiorProductFragment.EXTRA_NEED_LOAD_LAZY, needLazy);
        bundle.putSerializable(IntentUtils.INTENT_EXTRA_FROM, new SensorsUtils.StoreHomeAnchor(code, StoreHomeConstant.getPageNameByPageCode(code), code));
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle bundle = getArguments();
        if (bundle != null) {
            if (bundle.containsKey(EXTRA_NEED_LOAD_LAZY)) {
                setNeedLazyLoad(bundle.getBoolean(EXTRA_NEED_LOAD_LAZY));
            }
            page = bundle.getString(EXTRA_PAGE);
            code = bundle.getString(IntentUtils.INTENT_EXTRA_COMMON);
        }
    }

    @Override
    protected void initViews() {
        super.initViews();
        userIdStr = User.getsUserInstance().getUserIdStr();
    }

    @Override
    protected void onRefresh() {
        super.onRefresh();
        //刷新购物车数量
        SelfUserInfoLoader.getInstance().updateCartCountAndDispatch(getContext());
    }

    @Override
    public RefreshController<Object> createRefreshController() {
        super.createRefreshController();
        return new SuperiorProductRefreshController(this, mPageCode) {
            @Override
            public void onRefresh() {
                super.onRefresh();
                SuperiorProductFragment.this.onRefresh();
            }

            @Override
            public void initViews(View rootView) {
                super.initViews(rootView);
            }

            @Override
            protected void onLoadSuccess(int loadPage, List<Object> list, int loadType) {
                if (loadPage <= 1) {
                    mElementList = list;
                    List<Object> data = SuperiorProductFragment.this.buildData();
                    if (StoreHomeConstant.PAGE_CODE_VIP_RIGHTS.equals(mPageCode) && UserUtils.hasLogin()) {
                        if (data.isEmpty()) {
                            data.add(new EmptyInfo(getEmptyImageResId(), R.string.store_home_vip_empty));
                        }
                        //data.add(0, User.getsUserInstance());
                    }
                    super.onLoadSuccess(loadPage, data, loadType);
                } else {
                    super.onLoadSuccess(loadPage, list, loadType);
                }
            }

            @Override
            public int getErrorImageResId() {
                return R.drawable.ic_store_vip_empty;
            }

            @Override
            public int getEmptyImageResId() {
                return R.drawable.ic_store_vip_empty;
            }

            @Override
            public String getEmptyString() {
                return getContext().getString(R.string.store_home_vip_empty);
            }

            @Override
            protected boolean isNeedRequestMoreGoods() {
                return mPageCode.equals(StoreHomeConstant.PAGE_CODE_MORE_COMMODITY);
            }
        };
    }

    public void userChange() {
        if (isRealViewLoaded()) {
            //用户变更时刷新
            if (!UserUtils.isSelf(userIdStr) && getRefreshController() != null) {
                getRefreshController().onRefresh();
            }
            userIdStr = User.getsUserInstance().getUserIdStr();
        }

    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<Object> list) {
        if (StoreHomeConstant.PAGE_CODE_VIP_RIGHTS.equals(mPageCode)) {
            return new VipRightsAdapter(getContext(), super.createAdapter(list));
        }
        return super.createAdapter(list);
    }

    @Override
    protected int getAdPageCode() {
        if (StoreHomeConstant.PAGE_CODE_LIFE_GOODS.equals(mPageCode)) {
            return Ad2.POSITION.STORE_HOME_TOP_BANNER;
        }
        return 0;
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (hidden) {
            SensorsUtils.sensorsViewEndNew("服务-推荐", FinalSensors.BROWSE_LIFE_INFORMATION, "浏览" + "服务-推荐");
        } else {
            SensorsUtils.sensorsViewStart(FinalSensors.BROWSE_LIFE_INFORMATION);
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        SensorsUtils.sensorsViewStart(FinalSensors.BROWSE_LIFE_INFORMATION);
    }

    @Override
    public void onStop() {
        super.onStop();
        SensorsUtils.sensorsViewEndNew(page, FinalSensors.BROWSE_LIFE_INFORMATION, page);
    }
}
