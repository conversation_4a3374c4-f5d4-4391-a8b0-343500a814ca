package com.cloudy.linglingbang.activity.car.connected;

import android.Manifest;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.activity.travel.TravelFragment;
import com.cloudy.linglingbang.activity.travel.model.CarStatusFromBluetooth;
import com.cloudy.linglingbang.activity.travel.model.UnifyControlBtnStat;
import com.cloudy.linglingbang.activity.travel.model.UnifyServiceLists;
import com.cloudy.linglingbang.activity.travel.model.banma.BluetoothKey;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.FastClickUtil;
import com.cloudy.linglingbang.app.util.PermissionUtils;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog;
import com.cloudy.linglingbang.constants.EnvironmentConstant;
import com.cloudy.linglingbang.model.car.control.BanmaControlResult;
import com.cloudy.linglingbang.model.car.control.SGMWDefaultCarStatInfo;
import com.cloudy.linglingbang.model.car.control.UnifyControlResult;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.NormalSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.travel.UnifyMiniLife;
import com.ebanma.sdk.blekey.BMBleControlManager;
import com.ebanma.sdk.blekey.BleKeyUtils;
import com.ebanma.sdk.blekey.CommandTypeEnum;
import com.ebanma.sdk.blekey.IBMBleCommandListener;
import com.ebanma.sdk.blekey.IBMBleControlListener;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import androidx.fragment.app.Fragment;
import okhttp3.MediaType;
import okhttp3.RequestBody;

import static android.app.Activity.RESULT_OK;
import static com.cloudy.linglingbang.activity.travel.TravelFragment.PERMISSION_REQUEST_CODE_BLUE_TOOTH;

/**
 * 斑马车控相关
 *
 * <AUTHOR>
 * @date 2022/6/17
 */
public class BanmaCarController implements IBMBleCommandListener, IBMBleControlListener {

    private Context mContext;
    private ControlResult controlResult;
    private CarStatusResultListener mCarStatusResultListener;
    private CarStatusResultViewListener mCarStatusResultViewListener;
    private List<CarStatusResultListener> mOnCarStatusListeners;
    private btnListResult btnListResult;
    private miniLifeResult miniLifeResult;
    private volatile static BanmaCarController instance = null;
    private List<String> serviceListParam = new ArrayList<>();
    public final static String CONTROL_BTN_LIST_CODE = "service_control";
    public final static String SERVICE_BTN_LIST_CODE = "service_tools";
    public final static String SETTING_BTN_LIST_CODE = "service_setting";

    private int pollingCount;

    private BMBleControlManager mBMBleControlManager;

    Handler handler;
    Runnable runnable;

    public static final int MODEL_WIFI = 0;//4G模式
    public static final int MODEL_BLUETOOTH = 1;//蓝牙模式

    /** 当前状态（蓝牙或者4G） */
    private int mCurrentModel;

    /** 上一次蓝牙控制时间，控制蓝牙车控时间必须间隔5s */
    private long mLastBluetoothControlTime;

    private OnBluetoothControlListener mOnBluetoothControlListener;

    /**
     * GPS未开启提示框
     */
    private CommonAlertDialog mGpsDialog;

    public BanmaCarController(Context context) {
        this.mContext = context;
    }

    /**
     * 蓝牙操作按钮的位置
     */
    private int blueCmdPosition;

    public static BanmaCarController getInstance(Context context) {
        synchronized (CarControlController2.class) {
            if (instance == null) {
                instance = new BanmaCarController(context);
            } else {
                instance.mContext = context;
            }
        }
        return instance;
    }

    public static BanmaCarController getInstance() {
        return instance;
    }

    public void carControlCmd(String serviceCode, String url, Map<String, String> map, String paramRequestLocation, String backStatus, boolean isAsynchronous, String safeCode, boolean isNeedSaveCode, String vin) {
        if (TextUtils.isEmpty(paramRequestLocation) || paramRequestLocation.equals("body")) {
            JSONObject root = new JSONObject();
            try {
                Iterator<Map.Entry<String, String>> it = map.entrySet().iterator();
                while (it.hasNext()) {
                    Map.Entry<String, String> entry = it.next();
                    if (entry.getKey().equals("authCode")) {
                        root.put(entry.getKey(), safeCode);
                    } else {
                        root.put(entry.getKey(), entry.getValue());
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), root.toString());
            L00bangRequestManager2.getServiceInstance()
                    .unifyControl(EnvironmentConstant.BASE_BAOJUN_OPEN_API_URL + "junApi" + url, requestBody)
                    .compose(L00bangRequestManager2.setSchedulers())
                    .subscribe(new NormalSubscriber<UnifyControlResult>(mContext) {
                        @Override
                        public void onSuccess(UnifyControlResult unifyControlResult) {
                            super.onSuccess(unifyControlResult);
                            pollingCount = 0;
                            if (unifyControlResult == null || TextUtils.isEmpty(unifyControlResult.getCommandId())) {
                                if (controlResult != null) {
                                    controlResult.onFail(null, "车控异常~");
                                }
                                return;
                            }
                            if (isNeedSaveCode) {
                                BanmaCarControlHelper.saveSafeCode(mContext, safeCode, vin);
                            }
                            PollingRequest(unifyControlResult.getCommandId(), vin, unifyControlResult, serviceCode, backStatus, isAsynchronous);
                        }

                        @Override
                        public void onFailure(Throwable e) {
                            super.onFailure(e);
                            if (controlResult != null) {
                                controlResult.onFail(e, null);
                            }
                        }
                    });
        } else if (paramRequestLocation.equals("query")) {
            StringBuilder tUrl = new StringBuilder(url);
            boolean first = true;
            Iterator<Map.Entry<String, String>> it = map.entrySet().iterator();
            while (it.hasNext()) {
                Map.Entry<String, String> entry = it.next();
                if (first) {
                    first = false;
                    tUrl.append("?");
                } else {
                    tUrl.append("&");
                }
                tUrl.append(entry.getKey()).append("=").append(entry.getValue());
            }
            String finalSafeCode = safeCode;
            L00bangRequestManager2.getServiceInstance()
                    .unifyControl(EnvironmentConstant.BASE_BAOJUN_OPEN_API_URL + "junApi" + tUrl.toString())
                    .compose(L00bangRequestManager2.setSchedulers())
                    .subscribe(new NormalSubscriber<UnifyControlResult>(mContext) {
                        @Override
                        public void onSuccess(UnifyControlResult unifyControlResult) {
                            super.onSuccess(unifyControlResult);
                            if (unifyControlResult == null || TextUtils.isEmpty(unifyControlResult.getCommandId())) {
                                if (controlResult != null) {
                                    controlResult.onFail(null, "车控异常~");
                                }
                                return;
                            }
                            if (controlResult != null) {
                                if (isNeedSaveCode) {
                                    BanmaCarControlHelper.saveSafeCode(mContext, finalSafeCode, vin);
                                }
                                pollingCount = 0;
                                PollingRequest(unifyControlResult.getCommandId(), vin, unifyControlResult, serviceCode, backStatus, isAsynchronous);
                            }
                        }

                        @Override
                        public void onFailure(Throwable e) {
                            super.onFailure(e);
                            if (controlResult != null) {
                                controlResult.onFail(e, null);
                            }
                        }
                    });
        }
    }

    protected void PollingRequest(final String commandId, String vin, UnifyControlResult unifyControlResult, String serviceCode, String backStatus, boolean isAsynchronous) {
        Handler handler = new Handler();
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                Log.i("car_control", "第" + pollingCount + "次轮询开始");
                JSONObject root = new JSONObject();
                try {
                    root.put("vin", vin);
                    root.put("command", commandId);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), root.toString());
                L00bangRequestManager2.getServiceInstance()
                        .requestCarControlStatus(requestBody)
                        .compose(L00bangRequestManager2.setSchedulers())
                        .subscribe(new NormalSubscriber<BanmaControlResult>(mContext) {
                            @Override
                            public void onSuccess(BanmaControlResult banmaControlResult) {
                                super.onSuccess(banmaControlResult);
                                Log.i("car_control", "第" + (pollingCount) + "次轮询完成，结果" + banmaControlResult);
                                if (!banmaControlResult.isStatus()) {
                                    if (++pollingCount <= 30) {
                                        PollingRequest(commandId, vin, unifyControlResult, serviceCode, backStatus, isAsynchronous);
                                    } else {
                                        ToastUtil.showMessage(mContext, R.string.car_control_get_status_timeout);
                                        if (mContext != null && controlResult != null) {
                                            controlResult.onFail(null, mContext.getResources().getString(R.string.car_control_get_status_timeout));
                                        }
                                    }
                                } else {
                                    if (controlResult != null && unifyControlResult != null) {
                                        controlResult.onSuccess(serviceCode, backStatus, unifyControlResult.getCollectTime(), isAsynchronous);
                                    }
                                }
                            }

                            @Override
                            public void onFailure(Throwable e) {
                                super.onFailure(e);
                                Log.i("car_control", "第" + (pollingCount) + "次轮询完成，结果请求失败");
                                String error = "请求车控结果超时！";
                                if (!TextUtils.isEmpty(e.getMessage())) {
                                    error = e.getMessage();
                                }
                                ToastUtil.showMessage(mContext, "操作失败：" + error);
                                if (mContext != null && controlResult != null) {
                                    controlResult.onFail(e, null);
                                }
                            }
                        });
            }
        }, 3000);
    }

//    public void refreshCarStatus() {
//        if (FastClickUtil.isFastDoubleClick("refreshCarStatus")) return;
//        L00bangRequestManager2.getServiceInstance()
//                .getDefaultCarStatus()
//                .compose(L00bangRequestManager2.setSchedulers())
//                .subscribe(new NormalSubscriber<SGMWDefaultCarStatInfo>(mContext) {
//
//                    @Override
//                    public void onSuccess(SGMWDefaultCarStatInfo sgmwDefaultCarStatInfo) {
//                        super.onSuccess(sgmwDefaultCarStatInfo);
//                        if (carStatusResult != null) {
//                            carStatusResult.onResultSuccess(sgmwDefaultCarStatInfo);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(Throwable e) {
//                        super.onFailure(e);
//                        if (carStatusResult != null) {
//                            carStatusResult.onResultFail(e.getMessage());
//                        }
//                    }
//                });
//    }

    public void refreshCarStatus(String vin) {
        if (TextUtils.isEmpty(vin)) {
            ToastUtil.showMessage(mContext, "vin不能为空~");
            return;
        }
        mVin = vin;
        if (FastClickUtil.isFastDoubleClick("refreshCarStatus")) {
            return;
        }
        JSONObject root = new JSONObject();
        try {
            root.put("vin", vin);
        } catch (Exception e) {
            e.printStackTrace();
        }
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), root.toString());
        L00bangRequestManager2.getServiceInstance()
                .requestCarStatus(requestBody)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new NormalSubscriber<Object>(mContext) {

                    @Override
                    public void onSuccess(Object object) {
                        super.onSuccess(object);

                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                getBanmaCarInfo();
                            }
                        }, 3000);
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                        dispatchBanmaCarStatus(false, null, "通知车机上传状态失败~");
//                        if (mCarStatusResultListener != null) {
//                            mCarStatusResultListener.onResultFail(e.getMessage());
//                        }
                    }
                });
    }

    /**
     * 请求斑马车机获取数据
     */
    public void getBanmaCarInfo() {
        L00bangRequestManager2.getServiceInstance()
                .getDefaultCarStatus()
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new NormalSubscriber<SGMWDefaultCarStatInfo>(mContext) {
                    @Override
                    public void onSuccess(SGMWDefaultCarStatInfo sgmwDefaultCarStatInfo) {
                        super.onSuccess(sgmwDefaultCarStatInfo);
                        if (sgmwDefaultCarStatInfo != null) {
                            dispatchBanmaCarStatus(true, sgmwDefaultCarStatInfo, null);
                        } else {
                            dispatchBanmaCarStatus(false, null, "获取车况失败");
                        }
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                        dispatchBanmaCarStatus(false, null, "获取车控失败:" + e.getMessage());
                    }
                });

    }

    /**
     * 获取位置
     */
//    private void getAddress(Context context, final CarInfo carInfo) {
//        if (carInfo.getVehiclePosition() == null) {
//            dispatchBanmaCarStatus(true,carInfo,null);
//            return;
//        }
//        EocoderUtils.getAddressInfo(context, carInfo.getVehiclePosition().getLatitude(), carInfo.getVehiclePosition().getLongitude(), new EocoderUtils.OnResultListener() {
//            @Override
//            public void onSuccess(BaiduEocoderResult.EocodeAddressInfo result) {
//                if (!TextUtils.isEmpty(result.getFormatted_address())) {
//                    carInfo.setCarPositionName(result.getFormatted_address());
//                }
//                dispatchBanmaCarStatus(true,carInfo,null);
//            }
//
//            @Override
//            public void onFailure(String message) {
//                dispatchBanmaCarStatus(true,carInfo,null);
//            }
//        });
//    }

    /**
     * 分发斑马车况相关信息
     *
     * @param carInfo
     */
    private void dispatchBanmaCarStatus(boolean isSuccess, SGMWDefaultCarStatInfo carInfo, String errorMsg) {
        if (mCarStatusResultListener != null) {
            mCarStatusResultListener.onStatusResult(isSuccess, carInfo, errorMsg);
        }
        if (mCarStatusResultViewListener != null) {
            mCarStatusResultViewListener.onStatusResult(isSuccess, carInfo, errorMsg);
        }
        //分发斑马车况
        if (mOnCarStatusListeners != null) {
            for (int i = 0, z = mOnCarStatusListeners.size(); i < z; i++) {
                CarStatusResultListener listener = mOnCarStatusListeners.get(i);
                if (listener != null) {
                    listener.onStatusResult(isSuccess, carInfo, errorMsg);
                }
            }
        }

    }

    public void getServiceBtnLists(String vin) {
//        if (FastClickUtil.isFastDoubleClick("getserviceBtn")) return;
        JSONObject root = new JSONObject();
        JSONArray array = new JSONArray();
        array.put(CONTROL_BTN_LIST_CODE);
        array.put(SERVICE_BTN_LIST_CODE);
        try {
            root.put("servicePositionCodeList", array);
            root.put("vin", vin);
        } catch (Exception e) {
            e.printStackTrace();
        }
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), root.toString());
        L00bangRequestManager2.getServiceInstance()
                .getControlBtnList(requestBody)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new NormalSubscriber<List<UnifyServiceLists>>(mContext) {
                    @Override
                    public void onSuccess(List<UnifyServiceLists> statList) {
                        super.onSuccess(statList);
                        if (btnListResult != null) {
                            for (int i = 0; i < statList.size(); i++) {
                                switch (statList.get(i).getPositionCode()) {
                                    case CONTROL_BTN_LIST_CODE:
                                        btnListResult.onControlBtnSuccess(statList.get(i).getServiceList());
                                        break;
                                    case SERVICE_BTN_LIST_CODE:
                                        btnListResult.onServiceBtnSuccess(statList.get(i).getServiceList());
                                        break;
                                    default:
                                        throw new IllegalStateException("Unexpected value: " + statList.get(i).getPositionCode());
                                }
                            }
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        if (btnListResult != null) {
                            btnListResult.onFail(e.getMessage());
                        }
                    }
                });
    }

    /**
     * 初始化蓝牙车控
     */
    public void requestBluetoothKey() {
        if (TextUtils.isEmpty(mVin)) {
            ToastUtil.showMessage(mContext, "vin不能为空！");
            setBluetoothDisconnect();
            return;
        }
        BluetoothKey bluetoothKey = BanmaCarControlHelper.findBluetoothKeyByVin(mVin);
        if (bluetoothKey == null) {
            JSONObject root = new JSONObject();
            try {
                root.put("vin", mVin);
            } catch (Exception e) {
                e.printStackTrace();
            }
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), root.toString());

            L00bangRequestManager2.getServiceInstance()
                    .getBluetoothKey(requestBody)
                    .compose(L00bangRequestManager2.setSchedulers())
                    .subscribe(new ProgressSubscriber<BluetoothKey>(mContext) {
                        @Override
                        public void onSuccess(BluetoothKey bluetoothKey) {
                            super.onSuccess(bluetoothKey);
                            if (bluetoothKey == null) {
                                if (mContext != null) {
                                    ToastUtil.showMessage(mContext, R.string.car_control_get_bluetooth_key_fail);
                                }
//                                setWifiState();
                                setBluetoothDisconnect();
//                                mSwitchConnectedWay.setChecked(false);
                                return;
                            }
                            //保存蓝牙钥匙
                            BanmaCarControlHelper.saveBluetoothKey(bluetoothKey);
                            initBluetoothControl(bluetoothKey);
                        }

                        @Override
                        public void onFailure(Throwable e) {
                            super.onFailure(e);
                            ToastUtil.showMessage(mContext, R.string.car_control_get_bluetooth_key_fail);
                            setBluetoothDisconnect();
                            //切换回wifi模式
//                            setWifiState();
//                            mSwitchConnectedWay.setChecked(false);
//                            if (mCarControlForBanmaViewHolder!=null)
//                                mCarControlForBanmaViewHolder.getshizhi();

                        }
                    });
        } else {
            initBluetoothControl(bluetoothKey);
        }
    }

    /**
     * 初始化蓝牙车控
     *
     * @param bluetoothKey 蓝牙钥匙
     */
    public void initBluetoothControl(BluetoothKey bluetoothKey) {
        if (handler == null) {
            handler = new Handler();
        }
        //新建和初始化BMBleControlManager
        mBMBleControlManager = new BMBleControlManager(mContext, this, this);
        mBMBleControlManager.init();
        Log.i("blue", mContext.getString(R.string.car_control_bluetooth_connect_start));
        //发起和tbox的链连接，并传入蓝牙钥匙
        if (TextUtils.isEmpty(bluetoothKey.getTboxMac()) || TextUtils.isEmpty(bluetoothKey.getSecretKey()) || TextUtils.isEmpty(bluetoothKey.getAesKey()) || TextUtils.isEmpty(bluetoothKey.getTspUserId())) {
            ToastUtil.showMessage(mContext, "蓝牙钥匙信息不完整，请重试~");
            mBMBleControlManager.disconnect();
            return;
        }
        try {
            mBMBleControlManager.startConnection(BleKeyUtils.getDeviceMacAddress(bluetoothKey.getTboxMac()), bluetoothKey.getKeyReference(), bluetoothKey.getSecretKey(), bluetoothKey.getAesKey(), bluetoothKey.getTspUserId());
        }catch (Exception e){
            Log.e("blue", "banma blue startConnect catch ! ");
        }
        if (runnable == null) {
            runnable = new Runnable() {
                @Override
                public void run() {
                    //说明超时了，则断开连接
                    if (mContext != null) {
                        ToastUtil.showMessage(mContext, R.string.car_control_bluetooth_timeout);
                    }
                    setBluetoothDisconnect();
//                    setWifiState();
//                    mSwitchConnectedWay.setChecked(false);
                    mBMBleControlManager.disconnect();
                }
            };
        }
        //设置超时时间
        handler.postDelayed(runnable, 60000);//每40秒执行一次runnable.
    }

    /**
     * 蓝牙连接成功时的回调
     */
    private void onBluetoothConnect() {
        if (handler != null) {
            handler.removeCallbacks(runnable);
        }
        if (mContext != null) {
            ToastUtil.showMessage(mContext, R.string.car_control_bluetooth_connect_success);
        }
        mCurrentModel = MODEL_BLUETOOTH;
        if (mOnBluetoothControlListener != null) {
            mOnBluetoothControlListener.onBleConnectSuccess();
        }
        //setBleBgStatus(2);
//        mSwitchConnectedingLayout.setVisibility(View.GONE);
//        view_pageer_function_button.setVisibility(View.VISIBLE);
    }

    /**
     * 蓝牙连接错误
     */
    private void onBluetoothConnectError(String message) {
        if (handler != null) {
            handler.removeCallbacks(runnable);
        }
        if (mContext != null) {
            ToastUtil.showMessage(mContext, message);
        }
        mCurrentModel = MODEL_WIFI;
        setBluetoothDisconnect();
//        isSelectedBle=false;
//        //切换到wifi模式
//        setWifiState();
//        mSwitchConnectedWay.setChecked(false);
        //设置
        setAllBluetoothBtnComplete();

    }

    /**
     * 关闭所有蓝牙操作的状态，动画清除，按钮归位（蓝牙连接出错或断开时调用）
     */
    private void setAllBluetoothBtnComplete() {
        operationComplete();
        operationComplete();
        operationComplete();
    }

    /**
     * 操作完成的操作
     */
    private void operationComplete() {
        //成功以及失败都要先取消弹框
//        dismissLoadingDialog();
//        updateFunctionButtonStatus();
    }

    /**
     * 断开蓝牙连接,重置按钮
     */
    public void disBleConnect() {
//        if (isSelectedBle){
//            setWifiState();
//            if (mBMBleControlManager != null) {
//                mBMBleControlManager.disconnect();
//            }
//        }
//        if (mSwitchConnectedWay != null && mSwitchConnectedWay.isChecked()) {
//            mSwitchConnectedWay.setChecked(false);
//            if (mBMBleControlManager != null) {
//                mBMBleControlManager.disconnect();
//            }
//        }

        if (mBMBleControlManager != null) {
            mBMBleControlManager.disconnect();
        }
        mCurrentModel = MODEL_WIFI;
    }

    /**
     * 蓝牙控制之后设置按钮状态归位
     */
    private void onBluetoothCommandComplete(CommandTypeEnum commandTypeEnum) {
        //设置上次蓝牙车控完成时间为当前时间
        mLastBluetoothControlTime = AppUtil.getServerCurrentTime();
        switch (commandTypeEnum) {
            case LOCK_DOOR:
                operationComplete();
                break;
            case UNLOCK_DOOR:
                operationComplete();
                break;
            case UNLOCK_TRUNK:
                operationComplete();
                break;
        }
    }

    /**
     * 开始蓝牙控制
     */
    public void startBluetoothControl(int position, int controlCode) {
        switch (controlCode) {
            //锁车
            case BanmaCarControlHelper.ControlCode.LOCK:
//                showLoadingDialog("上锁中");
                mBMBleControlManager.lock();
                blueCmdPosition = position;
                //setButtonStatusInOperation(controlCode);
                break;
            //解锁
            case BanmaCarControlHelper.ControlCode.UNLOCK:
//                showLoadingDialog("解锁中");
                mBMBleControlManager.unlock();
                blueCmdPosition = position;
                //setButtonStatusInOperation(controlCode);
                break;
            //开后备箱
            case BanmaCarControlHelper.ControlCode.TRUNK:
//                showLoadingDialog("解锁中");
                mBMBleControlManager.unlockTrunk();
                blueCmdPosition = position;
                //setButtonStatusInOperation(controlCode);
                break;
        }
    }

    private String mVin;
    private TravelFragment mTravelFragment;

    /**
     * 初始化
     *
     * @param userCache 是否使用缓存
     * @param needRequestPermission
     */
    public void initAndConnect(Fragment fragment, String vin, boolean userCache, boolean needRequestPermission) {
        if (TextUtils.isEmpty(vin) || fragment == null) {
            return;
        }
        mVin = vin;
//        mIsOfflineMode = userCache;
//        if (mIsOfflineMode) {
//            if (internetCar != null && internetCar.getBlueKeyBean() != null) {
//                mBlueKeyBean = internetCar.getBlueKeyBean();
//            } else if (HEVCar != null && HEVCar.getBlueKeyBean() != null) {
//                mBlueKeyBean = HEVCar.getBlueKeyBean();
//            }
//
//        }
        //如果需要授权相关弹窗
        if (needRequestPermission) {
            if (fragment instanceof TravelFragment) {
                mTravelFragment = (TravelFragment) fragment;
                ((TravelFragment) fragment).checkPermissions(TravelFragment.ACTIVITY_PERMISSION_LOCATION,
                        fragment.getContext().getString(R.string.permission_location_pre),
                        fragment.getContext().getString(R.string.permission_location_setting),
                        Manifest.permission.ACCESS_FINE_LOCATION);
            }
        } else {
            //否则只给出提示
            if (!PermissionUtils.checkPermission(mContext, Manifest.permission.ACCESS_FINE_LOCATION)) {
//                ToastUtil.showMessage(mContext,"需要授予菱菱邦定位权限，以使用蓝牙车控！");
                return;
            }
            if (!PermissionUtils.checkPermission(mContext, Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
//                ToastUtil.showMessage(mContext,"需要授予菱菱邦存储权限，以使用蓝牙车控！");
                return;
            }
            if (!AppUtil.isOpenGPS(mContext)) {
//                ToastUtil.showMessage(mContext,"需要打开GPS，以使用蓝牙车控！");
                return;
            }
            BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
            if (!bluetoothAdapter.isEnabled()) {
//                ToastUtil.showMessage(mContext,"需要打开蓝牙，以使用蓝牙车控！");
                return;
            }
            //请求蓝牙钥匙
            requestBluetoothKey();

        }
    }

    /**
     * 设置蓝牙状态
     *
     * @param status 1:链接成功  0：未连接  -1：连接中
     */
    public static void setBluetoothStatus(int status, ImageView bluetoothButton, TextView bluetoothText) {
        if (status == 1) {
            bluetoothText.setTag(1);
            bluetoothText.setVisibility(View.VISIBLE);
            bluetoothText.setText(R.string.bluetooth_connect_success2);
            bluetoothButton.setImageResource(R.drawable.ic_oval_travel_bluetooth_connected);
        } else if (status == -1) {
            bluetoothText.setTag(-1);
            bluetoothText.setVisibility(View.VISIBLE);
            bluetoothText.setText(R.string.bluetooth_connect_now);
            bluetoothButton.setImageResource(R.drawable.ic_oval_travel_bluetooth_connect_now);
        } else if (status == -2) {
            bluetoothText.setTag(0);
            bluetoothText.setVisibility(View.GONE);
            bluetoothButton.setImageResource(R.drawable.ico_wuling_logo);
        } else {
            bluetoothText.setTag(0);
            bluetoothText.setVisibility(View.VISIBLE);
            bluetoothText.setText(R.string.bluetooth_not_connect2);
            bluetoothButton.setImageResource(R.drawable.ic_oval_travel_bluetooth_unconnect);
        }
        bluetoothButton.setEnabled(status == -1 ? false : true);
        Log.e("BluetoothCarControl", status + "调用了改变图标onConnectedWithBluetooth");
    }

    /**
     * 定位权限返回结果
     */
    public void onLocationPermissionPassed(boolean isGranted) {
        //如果定位通过，则判断蓝牙是否打开
        if (isGranted) {
            //如果定位通过，则请求存储权限
            if (mTravelFragment != null) {
                PermissionUtils.checkStoragePermissions(mTravelFragment, mTravelFragment.getActivity(), PERMISSION_REQUEST_CODE_BLUE_TOOTH, mTravelFragment.getActivity().getString(R.string.permission_write_read_bluetooth), mTravelFragment.getActivity().getString(R.string.permission_bluetooth_connect));
            }
        } else {
            ToastUtil.showMessage(mContext, R.string.car_control_need_location);
            setBluetoothDisconnect();
            //切换成wifi模式
//            mSwitchConnectedWay.setChecked(false);
        }

    }

    private void setBluetoothDisconnect() {
        if (mOnBluetoothControlListener != null) {
            mOnBluetoothControlListener.onBleConnectFailed();
        }
    }

    public void onWriteExternalStoragePermissionPassed(boolean isGranted) {
        //如果定位通过，则判断蓝牙是否打开
        if (isGranted) {
            boolean isOpen = AppUtil.isOpenGPS(mContext);//判断GPS是否打开
            if (!isOpen) {
                showGpsDialog();
            } else {
                //打开蓝牙
                BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
                if (!bluetoothAdapter.isEnabled()) {
                    mTravelFragment.openBluetooth();
                } else {
                    //请求蓝牙钥匙，并初始化蓝牙
                    requestBluetoothKey();
                }

            }
        } else {
            ToastUtil.showMessage(mContext, R.string.car_control_need_read_write_external_storage);
            setBluetoothDisconnect();
            //切换成wifi模式
//            mSwitchConnectedWay.setChecked(false);
        }

    }

    /**
     * GPS提示对话框
     */
    private void showGpsDialog() {
        if (mGpsDialog == null) {
            mGpsDialog = new CommonAlertDialog(mContext, R.string.car_control_need_gps_permission, R.string.permission_btn_setting, R.string.permission_btn_cancel, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    //跳转到设置页面让用户自己手动开启
                    mTravelFragment.requestOpenGPS();
                    dialog.dismiss();
                }
            }, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialogInterface, int i) {
                    ToastUtil.showMessage(mContext, R.string.car_control_need_gps_permission);
                    setBluetoothDisconnect();
                }
            });
        }
        if (mGpsDialog.isShowing()) {
            mGpsDialog.dismiss();
        }
        mGpsDialog.show();
    }

    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        //请求GPS返回
        if (requestCode == TravelFragment.ACTIVITY_REQUEST_OPEN_GPS) {
            if (AppUtil.isOpenGPS(mContext)) {
                ToastUtil.showMessage(mContext, R.string.car_control_open_gps_success);
                //请求蓝牙钥匙，并初始化蓝牙
                getBluetoothStatus();
            } else {
                ToastUtil.showMessage(mContext, R.string.car_control_need_gps_permission);
                setBluetoothDisconnect();
            }
        } else if (requestCode == TravelFragment.ACTIVITY_REQUEST_BLUETOOTH) {
            if (resultCode == RESULT_OK) {
                //请求蓝牙钥匙，并初始化蓝牙
                requestBluetoothKey();
            } else {
                ToastUtil.showMessage(mContext, R.string.car_control_need_open_bluetooth);
                setBluetoothDisconnect();
                //切换成wifi模式
//                mSwitchConnectedWay.setChecked(false);
            }
        }
    }

    /**
     * 获取蓝牙状态
     */
    public void getBluetoothStatus() {
        //打开蓝牙
        BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        if (!bluetoothAdapter.isEnabled()) {
            mTravelFragment.openBluetooth();
        } else {
            //请求蓝牙钥匙
            requestBluetoothKey();
        }
    }

    /**
     * 请求mini人生数据
     *
     * @param vin
     */
    public void requestMiniLife(String vin) {
        JSONObject root = new JSONObject();
        try {
            root.put("vin", vin);
        } catch (Exception e) {
            e.printStackTrace();
        }
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), root.toString());
        L00bangRequestManager2.getServiceInstance()
                .getUnifyMiniLife(requestBody)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new NormalSubscriber<UnifyMiniLife>(mContext) {
                    @Override
                    public void onSuccess(UnifyMiniLife miniLife) {
                        super.onSuccess(miniLife);
                        if (miniLifeResult != null && miniLife != null) {
                            miniLifeResult.miniResult(miniLife);
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        if (miniLifeResult != null) {
                            miniLifeResult.miniFault();
                        }
                    }
                });
    }

    @Override
    public void onCommandSuccess(final CommandTypeEnum commandTypeEnum, int i) {
        Log.i("blue", "蓝牙操作成功:" + commandTypeEnum.name());
        ((BaseActivity) mContext).runOnUiThread(new Runnable() {
            @Override
            public void run() {
                ToastUtil.showMessage(mContext, R.string.car_control_bluetooth_operation_success);
                //按钮状态归位
                onBluetoothCommandComplete(commandTypeEnum);
                if (mOnBluetoothControlListener != null) {
                    mOnBluetoothControlListener.onBlueCarControlSuccess(blueCmdPosition, commandTypeEnum);
                }
//                if (commandTypeEnum == UNLOCK_DOOR) {
//                    mCarControlForBanmaViewHolder.onBluetoothUnlockFinish();
//                    onLockStateChange(false);
//                } else if (commandTypeEnum == LOCK_DOOR) {
//                    onLockStateChange(true);
//                }
            }
        });

    }

    @Override
    public void onCommandFailure(final CommandTypeEnum commandTypeEnum, int i) {
        Log.i("blue", "蓝牙操作失败");
        ((BaseActivity) mContext).runOnUiThread(new Runnable() {
            @Override
            public void run() {
                ToastUtil.showMessage(mContext, R.string.car_control_bluetooth_operation_failed);
                //按钮状态归位
                onBluetoothCommandComplete(commandTypeEnum);
                if (mOnBluetoothControlListener != null) {
                    mOnBluetoothControlListener.onBlueCarControlFailed(commandTypeEnum);
                }
            }
        });
    }

    @Override
    public void onLeScan(BluetoothDevice bluetoothDevice, int i, byte[] bytes) {
        Log.i("blue", "蓝牙扫描中");
        ToastUtil.showMessage(mContext, R.string.car_control_bluetooth_scanning);
    }

    @Override
    public void onGattConnected() {
        Log.i("blue", "蓝牙已链接");

    }

    @Override
    public void onGattDisconnected() {
        Log.i("blue", "蓝牙已断开");
        ((BaseActivity) mContext).runOnUiThread(new Runnable() {
            @Override
            public void run() {
                onBluetoothConnectError(mContext.getString(R.string.car_control_bluetooth_disconnect));
            }
        });

    }

    @Override
    public void onGattConnectStateError() {
        Log.i("blue", "蓝牙连接状态错误");
        ((BaseActivity) mContext).runOnUiThread(new Runnable() {
            @Override
            public void run() {
                onBluetoothConnectError(mContext.getString(R.string.car_control_bluetooth_status_error));
            }
        });
    }

    @Override
    public void onTboxConnected(int i) {
        Log.i("blue", "tbox链接");
        ((BaseActivity) mContext).runOnUiThread(new Runnable() {
            @Override
            public void run() {
                //鉴权成功，可以进行蓝牙车控
                onBluetoothConnect();

            }
        });

    }

    @Override
    public void onTboxConnectFailure(final int reason) {
        Log.i("blue", "tbox连接错误");
        ((BaseActivity) mContext).runOnUiThread(new Runnable() {
            @Override
            public void run() {
                //删除保存的蓝牙钥匙
                BanmaCarControlHelper.deleteBluetoothKey(mVin);
                //连接错误
                onBluetoothConnectError(mContext.getString(R.string.car_control_tbox_connect_error) + reason);

            }
        });

    }

    @Override
    public void onTboxDisconnected() {
        Log.i("blue", "tbox断开");
        ((BaseActivity) mContext).runOnUiThread(new Runnable() {
            @Override
            public void run() {
                onBluetoothConnectError(mContext.getString(R.string.car_control_tbox_disconnect));
            }
        });
    }

    public interface ControlResult {

        void onSuccess(String serviceCode, String currentStatusValue, String vinTime, boolean isAsynchronous);

        void onFail(Throwable errorMsg, String msg);
    }

    /**
     * 车况返回的监听
     */
    public interface CarStatusResultListener {

        void onStatusResult(boolean isSuccess, SGMWDefaultCarStatInfo carInfo, String errorMsg);

    }

    /**
     * 车况返回的监听(给布局)
     */
    public interface CarStatusResultViewListener {

        void onStatusResult(boolean isSuccess, SGMWDefaultCarStatInfo carInfo, String errorMsg);
    }

    public interface btnListResult {

        void onControlBtnSuccess(List<UnifyControlBtnStat> list);

        void onServiceBtnSuccess(List<UnifyControlBtnStat> list);

        void onFail(String e);
    }

    /**
     * 设置车控的监听。支持多个
     */
    public void addOnCarControlListener(CarStatusResultListener listener) {
        if (mOnCarStatusListeners == null) {
            mOnCarStatusListeners = new ArrayList<>();
        }
        mOnCarStatusListeners.add(listener);
    }

    /**
     * 移除车控的监听
     */
    public void removeOnCarControlListener(CarStatusResultListener listener) {
        if (mOnCarStatusListeners != null) {
            mOnCarStatusListeners.remove(listener);
        }
    }

    /**
     * 修改视图的蓝牙操作的监听
     */
    public interface OnBluetoothControlListener {
        /**
         * 蓝牙连接成功
         */
        void onBleConnectSuccess();

        /**
         * 蓝牙连接断开（失败）
         */
        void onBleConnectFailed();

        /**
         * 蓝牙获取车况相关回调
         */
        void onCarStatusFromBluetooth(CarStatusFromBluetooth carStatusFromBluetooth);

        /**
         * 蓝牙操作成功回调（用于结束按钮等待状态）
         *
         * @param blueCmdPosition
         * @param commandTypeEnum
         */
        void onBlueCarControlSuccess(int blueCmdPosition, CommandTypeEnum commandTypeEnum);

        /**
         * 蓝牙操作失败回调（用于结束按钮等待状态）
         */
        void onBlueCarControlFailed(CommandTypeEnum commandTypeEnum);

    }

    public interface miniLifeResult {

        void miniResult(UnifyMiniLife miniLife);

        void miniFault();
    }

    public void setControlResult(ControlResult controlResult) {
        this.controlResult = controlResult;
    }

    public void setCarStatusResultListener(CarStatusResultListener carStatusResultListener) {
        mCarStatusResultListener = carStatusResultListener;
    }

    public void setCarStatusResultViewListener(CarStatusResultViewListener carStatusResultViewListener) {
        mCarStatusResultViewListener = carStatusResultViewListener;
    }

    public void setControlBtnList(btnListResult controlBtnList) {
        this.btnListResult = controlBtnList;
    }

    public void setMiniLifeResult(BanmaCarController.miniLifeResult miniLifeResult) {
        this.miniLifeResult = miniLifeResult;
    }

    public void setOnBluetoothControlListener(OnBluetoothControlListener onBluetoothControlListener) {
        mOnBluetoothControlListener = onBluetoothControlListener;
    }

    public int getCurrentModel() {
        return mCurrentModel;
    }
}
