package com.cloudy.linglingbang.activity.basic;

import android.view.View;
import android.view.ViewGroup;

import java.util.List;

import androidx.viewpager.widget.PagerAdapter;

/**
 * View的，这个基类本不应该直接到View的
 *
 * <AUTHOR> create at 2016/10/6 11:07
 */
public class BaseViewPagerAdapter extends PagerAdapter {

    private List<View> mData;

    public BaseViewPagerAdapter(List<View> data) {
        mData = data;
    }

    @Override
    public int getCount() {
        return mData.size();
    }

    @Override
    public boolean isViewFromObject(View view, Object object) {
        return view == object;
    }

    @Override
    public Object instantiateItem(ViewGroup container, int position) {
        View view = mData.get(position);
        container.addView(view);
        return view;
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        try {
            container.removeView((View) object);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
