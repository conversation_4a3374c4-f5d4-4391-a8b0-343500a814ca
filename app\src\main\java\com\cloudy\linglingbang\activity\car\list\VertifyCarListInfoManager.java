package com.cloudy.linglingbang.activity.car.list;

import com.cloudy.linglingbang.db.GreenDaoManager;
import com.cloudy.linglingbang.greendao.DefaultCarInfoDao;
import com.cloudy.linglingbang.model.server.DefaultCarInfo;

import androidx.annotation.Nullable;

/**
 * 认证车辆的数据管理
 *
 * <AUTHOR>
 * @date 2020/12/10
 */
public class VertifyCarListInfoManager {

    private final DefaultCarInfoDao mDefaultCarInfoDao;
    private static VertifyCarListInfoManager mCarInfoManager;

    public VertifyCarListInfoManager() {
        mDefaultCarInfoDao = GreenDaoManager.getInstance().getDaoSession().getDefaultCarInfoDao();
    }

    public static VertifyCarListInfoManager getInstance() {
        if (mCarInfoManager == null) {
            synchronized (VertifyCarListInfoManager.class) {
                if (mCarInfoManager == null) {
                    mCarInfoManager = new VertifyCarListInfoManager();
                }
            }
        }
        return mCarInfoManager;
    }

    /**
     * 插入
     *
     * @param defaultCarInfo
     */
    public long insertOrReplace(DefaultCarInfo defaultCarInfo) {
        if (mDefaultCarInfoDao != null) {
            return mDefaultCarInfoDao.insertOrReplace(defaultCarInfo);
        }
        return 0L;
    }

    public void delete(@Nullable DefaultCarInfo defaultCarInfo) {
        if (defaultCarInfo != null) {
            mDefaultCarInfoDao.delete(defaultCarInfo);
        }
    }

    public void deleteAll() {
        mDefaultCarInfoDao.deleteAll();
    }

    /**
     * 更新
     *
     * @param defaultCarInfo
     */
    public void update(DefaultCarInfo defaultCarInfo) {
        if (mDefaultCarInfoDao != null) {
            mDefaultCarInfoDao.update(defaultCarInfo);
        }
    }

    /**
     * 查询
     *
     * @param vin
     */
    public DefaultCarInfo loadData(String vin) {
        if (mDefaultCarInfoDao != null) {
            return mDefaultCarInfoDao.load(vin);
        }
        return null;
    }

}
