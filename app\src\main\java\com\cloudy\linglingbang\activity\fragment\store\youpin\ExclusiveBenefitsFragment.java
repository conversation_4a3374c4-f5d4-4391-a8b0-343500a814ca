package com.cloudy.linglingbang.activity.fragment.store.youpin;

import android.text.TextUtils;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshFragment;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.fragment.mynew.util.SelfUserInfoLoader;
import com.cloudy.linglingbang.activity.fragment.store.home.StoreHomeRefreshController;
import com.cloudy.linglingbang.activity.fragment.store.home.adapter.StoreHomeAdapter;
import com.cloudy.linglingbang.activity.fragment.store.home.model.StoreHomeElementEnum;
import com.cloudy.linglingbang.activity.store.commodity.CommodityListPageCode;
import com.cloudy.linglingbang.app.receiver.UserInfoChangeReceiver;
import com.cloudy.linglingbang.app.receiver.UserInfoChangedHelper;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.banner.BannerView;
import com.cloudy.linglingbang.app.widget.recycler.FunctionViewUtils;
import com.cloudy.linglingbang.constants.FinalSensors;
import com.cloudy.linglingbang.model.SourceModel;
import com.cloudy.linglingbang.model.entrance.HomeFunctionResult;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.NormalSubscriber;
import com.cloudy.linglingbang.model.server.Ad.Ad2;
import com.cloudy.linglingbang.model.server.Ad.AdRequestUtil2;
import com.google.android.material.appbar.AppBarLayout;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import butterknife.OnClick;
import io.reactivex.rxjava3.core.Observable;

/**
 * 尊享权益
 *
 * <AUTHOR>
 * @date 11/15/21
 */
public class ExclusiveBenefitsFragment extends BaseRecyclerViewRefreshFragment<Object> implements AppBarLayout.OnOffsetChangedListener {

    @BindView(R.id.banner_view)
    BannerView mBannerView;

    @BindView(R.id.appBar)
    AppBarLayout barLayout;

    /**
     * 功能图标
     */
    @BindView(R.id.recycler_view_item)
    RecyclerView mRecyclerViewItem;

    /**
     * 当前ling值
     */
    @BindView(R.id.tv_ling_value)
    TextView mTvLingValue;

    /**
     * 是否显示用ling值抵扣
     */
    @BindView(R.id.cb_user_ling)
    CheckBox mCbUserLing;

    /**
     * Ling值行（未登录隐藏）
     */
    @BindView(R.id.rl_ling_value)
    RelativeLayout mRlLingValue;

    @BindView(R.id.tv_comprehensive)
    TextView mTvComprehensive;

    @BindView(R.id.ll_price)
    LinearLayout mLlPrice;

    @BindView(R.id.tv_price)
    TextView mTvPrice;

    @BindView(R.id.tv_sales)
    TextView mTvSales;

    @BindView(R.id.swipe_target)
    CoordinatorLayout mSwipeTarget;

    private String mSortFieldsKey = "s4";
    private String mSortFieldsValue = "2";

    /**
     * 是否只刷新列表
     * 点击排序和筛选的时候，设置为true
     */
    private boolean mRefreshListOnly = false;

    /**
     * 是否选中ling值兑换商品：0:否，1:是
     */
    private int lingValue = 0;

    /**
     * 我的监听
     */
    private UserInfoChangedHelper mUserInfoChangedHelper;

    @Override
    protected void initViews() {
        super.initViews();
        barLayout.addOnOffsetChangedListener(this);
        mTvComprehensive.setSelected(true);
        mRecyclerViewItem.canScrollVertically(0);
        onUserLoginStatusChange();
        if (UserUtils.hasLogin()) {
            onUpdateUserBalance();
        }
        mCbUserLing.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    lingValue = 1;
                } else {
                    lingValue = 0;
                }
                refreshOnlyList();

            }
        });
        //各个状态都变化
        mUserInfoChangedHelper = new UserInfoChangedHelper(new UserInfoChangeReceiver() {

            @Override
            protected void onUpdateUser() {
                super.onUpdateUser();
                onUserLoginStatusChange();
                refreshOnlyList();
            }

            @Override
            protected void onUpdateBalanceInfo() {
                super.onUpdateBalanceInfo();
                onUpdateUserBalance();
            }

            @Override
            protected void onClearUser() {
                super.onClearUser();
                onUserLoginStatusChange();
                mCbUserLing.setChecked(false);
            }

        });
        mUserInfoChangedHelper.register(getContext());
    }

    private void onUpdateUserBalance() {
        mTvLingValue.setText(getResources().getString(R.string.present_ling_value, String.valueOf(SelfUserInfoLoader.getInstance().getUserBalanceInfo().getLingCurrency())));
    }

    private void onUserLoginStatusChange() {
        if (UserUtils.hasLogin()) {
            mRlLingValue.setVisibility(View.VISIBLE);
        } else {
            mRlLingValue.setVisibility(View.GONE);
        }
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_exclusive_benefits;
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List list) {

        return new StoreHomeAdapter(getContext(), list).setCountDownTag(this).setStoreHomeAnchor(new SensorsUtils.StoreHomeAnchor("尊享权益", "尊享权益", SourceModel.POSITION_TYPE.EXCLUSIVE_RIGHTS_TYPE, SourceModel.POSITION_TYPE.EXCLUSIVE_RIGHTS_VALUE));
    }

    @Override
    public Observable<BaseResponse<List<Object>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return null;
    }

    @OnClick({R.id.tv_comprehensive, R.id.tv_price, R.id.tv_sales})
    void clicks(View view) {
        switch (view.getId()) {
            case R.id.tv_comprehensive:
                mSortFieldsKey = "s4";
                mSortFieldsValue = "2";
                mTvComprehensive.setSelected(true);
                mTvPrice.setSelected(false);
                mTvSales.setSelected(false);
                mTvPrice.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_price_normal, 0);
                break;
            case R.id.tv_price:
                mSortFieldsKey = "s6";
                mTvPrice.setSelected(true);
                if (mTvPrice.getTag() != null && mTvPrice.getTag().toString().equals("rise")) {
                    mSortFieldsValue = "2";
                    mTvPrice.setTag("drop");
                    mTvPrice.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_price_drop, 0);
                } else {
                    mSortFieldsValue = "1";
                    mTvPrice.setTag("rise");
                    mTvPrice.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_price_rise, 0);
                }
                mTvComprehensive.setSelected(false);
                mTvSales.setSelected(false);
                break;
            case R.id.tv_sales:
                mSortFieldsKey = "s1";
                mSortFieldsValue = "2";
                mTvSales.setSelected(true);
                mTvPrice.setSelected(false);
                mTvComprehensive.setSelected(false);
                mTvPrice.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_price_normal, 0);
                break;
        }
        String eventPosition = "";
        switch (mSortFieldsKey) {
            case "s4":
                eventPosition = "综合";
                break;
            case "s6":
                eventPosition = "价格";
                break;
            case "s1":
                eventPosition = "销量";
                break;
        }
        SensorsUtils.sensorsClickBtn("点击排序", "权益页面", eventPosition);
        refreshOnlyList();
    }

    @Override
    public RefreshController createRefreshController() {
        return new StoreHomeRefreshController(this, "") {

            /**
             * 标记是否可以加载更多，不能直接设置swipeToLoadLayout，因为嵌套悬停控件冲突
             */
            private boolean isCanLoadMore = true;

            @Override
            public void onRefresh() {
                super.onRefresh();
                if (!mRefreshListOnly) {
                    loadAd();
                    requestFunctionIcon();
                    SelfUserInfoLoader.getInstance().getUserBalanceInfo(getContext());
                } else {
                    mRefreshListOnly = false;
                }
            }

            @Override
            protected Observable<BaseResponse<List<Object>>> getListDataFromNet(L00bangService2 service2, int pageNo, int pageSize) {
                Map<String,Object> map = new HashMap<>();
//                map.put("createType", 0);
//                map.put("virtualGoods", new Integer[]{3});
                map.put("lingValue", lingValue);
                map.put("pageNo", pageNo);
                map.put("pageSize", pageSize);
                map.put("pageCode", CommodityListPageCode.PAGE_CODE_VIP_RIGHT);
                if (!TextUtils.isEmpty(mSortFieldsKey)) {
                    Map<String, String> map1 = new HashMap<>();
                    map1.put(mSortFieldsKey, mSortFieldsValue);
                    map.put("sortFieldsMap", map1);
                }
                return service2.findEcCommodityAppList(map)
                        .map(ElementUtils.getStoreElementToObjectFun(Ad2.LINK_TYPE.TYPE_NEW_COMMODITY_DETAIL, 0, StoreHomeElementEnum.WELFARE_COMMODITY_1_2_V2.getType()));
            }

            @Override
            protected boolean isLoadMoreEnable() {
                return false;
            }

            @Override
            protected void onScrollToBottom() {
//                super.onScrollToBottom();
                if (isCanLoadMore) {
                    swipeToLoadLayout.setLoadMoreEnabled(true);
                    swipeToLoadLayout.setLoadingMore(true);
                } else {
                    if (mShowBottomToast) {
                        if (!mBottomToastLock) {
                            mBottomToastLock = true;
                            ToastUtil.showMessage(getContext(), "到底了~");
                        }
                    }
                }
            }

            @Override
            public void setLoadMoreEnable(boolean enable) {
                isCanLoadMore = enable;

            }

            @Override
            protected void onLoadMoreComplete() {
                adapter.notifyDataSetChanged();
                swipeToLoadLayout.setLoadingMore(false, true, recyclerView);
                getRefreshController().getSwipeToLoadLayout().setLoadMoreEnabled(false);

            }
        };

    }

    /**
     * 加载广告
     */
    private void loadAd() {
        AdRequestUtil2.getAdByPageCode(getContext(), Ad2.POSITION.BENEFIT_BANNER_NEW, true, mBannerView);
    }

    /**
     * 获取功能图标
     */
    private void requestFunctionIcon() {
        L00bangRequestManager2.getServiceInstance()
                .getHomeFunctionList(HomeFunctionResult.STORE_BENEFIT_NEW)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new NormalSubscriber<HomeFunctionResult>(getContext()) {
                    @Override
                    public void onSuccess(HomeFunctionResult homeFunctionResult) {
                        super.onSuccess(homeFunctionResult);
                        if (homeFunctionResult != null && homeFunctionResult.getFunctionEntranceList() != null && homeFunctionResult.getFunctionEntranceList().size() > 0) {
                            FunctionViewUtils.bindFunctionResult(getContext(), mRecyclerViewItem, homeFunctionResult);
                            mRecyclerViewItem.setVisibility(View.VISIBLE);
                        } else {
                            mRecyclerViewItem.setVisibility(View.GONE);
                        }

                    }

                    @Override
                    public void onFailure(Throwable e) {
                        mRecyclerViewItem.setVisibility(View.GONE);
                    }
                });

    }

    /**
     * 只刷新列表
     */
    public void refreshOnlyList() {
        mRefreshListOnly = true;
        getRefreshController().onRefresh();
    }

    @Override
    public void onOffsetChanged(AppBarLayout appBarLayout, int verticalOffset) {
        getRefreshController().getSwipeToLoadLayout().setRefreshEnabled(verticalOffset >= 0 && isTop());

    }

    public boolean isTop() {
        if (getRefreshController() == null) {
            return true;
        }
        if (getRefreshController().getData().size() == 0) {
            return true;
        }

        return !getRefreshController().getRecyclerView().canScrollVertically(-1);
    }

    @Override
    public void onStart() {
        super.onStart();
        SensorsUtils.sensorsViewStart(FinalSensors.BROWSE_LIFE_INFORMATION);
    }

    @Override
    public void onStop() {
        super.onStop();
        SensorsUtils.sensorsViewEndNew("尊享权益", FinalSensors.BROWSE_LIFE_INFORMATION, "浏览尊享权益");
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (hidden) {
            SensorsUtils.sensorsViewEndNew("尊享权益", FinalSensors.BROWSE_LIFE_INFORMATION, "浏览尊享权益");
        } else {
            SensorsUtils.sensorsViewStart(FinalSensors.BROWSE_LIFE_INFORMATION);
        }
    }
}
