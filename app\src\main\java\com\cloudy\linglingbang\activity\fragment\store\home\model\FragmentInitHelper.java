package com.cloudy.linglingbang.activity.fragment.store.home.model;

import androidx.fragment.app.Fragment;

/**
 * Fragment 初始化助手
 *
 * <AUTHOR>
 * @date 2018/10/22
 */
public class FragmentInitHelper {
    private boolean mInitialized;
    private Fragment mFragment;

    public FragmentInitHelper(Fragment fragment) {
        mFragment = fragment;
    }

    /**
     * 也可以直接调用 checkAndInit ，只是多一步 isVisibleToUser 的判断
     */
    public void setUserVisibleHint(boolean isVisibleToUser) {
        if (isVisibleToUser) {
            checkAndInit();
        }
    }

    /**
     * <pre>
     * 延迟初始化
     * 有三个地方可能调用初始化，分别是 onCreateView、onHiddenChanged、setUserVisibleHint
     *
     * 如果第一次进入，currentItem 不是当前 Fragment，则从 initViews() 调用，因为不可见，return
     * 当点过去时，由 setUserVisibleHint 调用，初始化
     *
     * 如果第一次进入，currentItem 就是当前 Fragment，从 setUserVisibleHint 调用，由于未附加，getContext 为 null，return
     * 然后由 initViews 调用，因为已可见，初始化
     * </pre>
     */
    public void checkAndInit() {
        checkAndInit(true);
    }

    /**
     * @param checkUserVisibleHint 是否检查 userVisibleHint，在滑动时可以不判断
     */
    public void checkAndInit(boolean checkUserVisibleHint) {
        if (mInitialized) {
            return;
        }
        if (mFragment == null) {
            return;
        }
        if (mFragment.getContext() == null) {
            //未附加，发生于首次 setCurrentItem 就是当前 Fragment，还没有附加直接调用的 setUserVisibleHint
            return;
        }
        if (checkUserVisibleHint && !mFragment.getUserVisibleHint()) {
            //不可见，发生于 currentItem 不是当前 Fragment，但是预加载 Fragment 时的 initViews
            return;
        }
        init();
    }

    protected void init() {
        mInitialized = true;
    }

    public boolean isInitialized() {
        return mInitialized;
    }

    public void setInitialized(boolean initialized) {
        mInitialized = initialized;
    }
}
