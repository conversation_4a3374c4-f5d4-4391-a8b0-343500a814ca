package com.cloudy.linglingbang.activity.basic;

import android.graphics.Paint;
import android.graphics.drawable.Drawable;
import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.util.DeviceUtil;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.textview.ColorTrackRadioButton;

import java.util.Arrays;
import java.util.List;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

/**
 * 滚动tab
 *
 * <AUTHOR>
 * @date 2017/5/16.
 */
public class ViewPagerTabController<T> implements ViewPager.OnPageChangeListener {
    //指示器宽度与RadioButton宽度一致
    public static final int INDICATOR_WIDTH_RADIO_BUTTON_WIDTH = -1;
    //指示器宽度与文字宽度一致
    public static final int INDICATOR_WIDTH_TEXT_WIDTH = -2;

    /**
     * 颜色渐变，按偏移百分比移动
     */
    public static final int COLOR_GRADIENT_TYPE_TRANSLATE_BY_OFFSET = 1;
    /**
     * 颜色渐变，按偏移像素移动
     */
    public static final int COLOR_GRADIENT_TYPE_TRANSLATE_BY_OFFSET_PIXELS = 2;

    /**
     * @deprecated 弃用原因是没有写，有时间再写
     * 颜色渐变，按偏移百分比变化透明度，没有写，IOS是这个类型的
     */
    @Deprecated
    public static final int COLOR_GRADIENT_TYPE_ALPHA_BY_OFFSET = 3;

    protected IViewPagerTabContext<T> mViewPagerTabContext;
    /**
     * radio button上的字
     */
    protected String[] mRadioButtonTextArray;
    /**
     * radio button前面的图标
     */
    protected int[] mRadioButtonIconArray;
    /**
     * radio group
     */
    protected RadioGroup mRadioGroup;
    /**
     * 下面的线
     */
    protected View mViewIndicator;
    /**
     * view pager
     */
    protected ViewPager mViewPager;
    /**
     * 数据，可以是fragment,也可以是view
     */
    protected List<T> mData;

    /**
     * 是否显示指示器
     */
    private boolean mShowIndicator = true;
    /**
     * adapter
     */
    protected PagerAdapter mAdapter;
    private SparseArray<PositionParams> mPositionParamsSparseArray = new SparseArray<>();

    /**
     * 指示器的宽度
     * {@linkplain #INDICATOR_WIDTH_RADIO_BUTTON_WIDTH}
     * {@linkplain #INDICATOR_WIDTH_TEXT_WIDTH}
     */
    private int mIndicatorWidth = INDICATOR_WIDTH_TEXT_WIDTH;
    /**
     * 颜色渐变类型，如果不需要，将其设置为0即可
     */
    private int mTextColorGradientType = 0;

    public ViewPagerTabController(IViewPagerTabContext<T> context, RadioGroup radioGroup, View viewIndicator, ViewPager viewPager) {
        mViewPagerTabContext = context;
        mRadioGroup = radioGroup;
        mViewIndicator = viewIndicator;
        mViewPager = viewPager;
    }

    /**
     * 初始化布局，在Activity或Fragment中调用
     */
    public void initViews() {

        mRadioButtonTextArray = mViewPagerTabContext.getRadioButtonTextArray();
        if (mRadioButtonTextArray == null) {
            return;
        }

        mData = mViewPagerTabContext.createAdapterData();
        mAdapter = mViewPagerTabContext.createViewPagerAdapter(mData);
        mViewPager.setAdapter(mAdapter);
        mViewPager.addOnPageChangeListener(this);

        initRadioGroup();
    }

    /**
     * 初始化radioGroup，注意初始化完成后会选中默认项
     */
    protected void initRadioGroup() {
        mRadioGroup.removeAllViews();
        LayoutInflater layoutInflater = LayoutInflater.from(mViewPagerTabContext.getContext());
        for (int i = 0; i < mRadioButtonTextArray.length; i++) {
            RadioButton radioButton = createRadioButton(layoutInflater, i, mRadioGroup);
            //如果设置了文字之前的图标
            if (mRadioButtonIconArray != null && mRadioButtonIconArray.length > i) {
                Drawable drawable = mViewPagerTabContext.getContext().getResources().getDrawable(mRadioButtonIconArray[i]);
                radioButton.setCompoundDrawablesWithIntrinsicBounds(drawable, null, null, null);
            }
            //设置点击监听
            setClickListener(i, radioButton);
            mRadioGroup.addView(radioButton);
        }
        setRadioGroupListener();
        //默认选中的
        RadioButton radioButton = (RadioButton) mRadioGroup.getChildAt(getDefaultItemIndex());
        //先清除一下，否则再次调用setChecked方法不生效
        mRadioGroup.clearCheck();
        radioButton.setChecked(true);
//        mViewPager.setCurrentItem(getDefaultItemIndex());
    }

    /**
     * 设置点击每个radioButton的监听
     */
    protected void setClickListener(final int i, RadioButton radioButton) {
        radioButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mViewPagerTabContext.onTabClick(i);
            }
        });
    }

    /**
     * 设置点击每个radioButton的监听 加埋点
     */
    protected void setClickListener(final int i, RadioButton radioButton,String eventName,String eventPage,String eventPosition) {
        radioButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mViewPagerTabContext.onTabClick(i);
                SensorsUtils.sensorsClickBtn(eventName,eventPage,eventPosition);
            }
        });
    }

    /**
     * 设置监听，因为社区需要点击OnclickListener监听，和OnCheckedChangeListener冲突，故抽取该方法，子类可以修改他的实现
     */
    protected void setRadioGroupListener() {
        mRadioGroup.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                mViewPager.setCurrentItem(checkedId);

            }
        });
    }

    public ViewPagerTabController<T> setShowIndicator(boolean showIndicator) {
        mShowIndicator = showIndicator;
        return this;
    }

    /**
     * 创建一个radioButton，设置id、文字
     */
    protected RadioButton createRadioButton(LayoutInflater layoutInflater, int index, ViewGroup viewgroup) {
        int radioButtonResourceId = mViewPagerTabContext.getRadioButtonResourceId(index);
        if (radioButtonResourceId <= 0) {
            radioButtonResourceId = getDefaultRadioButtonResourceId(index);
        }
        RadioButton radioButton = (RadioButton) layoutInflater.inflate(radioButtonResourceId, viewgroup, false);
        radioButton.setId(index);
        radioButton.setText(createRadioButtonText(index));
        if (radioButton instanceof ColorTrackRadioButton) {
            ((ColorTrackRadioButton) radioButton).setNeedTrack(mTextColorGradientType != 0);
        }
        return radioButton;
    }

    /**
     * 某个radioButton上的文字
     */
    protected String createRadioButtonText(int index) {
        if (mRadioButtonTextArray != null && index < mRadioButtonTextArray.length) {
            return mRadioButtonTextArray[index];
        }
        return "";
    }

    /**
     * radioButton的布局资源Id
     */
    protected int getDefaultRadioButtonResourceId(int index) {
        return R.layout.item_radio_button;
    }

    /**
     * 默认选中的item
     */
    public int getDefaultItemIndex() {
        return 0;
    }

    /**
     * implements这样方便子类重写
     */
    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
        //可以在滑动时就加载实际布局，或者是提供占位图，等展示出来再加载实际布局
        if (mData != null && positionOffset > 0) {
            if (position + 1 < mData.size()) {
                T t = mData.get(position + 1);
                if (t instanceof LazyFragment) {
                    ((LazyFragment) t).loadRealView();
                }
            }
        }
        if (mShowIndicator) {
            mViewIndicator.setVisibility(View.VISIBLE);
            calculateIndicatorPosition(position, positionOffset, positionOffsetPixels);
        } else {
            mViewIndicator.setVisibility(View.GONE);
        }
    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    @Override
    public void onPageSelected(int position) {
        RadioButton radioButton = (RadioButton) mRadioGroup.getChildAt(position);
        radioButton.setChecked(true);
        mViewPagerTabContext.onPageSelected(position);
        //重建检查
        if (mTextColorGradientType != 0) {
            for (int i = 0; i < mRadioButtonTextArray.length; i++) {
                if (i != position) {
                    //不相等的置为0
                    if (mTextColorGradientType == COLOR_GRADIENT_TYPE_TRANSLATE_BY_OFFSET || mTextColorGradientType == COLOR_GRADIENT_TYPE_TRANSLATE_BY_OFFSET_PIXELS) {
                        RadioButton iRadioButton = (RadioButton) mRadioGroup.getChildAt(i);
                        if (iRadioButton != null && iRadioButton instanceof ColorTrackRadioButton) {
                            //第1个
                            ColorTrackRadioButton colorTrackRadioButton = (ColorTrackRadioButton) iRadioButton;
                            colorTrackRadioButton.setDirection(ColorTrackRadioButton.DIRECTION_RIGHT);
                            colorTrackRadioButton.setProgress(0);
                        }
                    }
                }
            }
        }
        //http://blog.csdn.net/pingfangx/article/details/51930979
        //mRadioGroup.check(position);
    }

    //下面部分是一些get、set方法

    public ViewPager getViewPager() {
        return mViewPager;
    }

    public RadioGroup getRadioGroup() {
        return mRadioGroup;
    }

    public PagerAdapter getAdapter() {
        return mAdapter;
    }

    public int getIndicatorWidth() {
        return mIndicatorWidth;
    }

    public ViewPagerTabController<T> setIndicatorWidth(int indicatorWidth) {
        mIndicatorWidth = indicatorWidth;
        return this;
    }

    public int getTextColorGradientType() {
        return mTextColorGradientType;
    }

    public ViewPagerTabController<T> setTextColorGradientType(int textColorGradientType) {
        mTextColorGradientType = textColorGradientType;
        return this;
    }

    /**
     * 设置文字
     */
    public ViewPagerTabController<T> setRadioButtonTextArray(String[] radioButtonTextArray) {
        mRadioButtonTextArray = radioButtonTextArray;
        return this;
    }

    public ViewPagerTabController<T> setRadioButtonIconArray(int[] radioButtonIconArray) {
        mRadioButtonIconArray = radioButtonIconArray;
        return this;
    }

    //下面是计算指示器相关的方法

    /**
     * 返回按钮的相关布局参数
     *
     * @param position 位置
     * @return 参数数组
     */
    protected PositionParams getRadioButtonPositionParams(int position) {
        PositionParams savedPositionParams = mPositionParamsSparseArray.get(position);
        if (savedPositionParams != null) {
            return savedPositionParams;
        }
        PositionParams positionParams = new PositionParams(position, mRadioGroup);
        mPositionParamsSparseArray.put(position, positionParams);
        return positionParams;
    }

    /**
     * 获取渐变过程中某个百分比处的值
     */
    private float getMiddleValue(float start, float end, float offset) {
        return start + (end - start) * offset;
    }

    /**
     * 计算指示器的位置
     */
    protected void calculateIndicatorPosition(int position, float positionOffset, int positionOffsetPixels) {

        PositionParams positionParams1 = getRadioButtonPositionParams(position);

        if (mIndicatorWidth == INDICATOR_WIDTH_TEXT_WIDTH && positionParams1.textWidth <= 0) {
            //宽度为0不可用，重新赋值为宽度
            mIndicatorWidth = INDICATOR_WIDTH_RADIO_BUTTON_WIDTH;
        }
        positionParams1.calculate(positionOffset, mIndicatorWidth);

        PositionParams positionParams2 = null;

        boolean considerNext = false;
        //不管是否取为文字宽度或是按钮宽度，都要尝试取后一个
        if (position < mRadioGroup.getChildCount() - 1) {
            //可以获取后一个
            positionParams2 = getRadioButtonPositionParams(position + 1);
            positionParams2.calculate(positionOffset, mIndicatorWidth);
            considerNext = PositionParams.needConsiderNext(positionParams1, positionParams2);
        }

        setImageLayoutParams(mViewIndicator, positionParams1, positionParams2, considerNext, positionOffset, positionOffsetPixels);
    }

    protected void setImageLayoutParams(View imageView, PositionParams positionParams1, PositionParams positionParams2, boolean considerNext, float positionOffset, int positionOffsetPixels) {

        int indicatorWidth = 0;
        int indicatorLeftMargin = 0;
        float slideWidth = 0f;
        if (!considerNext || positionParams2 == null) {
            indicatorWidth = positionParams1.indicatorWidth;
            indicatorLeftMargin = positionParams1.indicatorLeftMargin;
            slideWidth = positionParams1.slideWidth;
        } else {
            indicatorWidth = (int) getMiddleValue(positionParams1.indicatorWidth, positionParams2.indicatorWidth, positionOffset);
            indicatorLeftMargin = (int) getMiddleValue(positionParams1.slideStart, positionParams2.slideStart, positionOffset);
            //滑动的宽度用leftMargin-左边的起点
            slideWidth = indicatorLeftMargin - positionParams1.slideStart;
        }

        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) imageView.getLayoutParams();
        layoutParams.width = indicatorWidth;
        layoutParams.leftMargin = indicatorLeftMargin;
        imageView.setLayoutParams(layoutParams);

        //设置颜色渐变
        if (mTextColorGradientType == COLOR_GRADIENT_TYPE_TRANSLATE_BY_OFFSET) {
            //按百分比计算
            calculateTextColorByOffset(positionParams1.position, positionOffset);
        } else if (mTextColorGradientType == COLOR_GRADIENT_TYPE_TRANSLATE_BY_OFFSET_PIXELS) {
            //按像素计算
            float slideOffset1 = 0f;
            float slideOffset2 = 0f;
            if (positionParams1.textWidth != 0) {
                slideOffset1 = (positionParams1.slideStart + slideWidth - positionParams1.textLeft) / positionParams1.textWidth;
            }
            if (positionParams2 != null) {
                if (positionParams2.textWidth != 0) {
                    slideOffset2 = (positionParams1.slideStart + slideWidth + indicatorWidth - positionParams2.textLeft) / positionParams2.textWidth;
                }
            }
            calculateTextColorByOffsetPixels(positionParams1.position, slideOffset1, slideOffset2);
        }
    }

    private void calculateTextColorByOffset(int position, float positionOffset) {
        RadioButton radioButton = (RadioButton) mRadioGroup.getChildAt(position);
        if (radioButton != null && radioButton instanceof ColorTrackRadioButton) {
            //第1个
            ColorTrackRadioButton colorTrackRadioButton = (ColorTrackRadioButton) radioButton;
            colorTrackRadioButton.setDirection(ColorTrackRadioButton.DIRECTION_RIGHT);
            colorTrackRadioButton.setProgress(1 - positionOffset);
        }
        if (position < mRadioGroup.getChildCount() - 1) {
            RadioButton nextRadioButton = (RadioButton) mRadioGroup.getChildAt(position + 1);
            if (nextRadioButton != null && nextRadioButton instanceof ColorTrackRadioButton) {
                //第2个
                ColorTrackRadioButton nextColorTrackRadioButton = (ColorTrackRadioButton) nextRadioButton;
                nextColorTrackRadioButton.setDirection(ColorTrackRadioButton.DIRECTION_LEFT);
                nextColorTrackRadioButton.setProgress(positionOffset);
            }
        }
    }

    private void calculateTextColorByOffsetPixels(int position, float slideOffset1, float slideOffset2) {
        //不取1负数，最小取0
        if (slideOffset1 < 0) {
            slideOffset1 = 0;
        }
        if (slideOffset2 < 0) {
            slideOffset2 = 0;
        }
        RadioButton radioButton = (RadioButton) mRadioGroup.getChildAt(position);
        if (radioButton != null && radioButton instanceof ColorTrackRadioButton) {
            //第1个
            ColorTrackRadioButton colorTrackRadioButton = (ColorTrackRadioButton) radioButton;
            colorTrackRadioButton.setDirection(ColorTrackRadioButton.DIRECTION_RIGHT);
            colorTrackRadioButton.setProgress(1 - slideOffset1);
        }
        if (position < mRadioGroup.getChildCount() - 1) {
            RadioButton nextRadioButton = (RadioButton) mRadioGroup.getChildAt(position + 1);
            if (nextRadioButton != null && nextRadioButton instanceof ColorTrackRadioButton) {
                //第2个
                ColorTrackRadioButton nextColorTrackRadioButton = (ColorTrackRadioButton) nextRadioButton;
                nextColorTrackRadioButton.setDirection(ColorTrackRadioButton.DIRECTION_LEFT);
                nextColorTrackRadioButton.setProgress(slideOffset2);
            }
        }
    }

    /**
     * 更新fragment列表的方法
     * 注意：
     * 1.更新fragment的个数的同时，要同时更新 mRadioButtonTextArray
     * 2.更新fragment的adapter需要使用FragmentStatePagerAdapter，并且需要重写getItemPosition方法，
     * 返回PagerAdapter.POSITION_NONE，否则不生效
     */
    public void updateFragmentList() {
        mAdapter.notifyDataSetChanged();
        //RadioGroup必须全部重新初始化，否则checkedId不会更改，会出问题
        initRadioGroup();
        mRadioGroup.postDelayed(new Runnable() {
            @Override
            public void run() {
                //手动调用刷新指示器
                onPageScrolled(getDefaultItemIndex(), 0, 0);
            }
        }, 1);
    }

    /**
     * 删除viewPager中某个fragment（下标从0开始）
     *
     * @param index 删降的index
     */
    public void deleteFragment(int index) {
        //将index删降，即往前移
        System.arraycopy(mRadioButtonTextArray, index + 1, mRadioButtonTextArray, index, mRadioButtonTextArray.length - 1 - index);
        //生成新数组
        mRadioButtonTextArray = Arrays.copyOf(mRadioButtonTextArray, mRadioButtonTextArray.length - 1);
        mData.remove(index);
        updateFragmentList();

    }

    //下面是静态方法和内部类

    /**
     * fragment的adapter
     */
    public static PagerAdapter createFragmentViewPagerAdapter(FragmentActivity activity, final List<Fragment> data) {
        return createFragmentViewPagerAdapter(activity.getSupportFragmentManager(), data);
    }

    public static PagerAdapter createFragmentViewPagerAdapter(Fragment fragment, final List<Fragment> data) {
        return createFragmentViewPagerAdapter(fragment.getChildFragmentManager(), data);
    }

    public static PagerAdapter createFragmentViewPagerAdapter(FragmentManager fragmentManager, final List<Fragment> data) {
        //在Fragment中嵌套Fragment，要用getChildFragmentManager
        //http://stackoverflow.com/questions/22150950
        return new FragmentPagerAdapter(fragmentManager) {
            @Override
            public Fragment getItem(int position) {
                return data.get(position);
            }

            @Override
            public int getCount() {
                return data.size();
            }
        };
    }

    /**
     * left：左
     * width:宽
     * leftMargin:左边的间距，包括drawableLeft,包含getCompoundDrawablePadding
     * gravity
     */
    static class PositionParams {
        /**
         * 位
         */
        int position;
        /**
         * 宽度
         */
        int width;
        /**
         * 左
         */
        int left;
        /**
         * 文字宽度，用于计算指示器宽度，如果有drawableLeft或drawableReight，会加上
         */
        int textWidth;
        /**
         * 文字起点，用于计算对文字颜色渐变，不包括drawableLeft
         */
        int textLeft;
        /**
         * drawable的宽度,包含getCompoundDrawablePadding
         */
        int drawableLeftWidth;
        int drawableRightWidth;

        int indicatorWidth;
        float slideStart;
        float slideWidth;
        int indicatorLeftMargin;

        public PositionParams(int position, RadioGroup radioGroup) {
            this.position = position;
            //推测的值
            RadioButton radioButton = (RadioButton) radioGroup.getChildAt(position);
            if (radioButton != null) {
                ViewGroup.LayoutParams layoutParams = radioButton.getLayoutParams();
                if (layoutParams != null) {
                    width = layoutParams.width;
                    if (width <= 0) {
                        width = radioButton.getMeasuredWidth();
                        if (width == 0) {
                            //有可能为0，如果为0，则取平分屏幕的宽度
                            width = DeviceUtil.getScreenWidth() / radioGroup.getChildCount();
                        }
                    }
                }

                Drawable[] compoundDrawables = radioButton.getCompoundDrawables();
                Drawable leftDrawable = compoundDrawables[0];
                Drawable rightDrawable = compoundDrawables[2];

                left = radioButton.getLeft();
                Paint paint = radioButton.getPaint();
                if (paint != null) {
                    textWidth = (int) paint.measureText(radioButton.getText().toString());
                    if (leftDrawable != null) {
                        drawableLeftWidth = leftDrawable.getIntrinsicWidth() + radioButton.getCompoundDrawablePadding();
                    }
                    if (rightDrawable != null) {
                        drawableRightWidth = rightDrawable.getIntrinsicWidth() + radioButton.getCompoundDrawablePadding();
                    }
                }
                //文字+drawable的起点
                textLeft = width / 2 - (textWidth + drawableLeftWidth + drawableRightWidth) / 2;
                //不包括括drawableLeft
                textLeft += drawableLeftWidth;
                //再加上按钮原始的left
                textLeft += left;
            }
        }

        private void calculate(float positionOffset, int indicatorWidth) {
            if (indicatorWidth == INDICATOR_WIDTH_RADIO_BUTTON_WIDTH) {
                this.indicatorWidth = width;
                slideStart = left;
                slideWidth = width * positionOffset;
                indicatorLeftMargin = (int) (slideStart + slideWidth);
            } else {
                if (indicatorWidth == INDICATOR_WIDTH_TEXT_WIDTH) {
                    this.indicatorWidth = textWidth + drawableLeftWidth + drawableRightWidth;
                } else {
                    this.indicatorWidth = indicatorWidth;
                }
                slideStart = left + width / 2f - this.indicatorWidth / 2f;
                slideWidth = width * positionOffset;
                indicatorLeftMargin = (int) (slideStart + slideWidth);
            }
        }

        public static boolean needConsiderNext(PositionParams positionParams1, PositionParams positionParams2) {
            return positionParams1.width != positionParams2.width
                    || positionParams1.textWidth != positionParams2.textWidth
                    || positionParams1.drawableLeftWidth != positionParams2.drawableLeftWidth
                    || positionParams1.drawableRightWidth != positionParams2.drawableRightWidth;
        }
    }

    public List<T> getData() {
        return mData;
    }
}
