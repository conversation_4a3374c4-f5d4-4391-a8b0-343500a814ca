package com.cloudy.linglingbang.activity.fragment.store.rights;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.fragment.store.home.adapter.StoreHomeAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.app.widget.recycler.holder.CommonEmptyInfoViewHolder;
import com.cloudy.linglingbang.model.wrapper.EmptyInfo;

import androidx.recyclerview.widget.RecyclerView;

/**
 * 尊享权益
 *
 * <AUTHOR>
 * @date 2021/8/17
 */
public class VipRightsAdapter extends BaseRecyclerViewAdapter<Object> {
    private final static int EMPTY_CONTENT = 1000001;

    private StoreHomeAdapter mStoreHomeAdapter;

    public VipRightsAdapter(Context context, RecyclerView.Adapter adapter) {
        super(context, null);
        setStoreHomeAdapter((StoreHomeAdapter) adapter);
    }

    public void setStoreHomeAdapter(StoreHomeAdapter storeHomeAdapter) {
        mStoreHomeAdapter = storeHomeAdapter;
    }

    @Override
    protected BaseRecyclerViewHolder<Object> createViewHolder(View itemView) {
        return null;
    }

    @Override
    public BaseRecyclerViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        if (EMPTY_CONTENT == viewType) {
            View itemView = View.inflate(mContext, getItemLayoutRes(viewType), null);
            itemView.setLayoutParams(new ViewGroup.LayoutParams(-1, -1));
            return new CommonEmptyInfoViewHolder(itemView);
        }
        return mStoreHomeAdapter.onCreateViewHolder(parent, viewType);
    }

    @Override
    protected int getItemLayoutRes(int viewType) {
        return R.layout.item_empty_info;
    }

    @Override
    public int getItemCount() {
        return mStoreHomeAdapter.getItemCount();
    }

    @Override
    public void onBindViewHolder(BaseRecyclerViewHolder holder, int position) {
        super.onBindViewHolder(holder, position);
        if (holder instanceof CommonEmptyInfoViewHolder) {
            holder.bindTo(mStoreHomeAdapter.getData().get(position), position);
        } else {
            mStoreHomeAdapter.onBindViewHolder(holder, position);
        }
    }

    @Override
    public int getItemViewType(int position) {
        Object obj = mStoreHomeAdapter.getData().get(position);
        if (obj instanceof EmptyInfo) {
            return EMPTY_CONTENT;
        }
        return mStoreHomeAdapter.getItemViewType(position);
    }
}
