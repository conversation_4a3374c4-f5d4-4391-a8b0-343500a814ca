package com.cloudy.linglingbang.activity.community.square;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.community.CommunityDoPostImageTextActivity;
import com.cloudy.linglingbang.activity.community.TopicDetailActivity;
import com.cloudy.linglingbang.activity.community.TopicListActivity;
import com.cloudy.linglingbang.activity.community.common.BasePostListFragment;
import com.cloudy.linglingbang.activity.community.common.HeaderWrapperPostAdapter;
import com.cloudy.linglingbang.activity.community.common.PostFlagsEnum;
import com.cloudy.linglingbang.activity.community.experience.CarBuyingExperiencePostListActivity;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.TopicUtils;
import com.cloudy.linglingbang.app.util.UserStatusChangeManager;
import com.cloudy.linglingbang.app.util.span.UserIdentitySpanUtils;
import com.cloudy.linglingbang.app.widget.banner.BannerView;
import com.cloudy.linglingbang.app.widget.image.ImageLoadUtils;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.app.widget.recycler.header.HeaderAndFooterWrapperAdapter;
import com.cloudy.linglingbang.model.community.PostLabel;
import com.cloudy.linglingbang.model.community.RecommendLabelCode;
import com.cloudy.linglingbang.model.community.TodayTopic;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BackgroundSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BaseSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.server.Ad.Ad2;
import com.cloudy.linglingbang.model.server.Ad.AdRequestUtil2;
import com.cloudy.linglingbang.model.user.User;

import java.util.List;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 社区广场
 *
 * <AUTHOR>
 * @date 2018/6/22
 */
public class CommunitySquareFragment extends BasePostListFragment {
    private HeaderAndFooterWrapperAdapter mWrapperAdapter;
    private BannerView mBannerView;
    private HeaderViewHolder mHeaderViewHolder;
    private LabelViewHolder mLabelViewHolder;
    private UserStatusChangeManager mUserStatusChangeManager;
    private PublishViewController mPublishViewController;

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_community_square;
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<PostCard> list) {
        mWrapperAdapter = new HeaderAndFooterWrapperAdapter();
        mWrapperAdapter.setInnerAdapter(new HeaderWrapperPostAdapter(getContext(), list, mWrapperAdapter)
                .addFlags(PostFlagsEnum.SHOW_ATTENTION)
                .addFlags(PostFlagsEnum.SHOW_GOLD_COIN)
        );
        initHeaderView();
        return mWrapperAdapter;
    }

    private void initHeaderView() {
        View headerView = LayoutInflater.from(getContext()).inflate(R.layout.item_community_square_header, getRefreshController().getRecyclerView(), false);
        mHeaderViewHolder = new HeaderViewHolder(headerView);
        mLabelViewHolder = new LabelViewHolder(headerView);
        //初始化设置
        mHeaderViewHolder.bindUser(User.getsUserInstance());
        mBannerView = headerView.findViewById(R.id.corner_banner_view);
        mWrapperAdapter.addHeaderView(headerView);
        mPublishViewController = new PublishViewController(mHeaderViewHolder.getIvPublishTop(), mRootView.findViewById(R.id.iv_publish), mRootView.findViewById(R.id.iv_back_top), getRefreshController().getRecyclerView());
        mPublishViewController.setDoPostData(null, CommunityDoPostImageTextActivity.OPEN_FROM_SQUARE);
    }

    @Override
    public Observable<BaseResponse<List<PostCard>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return service2.getSquarePostList(pageNo, pageSize);
    }

    @Override
    public RefreshController<PostCard> createRefreshController() {
        return new RefreshController<PostCard>(this) {
            @Override
            public void onRefresh() {
                super.onRefresh();
                getAd();
                getTodayTopic();
                mLabelViewHolder.getRecommendLabelList(false);
            }
        };
    }

    /**
     * 获取广告
     */
    private void getAd() {
        AdRequestUtil2.getAdByPageCode(getContext(), Ad2.POSITION.COMMUNITY_SQUARE, true, mBannerView);
    }

    /**
     * 获取今日话题
     */
    private void getTodayTopic() {
        L00bangRequestManager2.getServiceInstance()
                .getTodayTopic()
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new BackgroundSubscriber<TodayTopic>(getContext()) {
                    @Override
                    public void onSuccess(TodayTopic todayTopic) {
                        super.onSuccess(todayTopic);
                        mHeaderViewHolder.bindTodayTopic(todayTopic);
                    }
                });
    }

    private void updateUserInfo(User user) {
        if (mHeaderViewHolder != null) {
            mHeaderViewHolder.bindUser(user);
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mUserStatusChangeManager = new UserStatusChangeManager(getContext(), new UserStatusChangeManager.SimpleUserStatusChangeReceiver() {
            @Override
            protected void onUpdateUser(User user) {
                updateUserInfo(user);
            }
        });
        mUserStatusChangeManager.register();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mUserStatusChangeManager != null) {
            mUserStatusChangeManager.unregister();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mBannerView != null) {
            mBannerView.onPause();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mBannerView != null) {
            mBannerView.onResume();
        }
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (mBannerView != null) {
            mBannerView.onHiddenChanged(hidden);
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (mBannerView != null) {
            mBannerView.setUserVisibleHint(isVisibleToUser);
        }
    }

    public class HeaderViewHolder {
        //话题
        private final RelativeLayout mRlTodayTopic;
        private final TextView mTvTopicDate;
        private final TextView mTvTopicLunarDate;
        private final TextView mTvTopicSummary;
        private final TextView mTvTopicMore;

        //用户
        private final TextView mTvUserName;
        private final ImageView mIvPublishTop;

        private TodayTopic mTodayTopic;

        public HeaderViewHolder(View itemView) {
            mRlTodayTopic = itemView.findViewById(R.id.rl_today_topic);
            mTvTopicDate = itemView.findViewById(R.id.tv_topic_date);
            mTvTopicLunarDate = itemView.findViewById(R.id.tv_topic_lunar_date);
            mTvTopicSummary = itemView.findViewById(R.id.tv_topic_summary);
            mTvTopicMore = itemView.findViewById(R.id.tv_topic_more);

            mTvTopicDate.getPaint().setFakeBoldText(true);
            mTvTopicLunarDate.getPaint().setFakeBoldText(true);

            mTvUserName = itemView.findViewById(R.id.tv_user_name);
            mIvPublishTop = itemView.findViewById(R.id.iv_publish_top);

            mRlTodayTopic.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onClickRlTodayTopic(v);
                }
            });
            mTvTopicMore.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onClickTvTopicMore(v);
                }
            });
        }

        /**
         * 点击了今日话题，跳到话题详情
         */
        private void onClickRlTodayTopic(View v) {
            if (mTodayTopic != null) {
                TopicDetailActivity.startActivity(v.getContext(), String.valueOf(mTodayTopic.getTopicId()));
            }
        }

        /**
         * 点击了更多，跳到话题列表
         */
        private void onClickTvTopicMore(View v) {
            IntentUtils.startActivity(v.getContext(), TopicListActivity.class);
        }

        public ImageView getIvPublishTop() {
            return mIvPublishTop;
        }

        public void bindUser(User user) {
            boolean login = user != null && user.hasLogin();
            mTvUserName.setVisibility(login ? View.VISIBLE : View.GONE);
            if (login) {
                //登录
                UserIdentitySpanUtils.setUserNameAndIdentity(mTvUserName, user, false);
            }
        }

        public void bindTodayTopic(TodayTopic todayTopic) {
            mTodayTopic = todayTopic;
            mRlTodayTopic.setVisibility(todayTopic != null ? View.VISIBLE : View.GONE);
            if (todayTopic != null) {
                mTvTopicSummary.setText(TopicUtils.getNameAndSummaryString(todayTopic));
                mTvTopicDate.setText(todayTopic.getCurrentDate());
                mTvTopicLunarDate.setText(todayTopic.getCurrentLunar());
            }
        }
    }

    /**
     * 标签，因为太多了，也单独提出来
     */
    public class LabelViewHolder {
        private final int STATUS_SHOWN = 1;
        private final int STATUS_HIDDEN = 0;
        private int mAnimationStatus = STATUS_SHOWN;

        private List<PostLabel> mLabelList;
        private final ImageView mIvHotLabel;
        private final RelativeLayout mRlLabelList;
        private final RecyclerView mRecyclerViewLabelList;

        public LabelViewHolder(View itemView) {
            mIvHotLabel = itemView.findViewById(R.id.iv_hot_label);
            mRlLabelList = itemView.findViewById(R.id.rl_label_list);
            mRecyclerViewLabelList = itemView.findViewById(R.id.recycler_view_label_list);

            mIvHotLabel.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onClickIvHotLabel();
                }
            });
            mRecyclerViewLabelList.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));

            //根据初始值进行一次初始化
            if (mAnimationStatus == STATUS_SHOWN) {
                showLabelList();
            } else {
                hideLabelList();
            }
        }

        /**
         * 点击热门标签
         */
        private void onClickIvHotLabel() {
            if (mAnimationStatus == STATUS_HIDDEN) {
                //显示
                if (mLabelList != null && !mLabelList.isEmpty()) {
                    //不为空，显示
                    showLabelList();
                } else {
                    //为空，获取
                    getRecommendLabelList(true);
                }
            } else if (mAnimationStatus == STATUS_SHOWN) {
                //隐藏
                hideLabelList();
            }
        }

        /**
         * 刷新时后台获取，获取后设置
         * 点击按钮时，如果不为空则显示，如果为空，则前台获取，获取完显示
         */
        private void getRecommendLabelList(boolean foreground) {
            BaseSubscriber<List<PostLabel>> subscriber;
            if (foreground) {
                subscriber = new ProgressSubscriber<List<PostLabel>>(getContext()) {
                    @Override
                    public void onSuccess(List<PostLabel> postLabels) {
                        super.onSuccess(postLabels);
                        onGetLabelList(postLabels);
                        //如果是前台获取到的，显示
                        if (mLabelList != null && !mLabelList.isEmpty()) {
                            showLabelList();
                        }
                    }
                };
            } else {
                subscriber = new BackgroundSubscriber<List<PostLabel>>(getContext()) {
                    @Override
                    public void onSuccess(List<PostLabel> postLabels) {
                        super.onSuccess(postLabels);
                        onGetLabelList(postLabels);
                    }
                };
            }
            L00bangRequestManager2.getServiceInstance()
                    .getRecommendLabelList(RecommendLabelCode.HOT)
                    .compose(L00bangRequestManager2.setSchedulers())
                    .subscribe(subscriber);
        }

        /**
         * 前后台获取到，都进行设置
         */
        private void onGetLabelList(List<PostLabel> postLabels) {
            mLabelList = postLabels;
            //都设置可以保证在显示的状态下，如果下拉刷新，也会变化
            mRecyclerViewLabelList.setAdapter(new PostLabelAdapter(getContext(), mLabelList));
        }

        /**
         * 显示标签
         */
        private void showLabelList() {
            //显示
            mIvHotLabel.setImageResource(R.drawable.ic_community_square_hot_label_open);
            mRlLabelList.setVisibility(View.VISIBLE);
            mAnimationStatus = STATUS_SHOWN;
        }

        private void hideLabelList() {
            //隐藏
            mIvHotLabel.setImageResource(R.drawable.ic_community_square_hot_label_normal);
            mRlLabelList.setVisibility(View.GONE);
            mAnimationStatus = STATUS_HIDDEN;
        }
    }

    public class PostLabelAdapter extends BaseRecyclerViewAdapter<PostLabel> {
        public PostLabelAdapter(Context context, List<PostLabel> data) {
            super(context, data);
        }

        @Override
        protected BaseRecyclerViewHolder<PostLabel> createViewHolder(View itemView) {
            PostLabelViewHolder postLabelViewHolder = new PostLabelViewHolder(itemView);
            postLabelViewHolder.setOnItemClickListener(new BaseRecyclerViewHolder.OnItemClickListener() {
                @Override
                public void onItemClick(View itemView, int position) {
                    PostLabel postLabel = mData.get(position);
                    if (postLabel != null) {
                        onClickPostLabel(postLabel);
                    }
                }
            });
            return postLabelViewHolder;
        }

        /**
         * 点击了标签
         */
        private void onClickPostLabel(PostLabel postLabel) {
            if (postLabel != null) {
                if (postLabel.isCarBuyingExperiencePost()) {
                    IntentUtils.startActivity(getContext(), CarBuyingExperiencePostListActivity.class);
                } else {
                    JumpPageUtil.goLabelPostList(getContext(), postLabel.getLabelName(), postLabel.getLabelIdOrZero());
                }
            }
        }

        @Override
        protected int getItemLayoutRes(int viewType) {
            return R.layout.item_community_square_label;
        }

        public class PostLabelViewHolder extends BaseRecyclerViewHolder<PostLabel> {
            private ImageView mIvIcon;

            public PostLabelViewHolder(View itemView) {
                super(itemView);
            }

            @Override
            protected void initItemView(View itemView) {
                super.initItemView(itemView);
                mIvIcon = (ImageView) itemView;
            }

            @Override
            public void bindTo(PostLabel postLabel, int position) {
                super.bindTo(postLabel, position);
                ImageLoadUtils.load(mIvIcon, postLabel.getImage());
            }
        }
    }

}
