package com.cloudy.linglingbang.activity.car.energy.ble;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @describe 放双击工具类
 * @date 2021/12/28
 */
public class AntiRepeatUtils {
    private static final long DIFF = 2000;

    private static Map<String, Long> map = new HashMap<>();

    /**
     * 判断两次点击的间隔，如果小于1000，则认为是多次无效点击
     *
     * @return
     */
    public static boolean isFastDoubleClick() {
        return isFastDoubleClick("-1", DIFF);
    }

    /**
     * 判断两次点击的间隔，如果小于1000，则认为是多次无效点击
     *
     * @return
     */
    public static boolean isFastDoubleClick(String buttonId) {
        return isFastDoubleClick(buttonId, DIFF);
    }

    /**
     * 判断两次点击的间隔，如果小于diff，则认为是多次无效点击
     *
     * @param diff
     * @return
     */
    public static boolean isFastDoubleClick(String buttonId, long diff) {
        long time = System.currentTimeMillis();

        if (map.get(buttonId) != null && time - map.get(buttonId) < diff) {
            return true;
        }
        map.put(buttonId, time);
        return false;
    }
}
