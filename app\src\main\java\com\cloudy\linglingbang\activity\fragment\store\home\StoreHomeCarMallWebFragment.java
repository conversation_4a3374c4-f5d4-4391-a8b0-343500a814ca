package com.cloudy.linglingbang.activity.fragment.store.home;

import android.webkit.JavascriptInterface;

import com.cloudy.linglingbang.app.util.UserStatusChangeManager;
import com.cloudy.linglingbang.constants.WebUrlConfigConstant;
import com.cloudy.linglingbang.model.user.User;
import com.cloudy.linglingbang.web.BaseX5WebViewFragment;

import androidx.fragment.app.Fragment;

/**
 * 整车商城
 *
 * <AUTHOR>
 * @date 2020/3/3
 */
public class StoreHomeCarMallWebFragment extends BaseX5WebViewFragment {

    //用户状态广播接收管理类
    private UserStatusChangeManager mUserStatusChangeManager;

    public static Fragment newInstance() {
        return new StoreHomeCarMallWebFragment();
    }

    @Override
    protected String setUrl() {
        return WebUrlConfigConstant.HOME_ONLINE_WATCH_CAR;
    }

    @Override
    protected boolean isShowBar() {
        return false;
    }

    @Override
    public void onPause() {
        super.onPause();
        onFragmentHide();
    }

    @Override
    protected void initViews() {
        super.initViews();
        mUserStatusChangeManager = new UserStatusChangeManager(getContext(), new UserStatusChangeManager.SimpleUserStatusChangeReceiver() {
            @Override
            protected void onUpdateUser(User user) {
                reload();
            }
        });
        mUserStatusChangeManager.register();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (!isVisibleToUser) {
            onFragmentHide();
        }
    }

    /**
     * 当fragment不可见时调用
     */
    public void onFragmentHide() {
        noticeStopVideo();
    }

    /**
     * 通知H5该页面不可见
     */
    @JavascriptInterface
    public void noticeStopVideo() {
        if (getWebView() == null) {
            return;
        }
        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                getWebView().loadUrl("javascript: noticeStopVideo()");
            }
        });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mUserStatusChangeManager != null) {
            mUserStatusChangeManager.unregister();
        }
    }
}
