package com.cloudy.linglingbang.activity.fragment.store.home.model;

import com.cloudy.linglingbang.model.store.home.StoreLayoutElement;
import com.cloudy.linglingbang.model.wrapper.WrapperModelWithType;

import java.util.List;

/**
 * 为您推荐
 *
 * <AUTHOR>
 * @date 2/27/21
 */
public class ElementLifeRecommendWrapper extends WrapperModelWithType<List<StoreLayoutElement>> {
    private String title;

    public ElementLifeRecommendWrapper(List<StoreLayoutElement> original, int style) {
        super(original, style);
    }

    public String getTitle() {
        return title == null ? "" : title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
