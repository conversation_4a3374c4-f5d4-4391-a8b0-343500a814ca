package com.cloudy.linglingbang.activity.store.commodity.adapter

import android.content.Context
import android.view.View
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.store.commodity.adapter.holder.RecommendCommodityViewHolder
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder
import com.cloudy.linglingbang.model.SourceModel
import com.cloudy.linglingbang.model.store.commodity.CartInfo
import com.cloudy.linglingbang.model.store.home.StoreElementCommodity

/**
 * 推荐列表
 *
 * <AUTHOR>
 * @date 2022/11/14
 */
class RecommendCommodityAdapter(context: Context, data: List<Any>) :
    BaseRecyclerViewAdapter<Any>(context, data) {


    override fun createViewHolder(itemView: View?): BaseRecyclerViewHolder<Any> {
        val h = RecommendCommodityViewHolder(itemView)
        h.orderSourceType = SourceModel.POSITION_TYPE.SHOPPING_CAR_LIST_TYPE
        h.orderSource = SourceModel.POSITION_TYPE.SHOPPING_CAR_LIST_TYPE_VALUE
        h
        return   h
    }


    override fun getItemLayoutRes(viewType: Int): Int {
        return R.layout.item_commodity_recommond_commodity
    }
}