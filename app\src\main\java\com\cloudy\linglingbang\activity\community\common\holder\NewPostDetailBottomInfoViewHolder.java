package com.cloudy.linglingbang.activity.community.common.holder;

import android.view.View;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.widget.PraiseCountTextView;
import com.cloudy.linglingbang.model.postcard.PostCard;

/**
 * 仅用于帖子详情的底部信息
 */
public class NewPostDetailBottomInfoViewHolder extends BasePostChildViewHolder {
    private TextView mTvPostType;
    protected TextView mTvTime;

    private TextView mTvReadCount;

    private PraiseCountTextView mTvPraiseCount;

    public NewPostDetailBottomInfoViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        mTvPostType = itemView.findViewById(R.id.tv_post_type);
        mTvTime = itemView.findViewById(R.id.tv_time);

        mTvReadCount = itemView.findViewById(R.id.tv_read_count);

        mTvPraiseCount = itemView.findViewById(R.id.tv_praise_count);

    }

    @Override
    public void bindTo(PostCard postCard) {
        super.bindTo(postCard);
        if (needShowPostType()) {
            showPostType(postCard);
        }

        //时间
        if (mTvTime != null) {
            Long creationDate = mPostCard.getCreationDate();
            if (creationDate != null && creationDate != 0) {
                mTvTime.setText(AppUtil.checkMessTimeNew(creationDate));
            } else {
                mTvTime.setText("");
            }
        }

        if (mTvReadCount != null) {
            //围观数
            mTvReadCount.setText(mTvReadCount.getContext().getString(R.string.item_post_detail_bottom_info_tv_read, AppUtil.getCommentDesc(mPostCard.getPostShowCount())));
        }

        //点赞数
        if (mTvPraiseCount != null) {
            mTvPraiseCount.setText(mTvPraiseCount.getContext().getString(R.string.item_post_detail_bottom_info_tv_praise, AppUtil.getCommentDesc(mPostCard.getPostPraiseCount())));
        }

    }

    /**
     * 是否显示帖子类型
     * 默认展示，详情不展示，回帖不展示
     */
    protected boolean needShowPostType() {
        return true;
    }

    private void showPostType(PostCard postCard) {
        if (mTvPostType == null) {
            return;
        }
        //帖子类型，暂时只展示图文和话题
        int typeStringResId;
        switch (postCard.getPostTypeIdOrNegative()) {
            case PostCard.PostType.IMAGE_TEXT:
                typeStringResId = R.string.post_type_image_text;
                break;
            case PostCard.PostType.TOPIC:
                typeStringResId = R.string.post_type_topic;
                break;
            case PostCard.PostType.SHORT_VIDEO:
            case PostCard.PostType.VIDEO_POST:
                typeStringResId = R.string.post_type_video;
                break;
            case PostCard.PostType.VOTE:
                typeStringResId = R.string.post_type_vote;
                break;
            case PostCard.PostType.QUESTIONNAIRE:
                typeStringResId = R.string.post_type_questionnaire;
                break;
            default:
                typeStringResId = 0;
                break;
        }
        if (typeStringResId > 0) {
            mTvPostType.setVisibility(View.VISIBLE);
            mTvPostType.setText(typeStringResId);
        } else {
            mTvPostType.setVisibility(View.GONE);
        }
    }

}
