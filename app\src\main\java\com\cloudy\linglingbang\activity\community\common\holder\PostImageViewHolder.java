package com.cloudy.linglingbang.activity.community.common.holder;

import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.ScanImageActivity;
import com.cloudy.linglingbang.app.imageConfig.RoundedCornersTransformation;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.DeviceUtil;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.model.postcard.PostCard;

import androidx.annotation.DrawableRes;

/**
 * 图片
 *
 * <AUTHOR>
 * @date 2018/6/25
 */
public class PostImageViewHolder extends BasePostChildViewHolder {
    private LinearLayout mLlImageList;
    private ImageView mIvImage1;
    private ImageView mIvImage2;
    private ImageView mIvImage3;

    public PostImageViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        mLlImageList = itemView.findViewById(R.id.ll_image_list);
        mIvImage1 = itemView.findViewById(R.id.iv_image_1);
        mIvImage2 = itemView.findViewById(R.id.iv_image_2);
        mIvImage3 = itemView.findViewById(R.id.iv_image_3);

        View.OnClickListener onClickImageListener = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onClickImage(v);
            }
        };
        mIvImage1.setOnClickListener(onClickImageListener);
        mIvImage2.setOnClickListener(onClickImageListener);
        mIvImage3.setOnClickListener(onClickImageListener);
    }

    @Override
    public void bindTo(PostCard postCard) {
        super.bindTo(postCard);
        String[] images = postCard.getImages();
        if (images == null || images.length == 0) {
            mLlImageList.setVisibility(View.GONE);
        } else {
            mLlImageList.setVisibility(View.VISIBLE);
            int size = images.length > 3 ? 3 : images.length;
            calculateLayoutParams(mLlImageList.getContext(), size);
            loadImages(mLlImageList.getContext(), size, images);
        }
    }

    /**
     * 点击图片
     */
    private void onClickImage(View v) {
        //觉得不安全
//        int index=v.getId()-R.id.iv_image_1;
        int index = -1;
        switch (v.getId()) {
            case R.id.iv_image_1:
                index = 0;
                break;
            case R.id.iv_image_2:
                index = 1;
                break;
            case R.id.iv_image_3:
                index = 2;
                break;
            default:
                break;
        }
        if (index != -1) {
            if (mPostCard != null) {
                String[] images = mPostCard.getImages();
                if (images != null && images.length > 0) {
                    goToImageDetail(v.getContext(), images, index);
                }
            }
        }
    }

    /**
     * 图片详情
     */
    public void goToImageDetail(Context context, String[] urls, int index) {
        if (context == null) {
            return;
        }
        Intent intent = new Intent(context, ScanImageActivity.class);
        intent.putExtra("image_urls", urls);
        intent.putExtra("image_index", index);
        context.startActivity(intent);
    }

    /**
     * 计算图片布局参数
     * <p>
     * ①获取屏幕宽度
     * ②减去左右间距
     * 使用 padding 要求整个布局 march_parent 同时只使用 padding
     * ③减去图片中间间距
     * 最后给图片 2、3 添加 marginLeft
     * ④计算宽高
     */
    private void calculateLayoutParams(Context context, int size) {
        float width = DeviceUtil.getScreenWidth() - mLlImageList.getPaddingLeft() - mLlImageList.getPaddingRight();
        float margin;
        float imageWidth;
        float imageHeight;
        if (size == 1 || size == 2) {
            margin = context.getResources().getDimensionPixelSize(R.dimen.normal_20);
            //减去 1 个间隔
            float contentWidth = width - margin;
            imageWidth = contentWidth / 2;
            imageHeight = imageWidth * 225 / 335;
        } else {
            margin = context.getResources().getDimensionPixelSize(R.dimen.normal_15);
            //减去 2 个间隔
            float contentWidth = width - margin * 2;
            imageWidth = contentWidth / 3;
            imageHeight = imageWidth * 165 / 220;
        }
        //第一个不设置，后面的设置 marginLeft
        setImageLayoutParams(mIvImage1, imageWidth, imageHeight, 0);
        setImageLayoutParams(mIvImage2, imageWidth, imageHeight, margin);
        setImageLayoutParams(mIvImage3, imageWidth, imageHeight, margin);

        //设置可见性

        if (size == 1) {
            mIvImage1.setVisibility(View.VISIBLE);
            mIvImage2.setVisibility(View.GONE);
            mIvImage3.setVisibility(View.GONE);
        } else if (size == 2) {
            mIvImage1.setVisibility(View.VISIBLE);
            mIvImage2.setVisibility(View.VISIBLE);
            mIvImage3.setVisibility(View.GONE);
        } else {
            mIvImage1.setVisibility(View.VISIBLE);
            mIvImage2.setVisibility(View.VISIBLE);
            mIvImage3.setVisibility(View.VISIBLE);
        }
    }

    private void setImageLayoutParams(ImageView imageView, float imageWidth, float imageHeight, float margin) {
        ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) imageView.getLayoutParams();
        layoutParams.width = (int) imageWidth;
        layoutParams.height = (int) imageHeight;
        layoutParams.leftMargin = (int) margin;
        imageView.setLayoutParams(layoutParams);
    }

    /**
     * 加载图片
     */
    private void loadImages(Context context, int size, String[] images) {
        //IllegalArgumentException: You cannot start a load for a destroyed activity
        if (context == null) {
            return;
        }
        //加载
        String imageSize = size == 3 ? AppUtil._220X165 : AppUtil._330X200;
        int placeHolderResId = R.drawable.ic_common_place_holder_corner_10;
        //switch 控制，大于 size 不加载
        int cornerRadius = 10;
        switch (size) {
            case 3:
                loadImage(mIvImage3, AppUtil.getImageUrlBySize(images[2], imageSize), placeHolderResId, cornerRadius);
//                Glide.with(context).load(AppUtil.getImageUrlBySize(images[2], imageSize))
//                        .transform(new CenterCrop(context), new RoundedCornersTransformation(context, cornerRadius))
//                        .dontAnimate()
//                        .placeholder(placeHolderResId)
//                        .into(mIvImage3);

            case 2:
                loadImage(mIvImage2, AppUtil.getImageUrlBySize(images[1], imageSize), placeHolderResId, cornerRadius);
//                Glide.with(context).load(AppUtil.getImageUrlBySize(images[1], imageSize))
//                        .transform(new CenterCrop(context), new RoundedCornersTransformation(context, cornerRadius))
//                        .dontAnimate()
//                        .placeholder(placeHolderResId)
//                        .into(mIvImage2);
            case 1:
                loadImage(mIvImage1, AppUtil.getImageUrlBySize(images[0], imageSize), placeHolderResId, cornerRadius);
//                Glide.with(context).load(AppUtil.getImageUrlBySize(images[0], imageSize))
//                        .transform(new CenterCrop(context), new RoundedCornersTransformation(context, cornerRadius))
//                        .dontAnimate()
//                        .placeholder(placeHolderResId)
//                        .into(mIvImage1);
                break;
            default:
                break;
        }
    }

    private void loadImage(ImageView iv, String url, @DrawableRes int placeHolderResId, int cornerRadius) {
        new ImageLoad(iv, url)
                .setPlaceholder(placeHolderResId)
                .setTransformations(new CenterCrop(), new RoundedCornersTransformation(cornerRadius))
                .setDoNotAnimate()
                .load();
    }

}
