package com.cloudy.linglingbang.activity.fragment.store.home;

import android.os.Bundle;

import com.aspsine.swipetoloadlayout.OnRefreshListener;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.model.entrance.FunctionEntrance;
import com.cloudy.linglingbang.model.entrance.FunctionEntranceEnum;
import com.cloudy.linglingbang.model.entrance.HomeFunctionResult;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BackgroundSubscriber;
import com.cloudy.linglingbang.model.server.Ad.Ad2;
import com.cloudy.linglingbang.model.store.ShopCarTypeInfo;
import com.cloudy.linglingbang.model.wrapper.CommonModel;
import com.cloudy.linglingbang.model.wrapper.FunctionResultWrapper;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import io.reactivex.rxjava3.functions.Function;

/**
 * 整车
 *
 * <AUTHOR>
 * @date 2018/10/23
 */
public class StoreHomeCarFragment extends StoreHomeTabFragment {
    private FunctionResultWrapper mFunctionResultWrapper;
    private List<Object> mCarList;

    public static Fragment newInstance(String pageCode) {
        return IntentUtils.setFragmentArgument(new StoreHomeCarFragment(), pageCode);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh() {
                getFunctionEntrance();
                getCarList();
            }
        });
    }

    /**
     * 获取整车列表
     */
    private void getCarList() {
        L00bangRequestManager2.getServiceInstance()
                .getShopCarTypeList(-1L)
                .map(new Function<BaseResponse<List<ShopCarTypeInfo>>, BaseResponse<List<Object>>>() {
                    @Override
                    public BaseResponse<List<Object>> apply(BaseResponse<List<ShopCarTypeInfo>> listBaseResponse) {
                        List<Object> newData = new ArrayList<>();
                        List<ShopCarTypeInfo> data = listBaseResponse.getData();
                        if (data != null) {
                            newData.addAll(data);
                        }
                        return listBaseResponse.cloneWithData(newData);
                    }
                })
                .compose(L00bangRequestManager2.<List<Object>>setSchedulers())
                .subscribe(new BackgroundSubscriber<List<Object>>(getContext()) {
                    @Override
                    public void onSuccess(List<Object> objects) {
                        super.onSuccess(objects);
                        mCarList = objects;
                        onUpdateData();
                    }
                });
    }

    /**
     * 获取商城的功能入口
     */
    private void getFunctionEntrance() {
        int functionCategoryType = HomeFunctionResult.CATEGORY_SERVICE_STORE_HOME_CAR;
        //加载缓存
        L00bangRequestManager2
                .createUseCacheService(true)
                .getHomeFunctionList(functionCategoryType)
                .compose(L00bangRequestManager2.<HomeFunctionResult>setSchedulers(true))
                .subscribe(new BackgroundSubscriber<HomeFunctionResult>(getActivity()) {
                    @Override
                    public void onSuccess(HomeFunctionResult homeFunctionResult) {
                        super.onSuccess(homeFunctionResult);
                        mFunctionResultWrapper = new FunctionResultWrapper(homeFunctionResult);
                        onUpdateData();
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                    }
                });
        //联网加载
        L00bangRequestManager2
                .getServiceInstance()
                .getHomeFunctionList(functionCategoryType)
                .compose(L00bangRequestManager2.<HomeFunctionResult>setSchedulers())
                .subscribe(new BackgroundSubscriber<HomeFunctionResult>(getActivity()) {
                    @Override
                    public void onSuccess(HomeFunctionResult homeFunctionResult) {
                        super.onSuccess(homeFunctionResult);
                        mFunctionResultWrapper = new FunctionResultWrapper(homeFunctionResult);
                        onUpdateData();
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                    }
                });
    }

    private FunctionResultWrapper getOrCreateFunctionResultWrapper() {
        HomeFunctionResult homeFunctionResult = null;
        if (mFunctionResultWrapper != null) {
            homeFunctionResult = mFunctionResultWrapper.getOriginal();
        }
        //如果未赋值，或者取出来为 null，都重新赋值
        if (homeFunctionResult == null) {
            homeFunctionResult = new HomeFunctionResult();
            mFunctionResultWrapper = new FunctionResultWrapper(homeFunctionResult);
        }
        if (homeFunctionResult.getFunctionEntranceList() == null || homeFunctionResult.getFunctionEntranceList().size() == 0 || homeFunctionResult.getColumns() <= 0) {
            int columns = 4;
            List<FunctionEntrance> data = new ArrayList<>();
            //商城整车-默认功能图标
            //车型对比
            data.add(FunctionEntrance.create(getContext(), FunctionEntranceEnum.VEHICLE_COMPARE));
            //预约试驾
            data.add(FunctionEntrance.create(getContext(), FunctionEntranceEnum.APPOINTMENT_DRIVER));
            //订单查询
            data.add(FunctionEntrance.create(getContext(), FunctionEntranceEnum.ORDER_LIST, 0, R.drawable.ic_function_entrance_order_list_store_home));
            //客服MM
            data.add(FunctionEntrance.create(getContext(), FunctionEntranceEnum.SERVICE_MM, R.string.store_home_service_mm, R.drawable.ic_function_entrance_store_home_service_mm));
            homeFunctionResult.setColumns(columns);
            homeFunctionResult.setFunctionEntranceList(data);
        }
        return mFunctionResultWrapper;
    }

    @Override
    protected List<Object> buildData() {
        List<Object> data = new ArrayList<>();
        //广告
        if (mAdListWrapper != null) {
            List<Ad2> adList = mAdListWrapper.getOriginal();
            if (adList != null && adList.size() > 0) {
                data.add(mAdListWrapper);
            }
        }

        //功能图标
        data.add(getOrCreateFunctionResultWrapper());
        data.add(new CommonModel.Divider20());

        //组件元素
        if (mElementList != null) {
            data.addAll(mElementList);
            data.add(new CommonModel.Divider20());
        }

        //整车
        if (mCarList != null && mCarList.size() > 0) {
            data.add(new CommonModel.Transparent());
            data.addAll(mCarList);
        }
        return data;
    }
}
