package com.cloudy.linglingbang.activity.car.home;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.welfare.CarInfoEvent;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.LocationHelper;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.util.ViewHolderUtils;
import com.cloudy.linglingbang.app.widget.AutoVerticalScrollTextView;
import com.cloudy.linglingbang.app.widget.BaseOnClickListener;
import com.cloudy.linglingbang.app.widget.banner.AdImageView;
import com.cloudy.linglingbang.app.widget.dialog.ChooseCityDialog;
import com.cloudy.linglingbang.model.CityModel;
import com.cloudy.linglingbang.model.ProvinceModel;
import com.cloudy.linglingbang.model.Weather;
import com.cloudy.linglingbang.model.car.home.OilPrice;
import com.cloudy.linglingbang.model.entrance.FunctionEntrance;
import com.cloudy.linglingbang.model.entrance.FunctionEntranceEnum;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BackgroundSubscriber;
import com.cloudy.linglingbang.model.user.User;

import java.util.ArrayList;
import java.util.List;

/**
 * 油价加载
 * <p>
 * 刷新时会刷新油价，获取并赋值 mCityId 和 mCityName
 * <p>
 * 4s 店活动刷新时，再获取 mCityId 和 mCityName 保证两者一致
 * <p>
 * 首次进入，由 onRefresh 调用 refreshOilPrice
 * 刷新天气
 * 如果已定位，取定位的城市，
 * 如果定位城市为空，取用户城市
 * 否则取柳州
 * <p>
 * 刷新 4s 店活动时
 * 从 WeatherLoader 取 cityId（定位的，或是用户的），取 cityName（定位的或是默认的）
 * <p>
 * 刷新 4s 店活动时，调用 refreshOilPrice，对 cityId cityName 重新赋值，再刷新
 * 选择城市时，更新油价，直接更新 4s 店活动
 * 重新登录时，如果是默认城市，则 cityId 为0，重新赋值取到用户信息
 * 退出登录时，如果已经选择过城市，则保留
 *
 * <AUTHOR>
 * @date 2018/8/21
 */
public class OilPriceLoader {

    private TextView mTvLocation;
    private AdImageView mIvWeather, mIvCustomer;
    private ChooseCityDialog.ChooseCityUtil mChooseCityUtil;
    private Long mCityId;
    private String mCityName;
    private FunctionEntrance functionEntrance;
    private List<OilPrice> mOilPriceList = new ArrayList<>();
    private Weather mWeather;
    private AutoVerticalScrollTextView mTvOilScrollPrice;

    private boolean mNeedRefresh;

    public OilPriceLoader(View rootView) {
        initView(rootView);
    }

    private void initView(final View rootView) {
        mTvLocation = rootView.findViewById(R.id.tv_location);
        mIvWeather = rootView.findViewById(R.id.iv_weather);
//        mIvCustomer = rootView.findViewById(R.id.iv_customer);
        mTvOilScrollPrice = rootView.findViewById(R.id.tv_oil_scroll_price);
        functionEntrance = FunctionEntrance.create(rootView.getContext(), FunctionEntranceEnum.SERVICE_MM);
        ViewHolderUtils.setOnClickListener(new BaseOnClickListener(false) {
            @Override
            public void onClick(Context context) {
                super.onClick(context);
                onClickTvLocation(context);
            }
        }, mTvLocation);
        ViewHolderUtils.setOnClickListener(new BaseOnClickListener(true) {
            @Override
            public void onClick(Context context) {
                super.onClick(context);
                if (functionEntrance != null) {
                    JumpPageUtil.goHomeFunctionEntrance(rootView.getContext(), functionEntrance, "服务",new CarInfoEvent(""));
                }
            }
        }, mIvCustomer); //点击客服
    }

    public void refreshOil(Context context) {
        if (mCityId == null || mCityId == 0) {
            CityModel cityModel = getCurrentCity();
            mCityId = cityModel.getCityId();
            mCityName = cityModel.getCityName();
        }
        updateCityDirectly(context, mCityName);
    }

    public static CityModel getCurrentCity() {
        //没有 cityId 的时候，需要获取，如果选择了地址，则不再变化（如用户选择城市，然后退出登录）
        LocationHelper.LocationEntity lastLocation = LocationHelper.getInstance().getLastLocation();
        Long cityId = null;
        String cityName = "";
        if (lastLocation != null) {
            //有定位（不取缓存的cityId）
            cityId = null;
//            cityId = lastLocation.serverCityId;
            cityName = lastLocation.cityName;
        }
        if (TextUtils.isEmpty(cityName)) {
            //如果城市名为空，则取用户信息，如果不为空，则不管 cityId 是否为空，都可用
            User user = User.getsUserInstance();
            if (UserUtils.hasLogin(user)) {
                //已登录
                long userCityId = user.getCityId();
                String userCityStr = user.getCityStr();
                if (userCityId > 0 && !TextUtils.isEmpty(userCityStr)) {
                    //不为空，赋值
                    cityId = userCityId;
                    cityName = userCityStr;
                }
            }
        }
        if (TextUtils.isEmpty(cityName)) {
            //城市名仍为空，取默认
            cityId = null;
            cityName = LocationHelper.DEFAULT_CITY_LIUZHOU;
        }
        //初始化的时候，直接加载天气，不回调
        //选择城市的时候，回调城市
        return new CityModel(cityId, cityName);
    }

    /**
     * 选择位置
     */
    private void onClickTvLocation(final Context context) {
        if (mChooseCityUtil == null) {
            mChooseCityUtil = new ChooseCityDialog.ChooseCityUtil(context, new ChooseCityDialog.OnChooseCityListener() {
                @Override
                public boolean onChoseCity(ProvinceModel chosenProvinceModel, CityModel chosenCityModel) {
                    onUpdateCity(context, chosenCityModel.getCityId(), chosenCityModel.getCityName());
                    return false;
                }
            });
        }
        mChooseCityUtil.showDialog();
    }

    /**
     * 更新城市
     */
    protected void onUpdateCity(Context context, long cityId, String cityName) {
        mCityId = cityId;
        mCityName = cityName;
        updateCityDirectly(context, cityName);
    }

    public long getCityId() {
        return mCityId;
    }

    public String getCityName() {
        return mCityName;
    }

    /**
     * 直接更新，如果要回调，使用 {@link #onUpdateCity(Context, long, String)}
     */
    private void updateCityDirectly(Context context, String cityName) {
        if (TextUtils.isEmpty(cityName)) {
            cityName = LocationHelper.DEFAULT_CITY;
        }
        ViewHolderUtils.setText(cityName, mTvLocation);
        //重新获取天气
        loadWeather(context, cityName);
    }

    /**
     * 加载天气
     */
    private void loadWeather(Context context, String cityName) {
        L00bangRequestManager2.getServiceInstance()
                .getOilPrice(mCityId, cityName)
                .compose(L00bangRequestManager2.<List<OilPrice>>setSchedulers())
                .subscribe(new BackgroundSubscriber<List<OilPrice>>(context) {
                    @Override
                    public void onSuccess(List<OilPrice> oilPrices) {
                        super.onSuccess(oilPrices);
                        if (oilPrices != null) {
                            mOilPriceList.clear();
                            mOilPriceList.addAll(oilPrices);
                        }
                        onUpdateOilPrice();
                    }
                });
    }

    /**
     * 更新油价
     */
    public void onUpdateOilPrice() {
        //如果需要刷新，则重新刷新油价信息
        if (mNeedRefresh) {
            mNeedRefresh = false;
            refreshOil(mTvOilScrollPrice.getContext());
        } else {
            if (mOilPriceList != null && mOilPriceList.size() > 0) {
                List<String> list = new ArrayList<>();
                for (OilPrice oilPrice : mOilPriceList) {
                    list.add(oilPrice.getDesc());
                }
                mTvOilScrollPrice.setNotices(list);
                mTvOilScrollPrice.fillInDate();
            } else {
                mIvWeather.setVisibility(View.GONE);
            }
        }
    }

    public void startScroll() {
        if (mTvOilScrollPrice != null) {
            mIvWeather.postDelayed(new Runnable() {
                @Override
                public void run() {
                    mTvOilScrollPrice.start();
                }
            }, 1000);

        }
    }

    public void stopScroll() {
        if (mTvOilScrollPrice != null) {
            mTvOilScrollPrice.stop();
        }
    }

    /**
     * 重新设置城市，设置标志位，下次调用update的时候如果需要刷新，则重新刷新
     */
    public void resetCity() {
        mCityId = null;
        mNeedRefresh = true;

    }

}
