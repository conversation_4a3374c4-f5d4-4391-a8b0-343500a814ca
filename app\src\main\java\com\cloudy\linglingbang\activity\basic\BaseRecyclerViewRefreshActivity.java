package com.cloudy.linglingbang.activity.basic;

import android.content.Context;
import android.os.Bundle;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;

import java.util.List;

/**
 * 刷新的activity
 * 继承{@linkplain IRefreshContext}，调用{@linkplain RefreshController#initViews(View)}
 * <br/>子类实现{@linkplain IRefreshContext#createAdapter(List)}
 * 和{@linkplain IRefreshContext#getListDataFormNet(L00bangService2, int, int)}
 * <br/>要修改{@linkplain RefreshController}，可以重写{@linkplain #createRefreshController()}
 * <br/>添加常用的{@linkplain #getRefreshController()},{@linkplain #getPageSize()},{@linkplain #getData()}
 *
 * <AUTHOR> create at 2016/10/13 12:18
 */
public abstract class BaseRecyclerViewRefreshActivity<T> extends BaseActivity implements IRefreshContext<T> {
    private RefreshController<T> mRefreshController;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_base_refresh);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //在父类调用initialize之后，为了方便在initialize中获取接口所需的参数
        // 否则如果super.initialize()在前，可以会没获取到参数
        mRefreshController = createRefreshController();
        View rootView = findViewById(R.id.ll_root);
        mRefreshController.initViews(rootView);
    }

    @Override
    protected void initialize() {

    }

    @Override
    public Context getContext() {
        return this;
    }

    @Override
    public RefreshController<T> createRefreshController() {
        return new RefreshController<>(this);
    }

    @Override
    public RefreshController<T> getRefreshController() {
        return mRefreshController;
    }

    public int getPageSize() {
        return getRefreshController().getPageSize();
    }

    public List<T> getData() {
        return getRefreshController().getData();
    }
}
