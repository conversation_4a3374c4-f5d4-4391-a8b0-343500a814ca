package com.cloudy.linglingbang.activity.fragment.store.service;

import android.content.Context;
import android.os.Bundle;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.IRefreshContext;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.fragment.store.home.StoreHomeRefreshController;
import com.cloudy.linglingbang.activity.fragment.store.home.StoreHomeTabFragment;
import com.cloudy.linglingbang.activity.fragment.store.home.adapter.StoreHomeAdapter;
import com.cloudy.linglingbang.activity.fragment.store.home.adapter.StoreHomeCommodity_1_2_ViewHolder;
import com.cloudy.linglingbang.activity.fragment.store.home.model.StoreHomeElementWrapper;
import com.cloudy.linglingbang.activity.fragment.store.youpin.ElementUtils;
import com.cloudy.linglingbang.activity.store.commodity.CommodityListPageCode;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.util.timer.CountDownManager;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.constants.FinalSensors;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.server.Ad.Ad2;
import com.cloudy.linglingbang.model.store.home.StoreElementCommodity;
import com.cloudy.linglingbang.model.store.home.StoreLayoutElement;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 官方专区
 *
 * <AUTHOR>
 * @date 2019-11-13
 */
public class ServiceStoreOfficialFragment extends StoreHomeTabFragment {

    /**
     * 埋点信息，传给 adapter
     */
    private SensorsUtils.StoreHomeAnchor mStoreHomeAnchor;

    @Override
    protected void initViews() {
        super.initViews();
        Bundle bundle = getArguments();
        if (bundle != null) {
            mStoreHomeAnchor = (SensorsUtils.StoreHomeAnchor) bundle.getSerializable(IntentUtils.INTENT_EXTRA_FROM);
        }
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<Object> list) {
        return new StoreCommodityAdapter(getActivity(), list)
                .setCountDownTag(this)
                .setStoreHomeAnchor(mStoreHomeAnchor);
    }

    @Override
    public Observable<BaseResponse<List<Object>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return null;
    }

    /**
     * 继承，使用 LayoutManager 和 ItemDecoration
     */
    private static class StoreCommodityListRefreshController extends StoreHomeRefreshController {
        Map<String,Object> map;
        public StoreCommodityListRefreshController(IRefreshContext<Object> refreshContext) {
            super(refreshContext, null);
            map = new HashMap<>();
            map.put("pageCode", CommodityListPageCode.PAGE_CODE_OFFICIAL);
        }

        @Override
        protected Observable<BaseResponse<List<Object>>> getListDataFromNet(L00bangService2 service2, int pageNo, int pageSize) {
            map.put("pageNo", String.valueOf(pageNo));
            map.put("pageSize", String.valueOf(pageSize));
            return service2.findEcCommodityAppList(map)
                    .map(ElementUtils.getStoreElementToObjectFun(Ad2.LINK_TYPE.TYPE_NEW_COMMODITY_DETAIL, 0));
        }

        @Override
        protected boolean isLoadMoreEnable() {
            return true;
        }
    }

    @Override
    public RefreshController<Object> createRefreshController() {
        return new StoreCommodityListRefreshController(this);
    }

    @Override
    public void onResume() {
        super.onResume();
        CountDownManager.getInstance().onResume(this);
    }

    @Override
    public void onPause() {
        super.onPause();
        CountDownManager.getInstance().onPause(this);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        CountDownManager.getInstance().onDestroy(this);
    }

    /**
     * 使用倒计时等
     */
    private class StoreCommodityAdapter extends StoreHomeAdapter {
        public StoreCommodityAdapter(Context context, List data) {
            super(context, data);
        }

        @Override
        protected int getItemLayoutRes(int viewType) {
            return R.layout.item_store_home_commodity_1_2;
        }

        @Override
        protected BaseRecyclerViewHolder createViewHolderWithViewType(View itemView, int viewType) {
            StoreCommodityViewHolder holder = new StoreCommodityViewHolder(itemView);
            holder.setAdapter(this);
            holder.setCountDownTag(getCountDownTag());
            return holder;
        }

        @Override
        protected void onItemClick(Context context, Object o) {
            super.onItemClick(context, o);
            if (o instanceof StoreHomeElementWrapper) {
                StoreLayoutElement layoutElement = ((StoreHomeElementWrapper) o).getOriginal();
                if (layoutElement != null) {
                    StoreElementCommodity commodity = layoutElement.getProductVo();
                    if (commodity != null) {
//                        long productId = commodity.getProductIdOrZero();
//                        if (productId > 0) {
//                            JumpPageUtil.goEcologyCommodityDetail(context, productId, mStoreHomeAnchor.getPageCode(), new SourceModel(SourceModel.POSITION_TYPE.SERVICE_CHOOSE_ACCESSORIES_TYPE, SourceModel.POSITION_TYPE.SERVICE_CHOOSE_ACCESSORIES_VALUE));
//                        }
                        String commodityCategoryStr = commodity.getCommodityCategoryStr();
                        String componentName = commodity.getProductName();

                        SensorsUtils.sensorsClickComponent("点击(" + commodityCategoryStr + ")-(" + componentName + ")", componentName, mStoreHomeAnchor);
                    }
                }
            }
        }
    }

    /**
     * 单独处理图片加载
     */
    private class StoreCommodityViewHolder extends StoreHomeCommodity_1_2_ViewHolder {
        public StoreCommodityViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected String getImageUrl(StoreLayoutElement element) {
            if (element != null) {
                StoreElementCommodity commodity = element.getProductVo();
                if (commodity != null) {
                    return commodity.getProductMainImage();
                }
            }
            return null;
        }

        @Override
        protected boolean needShowDealerLogo(StoreElementCommodity commodity) {
            //判断展示经销商图标
            return commodity != null && commodity.isDealerAscription();
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        SensorsUtils.sensorsViewStart(FinalSensors.BROWSE_LIFE_INFORMATION);
    }

    @Override
    public void onStop() {
        super.onStop();
        SensorsUtils.sensorsViewEndNew("官方专区", FinalSensors.BROWSE_LIFE_INFORMATION, "浏览官方专区");
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (hidden) {
            SensorsUtils.sensorsViewEndNew("官方专区", FinalSensors.BROWSE_LIFE_INFORMATION, "浏览官方专区");
        } else {
            SensorsUtils.sensorsViewStart(FinalSensors.BROWSE_LIFE_INFORMATION);
        }
    }
}
