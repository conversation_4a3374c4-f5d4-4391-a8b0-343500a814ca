package com.cloudy.linglingbang.activity.shortvideo;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.media.MediaMetadataRetriever;
import android.os.Environment;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;

import com.alibaba.sdk.android.vod.upload.VODSVideoUploadCallback;
import com.alibaba.sdk.android.vod.upload.VODSVideoUploadClient;
import com.alibaba.sdk.android.vod.upload.common.utils.StringUtil;
import com.alibaba.sdk.android.vod.upload.model.SvideoInfo;
import com.alibaba.sdk.android.vod.upload.session.VodHttpClientConfig;
import com.alibaba.sdk.android.vod.upload.session.VodSessionCreateInfo;
import com.aliyun.common.utils.StorageUtils;
import com.aliyun.svideo.crop.AliyunVideoCropActivity;
import com.aliyun.svideo.crop.CropMediaActivity;
import com.aliyun.svideo.crop.bean.AlivcCropOutputParam;
import com.aliyun.svideo.recorder.activity.AlivcSvideoRecordActivity;
import com.aliyun.svideo.recorder.bean.AlivcRecordInputParam;
import com.aliyun.svideosdk.common.struct.common.AliyunSnapVideoParam;
import com.aliyun.svideosdk.common.struct.common.VideoDisplayMode;
import com.aliyun.svideosdk.common.struct.common.VideoQuality;
import com.aliyun.svideosdk.common.struct.recorder.CameraType;
import com.aliyun.svideosdk.common.struct.recorder.FlashType;
import com.cloudy.linglingbang.ApplicationLLB;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.app.log.LogUtils;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.FileUtil;
import com.cloudy.linglingbang.app.util.PermissionUtils;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.file.FileUtils;
import com.cloudy.linglingbang.constants.ConfigConstant;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BackgroundSubscriber;
import com.cloudy.linglingbang.model.shortvideo.AliSTSTokenInfo;

import java.io.File;
import java.io.FilenameFilter;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * 短视频工具类
 *
 * <AUTHOR>
 * @date 2018/1/3
 */

public class ShortVideoUtil {

    /**
     * 录制视频权限requestCode
     */
    public static final int PERMISSION_CODE_RECORD_VIDEO = 9601;
    /**
     * 裁剪视频权限requestCode
     */
    public static final int PERMISSION_CODE_CROP_VIDEO = 9602;
    /**
     * 录制视频请求码
     */
    public static final int REQUEST_FOR_RESULT_RECORD_VIDEO = 6601;
    /**
     * 裁剪视频请求码
     */
    public static final int REQUEST_FOR_RESULT_CROP_VIDEO = 6602;

    @Retention(RetentionPolicy.SOURCE)
    @IntDef({PERMISSION_CODE_RECORD_VIDEO, PERMISSION_CODE_CROP_VIDEO})
    private @interface VideoPermission {
    }

    /**
     * 请求视频相关权限
     *
     * @param activity              BaseActivity
     * @param permissionRequestCode 权限请求code
     */
    public static void requestVideoPermissions(@NonNull BaseActivity activity, @VideoPermission int permissionRequestCode) {
        if (AppUtil.checkLogin(activity)) {
            //申请相机、录音、文件读写权限
            PermissionUtils.checkStoragePermissions(null,
                    activity,
                    permissionRequestCode,
                    activity.getResources().getString(R.string.permission_picture_pre),
                    "该功能需要相关权限，否则无法正常使用，是否打开设置？"/*,
                    Manifest.permission.CAMERA,
                    Manifest.permission.RECORD_AUDIO*/);
        }
    }

    /**
     * 录制视频
     *
     * @param activity activity
     */
    public static void recordVideo(@NonNull Activity activity) {
        AlivcRecordInputParam alivcRecordInputParam = new AlivcRecordInputParam.Builder()
                //设置最大录制时长 单位毫秒
                .setMaxDuration(10 * 1000)
                //设置最小录制时长 单位毫秒
                .setMinDuration(2 * 1000)
                //设置关键帧间隔(1~300)
                .setGop(5)
                .setFrame(25)
                //设置视频质量
                .setVideoQuality(VideoQuality.HD)
                //设置视频比例，目前支持1:1,3:4,9:16
                .setRatioMode(AliyunSnapVideoParam.RATIO_MODE_9_16)
                //设置录制分辨率，目前支持360p，480p，540p，720p
                .setResolutionMode(AliyunSnapVideoParam.RESOLUTION_540P)
                .setSvideoRace(true)
                .build();
        AlivcSvideoRecordActivity.startRecordForResult(activity, alivcRecordInputParam, REQUEST_FOR_RESULT_RECORD_VIDEO);

//        AliyunSnapVideoParam recordParam = new AliyunSnapVideoParam.Builder()
//                //设置录制分辨率，目前支持360p，480p，540p，720p
//                .setResolutionMode(AliyunSnapVideoParam.RESOLUTION_540P)
//                //设置视频比例，目前支持1:1,3:4,9:16
//                .setRatioMode(AliyunSnapVideoParam.RATIO_MODE_9_16)
//                //设置录制模式，目前支持按录，点录和混合模式
//                .setRecordMode(AliyunSnapVideoParam.RECORD_MODE_AUTO)
//                //设置滤镜地址列表,具体滤镜接口接收的是一个滤镜数组
//                .setFilterList(unZipFilters(activity))
//                //设置美颜度
//                .setBeautyLevel(80)
//                //设置美颜开关
//                .setBeautyStatus(true)
//                //设置前后置摄像头
//                .setCameraType(CameraType.BACK)
//                // 设置闪光灯模式
//                .setFlashType(FlashType.AUTO)
//                //设置是否需要支持片段录制
//                .setNeedClip(true)
//                //设置最大录制时长 单位毫秒
//                .setMaxDuration(10 * 1000)
//                //设置最小录制时长 单位毫秒
//                .setMinDuration(2 * 1000)
//                //设置视频质量
//                .setVideoQuality(VideoQuality.HD)
//                //设置关键帧间隔(1~300)
//                .setGop(5)
//                //裁剪参数
//                .setMinVideoDuration(2 * 1000)
//                .setMaxVideoDuration(10 * 1000)
//                .setMinCropDuration(2 * 1000)
//                .setFrameRate(25)
////                .setCropMode(ScaleMode.PS)
//                .setCropMode(VideoDisplayMode.SCALE)
//                //设置导入相册过滤选择视频
//                .setSortMode(AliyunSnapVideoParam.SORT_MODE_VIDEO)
//                .build();
//        AliyunVideoRecorder.startRecordForResult(activity, REQUEST_FOR_RESULT_RECORD_VIDEO, recordParam);
    }

    /**
     * 裁剪视频
     *
     * @param activity activity
     */
    public static void cropVideo(@NonNull Activity activity) {
        AliyunSnapVideoParam mCropParam = new AliyunSnapVideoParam.Builder()
                //设置帧率(20~30)
                .setFrameRate(25)
                //设置关键帧间隔
                .setGop(5)
                //设置裁剪模式，目前支持有黑边和无黑边两种
//                .setCropMode(AliyunVideoCrop.SCALE_CROP)
                .setCropMode(VideoDisplayMode.SCALE)
                //设置裁剪质量
                .setVideoQuality(VideoQuality.SSD)
                //设置分辨率，目前支持360p，480p，540p，720p
                .setResolutionMode(AliyunSnapVideoParam.RESOLUTION_720P)
                //设置裁剪比例 目前支持1:1,3:4,9:16
                .setRatioMode(AliyunSnapVideoParam.RATIO_MODE_9_16)
                //设置是否需要开放录制入口
                .setNeedRecord(false)
                //设置过滤的视频最小长度 单位毫秒
                .setMinVideoDuration(3 * 1000)
                //设置过滤的视频最大长度 单位毫秒
                .setMaxVideoDuration(2 * 60 * 1000)
                //设置视频最小裁剪时间 单位毫秒
                .setMinCropDuration(3 * 1000)
                //设置裁剪方式，是否使用gpu进行裁剪，不设置则默认使用cpu来裁剪
                .setCropUseGPU(true)
                //设置导入相册过滤选择视频
                .setSortMode(AliyunSnapVideoParam.SORT_MODE_VIDEO)
                //录制参数
                .setRecordMode(AliyunSnapVideoParam.RECORD_MODE_AUTO)
                .setFilterList(unZipFilters(activity))
                .setBeautyLevel(80)
                .setBeautyStatus(true)
                .setCameraType(CameraType.BACK)
                .setFlashType(FlashType.AUTO)
                .setMaxDuration(10 * 1000)
                .setMinDuration(3 * 1000)
                .setNeedClip(true)
                .build();
        AliyunVideoCropActivity.startCropForResult(activity, REQUEST_FOR_RESULT_CROP_VIDEO, mCropParam);

//        AliyunVideoCrop.startCropForResult(activity, REQUEST_FOR_RESULT_CROP_VIDEO, mCropParam);
    }

    /**
     * 录制或裁剪视频结果回调
     *
     * @param requestCode 请求码
     * @param resultCode  结果码
     * @param data        数据
     * @return 视频保存路径
     */
    public static String onActivityResult(int requestCode, int resultCode, Intent data) {
        //录制或裁剪的视频保存路径
        String videoPath = "";
        switch (requestCode) {
            case REQUEST_FOR_RESULT_RECORD_VIDEO:
                if (resultCode == Activity.RESULT_OK && data != null) {
                    videoPath = data.getStringExtra(AlivcSvideoRecordActivity.EXTRA_PATH);
                    LogUtils.d("裁剪文件路径为 " + videoPath + " 时长为 " + data.
                            getLongExtra(AlivcSvideoRecordActivity.EXTRA_DURATION, 0));
                }
                break;
            case REQUEST_FOR_RESULT_CROP_VIDEO:
                if (resultCode == Activity.RESULT_OK && data != null) {
                    int type = data.getIntExtra(CropMediaActivity.RESULT_TYPE, 0);
                    if (type == CropMediaActivity.RESULT_TYPE_CROP) {
                        AlivcCropOutputParam param = (AlivcCropOutputParam) data.getSerializableExtra(AlivcCropOutputParam.RESULT_KEY_OUTPUT_PARAM);
                        if (param != null) {
                            videoPath = param.getOutputPath();
                            LogUtils.d("裁剪文件路径为 " + videoPath + " 时长为 " + param.getDuration());
                        }

                    } else if (type == CropMediaActivity.RESULT_TYPE_RECORD) {
//                        videoPath = data.getStringExtra(AliyunVideoRecorder.OUTPUT_PATH);
//                        LogUtils.d("录制文件路径为 " + videoPath);
                    }
                }
                break;
            default:
                break;
        }
        return videoPath;
    }

    /**
     * 获取视频封面(取视频第一帧作为封面)
     *
     * @param videoPath 视频本地路径
     * @return 第一帧图像
     */
    public static Bitmap getVideoCover(String videoPath) {
        //取视频第一帧作为封面
        if (!FileUtil.isHaveFile(videoPath)) {
            LogUtils.d("视频文件不存在");
            return null;
        }
        //取帧
        MediaMetadataRetriever mmr = new MediaMetadataRetriever();
        mmr.setDataSource(videoPath);
        Bitmap firstFrame = mmr.getFrameAtTime();
        mmr.release();
        return firstFrame;
    }

    /**
     * 保存视频封面到本地
     *
     * @return 封面图片路径
     */
    public static String saveVideoCoverToFile(Bitmap firstFrame) {
        //短视频封面图片保存的路径
        String imgPath = "";
        if (firstFrame != null) {
            //封面图片名称
            String tmpFileName = "svideo_cover_" + System.currentTimeMillis() + ".png";
            //父级目录
            String dirPath = FileUtils.getAppCacheDir(ApplicationLLB.ct()).getAbsolutePath() + ConfigConstant.SD_TMP_DIRECTORY;
            imgPath = dirPath + tmpFileName;
            FileUtil.saveBitmapToFile(firstFrame, imgPath);//保存图片到临时文件夹
        } else {
            LogUtils.d("获取视频封面图片失败");
        }
        return imgPath;
    }

    /**
     * 获取短视频上传或播放凭证
     */
    public static void getAliSTSToken(@NonNull Context context, final STSTokenCallback tokenCallback) {
        //请求Server获取STS信息
        L00bangRequestManager2
                .getServiceInstance()
                .getAliAccessKey()
                .compose(L00bangRequestManager2.<AliSTSTokenInfo>setSchedulers())
                .subscribe(new BackgroundSubscriber<AliSTSTokenInfo>(context) {
                    @Override
                    public void onSuccess(AliSTSTokenInfo aliSTSTokenInfo) {
                        super.onSuccess(aliSTSTokenInfo);
                        if (tokenCallback != null) {
                            tokenCallback.onSuccess(aliSTSTokenInfo);
                        }
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                        if (tokenCallback != null) {
                            tokenCallback.onFailure(e);
                        }
                    }
                });
    }

    /**
     * 上传短视频
     */
    public static void uploadShortVideo(@NonNull final Context context,
                                        @NonNull final VODSVideoUploadClient vodsVideoUploadClient,
                                        String videoPath, final String coverImagePath, AliSTSTokenInfo aliSTSTokenInfo,
                                        final VideoUploadCallback uploadCallback) {
        if (aliSTSTokenInfo != null) {
            final String accessKeyId = aliSTSTokenInfo.getAccessKeyId();//子accessKeyId
            final String accessKeySecret = aliSTSTokenInfo.getAccessKeySecret();//子accessKeySecret
            final String securityToken = aliSTSTokenInfo.getSecurityToken();//STS 的securityToken
            final String expriedTime = aliSTSTokenInfo.getExpiration();//STS的过期时间
            String requestID = null;//传空或传递访问STS返回的requestID
            if (StringUtil.isEmpty(accessKeyId)) {
                ToastUtil.showMessage(context, "accessKeyId is null");
                return;
            } else if (StringUtil.isEmpty(accessKeySecret)) {
                ToastUtil.showMessage(context, "accessKeySecret is null");
                return;
            } else if (StringUtil.isEmpty(securityToken)) {
                ToastUtil.showMessage(context, "securityToken is null");
                return;
            } else if (StringUtil.isEmpty(expriedTime)) {
                ToastUtil.showMessage(context, "expriedTime is null");
                return;
            } else if (!(new File(videoPath)).exists()) {
                ToastUtil.showMessage(context, "videoPath file not exists");
                return;
            } else if (!(new File(coverImagePath)).exists()) {
                ToastUtil.showMessage(context, "coverImagePath file not exists");
                return;
            }
            //初始化VodsVideoUploadClient(应该放在onCreate中，demo暂先在此初始化)
            //vodsVideoUploadClient = new VODSVideoUploadClientImpl(DebugActivity.this.getApplicationContext());
            //vodsVideoUploadClient.init();
            //参数请确保存在，如不存在SDK内部将会直接将错误throw Exception
            //文件路径保证存在之外因为Android 6.0之后需要动态获取权限，请开发者自行实现获取"文件读写权限".
            VodHttpClientConfig vodHttpClientConfig = new VodHttpClientConfig.Builder()
                    .setMaxRetryCount(2)//重试次数
                    .setConnectionTimeout(15 * 1000)//连接超时
                    .setSocketTimeout(15 * 1000)//socket超时
                    .build();
            //构建短视频VideoInfo,常见的描述，标题，详情都可以设置
            SvideoInfo svideoInfo = new SvideoInfo();
            svideoInfo.setTitle(new File(videoPath).getName());
            svideoInfo.setDesc("五菱汽车短视频");
            svideoInfo.setCateId(1);
            //构建点播上传参数
            VodSessionCreateInfo vodSessionCreateInfo = new VodSessionCreateInfo.Builder()
                    .setImagePath(coverImagePath)//图片地址
                    .setVideoPath(videoPath)//视频地址
                    .setAccessKeyId(accessKeyId)//临时accessKeyId
                    .setAccessKeySecret(accessKeySecret)//临时accessKeySecret
                    .setSecurityToken(securityToken)//securityToken
                    .setExpriedTime(expriedTime)//STStoken过期时间
                    .setRequestID(requestID)//requestID，可以传获取STS时返回的requestID,也可以不设.
                    .setIsTranscode(true)//是否转码.如开启转码请AppSever务必监听服务端转码成功的通知
                    .setSvideoInfo(svideoInfo)//短视频视频信息
                    .setVodHttpClientConfig(vodHttpClientConfig)//网络参数
                    .build();
            //上传
            vodsVideoUploadClient.uploadWithVideoAndImg(vodSessionCreateInfo, new VODSVideoUploadCallback() {
                @Override
                public void onUploadSucceed(String videoId, String imageUrl) {
                    LogUtils.d("onUploadSucceed" + "videoId:" + videoId + "imageUrl" + imageUrl);
                    if (uploadCallback != null) {
                        uploadCallback.onUploadSucceed(videoId, imageUrl);
                    }
                }

                @Override
                public void onUploadFailed(String code, String message) {
                    LogUtils.d("onUploadFailed" + "code" + code + "message" + message);
                    ToastUtil.showMessage(context, "视频上传失败" + "code:" +
                            code + ",message:" + message);
                    if (uploadCallback != null) {
                        uploadCallback.onUploadFailed(code, message);
                    }
                }

                @Override
                public void onUploadProgress(long uploadedSize, long totalSize) {
                    LogUtils.d("onUploadProgress" + uploadedSize * 100 / totalSize);
                    long progress = uploadedSize * 100 / totalSize;
                    if (uploadCallback != null) {
                        uploadCallback.onUploadProgress(progress);
                    }
                }

                @Override
                public void onSTSTokenExpried() {
                    LogUtils.d("onSTSTokenExpried");
                    //STS token过期之后刷新STStoken，如正在上传将会断点续传
                    vodsVideoUploadClient.refreshSTSToken(accessKeyId, accessKeySecret, securityToken, expriedTime);
                }

                @Override
                public void onUploadRetry(String code, String message) {
                    //上传重试的提醒
                    LogUtils.d("onUploadRetry" + "code" + code + "message" + message);
                }

                @Override
                public void onUploadRetryResume() {
                    //上传重试成功的回调.告知用户重试成功
                    LogUtils.d("onUploadRetryResume");
                }
            });
        }
    }

    /**
     * 解压滤镜文件
     */
    private static String[] unZipFilters(@NonNull Context context) {
        // TODO: 2017/12/26 解压滤镜压缩文件:需要在子线程中解压，解压完毕后回调主线程处理后续逻辑
        // TODO:demo暂不使用滤镜。若最后确定不需要滤镜功能，需要将滤镜压缩文件zip删除，大小约1.6M。
        String path = StorageUtils.getCacheDirectory(context).getAbsolutePath() +
                File.separator + "AliyunDemo" + File.separator;
        String[] eff_dirs = new String[]{
                null,
                path + "filter/chihuang",
                path + "filter/fentao",
                path + "filter/hailan",
                path + "filter/hongrun",
                path + "filter/huibai",
                path + "filter/jingdian",
                path + "filter/maicha",
                path + "filter/nonglie",
                path + "filter/rourou",
                path + "filter/shanyao",
                path + "filter/xianguo",
                path + "filter/xueli",
                path + "filter/yangguang",
                path + "filter/youya",
                path + "filter/zhaoyang"
        };
        return eff_dirs;
    }

    /**
     * 删除临时图片或者视频文件
     */
    public static void deleteTempFile() {
        // TODO: 2018/1/4 是否需要删除录制生成的视频文件
        //存放封面图片的父级目录
        String dirPath = Environment.getExternalStorageDirectory().getAbsolutePath()
                + ConfigConstant.SD_TMP_DIRECTORY;
        //删除之前保存的图片
        File file = new File(dirPath);
        if (file.exists()) {
            File[] coverFiles = file.listFiles(new FilenameFilter() {
                @Override
                public boolean accept(File dir, String filename) {
                    return filename.startsWith("svideo_cover_");
                }
            });
            if (coverFiles != null) {
                for (File coverFile : coverFiles) {
                    coverFile.delete();
                }
            }
        }
    }

    public interface STSTokenCallback {
        /**
         * STSToken获取成功
         */
        void onSuccess(AliSTSTokenInfo tokenInfo);

        /**
         * STSToken获取失败
         */
        void onFailure(Throwable e);
    }

    /**
     * 视频上传回调 子线程
     */
    public interface VideoUploadCallback {
        /**
         * 上传进度回调
         *
         * @param progress 进度值(0~100)
         */
        void onUploadProgress(long progress);

        /**
         * 视频上传成功回调
         */
        void onUploadSucceed(String videoId, String imageUrl);

        /**
         * 视频上传失败回调
         */
        void onUploadFailed(String code, String message);
    }

}
