package com.cloudy.linglingbang.activity.store.commodity.adapter.holder

import android.app.Activity
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import butterknife.BindView
import butterknife.ButterKnife
import butterknife.OnClick
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.store.commodity.CommodityDetailActivity
import com.cloudy.linglingbang.activity.store.commodity.CommodityOpenDialog
import com.cloudy.linglingbang.activity.store.commodity.ReplacementListActivity
import com.cloudy.linglingbang.activity.store.commodity.adapter.ShoppingCartSingleGiftAdapter
import com.cloudy.linglingbang.activity.store.commodity.adapter.ShoppingCartTotalGiftAdapter
import com.cloudy.linglingbang.activity.store.commodity.dialog.SkuSelectorDialog
import com.cloudy.linglingbang.app.util.AppUtil
import com.cloudy.linglingbang.app.util.DeviceUtil
import com.cloudy.linglingbang.app.util.ToastUtil
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils
import com.cloudy.linglingbang.app.util.timer.CountDownManager
import com.cloudy.linglingbang.app.util.timer.TimeDelta
import com.cloudy.linglingbang.app.widget.SlidingButtonView
import com.cloudy.linglingbang.app.widget.SlidingButtonView.OnSlidingButtonListener
import com.cloudy.linglingbang.app.widget.banner.AdRoundImageView
import com.cloudy.linglingbang.app.widget.ecologyStore.CountControlerView
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder
import com.cloudy.linglingbang.model.SourceModel
import com.cloudy.linglingbang.model.store.commodity.CartCommodity
import kotlin.math.pow

/**
 * 购物车中商品列表的viewHolder
 *
 * <AUTHOR>
 * @date 2022/10/12
 */
class ShoppingCartSingleCommodityViewHolder(itemView: View?) :
    BaseRecyclerViewHolder<CartCommodity>(itemView), CountDownManager.OnTickListener,
    OnSlidingButtonListener {

    @JvmField
    @BindView(R.id.rl_single_gift)
    var mRlSingleGift: RecyclerView? = null

    @JvmField
    @BindView(R.id.recycler_view_total_gift_list)
    var recyclerViewTotalGiftList: RecyclerView? = null

    @JvmField
    @BindView(R.id.tv_activity_name)
    var tvActivityName: TextView? = null

    @JvmField
    @BindView(R.id.tv_activity_time)
    var tvActivityTime: TextView? = null

    @JvmField
    @BindView(R.id.ll_sec_kill)
    var llSecKill: LinearLayout? = null

    /** 选择checkbox */
    @JvmField
    @BindView(R.id.cb_check_sku)
    var cbCheckSku: CheckBox? = null

    /** 选择checkbox */
    @JvmField
    @BindView(R.id.iv_sku_pic)
    var ivSkuPic: AdRoundImageView? = null

    /** sku名称 */
    @JvmField
    @BindView(R.id.tv_sku_name)
    var tvSkuName: TextView? = null

    /** 商品名称 */
    @JvmField
    @BindView(R.id.tv_style)
    var tvStyle: TextView? = null

    /** 价格 */
    @JvmField
    @BindView(R.id.tv_commodity_price)
    var tvCommodityPrice: TextView? = null

    /** 起购量、限购量 */
    @JvmField
    @BindView(R.id.tv_limit)
    var tvLimit: TextView? = null

    /** 点此换购文案 */
    @JvmField
    @BindView(R.id.tv_replacement)
    var tvReplacement: TextView? = null

    /** 数量 */
    @JvmField
    @BindView(R.id.count_controller)
    var countController: CountControlerView? = null

    /** 收货方式 */
    @JvmField
    @BindView(R.id.tv_receiving_method)
    var tvReceivingMethod: TextView? = null

    /** 服务门店 */
    @JvmField
    @BindView(R.id.tv_service_store)
    var tvServiceStore: TextView? = null

    /** 服务门店 */
    @JvmField
    @BindView(R.id.rl_service_store)
    var rlServiceStore: RelativeLayout? = null

    /** 活动策略类型标签（满减 等） */
    @JvmField
    @BindView(R.id.tv_activity_type)
    var tvActivityType: TextView? = null

    /** 促销活动优惠提示 */
    @JvmField
    @BindView(R.id.tv_activity_tips)
    var tvActivityTips: TextView? = null

    /** 满赠活动整体布局 */
    @JvmField
    @BindView(R.id.ll_with_gift_activity)
    var llWithGiftActivity: LinearLayout? = null

    /** 去凑单/去逛逛 */
    @JvmField
    @BindView(R.id.tv_go_gift_list)
    var tvGoGiftList: TextView? = null

    /** 删除 */
    @JvmField
    @BindView(R.id.tv_delete)
    var tvDelete: TextView? = null

    /** 所有赠品相关 */
    @JvmField
    @BindView(R.id.ll_all_gift)
    var llAllGift: LinearLayout? = null

    /** 价格相关 */
    @JvmField
    @BindView(R.id.ll_price)
    var llPrice: LinearLayout? = null

    /** 重新选择 */
    @JvmField
    @BindView(R.id.rl_reset_select)
    var rlResetSelect: RelativeLayout? = null

    /** 内容 */
    @JvmField
    @BindView(R.id.layout_content)
    var layoutContent: LinearLayout? = null

    @JvmField
    @BindView(R.id.slv_parent)
    var slvParent: SlidingButtonView? = null

    @JvmField
    @BindView(R.id.tv_tv_service_store_name)
    var tvTvServiceStoreName: TextView? = null

    /** 旧价格 */
    @JvmField
    @BindView(R.id.tv_commodity_estimate_price)
    var tvCommodityEstimatePrice: TextView? = null


    /**
     * 刷新接口的监听
     */
    var needRefreshListener: (Int) -> Unit = {}

    var giftList = mutableListOf<CartCommodity>()

    var cartCommodity: CartCommodity? = null

    var mOnChangeCommodityListener: OnChangeCommodityListener? = null

    override fun initItemView(itemView: View) {
        super.initItemView(itemView)
        ButterKnife.bind(this, itemView)
        slvParent?.setSlidingButtonListener(this)

    }

    override fun bindTo(bean: CartCommodity?, position: Int) {
        super.bindTo(bean, position)
        CountDownManager.getInstance().removeOnTickListener(this)
        //设置内容布局的宽为屏幕宽度
        layoutContent?.layoutParams?.width =
            DeviceUtil.getScreenWidth(layoutContent?.context as Activity?)
        layoutContent?.setOnClickListener {
            bean?.commodityId?.let { it1 -> closeMenuAndToDetail(it1) }
        }

        cartCommodity = bean
        bean?.apply {
            //基本属性
            ivSkuPic?.createImageLoad(skuImage)?.load()
            tvSkuName?.text = commodityName
            tvStyle?.text = skuName
            if (isReselect == 1) {
                //如果需要重新选择
                llAllGift?.visibility = View.GONE
                llPrice?.visibility = View.GONE
                rlResetSelect?.visibility = View.VISIBLE
                tvSkuName?.setTextColor(tvSkuName?.resources?.getColor(R.color.color_99a1b2)!!)
                if (!isEditModel) {
                    cbCheckSku?.isEnabled = false
                } else {
                    cbCheckSku?.isEnabled = true
                    cbCheckSku?.isChecked = isCheckForEdit == 1
                }
            } else {
                //如果不需要重新选择
                llAllGift?.visibility = View.VISIBLE
                llPrice?.visibility = View.VISIBLE
                rlResetSelect?.visibility = View.GONE
                tvSkuName?.setTextColor(tvSkuName?.resources?.getColor(R.color.color_1D1E23)!!)
                //=========加载单个商品的赠品=========
                mRlSingleGift?.layoutManager =
                    LinearLayoutManager(mRlSingleGift?.context)
                mRlSingleGift?.adapter = ShoppingCartSingleGiftAdapter(
                    mRlSingleGift?.context,
                    giftList
                )
                //添加附加商品
                additionList?.let { giftList.addAll(it) }
                //添加赠品
                giftActivity?.extCommodityList?.forEach {
                    it.additionAttributeId = -1
                    giftList.add(it)
                }
                //添加换购商品
                exchangeActivity?.extCommodityList?.forEach {
                    it.additionAttributeId = -2
                    giftList.add(it)
                }
                mRlSingleGift?.adapter?.notifyDataSetChanged()
                //设置列表显示和隐藏，防止UI有间隙
                if (giftList.isNullOrEmpty()) {
                    mRlSingleGift?.visibility = View.GONE
                } else {
                    mRlSingleGift?.visibility = View.VISIBLE
                }
                //其他基本属性设置
                //秒杀相关
                singleActivity?.apply {
                    tvActivityName?.text = singleActivity?.promotionStrategyTypeName
                    if (singleActivity?.activityStatus == 5) {
                        CountDownManager.getInstance()
                            .addOnTickListener(this@ShoppingCartSingleCommodityViewHolder)
                    } else {
                        llSecKill?.visibility = View.GONE
                    }
                }
                //基本属性
                if (promotionAmountStr?.isNotEmpty() == true) {
                    tvCommodityEstimatePrice?.visibility = View.VISIBLE
                    tvCommodityEstimatePrice?.text = "预估到手价 ¥ $promotionAmountStr"
//                    tvCommodityPrice?.setTextSize(TypedValue.COMPLEX_UNIT_PX, tvCommodityPrice?.context?.resources?.getDimension(R.dimen.activity_set_text_30)!!)
                    tvCommodityPrice?.setTextColor(tvCommodityPrice?.context?.resources?.getColor(R.color.color_1D1E23)!!)
                } else {
                    tvCommodityEstimatePrice?.visibility = View.GONE
                    tvCommodityPrice?.setTextColor(tvCommodityPrice?.context?.resources?.getColor(R.color.color_ea0029)!!)
                }
                tvCommodityPrice?.text = "¥ $sellPriceStr"

                countController?.setNeedToast(true)
                countController?.setOnCountChangeListener(object :
                    CountControlerView.OnCountChangeListener {
                    override fun onReduce(success: Boolean, minCount: Int) {
                        SensorsUtils.sensorsClickBtn("点击增减数量", "购物车", "加减按钮")
                        if (!success) {
                            ToastUtil.showMessage(
                                countController?.context,
                                countController?.context?.getString(
                                    R.string.commodity_buy_min_count,
                                    minCount
                                )
                            )
                        }
                    }

                    override fun onAdd(success: Boolean, maxCount: Int) {
                        SensorsUtils.sensorsClickBtn("点击增减数量", "购物车", "加减按钮")
                        if (!success) {
                            ToastUtil.showMessage(
                                countController?.context,
                                countController?.context?.getString(
                                    R.string.commodity_buy_max_count,
                                    maxCount
                                )
                            )
                        }
                    }
                })
                countController?.currentCount = quantity
                val limitText = StringBuilder()
                limitBuyCountMin?.let {
                    if (it > 1) {
                        limitText.append("起购量：$it")
                        countController?.minCount = it
                        limitTotalBuyCount?.apply {
                            limitText.append(";")
                        }
                    }
                }
                var maxBuy = inventory
                limitTotalBuyCount?.let {
                    limitText.append("限购量：$it")
                    if (limitTotalBuyCount < inventory) {
                        maxBuy = limitTotalBuyCount
                    }
                }
                countController?.maxCount = maxBuy
                tvLimit?.visibility = if (limitText.isEmpty()) View.GONE else View.VISIBLE
                tvLimit?.text = limitText
                //物流方式
                tvReceivingMethod?.text = logisticsTakeModeStr
                //服务门店地址
                if (logisticsTakeMode == 1) {
                    rlServiceStore?.visibility = View.VISIBLE
                    tvServiceStore?.text = serviceDealerName
                } else {
                    rlServiceStore?.visibility = View.GONE
                }
                tvTvServiceStoreName?.text = if (commodityClassifyId == 0) "购车门店" else "服务门店"
                //换购提示
                if (exchangeActivity != null) {
                    tvReplacement?.visibility = View.VISIBLE
                    tvReplacement?.text =
                        if (exchangeActivity.extCommodityList.isEmpty()) "点此换购" else "点此重新选择换购"

                } else {
                    tvReplacement?.visibility = View.GONE
                }

                //满赠类活动
                if (withGiftActivity != null) {
                    llWithGiftActivity?.visibility = View.VISIBLE

                    withGiftActivity.apply {

                        tvActivityType?.text = promotionStrategyTypeName
                        tvActivityTips?.text = promotionActivityTips
                        tvGoGiftList?.text = if (isApply) "再逛逛" else "去凑单"

                        //赠品列表
                        recyclerViewTotalGiftList?.layoutManager =
                            LinearLayoutManager(recyclerViewTotalGiftList?.context)
                        recyclerViewTotalGiftList?.adapter = ShoppingCartTotalGiftAdapter(
                            recyclerViewTotalGiftList?.context,
                            extCommodityList
                        )
                    }
                } else {
                    llWithGiftActivity?.visibility = View.GONE
                }

                if (!isEditModel) {
                    if (isCheck == 2) cbCheckSku?.isEnabled = false else {
                        cbCheckSku?.isEnabled = true
                        cbCheckSku?.isChecked = isCheck == 1
                    }
                } else {
                    cbCheckSku?.isEnabled = true
                    cbCheckSku?.isChecked = isCheckForEdit == 1
                }

                countController?.setClickButtonListener {
                    countController?.currentCount?.toLong()?.let {
                        mOnChangeCommodityListener?.changeNumToCart(
                            cartId,
                            it
                        )
                    }
                }

                //换购
                tvReplacement?.setOnClickListener {
                    SensorsUtils.sensorsClickBtn("点击换购", "购物车", "换购按钮")
                    if (AppUtil.checkLogin(it?.context))
                        ReplacementListActivity.toReplacementPage(
                            itemView.context,
                            cartId,
                            commodityId,
                            skuId,
                            exchangeActivity.promotionActivityId.toLong()
                        )

                }


                //去逛逛
                tvGoGiftList?.setOnClickListener {
                    SensorsUtils.sensorsClickBtn("点击去凑单逛逛", "购物车", "去凑单逛逛按钮")
                    if (AppUtil.checkLogin(it?.context))
                        ReplacementListActivity.startActivity(
                            itemView.context,
                            withGiftActivity.promotionActivityId.toLong()
                        )
                }


            }

            cbCheckSku?.setOnCheckedChangeListener { _, isChecked ->
                if (isEditModel) {
                    isCheckForEdit = if (isChecked) 1 else 0
                }
                mOnChangeCommodityListener?.onChangeSingleSelect(
                    cartId,
                    if (isChecked) 1 else 0
                )
            }
            //动态设置删除按钮的高度
            val w = View.MeasureSpec.makeMeasureSpec(
                0,
                View.MeasureSpec.UNSPECIFIED
            )
            val h = View.MeasureSpec.makeMeasureSpec(
                0,
                View.MeasureSpec.UNSPECIFIED
            )
            layoutContent?.measure(w, h)
            val height: Int = layoutContent!!.measuredHeight
            val params: ViewGroup.LayoutParams = tvDelete!!.layoutParams
            params.height = height
            tvDelete?.layoutParams = params
            tvDelete?.setOnClickListener {
                mOnChangeCommodityListener?.onDeleteSingle(bean.cartId)
            }
        }


    }

    private fun closeMenuAndToDetail(commodityId: Long) {
        //判断是否有删除菜单打开
        if (slvParent?.open == true) {
            slvParent?.closeMenu()
        } else {
            CommodityDetailActivity.startActivity(
                itemView.context, commodityId, SourceModel(
                    SourceModel.POSITION_TYPE.SHOPPING_CAR_TYPE,
                    SourceModel.POSITION_TYPE.SHOPPING_CAR_VALUE
                )
            )
        }

    }

    override fun onTick(currentTime: Long) {
        cartCommodity?.singleActivity?.let {
            val tel = currentTime.toString().length - it.promotionEndTime.toString().length
            val times = StringBuilder()
            //如果是进行中，且时间没有结束
            if (it.activityStatus == 5 && currentTime < it.promotionEndTime * 10.0.pow(tel.toDouble())
                    .toLong()
            ) {
                if (currentTime < it.promotionEndTime * 10.0.pow(tel.toDouble()).toLong()) {
                    llSecKill?.visibility = View.VISIBLE
                    times.append(itemView.resources.getString(R.string.commodity_new_end_time))
                    val timeDelta = TimeDelta.create(
                        currentTime,
                        it.promotionEndTime * 10.0.pow(tel.toDouble()).toLong()
                    )

                    if (timeDelta.days > 0) {
                        times.append(timeDelta.days)
                        times.append("天")
                        times.append(" ")
                    } else {
                        times.append(" ")
                    }

                    if (timeDelta.hours > 0) {
                        if (timeDelta.hours < 10) {
                            times.append("0")
                        }
                        times.append(timeDelta.hours)
                        times.append(":")
                    }

                    if (timeDelta.minutes < 10) {
                        times.append("0")
                    }
                    times.append(timeDelta.minutes)
                    times.append(":")
                    if (timeDelta.seconds < 10) {
                        times.append("0")
                    }
                    times.append(timeDelta.seconds)
                    tvActivityTime?.text = times
                } else {
                    //时间到了，改成已结束
                    it.activityStatus = 6
                    llSecKill?.visibility = View.GONE
                    // TODO: 2022/10/17  刷新数据
                    needRefreshListener(1)
                }
            } else {
                llSecKill?.visibility = View.GONE

            }

        }

    }

    override fun getCountDownTag(): Any {
        return AppUtil.getActivity(itemView.context)
    }

    fun setOnNeedRefreshListener(e: (Int) -> Unit) {
        this.needRefreshListener = e
    }

    override fun onMenuIsOpen(view: View?) {
        mOnChangeCommodityListener?.onMenuIsOpen(view)

    }

    override fun onDownOrMove(slidingButtonView: SlidingButtonView?) {
        mOnChangeCommodityListener?.onDownOrMove(slidingButtonView)
    }

    /**
     * 更改sku
     */
    @OnClick(R.id.tv_style, R.id.tv_reset, R.id.tv_receiving_method, R.id.tv_service_store)
    fun onChangeStyleActivity(view: View?) {
        when (view?.id) {
            R.id.tv_style -> {
                SensorsUtils.sensorsClickBtn("点击选择款式", "购物车", "选择款式按钮")
            }
            R.id.tv_receiving_method -> {
                SensorsUtils.sensorsClickBtn("点击收货方式", "购物车", "收货方式按钮")
            }
            R.id.tv_service_store -> {
                SensorsUtils.sensorsClickBtn("点击服务门店", "购物车", "服务门店按钮")
            }
            R.id.tv_reset -> {
                SensorsUtils.sensorsClickBtn("点击重选商品规格", "购物车", "重选按钮")
            }

        }
        cartCommodity?.apply {
            if ((view?.id == R.id.tv_receiving_method || view?.id == R.id.tv_receiving_method) && isSwitchTakeMode == 0) {
//                ToastUtil.showMessage(view.context, "不支持修改")
                return
            }
            val dialog = SkuSelectorDialog(itemView.context)
            dialog.openType = CommodityOpenDialog.NOW_UPDATE_CART
            dialog.mCartCommodity = cartCommodity
            dialog.showDialog()
        }


    }


}