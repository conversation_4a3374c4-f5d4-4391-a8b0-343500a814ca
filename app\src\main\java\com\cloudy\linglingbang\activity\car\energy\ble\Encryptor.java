package com.cloudy.linglingbang.activity.car.energy.ble;

import android.os.Build;
import android.security.keystore.KeyGenParameterSpec;
import android.security.keystore.KeyProperties;
import android.text.TextUtils;
import android.util.Base64;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;

import com.blankj.utilcode.util.SPUtils;

import java.io.IOException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.SignatureException;
import java.security.UnrecoverableEntryException;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.KeyGenerator;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;

public class Encryptor {

    private static final String ENCRYPTOR_FILE = "encrypt_result_file";
    private static final String ENCRYPTOR_IV = "iv_key";
    private static final String ENCRYPTOR_ENCRYPTION = "encryption_key";

    private static final String TRANSFORMATION = "AES/GCM/NoPadding";
    private static final String ANDROID_KEY_STORE = "AndroidKeyStore";

    private byte[] encryption;
    private byte[] iv;

    Encryptor() {
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    byte[] encryptText(final String alias, final String textToEncrypt)
            throws UnrecoverableEntryException, NoSuchAlgorithmException, KeyStoreException,
            NoSuchProviderException, NoSuchPaddingException, InvalidKeyException, IOException,
            InvalidAlgorithmParameterException, SignatureException, BadPaddingException,
            IllegalBlockSizeException {

        final Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.ENCRYPT_MODE, getSecretKey(alias));

        iv = cipher.getIV();
        SPUtils.getInstance(ENCRYPTOR_FILE).put(ENCRYPTOR_IV + alias, Base64.encodeToString(iv, Base64.DEFAULT));

        encryption = cipher.doFinal(textToEncrypt.getBytes("UTF-8"));
        SPUtils.getInstance(ENCRYPTOR_FILE).put(ENCRYPTOR_ENCRYPTION + alias, Base64.encodeToString(encryption, Base64.DEFAULT));

        return encryption;
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    @NonNull
    private SecretKey getSecretKey(final String alias) throws NoSuchAlgorithmException,
            NoSuchProviderException, InvalidAlgorithmParameterException {

        final KeyGenerator keyGenerator = KeyGenerator
                .getInstance(KeyProperties.KEY_ALGORITHM_AES, ANDROID_KEY_STORE);
        keyGenerator.init(new KeyGenParameterSpec.Builder(alias,
                KeyProperties.PURPOSE_ENCRYPT | KeyProperties.PURPOSE_DECRYPT)
                .setBlockModes(KeyProperties.BLOCK_MODE_GCM)
                .setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_NONE)
                .build());

        return keyGenerator.generateKey();
    }

    byte[] getEncryption(final String alias) {
        if (encryption == null) {
            String encryptionStr = SPUtils.getInstance(ENCRYPTOR_FILE).getString(ENCRYPTOR_ENCRYPTION + alias);
            if (!TextUtils.isEmpty(encryptionStr)) {
                encryption = Base64.decode(encryptionStr, Base64.DEFAULT);
            }
        }
        return encryption;
    }

    byte[] getIv(final String alias) {
        if (iv == null) {
            String ivStr = SPUtils.getInstance(ENCRYPTOR_FILE).getString(ENCRYPTOR_IV + alias);
            if (!TextUtils.isEmpty(ivStr)) {
                iv = Base64.decode(ivStr, Base64.DEFAULT);
            }
        }
        return iv;
    }
}
