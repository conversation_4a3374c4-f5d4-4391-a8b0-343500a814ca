package com.cloudy.linglingbang.activity.car.home;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.app.widget.recycler.TransparentGridItemDecoration;
import com.cloudy.linglingbang.app.widget.recycler.holder.FunctionResultViewHolder;
import com.cloudy.linglingbang.model.entrance.HomeFunctionResult;

/**
 * 爱车-功能图标
 *
 * <AUTHOR>
 * @date 2020-02-20
 */
public class MyCarFunctionWithTitleViewHolder extends BaseRecyclerViewHolder<MyCarModel.FunctionModel> {
    private TextView mTvFunctionName;
    private ImageView mIvFunctionIcon;
    private TextView mTvSeeMore;
    private MyCarFunctionResultViewHolder mMyCarFunctionResultViewHolder;

    private Context mContext;

    public MyCarFunctionWithTitleViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        mContext = itemView.getContext();
        mMyCarFunctionResultViewHolder = new MyCarFunctionResultViewHolder(itemView);
        mTvFunctionName = itemView.findViewById(R.id.tv_function_name);
        mIvFunctionIcon = itemView.findViewById(R.id.iv_function_icon);
        mTvSeeMore = itemView.findViewById(R.id.tv_see_more);
    }

    @Override
    public void bindTo(final MyCarModel.FunctionModel functionModel, int position) {
        super.bindTo(functionModel, position);
        mMyCarFunctionResultViewHolder.bindFunctionResult(functionModel.getFunctionResultWrapper().getOriginal());
        mTvFunctionName.setText(functionModel.getFunctionName());
        mIvFunctionIcon.setImageDrawable(itemView.getContext().getResources().getDrawable(functionModel.getFunctionIcon()));
        //如果是潜客的用车服务或者保客的其他服务，添加查看更多
        if (functionModel.getFunctionType() == HomeFunctionResult.MY_CAR_USER_CAR_SERVICE_NORMAL || functionModel.getFunctionType() == HomeFunctionResult.MY_CAR_OTHER_SERVICE_MEMBER) {
            mTvSeeMore.setVisibility(View.VISIBLE);
        } else {
            mTvSeeMore.setVisibility(View.GONE);
        }
        mTvSeeMore.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                int type = functionModel.getFunctionType() == HomeFunctionResult.MY_CAR_USER_CAR_SERVICE_NORMAL ? HomeFunctionResult.MY_CAR_USER_CAR_MORE_NORMAL : HomeFunctionResult.MY_CAR_OTHER_MORE_MEMBER;
                MyCarMoreServiceActivity.startActivity(mContext, type);
            }
        });
    }

    public class MyCarFunctionResultViewHolder extends FunctionResultViewHolder {

        public MyCarFunctionResultViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected int getColumns(HomeFunctionResult functionResult) {
            //写死为3列
            return 3;
        }

        @Override
        protected int getFunctionItemLayoutResId(int type) {
            return R.layout.item_my_car_function;
        }

        @Override
        protected TransparentGridItemDecoration createItemDecoration(Context context, int spanCount) {
            int px30 = context.getResources().getDimensionPixelOffset(R.dimen.normal_70);
            return new TransparentGridItemDecoration(spanCount, 0, px30);
        }

    }

}
