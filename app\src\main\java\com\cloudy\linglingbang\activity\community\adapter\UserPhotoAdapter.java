package com.cloudy.linglingbang.activity.community.adapter;

import android.content.Context;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.widget.banner.AdImageView;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.user.User;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/22
 */
public class UserPhotoAdapter extends BaseRecyclerViewAdapter<User> {
    public UserPhotoAdapter(Context context, List<User> data) {
        super(context, data);
    }

    @Override
    protected BaseRecyclerViewHolder<User> createViewHolder(View itemView) {
        return new UserViewHolder(itemView);
    }

    public void setNewData(List<User> data) {
        this.mData = data;
        notifyDataSetChanged();
    }

    @Override
    protected int getItemLayoutRes(int viewType) {
        return R.layout.item_post_select_user_photo;
    }

    private static class UserViewHolder extends BaseRecyclerViewHolder<User> {
        AdImageView mHeaderImg;

        public UserViewHolder(View itemView) {
            super(itemView);
            mHeaderImg = itemView.findViewById(R.id.iv_head);
        }

        @Override
        public void bindTo(User user, int position) {
            super.bindTo(user, position);
            ImageLoad.LoadUtils.loadAvatar(itemView.getContext(), mHeaderImg, user.getPhoto());
        }
    }
}
