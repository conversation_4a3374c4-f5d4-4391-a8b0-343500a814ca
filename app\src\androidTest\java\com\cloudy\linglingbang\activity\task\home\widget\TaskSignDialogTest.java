package com.cloudy.linglingbang.activity.task.home.widget;

import com.cloudy.linglingbang.BaseInstrumentedTest;
import com.cloudy.linglingbang.ReflectUtils;
import com.cloudy.linglingbang.model.sign_in.AutoSignInfo;
import com.cloudy.linglingbang.model.sign_in.UserAwards;
import com.cloudy.linglingbang.model.task.TaskInfo;
import com.cloudy.linglingbang.model.task.TaskLabel;
import com.cloudy.linglingbang.model.task.TaskListInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/8/23
 */
public class TaskSignDialogTest extends BaseInstrumentedTest {
    @Override
    public void test() {
        super.test();
        new TaskSignDialog(getContext(), createTaskListInfo(), createAutoSignInfo()).show();
    }

    private TaskListInfo createTaskListInfo() {
        TaskListInfo taskListInfo = new TaskListInfo();

        List<TaskInfo> taskInfoList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            TaskInfo taskInfo = new TaskInfo();
            ReflectUtils.setFieldValue(taskInfo, "taskName", "任务" + i);
            ReflectUtils.setFieldValue(taskInfo, "frequency", i + 1);
            ReflectUtils.setFieldValue(taskInfo, "completeStatus", i & 1);
            taskInfoList.add(taskInfo);
        }
        ReflectUtils.setFieldValue(taskListInfo, "taskUserVoList", taskInfoList);

        TaskLabel taskLabel = new TaskLabel();
        ReflectUtils.setFieldValue(taskLabel, "labelTitle", "title");
        ReflectUtils.setFieldValue(taskLabel, "labelDescribe", "desc");

        ReflectUtils.setFieldValue(taskListInfo, "taskLabel", taskLabel);

        return taskListInfo;
    }

    private AutoSignInfo createAutoSignInfo() {
        AutoSignInfo autoSignInfo = new AutoSignInfo();
        UserAwards awardCoin = new UserAwards();
        awardCoin.setCurrentAwardCount(5);
        autoSignInfo.setAwardCoin(awardCoin);
        return autoSignInfo;
    }
}