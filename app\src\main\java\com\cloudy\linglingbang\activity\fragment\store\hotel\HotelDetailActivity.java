package com.cloudy.linglingbang.activity.fragment.store.hotel;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.ApplicationLLB;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.fragment.mynew.util.SelfUserInfoLoader;
import com.cloudy.linglingbang.app.receiver.UserInfoChangeReceiver;
import com.cloudy.linglingbang.app.receiver.UserInfoChangedHelper;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.DeviceUtil;
import com.cloudy.linglingbang.app.util.NotchScreenUtils;
import com.cloudy.linglingbang.app.util.StatusBarUtils;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.widget.dialog.hotel.HotelUtil;
import com.cloudy.linglingbang.model.hotel.Hotel;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BaseSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.NormalSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.wrapper.EmptyInfo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 酒店详情页面
 *
 * <AUTHOR>
 * @date 2022/3/16
 */
public class HotelDetailActivity extends BaseRecyclerViewRefreshActivity<Object> {

    public static void startActivity(Context context, String hotelId, long startDate, long endDate) {
        Intent intent = new Intent(context, HotelDetailActivity.class);
        intent.putExtra("hotelId", hotelId);
        intent.putExtra("startDate", startDate);
        intent.putExtra("endDate", endDate);
        context.startActivity(intent);
    }

    @BindView(R.id.ll_title)
    View mLlTitle;
    @BindView(R.id.tv_title)
    TextView mTvTitle;
    @BindView(R.id.iv_call)
    ImageView mIvCall;
    @BindView(R.id.iv_back)
    ImageView mIvBack;
    @BindView(R.id.recycler_view)
    RecyclerView mRecyclerView;
    private RecyclerView.OnScrollListener mOnScrollListener;
    private HotelDetailAdapter mHotelDetailAdapter;
    private Hotel mHotel;
    /**
     * 入住时间
     */
    private long startDate;
    /**
     * 离店时间
     */
    private long endDate;
    /**
     * 酒店id
     */
    private String hotelId;
    private int mScrollDy;

    /**
     * 我的监听
     */
    private UserInfoChangedHelper mUserInfoChangedHelper;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_hotel_detail);
    }

    @Override
    protected void initialize() {
        super.initialize();
        hotelId = getIntent().getStringExtra("hotelId");
        startDate = getIntent().getLongExtra("startDate", 0);
        endDate = getIntent().getLongExtra("endDate", 0);
        if (TextUtils.isEmpty(hotelId)) {
            onIntentExtraError();
            return;
        }
        if (startDate == 0 || endDate == 0) {
            startDate = AppUtil.getServerCurrentTime();
            endDate = startDate + AppUtil.ONEDAY;
        }
        //设置状态栏通栏
        StatusBarUtils.setFullScreenTransparentStatusBarAndWhiteText(this);
        int top = Math.max(NotchScreenUtils.getNotchSafeWH()[1], StatusBarUtils.getStatusBarHeight(ApplicationLLB.ct()));
        mLlTitle.setPadding(mLlTitle.getPaddingLeft(), mLlTitle.getPaddingTop() + top, mLlTitle.getPaddingTop(), mLlTitle.getPaddingBottom());

        mIvBack.setOnClickListener(v -> onBackPressed());
        mIvCall.setOnClickListener(v -> {
            if (mHotel == null || TextUtils.isEmpty(mHotel.getInnPhone())) {
                return;
            }
            HotelUtil.showCallDialogOrToCall(mHotel.getInnPhone(), v.getContext());
        });
        queryHotelDetail(true);
        //监听用户变更
        mUserInfoChangedHelper = new UserInfoChangedHelper(new UserInfoChangeReceiver() {

            @Override
            protected void onUpdateBaseInfo() {
                super.onUpdateBaseInfo();
                if (UserUtils.hasLogin()) {
                    SelfUserInfoLoader.getInstance().getUserBalanceInfo(HotelDetailActivity.this);
                } else {
                    onRefresh();
                }
            }

            @Override
            protected void onUpdateBalanceInfo() {
                super.onUpdateBalanceInfo();
                onRefresh();
            }

        });
        mUserInfoChangedHelper.register(getContext());
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<Object> list) {
        return mHotelDetailAdapter = new HotelDetailAdapter(this, list);
    }

    @Override
    public Observable<BaseResponse<List<Object>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        Map<String, String> map = new HashMap<>();
        map.put("pageNum", String.valueOf(pageNo));
        map.put("pageSize", String.valueOf(pageSize));

        map.put("innId", hotelId);
        map.put("dtArrorig", AppUtil.formatDate(mHotelDetailAdapter.getStartDate(), "yyyy-MM-dd"));
        map.put("dtDeporig", AppUtil.formatDate(mHotelDetailAdapter.getEndDate(), "yyyy-MM-dd"));
        return service2.hotelRoomTypePage(map).map(listBaseResponse -> {
            List<Object> list = new ArrayList<>();
            if (listBaseResponse.getData() != null) {
                list.addAll(listBaseResponse.getData());
            }
            if (pageNo == 1 && list.isEmpty()) {
                list.add(new EmptyInfo(R.drawable.ic_hotel_place_no_txt, R.string.txt_hotel_no_room));
            }
            return listBaseResponse.cloneWithData(list);
        });
    }

    @Override
    public RefreshController<Object> createRefreshController() {
        return new RefreshController<Object>(this) {
            @Override
            public void initViews(View rootView) {
                super.initViews(rootView);
                setShowBottomToast(false);
                swipeToLoadLayout.setBackgroundColor(Color.TRANSPARENT);
                recyclerView.addOnScrollListener(mOnScrollListener = new RecyclerView.OnScrollListener() {

                    @Override
                    public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                        super.onScrolled(recyclerView, dx, dy);
                        HotelDetailActivity.this.onScrolled(dy);
                    }
                });
            }

            @Override
            public void onRefresh() {
//                super.onRefresh();
                queryHotelDetail(false);
            }

            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return null;
            }

            @Override
            protected int getFirstPageSizeOffset() {
                return mHotel != null ? 2 + super.getFirstPageSizeOffset() : super.getFirstPageSizeOffset();
            }

            @Override
            protected boolean loadDataAfterInitViews() {
                return false;
            }

            @Override
            protected boolean showRefreshingWhenLoadDataAfterInitViews() {
                return false;
            }
        };
    }

    /**
     * 查询酒店详情
     */
    private void queryHotelDetail(boolean isFirstLoad) {
        BaseSubscriber<Hotel> baseSubscriber;
        if (isFirstLoad) {
            baseSubscriber = new ProgressSubscriber<Hotel>(this) {
                @Override
                public void onSuccess(Hotel hotel) {
                    super.onSuccess(hotel);
                    onHotelSuccess(hotel);
                }

                @Override
                public void onFailure(Throwable e) {
                    super.onFailure(e);
                    onHotelFailure();
                }
            };
        } else {
            baseSubscriber = new NormalSubscriber<Hotel>(this) {
                @Override
                public void onSuccess(Hotel hotel) {
                    super.onSuccess(hotel);
                    onHotelSuccess(hotel);
                }

                @Override
                public void onFailure(Throwable e) {
                    super.onFailure(e);
                    onHotelFailure();
                }
            };
        }
        L00bangRequestManager2.getServiceInstance()
                .hotelDetail(hotelId)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(baseSubscriber);
    }

    private void onHotelSuccess(Hotel hotel) {
        mHotel = hotel;
        setHeaderView();
        getRefreshController().getListData(0);
    }

    private void onHotelFailure() {
        if (mHotel == null) {
            mHotel = new Hotel();
            mHotel.setInnId(hotelId);
            setHeaderView();
        }
        if (getRefreshController() != null && getRefreshController().getSwipeToLoadLayout() != null) {
            getRefreshController().getSwipeToLoadLayout().setRefreshing(false);
        }
    }

    /**
     * 酒店信息展示
     */
    private void setHeaderView() {
        if (getRefreshController() == null) {
            return;
        }
        mHotelDetailAdapter.setHotel(mHotel);
        mHotelDetailAdapter.setDate(startDate, endDate);
        if (mHotelDetailAdapter.getItemCount() == 0) {
            getRefreshController().getData().add(0, null);
            getRefreshController().getData().add(0, null);
            getRefreshController().getData().add(new EmptyInfo(R.drawable.ic_hotel_place_no_txt, R.string.txt_hotel_no_room));
            mHotelDetailAdapter.notifyItemRangeInserted(0, 3);
        } else {
            mHotelDetailAdapter.notifyItemRangeChanged(0, 2);
        }
    }

    /**
     * 列表滚动 标题栏UI变化
     */
    private void onScrolled(int offset) {
        mScrollDy += offset;
        int mTitleOffsetHeight = DeviceUtil.getScreenWidth() * 235 / 375;
        float radio;
        if (mScrollDy >= mTitleOffsetHeight) {
            radio = 1;
        } else {
            radio = (float) mScrollDy / mTitleOffsetHeight;
        }
        boolean isNormal = radio < 0.75;

        if (isNormal && Boolean.TRUE.equals(mLlTitle.getTag())) {
            return;
        } else if (!isNormal && Boolean.FALSE.equals(mLlTitle.getTag())) {
            return;
        }

        mLlTitle.setTag(isNormal);
        mLlTitle.setBackgroundColor(isNormal ? Color.TRANSPARENT : getResources().getColor(R.color.white));
        //状态栏字体颜色
        StatusBarUtils.setLightStatusBar(this, !isNormal);
        mTvTitle.setText(isNormal ? "" : mHotel.getInnName());
        mIvBack.setImageResource(isNormal ? R.drawable.ic_back_circle_with_hotel : R.drawable.ic_back);
        mIvCall.setImageResource(isNormal ? R.drawable.ic_call_circle : R.drawable.ic_hotel_call);
    }

    private void onRefresh() {
        if (mHotelDetailAdapter != null) {
            mHotelDetailAdapter.notifyItemRangeChanged(0, mHotelDetailAdapter.getItemCount());
        }
    }

    @Override
    protected void onDestroy() {
        if (mRecyclerView != null) {
            mRecyclerView.removeOnScrollListener(mOnScrollListener);
        }
        super.onDestroy();
        if (mUserInfoChangedHelper != null) {
            mUserInfoChangedHelper.unregister(getContext());
        }
    }

}
