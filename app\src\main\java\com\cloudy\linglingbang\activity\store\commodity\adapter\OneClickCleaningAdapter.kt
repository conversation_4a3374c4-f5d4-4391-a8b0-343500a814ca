package com.cloudy.linglingbang.activity.store.commodity.adapter

import android.content.Context
import android.view.View
import android.widget.CheckBox
import android.widget.ImageView
import butterknife.BindView
import butterknife.ButterKnife
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.app.util.timer.CountDownManager
import com.cloudy.linglingbang.app.widget.banner.AdRoundImageView
import com.cloudy.linglingbang.app.widget.banner.AutoResizeRelativeLayout
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder
import com.cloudy.linglingbang.model.store.commodity.CartCommodity

/**
 * 一键清理的adapter
 *
 * <AUTHOR>
 * @date 2022/10/18
 */
class OneClickCleaningAdapter(context: Context?, data: List<CartCommodity>?) :
    BaseRecyclerViewAdapter<CartCommodity>(context, data) {
    override fun createViewHolder(itemView: View?): BaseRecyclerViewHolder<CartCommodity> {
        return OneClickCleanViewHolder(itemView)
    }

    override fun getItemLayoutRes(viewType: Int): Int {
        return R.layout.item_one_click_clean
    }

    /**
     * 选择项点击监听
     */
    var checkedOrCancel: () -> Unit = {}


    inner class OneClickCleanViewHolder(itemView: View?) :
        BaseRecyclerViewHolder<CartCommodity>(itemView) {

        @JvmField
        @BindView(R.id.iv_sku_pic)
        var ivSkuPic: AdRoundImageView? = null

        @JvmField
        @BindView(R.id.cb_check_sku)
        var cbCheckSku: CheckBox? = null

        @JvmField
        @BindView(R.id.root_view)
        var rootView: AutoResizeRelativeLayout? = null


        override fun initItemView(itemView: View) {
            super.initItemView(itemView)
            ButterKnife.bind(this, itemView)
        }

        override fun bindTo(cartCommodity: CartCommodity?, position: Int) {
            super.bindTo(cartCommodity, position)
            cartCommodity?.apply {
                ivSkuPic?.createImageLoad(skuImage)?.load()
                cbCheckSku?.let {
                    it.isChecked = isCheckForOneClean
                    rootView?.setOnClickListener { _ ->
                        it.isChecked = !it.isChecked
                        isCheckForOneClean = it.isChecked
                        checkedOrCancel()
                    }
                }


            }
        }


    }


    fun setCheckedOrCancelListener(e: () -> Unit) {
        this.checkedOrCancel = e
    }


}