package com.cloudy.linglingbang.activity.shortvideo;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.text.InputFilter;
import android.text.TextUtils;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.alibaba.sdk.android.vod.upload.VODSVideoUploadClient;
import com.alibaba.sdk.android.vod.upload.VODSVideoUploadClientImpl;
import com.baidu.mapapi.search.core.PoiInfo;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.BitmapImageViewTarget;
import com.bumptech.glide.request.transition.Transition;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.community.CommunityDoPostImageTextActivity;
import com.cloudy.linglingbang.activity.community.CommunityDoPostSuccessActivity;
import com.cloudy.linglingbang.activity.community.HotTopicListActivity;
import com.cloudy.linglingbang.app.imageConfig.GlideApp;
import com.cloudy.linglingbang.app.imageConfig.RoundedCornersTransformation;
import com.cloudy.linglingbang.app.util.ClearCacheUtil;
import com.cloudy.linglingbang.app.util.FileUtil;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.InputLengthLimitFilter;
import com.cloudy.linglingbang.app.util.NetworkUtil;
import com.cloudy.linglingbang.app.util.PreferenceUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.TopicUtils;
import com.cloudy.linglingbang.app.util.ViewHolderUtils;
import com.cloudy.linglingbang.app.util.file.FileUtils;
import com.cloudy.linglingbang.app.widget.ExpressionEditText;
import com.cloudy.linglingbang.app.widget.UploadProgressDialog;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog;
import com.cloudy.linglingbang.model.ImageModel;
import com.cloudy.linglingbang.model.community.ContentGroupInfo;
import com.cloudy.linglingbang.model.community.Topic;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.postcard.PostCardItem;
import com.cloudy.linglingbang.model.postcard.PostCardManager;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.shortvideo.AliSTSTokenInfo;
import com.donkingliang.imageselector.utils.ImageSelector;
import com.google.gson.Gson;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import butterknife.BindView;
import butterknife.OnClick;

import static com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2.getServiceInstance;

/**
 * 社区发视频帖页面
 *
 * <AUTHOR>
 * @date 2017/12/28
 */

public class CommunityDoPostShortVideoActivity extends CommunityDoPostImageTextActivity {

    public static final int REQUEST_CODE_SELECT_TOPIC = 10;
    public static final int REQUEST_CODE_SELECT_COVER_IMAGE = 12;

    /**
     * 添加视频
     */
    @BindView(R.id.rl_add_video)
    RelativeLayout mRlAddVideo;

    /**
     * 内容文本
     */
    @BindView(R.id.et_text)
    ExpressionEditText mEtText;

    /**
     * 视频封面
     */
    @BindView(R.id.rl_pic)
    RelativeLayout mRlPic;

    /**
     * 视频封面图
     */
    @BindView(R.id.iv_pic)
    ImageView mIvPic;

    /**
     * 视频删除图标
     */
    @BindView(R.id.delete_icon)
    ImageView mDeleteIcon;

    /**
     * 标题
     */
    @BindView(R.id.et_image_text_post_title)
    EditText mEtImageTextPostTitle;

    /**
     * 选择车型
     */
    @BindView(R.id.tv_car)
    TextView mTvCar;
    /**
     * 话题
     */
    @BindView(R.id.tv_topic)
    TextView mTvTopic;
    private Topic mTopic;

    private UploadProgressDialog mUploadProgressDialog;
    /**
     * 是否已经提交
     */
    private boolean isCommit = false;
    /**
     * 是否已经添加视频
     */
    private boolean hasAddVideo = false;
    /**
     * 是否已经上传视频
     */
    private boolean hasPostVideo = false;
    /**
     * 是否已经上传图片
     */
    private boolean hasUpdatePostCover = false;
    /**
     * 封面图的路径
     */
    private String mCoverUrl;
    private String videoPath;
    private String coverImagePath;
    private String mVideoId;
    private VODSVideoUploadClient vodsVideoUploadClient;//视频上传工具
    /**
     * 压缩之后的图片
     */
    private ImageModel mImageModel;
    /**
     * 标识视频被删除过，以便返回的时候不关闭该页面
     */
    private boolean mIsDelete;

    /**
     * 标题最大字数
     */
    private final static int MAX_TITLE_COUNT = 15;

    @Override
    public void loadViewLayout() {
        setContentView(R.layout.activity_community_do_post_short_video);
    }

    @Override
    public void initialize() {
        mIntentExtra = (IntentExtra) getIntentExtra(null);
        if (mIntentExtra == null) {
            onIntentExtraError();
            return;
        }
        mOpenFrom = mIntentExtra.getOpenFrom();
        expression_view_pager.setEditText(mEtText);
        mUploadProgressDialog = new UploadProgressDialog(this, R.style.Dialog);
        //初始化VodsVideoUploadClient
        vodsVideoUploadClient = new VODSVideoUploadClientImpl(this.getApplicationContext());
        vodsVideoUploadClient.init();
        //因为视频贴不支持编辑，所以写死了视频贴类型
        mPostType = PostCard.PostType.SHORT_VIDEO;
        setLeftTitle(getString(R.string.title_post_type_short_video));
        //展示图文帖标题
        showImageTextPostTitle();
        //产品设计，自动跳转到选择视频页面
//        onClickCamera();
        mEtText.setFilters(new InputFilter[]{new InputLengthLimitFilter(60, true, getString(R.string.post_detail_input_reply_max_length, 60))});
        setLabelViews();
        showLocation();
    }

    @Override
    public int getTitleMaxLength() {
        return MAX_TITLE_COUNT;
    }

    /**
     * 进入发视频帖页面方法
     */
    public static void startActivity(Context context, int postType, String communityId, int jumpAfterSuccess, int openFrom) {
        IntentExtra intentExtra = new IntentExtra(postType, communityId, null);
        intentExtra.setJumpAfterSuccess(jumpAfterSuccess);
        intentExtra.setOpenFrom(openFrom);
        IntentUtils.startActivity(context, CommunityDoPostShortVideoActivity.class, intentExtra);
    }

    public static void startActivity(Context context, IntentExtra intentExtra) {
        IntentUtils.startActivity(context, CommunityDoPostShortVideoActivity.class, intentExtra);
    }

    /**
     * 进入发视频帖页面方法
     */
    public static void startActivity(Context context, int postType, String communityId, String communityName, ContentGroupInfo contentGroupInfo, int jumpAfterSuccess, int openFrom) {
        IntentExtra intentExtra = new IntentExtra(postType, communityId, null);
        intentExtra.setJumpAfterSuccess(jumpAfterSuccess);
        intentExtra.setOpenFrom(openFrom);
        intentExtra.setGroupInfo(contentGroupInfo);
        intentExtra.setChannelName(communityName);
        IntentUtils.startActivity(context, CommunityDoPostShortVideoActivity.class, intentExtra);
    }

    @Override
    protected boolean needSaveDraft() {
        return false;
    }

    /**
     * 点击拍摄按钮
     */
    @OnClick(R.id.rl_add_video)
    @Override
    protected void onClickCamera() {
        //申请相机、录音、文件读写权限
        ShortVideoUtil.requestVideoPermissions(this, ShortVideoUtil.PERMISSION_CODE_CROP_VIDEO);
    }

    /**
     * 删除视频贴
     */
    @OnClick(R.id.delete_icon)
    protected void onDeleteClick(View view) {
        hasAddVideo = false;
        mIsDelete = true;
        mRlPic.setVisibility(View.GONE);
        mRlAddVideo.setVisibility(View.VISIBLE);
        clearVideo();
    }

    @BindView(R.id.btn_delete)
    TextView mBtnDeleteCover;
    @BindView(R.id.iv_select_image)
    TextView mBtnSelectCover;

    /**
     * 删除封面
     */
    @OnClick(R.id.btn_delete)
    protected void onDeleteCoverClick(View view) {
        mIvPic.setImageDrawable(null);
        loadCoverByVideo();
        view.setVisibility(View.GONE);
        mBtnSelectCover.setText("+添加封面");
    }

    /**
     * 修改或者添加封面
     */
    @OnClick(R.id.iv_select_image)
    protected void onSelectCoverClick(View view) {
        ImageSelector.builder()
                .useCamera(false)
                .setSingle(true)
                .canPreview(true)
                .start(this, REQUEST_CODE_SELECT_COVER_IMAGE);
    }

    /**
     * 点击播放
     */
    @OnClick(R.id.rl_pic)
    protected void onPlayClick() {
        if (mPostType == PostCard.PostType.SHORT_VIDEO && !TextUtils.isEmpty(videoPath)) {
            ShortVideoActivity.playLocalVideo(this, videoPath);
        }
    }

    @OnClick(R.id.tv_car)
    protected void onLabel() {
        //人车生活、好货互通
        /*if (mOpenFrom == OPEN_FROM_GOODS || mOpenFrom == OPEN_FROM_PEOPLE_CAR_LIFT) {
            long id = mIntentExtra.getGroupInfo() == null ? 0 : mIntentExtra.getGroupInfo().getContentGroupIdOrZero();
            CommunityPostChooseLabelActivity.startActivity(this, mIntentExtra.getSeries(), id, REQUEST_CODE_CHOOSE_TYPE_LABEL);
        } */
        //现在发帖是图文贴展示标签且可以选择，好货贴是在进入发帖之前已经指定了标签且不展示
        if (mOpenFrom == OPEN_FROM_CAR_TYPE) {
            //车型论坛
            getLabelList();
//            SensorsUtils.sensorsClickBtn("点击发长文-选择车型", "长文发布页", "选择车型");
        }
    }

    @Override
    protected void showCar() {
//        super.showCar();
        if (mIntentExtra.getGroupInfo() != null) {
            mTvCar.setText(mIntentExtra.getGroupInfo().getContentGroupName());
        }
    }

    /**
     * 选择话题
     */
    @OnClick(R.id.tv_topic)
    protected void onTopicClick() {
//        SensorsUtils.sensorsClickBtn("点击图文发布页-话题", "图文发布页", "图文发布页");
        IntentUtils.startActivityForResult(this, HotTopicListActivity.class, REQUEST_CODE_SELECT_TOPIC);
    }

    @Override
    protected void onClickMenu() {
        //判断是否是wifi环境
        if (NetworkUtil.getNetWorkStates(CommunityDoPostShortVideoActivity.this) != NetworkUtil.TYPE_WIFI) {
            //非WIFI提醒
            //弹出弹窗询问用户
            new CommonAlertDialog(CommunityDoPostShortVideoActivity.this, R.string.wifi_upload_confirm, R.string.upload_start, R.string.upload_stop, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    CommunityDoPostShortVideoActivity.super.onClickMenu();
                }
            }, null).show();
        } else {
            CommunityDoPostShortVideoActivity.super.onClickMenu();
        }
    }

    /**
     * 发帖提交操作
     */
    @Override
    protected void doPost() {
        if (isCommit) {
            ToastUtil.showMessage(CommunityDoPostShortVideoActivity.this, getString(R.string.community_do_post_toast_is_committing));
            return;
        }
        if (!checkCommitInput()) {
            return;
        }
        //检查网络是否可用
        if (NetworkUtil.isNetworkAvailable(CommunityDoPostShortVideoActivity.this)) {
            isCommit = true;
            if (hasPostVideo) {
                if (hasUpdatePostCover) {
                    //直接发帖
                    commit(getPostCardForCommit());
                } else {
                    //先上传图片
                    PreprocessorCompress();
                }

            } else {
                uploadShortVideo();
            }
        } else {
            ToastUtil.showMessage(CommunityDoPostShortVideoActivity.this, getString(R.string.common_network_unavailable));
        }
    }

    /**
     * 提交前检查填写信息
     */
    @Override
    protected boolean checkCommitInput() {
        if (!hasAddVideo) {
            ToastUtil.showMessage(this, getString(R.string.add_video_please));
            return false;
        }
        /*if (TextUtils.isEmpty(mIntentExtra.getChannelId())) {
            ToastUtil.showMessage(this, getString(R.string.community_do_post_toast_no_channel_id));
            return false;
        }*/
        if (mEtImageTextPostTitle.getText() == null || TextUtils.isEmpty(mEtImageTextPostTitle.getText().toString())) {
            ToastUtil.showMessage(this, getString(R.string.add_title_please));
            return false;
        }
        if (mEtText.getText() == null || TextUtils.isEmpty(mEtText.getText().toString())) {
            ToastUtil.showMessage(this, getString(R.string.add_content_please));
            return false;
        }
        if (mEtText.getText().toString().trim().length() < 10) {
            ToastUtil.showMessage(this, getString(R.string.vidio_content_short));
            return false;
        }

        return checkCommitMessage();
    }

    /**
     * 上传短视频
     */
    private void uploadShortVideo() {
        //获取短视频上传凭证
        ShortVideoUtil.getAliSTSToken(this, new ShortVideoUtil.STSTokenCallback() {
            @Override
            public void onSuccess(AliSTSTokenInfo tokenInfo) {
                mUploadProgressDialog = new UploadProgressDialog(CommunityDoPostShortVideoActivity.this, R.style.Dialog);
                mUploadProgressDialog.show();
                mUploadProgressDialog.setCallBackListener(new UploadProgressDialog.OnListener() {
                    @Override
                    public void execute() {
                        if (vodsVideoUploadClient != null) {
                            vodsVideoUploadClient.cancel();
                            isCommit = false;
                            mUploadProgressDialog.dismiss();
                            ToastUtil.showMessage(CommunityDoPostShortVideoActivity.this, R.string.upload_short_video_stop);
                        }
                    }
                });
                //上传
                ShortVideoUtil
                        .uploadShortVideo(CommunityDoPostShortVideoActivity.this, vodsVideoUploadClient, videoPath, coverImagePath, tokenInfo,
                                new ShortVideoUtil.VideoUploadCallback() {
                                    @Override
                                    public void onUploadProgress(final long progress) {
                                        //子线程回调
                                        runOnUiThread(new Runnable() {
                                            @Override
                                            public void run() {
                                                //显示视频上传进度
                                                mUploadProgressDialog.setProgress(getString(R.string.update_loading_process, (int) progress), (int) progress);
                                            }
                                        });
                                    }

                                    @Override
                                    public void onUploadSucceed(String videoId, String imageUrl) {
                                        mVideoId = videoId;
                                        runOnUiThread(new Runnable() {
                                            @Override
                                            public void run() {
                                                mUploadProgressDialog.dismiss();
                                                hasPostVideo = true;
                                                //处理图文混排中的图片和文本
                                                PreprocessorCompress();
                                            }
                                        });
                                    }

                                    @Override
                                    public void onUploadFailed(String code, String message) {
                                        isCommit = false;
                                        runOnUiThread(new Runnable() {
                                            @Override
                                            public void run() {
                                                mUploadProgressDialog.dismiss();
                                            }
                                        });
                                    }

                                });
            }

            @Override
            public void onFailure(Throwable e) {
                ToastUtil.showMessage(CommunityDoPostShortVideoActivity.this, getString(R.string.get_sts_failed));
                isCommit = false;
            }
        });
    }

    /**
     * 预处理压缩的图片
     */
    private void PreprocessorCompress() {
        ArrayList<String> preCompress = new ArrayList<>();
        if (coverImagePath != null && mCoverUrl == null) {//添加地址到集合，供下一步压缩
            if (isExistFIle(coverImagePath)) {//如果图片存在，则添加到预处理文件中
                preCompress.add(coverImagePath);
            }
        }
        if (preCompress.size() > 0) {
            //压缩图片
            compressPic(preCompress);
        }
    }

    /**
     * 图片上传成功之后执行的操作
     */
    @Override
    protected void onUploadPicSuccess(ArrayList<String> ImageList, List<ImageModel> imageModels) {

        if (mOpenFrom == OPEN_FROM_GOODS && mGoodsCoverImagePath != null && isExistFIle(mGoodsCoverImagePath)) {
            // 开始发帖
            //视频贴只有一张封面图，只取第一张即可
            if (ImageList != null && ImageList.size() > 0 && imageModels.size() > 0) {//如果是多张图
                mGoodsCoverImagePath = "";
//                mCoverUrl = ImageList.get(0);
                ImageModel mCoverModel = imageModels.get(0);
                mGoodsCoverImg = new PostCardItem();
                mGoodsCoverImg.setImg(ImageList.get(0));
                mGoodsCoverImg.setPath(mCoverModel.getPath());
                mGoodsCoverImg.setHeight(String.valueOf(mCoverModel.getHeight()));
                mGoodsCoverImg.setWidth(String.valueOf(mCoverModel.getWidth()));
                isCommit = false;
                doPost();
            }
            return;
        }

        // 开始发帖
        //视频贴只有一张封面图，只取第一张即可
        if (ImageList != null && ImageList.size() > 0 && imageModels.size() > 0) {//如果是多张图
            //将返回的值依次赋值给listItem
            hasUpdatePostCover = true;
            mCoverUrl = ImageList.get(0);
            this.mImageModel = imageModels.get(0);
            commit(getPostCardForCommit());
        }
    }

    /**
     * 获取要提交的postCard对象
     */
    private PostCard getPostCardForCommit() {
        PostCard postCard = new PostCard();
        PostCardItem postCardItem = new PostCardItem();
        postCardItem.setImg(mCoverUrl);
        postCardItem.setText(mEtText.getText().toString());
        postCardItem.setHeight(String.valueOf(mImageModel.getHeight()));
        postCardItem.setWidth(String.valueOf(mImageModel.getWidth()));
        postCardItem.setVideoId(mVideoId);
        List<PostCardItem> postCardItems = new ArrayList<>();
        postCardItems.add(postCardItem);
        postCard.setImgTexts(postCardItems);
        postCard.setPostTitle(mEtImageTextPostTitle.getText().toString());
        postCard.setLocalColumnId(mLocalColumnId);
        postCard.setChannelId(mIntentExtra.getChannelId());
        postCard.setPostTypeId(String.valueOf(mPostType));
        setPostCardParam(postCard);
        return postCard;
    }

    /**
     * 重写点击返回时检查是否有内容的方法
     */
    @Override
    protected boolean checkBackInput() {
        return hasAddVideo || !TextUtils.isEmpty(mEtText.getText().toString()) || !TextUtils.isEmpty(mEtImageTextPostTitle.getText().toString());
    }

    @Override
    protected void onExit() {
        super.onExit();
        if (hasAddVideo) {
            clearVideo();
        }
    }

    /**
     * 提交问题
     */
    @Override
    protected void commit(final PostCard postCard) {
        if (mIntentExtra.getPostType() == PostCard.PostType.SHORT_VIDEO) {
            if (poiInfo != null) {
                postCard.setLocationName(poiInfo.getName());
                postCard.setLocationDetail(poiInfo.getAddress());
                postCard.setLatitude(poiInfo.location.latitude);
                postCard.setLongitude(poiInfo.location.longitude);
            }
            if (mTopic != null) {
                postCard.setTopicId(mTopic.getTopicId());
                postCard.setTopicName(mTopic.getTopicName());
            }
            //发帖操作
            L00bangRequestManager2
                    .setSchedulers(getServiceInstance().pubPostV3(postCard))
                    .subscribe(new ProgressSubscriber<PostCard>(CommunityDoPostShortVideoActivity.this) {
                        @Override
                        public void onSuccess(PostCard postCard1) {
                            super.onSuccess(postCard);
                            isCommit = false;
                            PostCardManager.getInstance().getUploadImgs().clear();
                            //如果是从社区进来的
                            /*if (mIntentExtra.isJumpAfterSuccess() == 1) {
                                Intent intent = new Intent(CommunityDoPostShortVideoActivity.this, CommunityDetailActivity.class);
                                intent.putExtra("channelId", mIntentExtra.getChannelId());
                                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                                startActivity(intent);
                            } else if (mIntentExtra.isJumpAfterSuccess() == 2) {
                                //如果是从车友会进来的
                                try {
                                    Intent intent = new Intent(CommunityDoPostShortVideoActivity.this, CarClubDetailActivity.class);
                                    Long channelId = Long.valueOf(mIntentExtra.getChannelId());
                                    intent.putExtra("channelId", channelId);
                                    intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                                    startActivity(intent);
                                } catch (NumberFormatException e) {
                                    e.printStackTrace();
                                }
                            }*/
                            // 跳转选择标签页
                            if (mOpenFrom != OPEN_FROM_GOODS && mOpenFrom != OPEN_FROM_CAR_TYPE && mOpenFrom != OPEN_FROM_PEOPLE_CAR_LIFT) {
                                IntentUtils.startActivity(CommunityDoPostShortVideoActivity.this, CommunityDoPostSuccessActivity.class, postCard1.getPostId());
                            }
                            ToastUtil.showMessage(CommunityDoPostShortVideoActivity.this,
                                    mOpenFrom == OPEN_FROM_GOODS || mOpenFrom == OPEN_FROM_CAR_TYPE || mOpenFrom == OPEN_FROM_PEOPLE_CAR_LIFT ? R.string.community_do_post_post_success_goods : R.string.community_do_post_post_success);
                            //清除视频
                            clearVideo();
                            onRequestCompleted();
                            finish();
                        }

                        @Override
                        public void onFailure(Throwable e) {
                            super.onFailure(e);
                            isCommit = false;
                        }
                    });
        }
    }

    /**
     * 清除视频文件
     */
    private void clearVideo() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                ClearCacheUtil.deleteFileRecursively(new File(videoPath), false);
                //删除封面
                ClearCacheUtil.deleteFileRecursively(new File(coverImagePath), false);
            }
        }).start();
    }

    @Override
    protected void onPermissionResult(boolean isGranted, int requestCode) {
        super.onPermissionResult(isGranted, requestCode);
        if (isGranted) {
            if (requestCode == ShortVideoUtil.PERMISSION_CODE_CROP_VIDEO) {
                ShortVideoUtil.cropVideo(this);
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        //剪辑视频返回activityResult
        if (requestCode == ShortVideoUtil.REQUEST_FOR_RESULT_CROP_VIDEO) {
            if (resultCode == RESULT_CANCELED) {
                //如果没有删除过视频，则说明是第一次进入，则直接关闭，因为第一次进入的时候直接打开视频选择页面
                if (!mIsDelete) {
                    finish();
                }
                return;
            }
            videoPath = ShortVideoUtil.onActivityResult(requestCode, resultCode, data);
            loadCoverByVideo();
        } else if (requestCode == REQUEST_CODE_SELECT_TOPIC && resultCode == RESULT_OK) {
            mTopic = (Topic) IntentUtils.getExtra(data, null);
            ViewHolderUtils.setVisibility(mTopic != null, tv_topic_name);
            if (mTopic != null) {
                ViewHolderUtils.setText(TopicUtils.getNameWithHashtag(mTopic.getTopicName()), tv_topic_name);
            }
        }
        if (requestCode == REQUEST_CODE_SELECT_COVER_IMAGE && resultCode == Activity.RESULT_OK && data != null) {
            List<String> images = data.getStringArrayListExtra(ImageSelector.SELECT_RESULT);
            if (images.isEmpty()) {
                return;
            }
            ShortVideoUtil.deleteTempFile();
            coverImagePath = images.get(0);
            String tempPath = getExternalCacheDir() + "/" + System.currentTimeMillis();
            FileUtil.copyFile(coverImagePath, tempPath);
            coverImagePath = tempPath;

            hasAddVideo = true;
            Uri uri = FileUtils.pathToUri(coverImagePath);
            new ImageLoad(this, mIvPic, uri)
                    .setPlaceholderAndError(R.drawable.ic_common_place_holder)
                    .setScaleType(ImageView.ScaleType.CENTER_CROP)
                    .setCircle(true)
                    .setRadius(getResources().getDimensionPixelSize(R.dimen.normal_4))
                    .load();
            mBtnSelectCover.setText("修改封面");
            mBtnDeleteCover.setVisibility(View.VISIBLE);
        }

    }

    private void loadCoverByVideo() {
        if (!TextUtils.isEmpty(videoPath) && isExistFIle(videoPath)) {
            //取视频第一帧作为封面
            Bitmap videoCover = ShortVideoUtil.getVideoCover(videoPath);
            ShortVideoUtil.deleteTempFile();
            coverImagePath = ShortVideoUtil.saveVideoCoverToFile(videoCover);
            hasAddVideo = true;
            //更新状态
            mRlPic.setVisibility(View.VISIBLE);
            if (TextUtils.isEmpty(coverImagePath) || !isExistFIle(coverImagePath)) {
                GlideApp.with(this)
                        .asBitmap()
                        .load(videoPath)
                        .apply(RequestOptions.frameOf(0)
                                .transform(new CenterCrop(), new RoundedCornersTransformation(getResources().getDimensionPixelSize(R.dimen.normal_4)))
                                .skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE))
                        .into(new BitmapImageViewTarget(mIvPic) {
                            @Override
                            public void onResourceReady(@NonNull Bitmap resource, @Nullable Transition<? super Bitmap> transition) {
                                super.onResourceReady(resource, transition);
                                ShortVideoUtil.deleteTempFile();
                                coverImagePath = ShortVideoUtil.saveVideoCoverToFile(resource);
                            }
                        });
            } else {
                Uri uri = FileUtils.pathToUri(coverImagePath);
                new ImageLoad(this, mIvPic, uri)
                        .setPlaceholderAndError(R.drawable.ic_common_place_holder)
                        .setScaleType(ImageView.ScaleType.CENTER_CROP)
                        .setCircle(true)
                        .setRadius(getResources().getDimensionPixelSize(R.dimen.normal_4))
                        .load();
            }
            hasUpdatePostCover = false;
            mBtnSelectCover.setText("+添加封面");
            mBtnDeleteCover.setVisibility(View.GONE);
            mRlAddVideo.setVisibility(View.GONE);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (vodsVideoUploadClient != null && isCommit) {
            //上传暂停和恢复需要成对出现
            vodsVideoUploadClient.pause();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (vodsVideoUploadClient != null) {
            //上传暂停和恢复需要成对出现
            vodsVideoUploadClient.resume();
        }
    }

    @Override
    protected void onDestroy() {
        try {
            //退出时方式progress报错
            if (mUploadProgressDialog != null) {
                if (mUploadProgressDialog.isShowing()) {
                    mUploadProgressDialog.dismiss();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (vodsVideoUploadClient != null) {
            vodsVideoUploadClient.release();
        }
        super.onDestroy();
    }

    @Override
    protected void hideKeyboard() {
        super.hideKeyboard();
        if (mEtText != null) {
            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            if (imm != null) {
                imm.hideSoftInputFromWindow(mEtText.getWindowToken(), 0);
            }
        }
    }

    @Override
    protected void showKeyBoard() {
        super.showKeyBoard();
        if (mEtText != null) {
            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            if (imm != null) {
                imm.showSoftInput(mEtText, InputMethodManager.SHOW_FORCED);
            }
        }
    }

    @BindView(R.id.tv_location)
    protected TextView mTvLocation;

    @Override
    protected void showLocation() {
//        super.showLocation();
        if (mTvLocation == null) {
            return;
        }
        mTvLocation.setVisibility(View.GONE);
        mTvLocation.setOnClickListener(null);
        if (mOpenFrom == OPEN_FROM_CAR_TYPE || mIntentExtra.getOpenFrom() == OPEN_FROM_GOODS) {
            if ((mPostType == PostCard.PostType.IMAGE_TEXT || mPostType == PostCard.PostType.SHORT_VIDEO || mPostType == PostCard.PostType.SHARE_GAME)) {
                mTvLocation.setText("添加位置");
                mTvLocation.setVisibility(View.VISIBLE);
                String postTitle = et_post_title.getText().toString();
                if (!TextUtils.isEmpty(postTitle)) {
                    String json = PreferenceUtil.getStringPreference(this, POI_INFO_KEY, null);
                    if (!TextUtils.isEmpty(json)) {
                        poiInfo = new Gson().fromJson(json, PoiInfo.class);
                        mTvLocation.setText(poiInfo.getName());
                        return;
                    }
                }
                mTvLocation.setOnClickListener(this::selectLocation);
            }
        }
    }
}
