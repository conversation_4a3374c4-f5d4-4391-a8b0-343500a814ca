package com.cloudy.linglingbang.activity.fragment.store.hotel;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.View;
import android.view.inputmethod.EditorInfo;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.InputMethodUtils;
import com.cloudy.linglingbang.app.util.LocationHelper;
import com.cloudy.linglingbang.app.util.baiduMap.CoordinateTransform;
import com.cloudy.linglingbang.app.widget.ClearEditText;
import com.cloudy.linglingbang.model.hotel.HotelList;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import butterknife.OnClick;
import io.reactivex.rxjava3.core.Observable;

/**
 * 酒店搜索结果页面
 *
 * <AUTHOR>
 * @date 2022/3/16
 */
public class HolderSearchResultActivity extends BaseRecyclerViewRefreshActivity<HotelList> {
    @BindView(R.id.et_content)
    ClearEditText et_content;
    /**
     * 城市code
     */
    private String cityCode = null;
    /**
     * 入住时间
     */
    private long startDate;
    /**
     * 离店时间
     */
    private long endDate;
    /**
     * 搜索关键字
     */
    private String keyWord;

    public static void startActivity(Context context, long startDate, long endDate, String cityCode) {
        Intent intent = new Intent(context, HolderSearchResultActivity.class);
        intent.putExtra("startDate", startDate);
        intent.putExtra("endDate", endDate);
        intent.putExtra("cityCode", cityCode);
        context.startActivity(intent);
    }

    @Override
    protected void loadViewLayout() {
        super.loadViewLayout();
        setContentView(R.layout.activity_hotel_search_result);
    }

    @Override
    protected void initialize() {
        super.initialize();
        Intent intent = getIntent();
        startDate = intent.getLongExtra("startDate", 0);
        endDate = intent.getLongExtra("endDate", 0);
        cityCode = intent.getStringExtra("cityCode");
        if (startDate == 0 || endDate == 0) {
            onIntentExtraError();
            return;
        }
        et_content.setHint(R.string.txt_hotel_search_hint);
        et_content.setImeOptions(EditorInfo.IME_ACTION_SEARCH);
        et_content.setOnEditorActionListener((v, actionId, event) -> {
            boolean handled = false;
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                checkAndSearch();
                handled = true;
            }
            return handled;
        });
        InputMethodUtils.showInputMethodByPostDelayed(et_content);
    }

    /**
     * 点击搜索
     */
    private void checkAndSearch() {
        String content = et_content.getText().toString().trim();
        if (TextUtils.isEmpty(content)) {
            return;
        }
        keyWord = content;
        InputMethodUtils.hideInputMethod(this, et_content);
        getRefreshController().onRefresh();
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<HotelList> list) {
        HotelAdapter adapter = new HotelAdapter(this, list);
        adapter.setOnItemClickListener((itemView, position) -> HotelDetailActivity.startActivity(itemView.getContext(), list.get(position).getInnId(), startDate, endDate));
        return adapter;
    }

    @Override
    public Observable<BaseResponse<List<HotelList>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        Map<String, String> map = new HashMap<>();
        map.put("pageNum", String.valueOf(pageNo));
        map.put("pageSize", String.valueOf(pageSize));

        map.put("cityCode", cityCode);
        map.put("hotelName", keyWord);

        map.put("mapType", "0");//地图类型 0 百度 1 google 2 腾讯 3 高德
        LocationHelper.LocationEntity locationEntity = LocationHelper.getInstance().getLastLocation();
        if (!TextUtils.isEmpty(locationEntity.cityName)) {
            double[] ls = CoordinateTransform.transformGCJ02ToBD09(locationEntity.longitude, locationEntity.latitude);
            map.put("lng", String.valueOf(ls[0]));
            map.put("lag", String.valueOf(ls[1]));
        }

        map.put("dtArrorig", AppUtil.formatDate(startDate, "yyyy-MM-dd"));
        map.put("dtDeporig", AppUtil.formatDate(endDate, "yyyy-MM-dd"));
        return service2.hotelPage(map);
    }

    @Override
    public RefreshController<HotelList> createRefreshController() {
        return new RefreshController<HotelList>(this) {
            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return null;
            }

            @Override
            protected boolean loadDataAfterInitViews() {
                return false;
            }

            @Override
            public void initViews(View rootView) {
                super.initViews(rootView);
                recyclerView.setBackgroundColor(Color.TRANSPARENT);
            }

            @Override
            public String getEmptyString() {
                return getContext().getString(R.string.empty_today);
            }

            @Override
            public int getEmptyImageResId() {
                return R.drawable.ic_hotel_place_no_txt;
            }

            @Override
            public int getErrorImageResId() {
                return R.drawable.ic_hotel_place_no_txt;
            }
        };
    }

    /**
     * 点击取消
     */
    @OnClick(R.id.tv_search)
    public void onClickCancelButton() {
        onBackPressed();
    }
}
