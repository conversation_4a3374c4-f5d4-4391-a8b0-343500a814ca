package com.cloudy.linglingbang.activity.car.home;

import android.content.Context;
import android.view.View;
import android.widget.LinearLayout;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.fragment.HomeFragmentManager;
import com.cloudy.linglingbang.activity.fragment.store.home.StoreHomeFragment;
import com.cloudy.linglingbang.activity.task.util.TaskNavigateUtils;
import com.cloudy.linglingbang.app.widget.MyCarCarListBannerView;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.server.Ad.Ad2;

import java.util.List;

/**
 * 我的爱车中部推荐广告
 *
 * <AUTHOR>
 * @date 2020-02-17
 */
public class MyCarRecommendViewHolder extends BaseRecyclerViewHolder<MyCarModel.AdListMiddle> {

    private LinearLayout mLinearLayout;

    private Context mContext;

    public MyCarRecommendViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        mLinearLayout = itemView.findViewById(R.id.ll_car_list);
        mContext = itemView.getContext();

    }

    @Override
    public void bindTo(MyCarModel.AdListMiddle adListMiddle, int position) {
        super.bindTo(adListMiddle, position);
        List<Ad2> ad2s = adListMiddle.getOriginal();
        mLinearLayout.removeAllViews();
        if (ad2s != null && ad2s.size() > 0) {
            for (final Ad2 ad2 : ad2s) {
                MyCarCarListBannerView myCarCarListBannerView = new MyCarCarListBannerView(mContext);
                myCarCarListBannerView.setPadding(mContext.getResources().getDimensionPixelSize(R.dimen.normal_24), 0, 0, 0);
                myCarCarListBannerView.setAd(ad2);
                mLinearLayout.addView(myCarCarListBannerView);
            }
        }
        //加载更多车辆
        MyCarCarListBannerView myCarCarListBannerView = new MyCarCarListBannerView(mContext);
        myCarCarListBannerView.setPadding(mContext.getResources().getDimensionPixelSize(R.dimen.normal_24), 0, mContext.getResources().getDimensionPixelSize(R.dimen.normal_24), 0);
        myCarCarListBannerView.setHeadResource(R.drawable.ic_my_car_more_car);
        myCarCarListBannerView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                TaskNavigateUtils.navigateToHome(mContext, HomeFragmentManager.INDEX_STORE, StoreHomeFragment.TYPE_CAR);
            }
        });
        mLinearLayout.addView(myCarCarListBannerView);
    }

}
