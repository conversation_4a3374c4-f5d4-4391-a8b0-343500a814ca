package com.cloudy.linglingbang.activity.community.linglab;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.app.widget.dialog.alert.BaseBottomAlertDialog;
import com.cloudy.linglingbang.app.widget.image.ImageLoadUtils;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

/**
 * 删除图片的弹窗
 *
 * <AUTHOR>
 * @date 2021/9/6
 */
class FullScreenImgDialog extends BaseBottomAlertDialog {

    private ViewPager mViewPager;
    private ImageView mIvBack;
    private TextView mTitle;

    private final List<String> mList = new ArrayList<>();
    private final int currentPosition;

    //private int imageH;

    public FullScreenImgDialog(Context context, List<String> list, int position) {
        super(context);
        mList.clear();
        for (int i = 0; i < list.size(); i++) {
            mList.add(list.get(i));
        }
        mList.remove(null);
        this.currentPosition = position;
    }


    @Override
    protected int getDefaultLayoutResId() {
        return R.layout.dialog_full_screen_img;
    }

    @Override
    protected void initView() {
        super.initView();
        mViewPager = findViewById(R.id.viewPager);
        mIvBack = findViewById(R.id.iv_back);
        mTitle = findViewById(R.id.tv_title);

        mTitle.setText(currentPosition + 1 + "/" + mList.size());
        ImgPagerAdapter imgPagerAdapter = new ImgPagerAdapter(mList);
        mViewPager.setAdapter(imgPagerAdapter);
        mViewPager.setCurrentItem(currentPosition);

        mIvBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dismiss();
            }
        });
        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                mTitle.setText(position + 1 + "/" + mList.size());
            }

            @Override
            public void onPageSelected(int position) {

            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
    }

    @Override
    protected void init(Context context) {
        super.init(context);

    }

    class ImgPagerAdapter extends PagerAdapter {

        private final List<String> mStringList;

        public ImgPagerAdapter(List<String> stringList) {
            mStringList = stringList;
        }

        public void removeItem(int position) {
            mStringList.remove(position);
            notifyDataSetChanged();

        }

        @Override
        public int getCount() {
            return mList.size();
        }

        @Override
        public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
            return view == object;
        }

        @NonNull
        @Override
        public Object instantiateItem(@NonNull ViewGroup container, int position) {
            View v = View.inflate(mContext, R.layout.layout_large_img, null);
            ImageView imageView = v.findViewById(R.id.iv_image);
            ImageLoadUtils.load(imageView, mStringList.get(position), R.drawable.ic_common_place_holder);

            ImageView mIvAdd = v.findViewById(R.id.iv_add_tag);
            mIvAdd.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    IntentUtils.startActivity(getContext(), ChooseTagListActivity.class);
                }
            });
            /*mIvAdd.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    TagInfoBean bean = new TagInfoBean();
                    bean.setCanMove(true);
                    bean.setNotesTagId(652);
                    bean.setNotesTagType(TagTextView.TAG_TEXT);
                    //通过手机中的图片地址  或者  网络拉取的图片信息  获得图片宽高
                    *//*bean.setPicWidth(1010);
                    bean.setPicHeight(1324);*//*
                    bean.setUrl("tag点的链接url");
                    //imageH = (int) (bean.getWidth() * 4 / 3);
                    // 显示控件的显示 依照图片的本身的宽高比例进行动态设置
                    bean.setWidth(DeviceUtil.getScreenWidth());
                    //标签在控件上的比例
                    bean.setLeft(true);
                    bean.setX(0.5);
                    bean.setY(0.5);
                    bean.setHeight(DeviceUtil.getScreenHeight());
                    ViewDialogFragment dialogFragment = new ViewDialogFragment();
                    dialogFragment.setCallback(new ViewDialogFragment.Callback() {
                        @Override
                        public void onClick(String tabName) {
                            if (TextUtils.isEmpty(tabName)) {
                                tabName = "女孩";
                            }
                            bean.setName(tabName);
                            Log.e("zz", "onClick: " + bean.getName() + "  " + bean.getHeight());

                            tagImageView.addTag(bean);
                        }
                    });
                    dialogFragment.show(((CommunityDoPostLingLabActivity) mContext).getSupportFragmentManager());
                }
            });*/

            /*tagImageView.setAddTagListener(new TagImageView.AddTagListener() {
                @Override
                public void addTag(String path, double rawX, double rawY) {
                    final TagInfoBean bean = new TagInfoBean();
                    bean.setCanMove(true);
                    bean.setNotesTagId(652);
                    bean.setNotesTagType(TagTextView.TAG_TEXT);
                    //通过手机中的图片地址  或者  网络拉取的图片信息  获得图片宽高
                    bean.setPicWidth(1010);
                    bean.setPicHeight(1324);
                    bean.setUrl("tag点的链接url");
                    imageH = imageH = (int) (bean.getWidth() * 4 / 3);
                    // 显示控件的显示 依照图片的本身的宽高比例进行动态设置
                    bean.setWidth(DeviceUtil.getScreenWidth());
                    //标签在控件上的比例
                    bean.setLeft(!(rawX > bean.getWidth()/ 2));
                    bean.setX(rawX / bean.getWidth());
                    bean.setY(rawY / imageH);
                    bean.setHeight(imageH);
                    ViewDialogFragment dialogFragment = new ViewDialogFragment();
                    dialogFragment.setCallback(new ViewDialogFragment.Callback() {
                        @Override
                        public void onClick(String tabName) {
                            if (TextUtils.isEmpty(tabName))
                                tabName = "女孩";
                            bean.setName(tabName);
                            Log.e("zz", "onClick: "+bean.getName() + "  " + imageH );


                            tagImageView.addTag(bean);
                        }
                    });
                    dialogFragment.show(((CommunityDoPostLingLabActivity)mContext).getSupportFragmentManager());
                }
            });*/
            container.addView(v);
            return v;
        }

        @Override
        public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
            container.removeView((View) object);
        }

        @Override
        public int getItemPosition(@NonNull Object object) {
            return POSITION_NONE;
        }
    }

}
