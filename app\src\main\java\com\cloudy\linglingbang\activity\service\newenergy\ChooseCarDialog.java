package com.cloudy.linglingbang.activity.service.newenergy;

import android.content.Context;
import android.content.DialogInterface;
import android.text.TextUtils;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.widget.dialog.alert.BaseAlertDialog;
import com.cloudy.linglingbang.model.server.NewEnergyCarInfo;

import java.util.List;

import kankan.wheel.widget.WheelView;
import kankan.wheel.widget.adapters.AbstractWheelTextAdapter;

/**
 * 选择车辆颜色的弹窗
 */
public class ChooseCarDialog extends BaseAlertDialog {

    private OnChooseColorListener mOnChooseColorListener;
    private WheelView mColorWheelView;

    private List<NewEnergyCarInfo> mCarBeanList;
    private String mCurrentVin;

    /**
     * @param context the context
     * @param title 标题，如果不为空将显示标题，隐藏取消按钮
     * @param onChooseColorListener 选择颜色点确认后的回调
     */
    public ChooseCarDialog(Context context, CharSequence title, List<NewEnergyCarInfo> carBeanList, String currentVin, OnChooseColorListener onChooseColorListener) {
        super(context, R.style.Dialog_bottom);
        if (!TextUtils.isEmpty(title)) {
            getAlertController().setTitle(title);
            getAlertController().setButton(DialogInterface.BUTTON_NEGATIVE, null, null);
        }
        mCarBeanList = carBeanList;
        mOnChooseColorListener = onChooseColorListener;
        mCurrentVin = currentVin;
    }

    @Override
    protected boolean isBottomDialog() {
        return true;
    }

    @Override
    protected int getDefaultLayoutResId() {
        return R.layout.dialog_car_color_picker;
    }

    @Override
    protected void initView() {
        super.initView();

        getAlertController().getButton(DialogInterface.BUTTON_POSITIVE).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnChooseColorListener != null) {
                    int index = mColorWheelView.getCurrentItem();
                    if (mCarBeanList != null && index >= 0 && index < mCarBeanList.size()) {
                        NewEnergyCarInfo newEnergyCarInfo = mCarBeanList.get(index);
                        mOnChooseColorListener.onChooseCar(newEnergyCarInfo);
                    }
                }
                dismiss();
            }
        });

        mColorWheelView = findViewById(R.id.car_color);
        ColorAdapter colorAdapter = new ColorAdapter(getContext());
        mColorWheelView.setViewAdapter(colorAdapter);
        int currentItem = 0;
        mColorWheelView.setCurrentItem(currentItem);
        if (mCurrentVin != null && mCarBeanList != null) {
            for (int i = 0; i < mCarBeanList.size(); i++) {
                if (mCurrentVin.equals(mCarBeanList.get(i).getVin())) {
                    currentItem = i;
                    mColorWheelView.setCurrentItem(currentItem);
                    return;
                }
            }
        }

        mColorWheelView.setDividerDrawable(mContext.getResources().getDrawable(R.drawable.wheel_color_divider));
    }

    /**
     * Adapter
     */
    private class ColorAdapter extends AbstractWheelTextAdapter {

        protected ColorAdapter(Context context) {
            super(context, R.layout.item_dialog_color_view, R.id.textView);
        }

        @Override
        protected CharSequence getItemText(int index) {
            if (mCarBeanList != null && index >= 0 && index < mCarBeanList.size()) {
                String vin = mCarBeanList.get(index).getVin();
                String vinStr;
                if (vin.length() > 4) {
                    vinStr = vin.substring(vin.length() - 4);
                } else {
                    vinStr = vin;
                }
                return mContext.getResources().getString(R.string.txt_car_name, mCarBeanList.get(index).getCarTypeName(), vinStr);
            } else {
                return null;
            }
        }

        @Override
        public int getItemsCount() {
            if (mCarBeanList == null) {
                return 0;
            } else {
                return mCarBeanList.size();
            }
        }
    }

    public interface OnChooseColorListener {
        /**
         * 选择颜色
         *
         * @param newEnergyCarInfo
         */
        void onChooseCar(NewEnergyCarInfo newEnergyCarInfo);
    }
}
