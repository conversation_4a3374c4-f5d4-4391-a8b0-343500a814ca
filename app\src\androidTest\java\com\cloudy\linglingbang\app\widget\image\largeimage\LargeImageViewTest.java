package com.cloudy.linglingbang.app.widget.image.largeimage;

import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.ScrollView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.cloudy.linglingbang.BaseInstrumentedTest;
import com.cloudy.linglingbang.activity.ScanImageActivity;
import com.cloudy.linglingbang.activity.store.CarStyleActivity;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.widget.image.largeimage.factory.FileBitmapDecoderFactory;

import java.io.File;

/**
 * 测试大图
 *
 * <AUTHOR>
 * @date 2018/10/30
 */
public class LargeImageViewTest extends BaseInstrumentedTest {
    @Override
    public void test() {
        super.test();
        LinearLayout linearLayout = new LinearLayout(getContext());
        linearLayout.setOrientation(LinearLayout.VERTICAL);
        getActivity().setContentView(linearLayout);

        addButton(linearLayout, "查看大图", new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                testScanImageActivity();
            }
        });

        addButton(linearLayout, "查看帖子", new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                testPostDetail();
            }
        });

        addButton(linearLayout, "查看车型", new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                testCarStyle();
            }
        });

        addButton(linearLayout, "LargeImageView", new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                testLargeImageView();
            }
        });

        addButton(linearLayout, "UpdateImageView", new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                testUpdateImageView();
            }
        });
    }

    private void addButton(LinearLayout linearLayout, String title, View.OnClickListener onClickListener) {
        Button button = new Button(getContext());
        button.setText(title);
        button.setOnClickListener(onClickListener);
        linearLayout.addView(button);
    }

    private void testScanImageActivity() {
        Intent intent = new Intent(getContext(), ScanImageActivity.class);
        String[] urls = {
                "https://dftest.00bang.cn/images/T1xEKTBjxT1RCvBVdK.jpg",
                "https://cdn-df.00bang.cn/images/T1NFATBTbv1RCvBVdK.jpg",
        };
        intent.putExtra("image_urls", urls);
        intent.putExtra("image_index", 0);
        getContext().startActivity(intent);
    }

    /**
     * 帖子详情的展示
     */
    private void testPostDetail() {
        JumpPageUtil.goToPost(getContext(), "142963");
    }

    /**
     * 车型
     */
    private void testCarStyle() {
        CarStyleActivity.startActivity(getContext(), 17L, -1L, "", "");
    }

    private void testUpdateImageView() {
        Context context = getContext();
        ScrollView scrollView = new ScrollView(context);
        getActivity().setContentView(scrollView);

        LinearLayout linearLayout = new LinearLayout(context);
        scrollView.addView(linearLayout);

        UpdateImageView updateImageView = new UpdateImageView(getActivity());
        linearLayout.addView(updateImageView, ViewGroup.LayoutParams.MATCH_PARENT, 10000);

        loadImage(updateImageView);
    }

    private void testLargeImageView() {
        FrameLayout frameLayout = new FrameLayout(getActivity());
        LargeImageView largeImageView = new LargeImageView(getActivity());
        frameLayout.addView(largeImageView);

        getActivity().setContentView(frameLayout);
        loadImage(largeImageView);
    }

    private void loadImage(final ILargeImageView largeImageView) {
        String image = "https://cdn-df.00bang.cn/images/T1NFATBTbv1RCvBVdK.jpg";
        //String image = "https://cdn-df.00bang.cn/images/T1sHKTBv_T1RCvBVdK.png";
        Glide.with(getActivity())
                .load(image)
                .downloadOnly(new SimpleTarget<File>() {
                    @Override
                    public void onResourceReady(File resource, Transition<? super File> glideAnimation) {
                        largeImageView.setImage(new FileBitmapDecoderFactory(resource));
                    }
                });
    }
}