package com.cloudy.linglingbang.activity.community;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.baidu.location.Address;
import com.baidu.mapapi.SDKInitializer;
import com.baidu.mapapi.model.LatLng;
import com.baidu.mapapi.search.core.PoiInfo;
import com.baidu.mapapi.search.geocode.GeoCodeResult;
import com.baidu.mapapi.search.geocode.GeoCoder;
import com.baidu.mapapi.search.geocode.OnGetGeoCoderResultListener;
import com.baidu.mapapi.search.geocode.ReverseGeoCodeOption;
import com.baidu.mapapi.search.geocode.ReverseGeoCodeResult;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.activity.basic.IRefreshContext;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.LocationHelper;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;

import java.util.ArrayList;
import java.util.List;

import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 发帖选择位置
 *
 * <AUTHOR>
 * @date 2/27/21
 */
public class SelectLocationActivity extends BaseRecyclerViewRefreshActivity<PoiInfo> {
    private LocationHelper.LocationEntity mLocationEntity;
    private GeoCoder mGeoCoder;
    private boolean isShowGpsDialog;
    private boolean isOpenGps;
    private LocationRefreshController mRefreshController;

    @Override
    protected void initialize() {
        super.initialize();
        setLeftTitle(getResources().getString(R.string.community_do_post_select_location));
        SDKInitializer.initialize(this.getApplicationContext());
        if (mGeoCoder == null) {
            mGeoCoder = GeoCoder.newInstance();
        }
        isOpenGps = AppUtil.isOpenLocationService(this);
        if (!isOpenGps) {
            CommonAlertDialog mGpsDialog = new CommonAlertDialog(this, R.string.txt_tip_start_gps, R.string.permission_btn_setting, R.string.permission_btn_cancel, new DialogInterface.OnClickListener() {

                @Override
                public void onClick(DialogInterface dialog, int which) {
                    Intent intent = new Intent(Settings.ACTION_SETTINGS);
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    if (intent.resolveActivity(getPackageManager()) != null) {
                        startActivity(intent);
                    }
                    isShowGpsDialog = true;
                    dialog.dismiss();
                }
            }, null);
            mGpsDialog.show();
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        isOpenGps = AppUtil.isOpenLocationService(this);
        if (mRefreshController != null) {
            mRefreshController.setRefreshEnable(isOpenGps);
        }
        if (isShowGpsDialog) {
            startLocation();
        }
    }

    /**
     * 定位，获取当前位置
     */
    private void startLocation() {
        if (AppUtil.isOpenLocationService(this)) {
            LocationHelper.getInstance().requestLocation(this, new LocationHelper.LocCallBack() {
                @Override
                public void onSuccess(LocationHelper.LocationEntity entity) {
                    mLocationEntity = entity;
                    if (getRefreshController() != null) {
                        getRefreshController().onRefresh();
                    }
                }

                @Override
                public void onError(String errMsg) {
                    showNoLocation();
                }
            });
            return;
        }
        showNoLocation();
    }

    /**
     * 定位失败处理
     */
    private void showNoLocation() {
        if (getRefreshController() != null) {
            getRefreshController().getData().clear();
            getRefreshController().getData().add(null);
            getRefreshController().getSwipeToLoadLayout().setRefreshing(false);
            getRefreshController().getAdapter().notifyDataSetChanged();
        }
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<PoiInfo> list) {
        BaiduGeoCodeAdapter adapter = new BaiduGeoCodeAdapter(this, list);
        adapter.setOnItemClickListener((itemView, position) -> {
            List<PoiInfo> d = adapter.getData();
            if (d == null || d.size() <= position) {
                return;
            }
            Intent intent = new Intent();
            intent.putExtra(IntentUtils.INTENT_EXTRA_COMMON, d.get(position));
            setResult(Activity.RESULT_OK, intent);
            finish();
        });
        return adapter;
    }

    @Override
    protected void onBack() {
        super.onBack();
        setResult(Activity.RESULT_CANCELED);
    }

    @Override
    public Observable<BaseResponse<List<PoiInfo>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return null;
    }

    @Override
    public RefreshController<PoiInfo> createRefreshController() {
        mRefreshController = new LocationRefreshController(this);
        if (mGeoCoder == null) {
            mGeoCoder = GeoCoder.newInstance();
        }
        mGeoCoder.setOnGetGeoCodeResultListener(mRefreshController);
        return mRefreshController;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mGeoCoder != null) {
            mGeoCoder.destroy();
        }
    }

    @Override
    protected void onPermissionResult(boolean isGranted, int requestCode) {
        super.onPermissionResult(isGranted, requestCode);
        LocationHelper.getInstance().onPermissionResult(isGranted, requestCode);
    }

    class LocationRefreshController extends RefreshController<PoiInfo> implements OnGetGeoCoderResultListener {
        private boolean isLoading;
        private int tempPage;

        public LocationRefreshController(IRefreshContext<PoiInfo> refreshContext) {
            super(refreshContext);
            setPageSize(10);
        }

        @Override
        protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
            return null;
        }

        @Override
        public void initViews(View rootView) {
            super.initViews(rootView);
            startLocation();
        }

        @Override
        public void onRefresh() {
            tempPage = 1;
            getListData(tempPage);
        }

        @Override
        public void onLoadMore() {
            tempPage = tempPage + 1;
            getListData(tempPage);
        }

        @Override
        protected boolean showRefreshingWhenLoadDataAfterInitViews() {
            return false;
        }

        @Override
        public void getListData(int page) {
            if (isLoading) {
                return;
            }
            if (mLocationEntity == null) {
                startLocation();
                isLoading = false;
                return;
            }
            isLoading = true;
            mGeoCoder.reverseGeoCode(new ReverseGeoCodeOption()
                    .location(new LatLng(mLocationEntity.latitude, mLocationEntity.longitude))
                    .pageNum(page)
                    .pageSize(getPageSize())
            );
        }

        @Override
        public void onGetGeoCodeResult(GeoCodeResult geoCodeResult) {
            isLoading = false;
            onRefreshComplete();
        }

        @Override
        public void onGetReverseGeoCodeResult(ReverseGeoCodeResult reverseGeoCodeResult) {
            List<PoiInfo> list = reverseGeoCodeResult.getPoiList();
            List<PoiInfo> data = list == null || list.isEmpty() ? new ArrayList<>() : list;
            if (tempPage < 2) {
                //若是定位成功则插入当前位置
                if (mLocationEntity != null && mLocationEntity.location != null) {
                    PoiInfo poiInfo = new PoiInfo();
                    poiInfo.setLocation(new LatLng(mLocationEntity.latitude, mLocationEntity.longitude));
                    Address address = mLocationEntity.location.getAddress();
                    if (address != null) {
                        poiInfo.setAddress(address.address);
                        poiInfo.setName(address.street);
                    } else {
                        poiInfo.setName(mLocationEntity.cityName);
                    }
                    data.add(0, poiInfo);
                }
                //不显示位置
                data.add(0, null);
            }
            onLoadSuccess(tempPage, data, 1);
            isLoading = false;
        }
    }

    class BaiduGeoCodeAdapter extends BaseRecyclerViewAdapter<PoiInfo> {

        public BaiduGeoCodeAdapter(Context context, List<PoiInfo> data) {
            super(context, data);
        }

        @Override
        protected BaseRecyclerViewHolder<PoiInfo> createViewHolder(View itemView) {
            return new ViewHolder(itemView);
        }

        @Override
        protected int getItemLayoutRes(int viewType) {
            return R.layout.item_baidu_loction;
        }

        class ViewHolder extends BaseRecyclerViewHolder<PoiInfo> {

            TextView mTvName;
            TextView mTvTips;
            TextView mTvSecondName;

            public ViewHolder(View itemView) {
                super(itemView);
            }

            @Override
            protected void initItemView(View itemView) {
                super.initItemView(itemView);
                mTvName = itemView.findViewById(R.id.tv_name);
                mTvTips = itemView.findViewById(R.id.tv_tip);
                mTvSecondName = itemView.findViewById(R.id.tv_second_name);
            }

            @Override
            public void bindTo(PoiInfo poiInfo, int position) {
                super.bindTo(poiInfo, position);
                if (poiInfo == null) {
                    mTvName.setText("不显示位置");
                    mTvName.setTextColor(itemView.getResources().getColor(R.color.color_ee0029));
                    mTvSecondName.setVisibility(View.GONE);
                    mTvTips.setVisibility(isOpenGps && getItemCount() > 2 ? View.GONE : View.VISIBLE);
                } else {
                    mTvTips.setVisibility(View.GONE);
                    mTvName.setTextColor(itemView.getResources().getColor(R.color.color_383a40));
                    mTvName.setText(poiInfo.getName());
                    mTvSecondName.setText(poiInfo.getAddress());
                    mTvSecondName.setVisibility(TextUtils.isEmpty(poiInfo.getAddress()) ? View.GONE : View.VISIBLE);
                }
            }
        }

    }

}
