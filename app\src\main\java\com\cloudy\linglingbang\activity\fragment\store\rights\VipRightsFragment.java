package com.cloudy.linglingbang.activity.fragment.store.rights;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.animation.TranslateAnimation;
import android.widget.CompoundButton;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseScrollTabViewPagerFragment;
import com.cloudy.linglingbang.activity.fragment.store.youpin.ExclusiveBenefitsFragment;
import com.cloudy.linglingbang.activity.fragment.store.youpin.LifePaymentFragment;
import com.cloudy.linglingbang.activity.fragment.store.youpin.SuperiorProductFragment;
import com.cloudy.linglingbang.app.receiver.UserInfoChangeReceiver;
import com.cloudy.linglingbang.app.receiver.UserInfoChangedHelper;
import com.cloudy.linglingbang.app.util.DeviceUtil;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.PagerSlidingTabStrip;
import com.cloudy.linglingbang.app.widget.textview.CenterDrawableTextView;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;
import butterknife.BindView;
import butterknife.BindViews;
import butterknife.OnClick;

/**
 * 生活-尊享权益
 *
 * <AUTHOR>
 * @date 5/25/21
 */
public class VipRightsFragment extends BaseScrollTabViewPagerFragment<Fragment> {
    @BindView(R.id.tv_indicator)
    View mTvIndicator;
    @BindViews({R.id.tv_left, R.id.tv_life_payment, R.id.tv_right})
    CenterDrawableTextView[] mCenterDrawableTextViews;
    //    @BindView(R.id.tv_left)
//    CenterDrawableTextView mTvLeft;
//    @BindView(R.id.tv_right)
//    CenterDrawableTextView mTvRight;
//    @BindView(R.id.tv_hotel)
//    CenterDrawableTextView mTvHotel;
//    @BindView(R.id.tv_life_payment)
//    CenterDrawableTextView mTvLifePayment;
    private float mTvIndicatorWidth;

    public static Fragment newInstance() {
        return new VipRightsFragment();
    }

    private UserInfoChangedHelper mUserInfoChangedHelper;

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_vip_right;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (mUserInfoChangedHelper == null) {
            mUserInfoChangedHelper = new UserInfoChangedHelper(new UserInfoChangeReceiver() {
                @Override
                protected void onUpdateBaseInfo() {
                    super.onUpdateBaseInfo();
                    userChange();
                }

                @Override
                protected void onUpdateBalanceInfo() {
                    super.onUpdateBalanceInfo();
                    userChange();
                }

                @Override
                protected void onUpdateExpireData() {
                    super.onUpdateCartInfo();
                    userChange();
                }

                private void userChange() {
                    for (Fragment datum : mData) {
                        if (datum instanceof SuperiorProductFragment) {
                            ((SuperiorProductFragment) datum).userChange();
                        }
                    }
                }

            });
        }
        mUserInfoChangedHelper.register(getContext());
    }

    @Override
    protected PagerAdapter createViewPagerAdapter(List<Fragment> data, String[] titles) {
        tabs.setListener(new PagerSlidingTabStrip.SingleListener() {
            @Override
            public TextView createTextTab(Context context, int position) {
                TextView textView = new CompoundButton(context) {
                    @Override
                    public void toggle() {
//                        super.toggle();
                    }
                };
                textView.setBackgroundResource(R.drawable.select_refit_txt2);
                return textView;
            }
        });
        return new FragmentStatePagerAdapter(getChildFragmentManager()) {

            @Override
            public Fragment getItem(int position) {
                return data.get(position);
            }

            @Override
            public int getCount() {
                return data.size();
            }

            @Nullable
            @Override
            public CharSequence getPageTitle(int position) {
                return titles[position];
            }
        };
    }

    @Override
    protected void initViews() {
        super.initViews();
        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) mTvIndicator.getLayoutParams();
        mTvIndicatorWidth = DeviceUtil.getScreenWidth() / mAdapter.getCount();
        layoutParams.width = (int) mTvIndicatorWidth;
        mTvIndicator.setLayoutParams(layoutParams);
        tabs.setShouldExpand(true);
//        LinearLayout.LayoutParams leftParam = (LinearLayout.LayoutParams) mTvLeft.getLayoutParams();
//        leftParam.width = (int) mTvIndicatorWidth;
//        mTvLeft.setLayoutParams(leftParam);
//        LinearLayout.LayoutParams rightParam = (LinearLayout.LayoutParams) mTvRight.getLayoutParams();
//        rightParam.width = (int) mTvIndicatorWidth;
//        mTvRight.setLayoutParams(rightParam);
//        LinearLayout.LayoutParams lifePaymentParam = (LinearLayout.LayoutParams) mTvLifePayment.getLayoutParams();
//        lifePaymentParam.width = (int) mTvIndicatorWidth;
//        mTvLifePayment.setLayoutParams(lifePaymentParam);

        mCenterDrawableTextViews[0].setText(titles[0]);
        mCenterDrawableTextViews[1].setText(titles[1]);
//        mCenterDrawableTextViews[2].setText(titles[2]);
//        mCenterDrawableTextViews[3].setText(titles[3]);
        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                for (CenterDrawableTextView centerDrawableTextView : mCenterDrawableTextViews) {
                    centerDrawableTextView.setChecked(false);
                    centerDrawableTextView.setEnabled(true);
                }
                mCenterDrawableTextViews[position].setChecked(true);
                mCenterDrawableTextViews[position].setEnabled(false);
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
    }

    @OnClick({R.id.tv_left, R.id.tv_right, R.id.tv_hotel, R.id.tv_life_payment})
    void clicks(View view) {
        switch (view.getId()) {
            case R.id.tv_left:
                mViewPager.setCurrentItem(0);
                break;
            case R.id.tv_life_payment:
                mViewPager.setCurrentItem(1);
                break;
            case R.id.tv_hotel:
                mViewPager.setCurrentItem(2);
                break;
            case R.id.tv_right:
                mViewPager.setCurrentItem(3);
                break;
        }
    }

    @Override
    protected void onItemSelected(int position) {
        super.onItemSelected(position);
        TranslateAnimation animation = new TranslateAnimation(Math.abs(mTvIndicatorWidth * (position - 1)), mTvIndicatorWidth * (position), 0, 0);
        animation.setDuration(200);
        animation.setFillAfter(true);
        mTvIndicator.startAnimation(animation);
        SensorsUtils.sensorsClickBtn("点击" + titles[position], "好物", "好物");
    }

    @Override
    protected List<Fragment> createAdapterData() {
        List<Fragment> fragmentList = new ArrayList<>();
        fragmentList.add(new ExclusiveBenefitsFragment());
        fragmentList.add(LifePaymentFragment.newInstance(true));
//        fragmentList.add(new HotelFragment());
//        fragmentList.add(AntRightsFragment.newInstance());

        return fragmentList;

    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mUserInfoChangedHelper != null) {
            mUserInfoChangedHelper.unregister(getContext());
        }
    }

    @Override
    protected String[] getTitles() {
//        return new String[]{"尊享权益", "生活缴费", "酒店服务", "跨界权益"};
        return new String[]{"尊享权益", "生活缴费"};
    }

}
