package com.cloudy.linglingbang.activity.store.commodity.adapter

import android.content.Context
import android.view.View
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.store.commodity.adapter.holder.OnChangeCommodityListener
import com.cloudy.linglingbang.activity.store.commodity.adapter.holder.ShoppingCartLoseEfficacyViewHolder
import com.cloudy.linglingbang.activity.store.commodity.adapter.holder.ShoppingCartViewHolder
import com.cloudy.linglingbang.app.widget.SlidingButtonView
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder
import com.cloudy.linglingbang.model.store.commodity.CartInfo

/**
 * 新版购物车的adapter
 *
 * <AUTHOR>
 * @date 2022/9/29
 */
class ShoppingCartAdapter(context: Context, data: List<CartInfo>) :
    BaseRecyclerViewAdapter<CartInfo>(context, data) {


    object ViewType {
        /**
         * 正常商品
         */
        const val VIEW_TYPE_NORMAL = 1

        /**
         * 失效商品
         */
        const val VIEW_TYPE_LOSE_EFFICACY = 2
    }

    /**
     * 刷新接口的监听
     */
    var needRefreshListener: (Int) -> Unit = {}

    var mOnChangeCommodityListener: OnChangeCommodityListener? = null



    override fun createViewHolder(itemView: View?): BaseRecyclerViewHolder<CartInfo>? {
        return null
    }

    override fun getItemLayoutRes(viewType: Int): Int {
        return if (viewType == ViewType.VIEW_TYPE_NORMAL) {
            R.layout.item_shopping_cart
        } else {
            R.layout.item_shopping_cart_lose_efficacy
        }
    }


    override fun createViewHolderWithViewType(
        itemView: View?,
        viewType: Int
    ): BaseRecyclerViewHolder<CartInfo> {
        return if (viewType == ViewType.VIEW_TYPE_NORMAL) {
            var shoppingCartViewHolder = ShoppingCartViewHolder(itemView)
            shoppingCartViewHolder.setOnNeedRefreshListener {
                needRefreshListener(it)
            }
            shoppingCartViewHolder.mOnChangeCommodityListener = object : OnChangeCommodityListener {
                override fun onChangeSingleSelect(cartId: Long, isSelect: Int) {
                    mOnChangeCommodityListener?.onChangeSingleSelect(cartId, isSelect)
                }

                override fun onChangeSelect(cartIds: MutableList<Long>, isSelect: Int) {
                    mOnChangeCommodityListener?.onChangeSelect(cartIds, isSelect)
                }

                override fun onDeleteSingle(cartId: Long) {
                    mOnChangeCommodityListener?.onDeleteSingle(cartId)
                }

                override fun onDeleteSingle(cartIds: MutableList<Long>, tips: String) {
                    mOnChangeCommodityListener?.onDeleteSingle(cartIds, tips)
                }

                override fun changeNumToCart(cartId: Long, count: Long) {
                    mOnChangeCommodityListener?.changeNumToCart(cartId, count)
                }

                override fun onMenuIsOpen(view: View?) {
                    mOnChangeCommodityListener?.onMenuIsOpen(view)
                }

                override fun onDownOrMove(slidingButtonView: SlidingButtonView?) {
                    mOnChangeCommodityListener?.onDownOrMove(slidingButtonView)
                }
            }
            shoppingCartViewHolder
        } else {
            var viewHolder = ShoppingCartLoseEfficacyViewHolder(itemView)
            viewHolder.mOnChangeCommodityListener = object : OnChangeCommodityListener {
                override fun onChangeSingleSelect(cartId: Long, isSelect: Int) {
                    mOnChangeCommodityListener?.onChangeSingleSelect(cartId, isSelect)
                }

                override fun onChangeSelect(cartIds: MutableList<Long>, isSelect: Int) {
                    mOnChangeCommodityListener?.onChangeSelect(cartIds, isSelect)
                }

                override fun onDeleteSingle(cartId: Long) {
                    mOnChangeCommodityListener?.onDeleteSingle(cartId)
                }

                override fun onDeleteSingle(cartIds: MutableList<Long>, tips: String) {
                    mOnChangeCommodityListener?.onDeleteSingle(cartIds, tips)
                }

                override fun changeNumToCart(cartId: Long, count: Long) {
                    mOnChangeCommodityListener?.changeNumToCart(cartId, count)
                }

                override fun onMenuIsOpen(view: View?) {
                    mOnChangeCommodityListener?.onMenuIsOpen(view)
                }

                override fun onDownOrMove(slidingButtonView: SlidingButtonView?) {
                    mOnChangeCommodityListener?.onDownOrMove(slidingButtonView)
                }

            }
            viewHolder
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (mData?.get(position)?.isExpired == 0) {
            ViewType.VIEW_TYPE_NORMAL
        } else {
            ViewType.VIEW_TYPE_LOSE_EFFICACY
        }
    }


    fun setOnNeedRefreshListener(e: (Int) -> Unit) {
        this.needRefreshListener = e
    }


}