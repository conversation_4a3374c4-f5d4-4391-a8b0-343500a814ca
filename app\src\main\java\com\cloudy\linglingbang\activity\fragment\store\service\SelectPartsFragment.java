package com.cloudy.linglingbang.activity.fragment.store.service;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshFragment;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.fragment.store.home.StoreHomeRefreshController;
import com.cloudy.linglingbang.activity.fragment.store.home.adapter.StoreHomeAdapter;
import com.cloudy.linglingbang.activity.fragment.store.youpin.ElementUtils;
import com.cloudy.linglingbang.activity.store.commodity.CommodityListPageCode;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.constants.FinalSensors;
import com.cloudy.linglingbang.model.SourceModel;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.server.Ad.Ad2;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import butterknife.OnClick;
import io.reactivex.rxjava3.core.Observable;

/**
 * 服务-选配件
 *
 * <AUTHOR>
 * @date 2021/12/15
 */
public class SelectPartsFragment extends BaseRecyclerViewRefreshFragment<Object> {

    /**
     * 组件页
     */
    private String mPageCode;

    /**
     * 埋点信息，传给 adapter
     */
    protected SensorsUtils.StoreHomeAnchor mStoreHomeAnchor;

    @BindView(R.id.tv_comprehensive)
    TextView mTvComprehensive;

    @BindView(R.id.ll_price)
    LinearLayout mLlPrice;

    @BindView(R.id.tv_price)
    TextView mTvPrice;

    @BindView(R.id.tv_sales)
    TextView mTvSales;

    private String mSortFieldsKey = "s4";
    private String mSortFieldsValue = "2";

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mPageCode = IntentUtils.getFragmentArgument(this);
        mStoreHomeAnchor = new SensorsUtils.StoreHomeAnchor("选配件", "服务-选配件", SourceModel.POSITION_TYPE.SERVICE_CHOOSE_ACCESSORIES_TYPE, SourceModel.POSITION_TYPE.SERVICE_CHOOSE_ACCESSORIES_VALUE);

    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List list) {
        return new StoreHomeAdapter(getContext(), list).setCountDownTag(this).setStoreHomeAnchor(mStoreHomeAnchor);
    }

    @Override
    public Observable<BaseResponse<List<Object>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return null;
    }

    public static Fragment newInstance(String pageCode) {
        return IntentUtils.setFragmentArgument(new SelectPartsFragment(), pageCode);
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_select_parts;
    }

    @OnClick({R.id.tv_comprehensive, R.id.tv_price, R.id.tv_sales})
    void clicks(View view) {
        switch (view.getId()) {
            case R.id.tv_comprehensive:
                mSortFieldsKey = "s4";
                mSortFieldsValue = "2";
                mTvComprehensive.setSelected(true);
                mTvPrice.setSelected(false);
                mTvSales.setSelected(false);
                mTvPrice.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_price_normal, 0);
                break;
            case R.id.tv_price:
                mSortFieldsKey = "s6";
                mTvPrice.setSelected(true);
                if (mTvPrice.getTag() != null && mTvPrice.getTag().toString().equals("rise")) {
                    mSortFieldsValue = "2";
                    mTvPrice.setTag("drop");
                    mTvPrice.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_price_drop, 0);
                } else {
                    mSortFieldsValue = "1";
                    mTvPrice.setTag("rise");
                    mTvPrice.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_price_rise, 0);
                }
                mTvComprehensive.setSelected(false);
                mTvSales.setSelected(false);
                break;
            case R.id.tv_sales:
                mSortFieldsKey = "s1";
                mSortFieldsValue = "2";
                mTvSales.setSelected(true);
                mTvPrice.setSelected(false);
                mTvComprehensive.setSelected(false);
                mTvPrice.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_price_normal, 0);
                break;
        }
        String eventPosition = "";
        switch (mSortFieldsKey) {
            case "s4":
                eventPosition = "综合";
                break;
            case "s6":
                eventPosition = "价格";
                break;
            case "s1":
                eventPosition = "销量";
                break;
        }
        SensorsUtils.sensorsClickBtn("点击排序", "权益页面", eventPosition);
        getRefreshController().onRefresh();
    }

    @Override
    public RefreshController<Object> createRefreshController() {
        return new StoreHomeRefreshController(this, "") {
            @Override
            protected Observable<BaseResponse<List<Object>>> getListDataFromNet(L00bangService2 service2, int pageNo, int pageSize) {
                Map<String,Object> map = new HashMap<>();
                map.put("pageNo", pageNo);
                map.put("pageSize", pageSize);
                map.put("pageCode", CommodityListPageCode.PAGE_CODE_SELECT_PARTS);
                if (!TextUtils.isEmpty(mSortFieldsKey)) {
                    Map<String, String> map1 = new HashMap<>();
                    map1.put(mSortFieldsKey, mSortFieldsValue);
                    map.put("sortFieldsMap", map1);
                }
                //销售模式  1非直发  2直发
                List<Integer> salesModelList = new ArrayList<>();
                salesModelList.add(2);
                map.put("salesModelList", salesModelList);
                return service2.findEcCommodityAppList(map)
                        .map(ElementUtils.getStoreElementToObjectFun(Ad2.LINK_TYPE.TYPE_NEW_COMMODITY_DETAIL, 0));
            }
        };
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (hidden) {
            SensorsUtils.sensorsViewEndNew("服务-选配件", FinalSensors.BROWSE_LIFE_INFORMATION, "浏览" + "选配件");
        } else {
            SensorsUtils.sensorsViewStart(FinalSensors.BROWSE_LIFE_INFORMATION);
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        SensorsUtils.sensorsViewStart(FinalSensors.BROWSE_LIFE_INFORMATION);
    }

    @Override
    public void onStop() {
        super.onStop();
        SensorsUtils.sensorsViewEndNew("服务-选配件", FinalSensors.BROWSE_LIFE_INFORMATION, "浏览服务-选配件");
    }
}
