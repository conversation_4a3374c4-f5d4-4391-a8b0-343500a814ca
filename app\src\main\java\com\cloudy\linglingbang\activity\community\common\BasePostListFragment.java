package com.cloudy.linglingbang.activity.community.common;

import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshFragment;
import com.cloudy.linglingbang.model.postcard.PostCard;

import java.util.List;

import androidx.recyclerview.widget.RecyclerView;

/**
 * 新版帖子列表
 *
 * <AUTHOR>
 * @date 2018/6/23
 */
public abstract class BasePostListFragment extends BaseRecyclerViewRefreshFragment<PostCard> {
    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<PostCard> list) {
        return new BasePostAdapter(getContext(), list);
    }
}
