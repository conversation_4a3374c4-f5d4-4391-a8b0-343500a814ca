package com.cloudy.linglingbang.activity.community.post.detail;

import android.content.res.Resources;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewStub;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.community.common.holder.BasePostChildViewHolder;
import com.cloudy.linglingbang.app.log.LLBTextUtils;
import com.cloudy.linglingbang.model.postcard.PersonalCard;
import com.cloudy.linglingbang.model.postcard.PostCard;

/**
 * 帖子详情-个人 名片
 *
 * <AUTHOR>
 * @date 2019/11/27
 */
public class PostDetailPersonalCardViewHolder extends BasePostChildViewHolder implements View.OnClickListener {

    private ViewStub mViewStub;
    private View mCardView;
    private TextView name;
    private TextView mobile;
    private TextView wx;

    public PostDetailPersonalCardViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        mViewStub = itemView.findViewById(R.id.view_stub_personal_card);
    }

    @Override
    public void bindTo(PostCard postCard) {
        super.bindTo(postCard);
        if (!showPersonalCardInfo(postCard)) {
            if (mCardView != null) {
                mCardView.setVisibility(View.GONE);
            }
            return;
        }
        if (mCardView == null) {
            mCardView = mViewStub.inflate();
            name = mCardView.findViewById(R.id.tv_card_name);
            mobile = mCardView.findViewById(R.id.tv_card_mobile);
            wx = mCardView.findViewById(R.id.tv_card_wx);
            mCardView.findViewById(R.id.iv_copy_wx).setOnClickListener(this);
            mCardView.findViewById(R.id.iv_call).setOnClickListener(this);
        } else {
            mCardView.setVisibility(View.VISIBLE);
        }
        Resources resources = mCardView.getResources();
        PersonalCard personalCard = postCard.getCallingCard();
        wx.setText(resources.getString(R.string.text_post_detail_card_wx, personalCard.getWx()));
        mobile.setText(resources.getString(R.string.text_post_detail_card_mobile, personalCard.getPhone()));
        name.setText(personalCard.getName());
    }

    /**
     * 是否展示个人名片信息
     */
    private boolean showPersonalCardInfo(PostCard postCard) {
        return postCard != null && postCard.getSeries() == 3 && postCard.getCallingCard() != null;
    }

    @Override
    public void onClick(View v) {
        if (showPersonalCardInfo(mPostCard)) {
            PersonalCard personalCard = mPostCard.getCallingCard();
            if (v.getId() == R.id.iv_copy_wx) {
                CharSequence str = personalCard.getWx();
                if (!TextUtils.isEmpty(str)) {
                    LLBTextUtils.copyText(v.getContext(),str, true);
                }
            } else if (v.getId() == R.id.iv_call && !TextUtils.isEmpty(personalCard.getPhone())) {
                IntentUtils.startActivityByDialIntent(v.getContext(), personalCard.getPhone());
            }
        }
    }
}
