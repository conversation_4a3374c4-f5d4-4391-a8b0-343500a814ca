package com.cloudy.linglingbang.activity.community.post.detail;

import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.community.common.holder.BasePostChildViewHolder;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.widget.FlowLayout;
import com.cloudy.linglingbang.model.community.PostLabel;
import com.cloudy.linglingbang.model.postcard.PostCard;

import java.util.List;

/**
 * 帖子详情-标签
 *
 * <AUTHOR>
 * @date 2018/6/27
 */
public class PostDetailLabelsViewHolder extends BasePostChildViewHolder implements View.OnClickListener {
    private View mViewDividerLabel;
    private FlowLayout mFlowLayoutLabels;

    public PostDetailLabelsViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        mViewDividerLabel = itemView.findViewById(R.id.view_divider_label);
        mFlowLayoutLabels = itemView.findViewById(R.id.flow_layout_labels);
    }

    @Override
    public void bindTo(PostCard postCard) {
        super.bindTo(postCard);
        List<PostLabel> labels = postCard.getLabels();
        if (labels == null || labels.isEmpty()) {
            mViewDividerLabel.setVisibility(View.GONE);
            mFlowLayoutLabels.setVisibility(View.GONE);
            mFlowLayoutLabels.removeAllViews();
        } else {
            mViewDividerLabel.setVisibility(View.VISIBLE);
            mFlowLayoutLabels.setVisibility(View.VISIBLE);
            mFlowLayoutLabels.removeAllViews();
            LayoutInflater inflater = LayoutInflater.from(mFlowLayoutLabels.getContext());
            for (PostLabel label : labels) {
                Button button = (Button) inflater.inflate(R.layout.item_post_detail_label_btn, mFlowLayoutLabels, false);
                button.setText(mFlowLayoutLabels.getContext().getString(R.string.item_post_detail_bottom_info_tv_label, label.getLabelName()));
                button.setTag(R.id.tag_position, label.getLabelIdOrZero());
                button.setOnClickListener(this);
                mFlowLayoutLabels.addView(button);
            }
        }
    }

    @Override
    public void onClick(View v) {
        //点击标签
        if (v instanceof TextView) {
            Object tag = v.getTag(R.id.tag_position);
            if (tag != null && tag instanceof Long) {
                long labelId = (long) tag;
                JumpPageUtil.goLabelPostList(v.getContext(), ((TextView) v).getText().toString(), labelId);
            }
        }
    }
}
