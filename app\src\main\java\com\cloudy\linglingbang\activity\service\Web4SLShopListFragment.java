package com.cloudy.linglingbang.activity.service;

import android.view.View;

import com.cloudy.linglingbang.app.util.LocationHelper;
import com.cloudy.linglingbang.constants.WebUrlConfigConstant;
import com.cloudy.linglingbang.web.BaseX5WebViewFragment;

/**
 * Created by hp on 16/9/21.
 */
public class Web4SLShopListFragment extends BaseX5WebViewFragment {

    public static Web4SLShopListFragment newInstance() {
        return new Web4SLShopListFragment();
    }

    @Override
    protected void initialize(View view) {
        super.initialize(view);
    }

    @Override
    public void onDestroyView() {
        LocationHelper.getInstance().stopLocation();
        super.onDestroyView();
    }

    @Override
    protected String setUrl() {
        return WebUrlConfigConstant.FRAGMENT_4S;
    }

    @Override
    protected boolean isShowBar() {
        return false;
    }

    @Override
    protected void onPageLoadFinish() {
        super.onPageLoadFinish();
        if (!this.isHidden()) {
            passLocation();
        }

    }

}
