package com.cloudy.linglingbang.activity.store.commodity.adapter.holder

import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.store.commodity.CommodityDetailActivity
import com.cloudy.linglingbang.app.util.AppUtil
import com.cloudy.linglingbang.app.util.ModelUtils
import com.cloudy.linglingbang.app.util.ViewHolderUtils
import com.cloudy.linglingbang.app.util.timer.CountDownCommodityUtils
import com.cloudy.linglingbang.app.util.timer.CountDownManager
import com.cloudy.linglingbang.app.util.timer.TimeDelta
import com.cloudy.linglingbang.app.widget.FlowLayout
import com.cloudy.linglingbang.app.widget.banner.AdRoundImageView
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder
import com.cloudy.linglingbang.app.widget.textview.CountDownTextView
import com.cloudy.linglingbang.model.SourceModel
import com.cloudy.linglingbang.model.store.home.StoreElementCommodity

/**
 * 帖子详情-推荐商品
 *
 * <AUTHOR>
 * @date 2022/9/26
 */
class RecommendCommodityViewHolder(itemView: View?) : BaseRecyclerViewHolder<Any>(itemView),
    CountDownManager.OnTickListener {
    var mIvImage: AdRoundImageView? = null
    var mTvCommodityName: TextView? = null
    var mFlowLayout: FlowLayout? = null
    var mTvPrice: TextView? = null
    var mTvOriginalPrice: TextView? = null
    var mTvDes: TextView? = null
    var mTvStartCountDown: CountDownTextView? = null
    var commodity: StoreElementCommodity? = null

    var orderSourceType: String? = null
    var orderSource: String? = null


    override fun initItemView(itemView: View?) {
        super.initItemView(itemView)
        mTvStartCountDown = itemView?.findViewById(R.id.tv_count_down)
        mIvImage = itemView?.findViewById(R.id.iv_image)
        mTvCommodityName = itemView?.findViewById(R.id.tv_title)
        mTvPrice = itemView?.findViewById(R.id.tv_price)
        mTvOriginalPrice = itemView?.findViewById(R.id.tv_original_price)
        mTvOriginalPrice?.paint?.isStrikeThruText = true
        mFlowLayout = itemView?.findViewById(R.id.flow_layout)
        mTvDes = itemView?.findViewById(R.id.tv_des)
        itemView?.setOnClickListener {
            commodity?.apply {
                CommodityDetailActivity.startActivity(
                    itemView.context, productIdOrZero,
                    SourceModel(
                        orderSourceType,
                        orderSource
                    )
                )
            }
        }
    }


    override fun bindTo(bean: Any?, position: Int) {
        super.bindTo(bean, position)
        commodity = if (bean is StoreElementCommodity) {
            bean
        } else {
            null
        }
        commodity?.apply {
            mIvImage?.createImageLoad(productMainImage)
                ?.load()
            mTvCommodityName?.text = productName
            mTvDes?.visibility = if (TextUtils.isEmpty(commoditySecondName)) {
                View.GONE
            } else {
                mTvDes?.text = commoditySecondName
                View.VISIBLE
            }
            //ling 值
            var lingVale = ""
            if (mixedFlag == 1) {
                lingVale = "+" + skuLingPriceStr + "Ling"
            }
            //价格
            ViewHolderUtils.setTextAndVisibilityDependsOnText(
                mTvPrice,
                ModelUtils.getRmbOrEmptyString(minPriceStr) + lingVale
            )
            //原价
            if ((TextUtils.isEmpty(originalMinPriceStr)
                        || originalMinPriceStr == minPriceStr)
                && TextUtils.isEmpty(lingVale)
            ) {
                mTvOriginalPrice?.visibility = View.GONE
            } else {
                ViewHolderUtils.setTextAndVisibilityDependsOnText(
                    mTvOriginalPrice,
                    ModelUtils.getRmbOrEmptyString(originalMinPriceStr)
                )
            }
            //活动标签
            loadActivityLabel(mFlowLayout)
        }

        if (mTvStartCountDown != null) {
            if (isShowCountDownStatus(commodity?.activityStatus ?: 0)) {
                //在监听前先展示一个时间
                onTick(AppUtil.getServerCurrentTime())
                CountDownManager.getInstance().addOnTickListener(this)
            } else {
                mTvStartCountDown?.visibility = View.GONE
                CountDownManager.getInstance().removeOnTickListener(this)
            }
        }
    }

    private fun loadActivityLabel(flowLayout: FlowLayout?) {
        if (flowLayout == null) {
            return
        }
        flowLayout.visibility = View.GONE
        flowLayout.removeAllViews()
        if (commodity == null || commodity?.activityLabelList == null) {
            return
        }
        val activityLabelList = commodity?.activityLabelList
        if (activityLabelList?.isEmpty() == true) {
            return
        }
        flowLayout.visibility = View.VISIBLE
        val inflater = LayoutInflater.from(itemView.context)
        for (label in activityLabelList!!) {
            val view = inflater.inflate(R.layout.layout_commodity_activity_label, null)
            val tv = view as TextView
            tv.text = label
            flowLayout.addView(view)
        }
    }

    override fun onTick(currentTime: Long) {
        commodity?.apply {
            if (!(activityStatus == 4 || activityStatus == 5)) {
                mTvStartCountDown?.visibility = View.GONE
                CountDownManager.getInstance()
                    .removeOnTickListener(this@RecommendCommodityViewHolder)
                return@apply
            }

            activityStatus = when {
                currentTime < activityStartTime -> {
                    //开始前，预热中
                    CountDownCommodityUtils.ACTIVITY_STATUS_PREHEATING
                }
                currentTime < activityEndTime -> {
                    //结束前，活动中
                    CountDownCommodityUtils.ACTIVITY_STATUS_IN_PROGRESS
                }
                else -> {
                    //已结束
                    CountDownCommodityUtils.ACTIVITY_STATUS_FINISH
                }
            }
            when (activityStatus) {
                CountDownCommodityUtils.ACTIVITY_STATUS_IN_PROGRESS -> {
                    //活动中，展示距结束的时间
                    mTvStartCountDown?.updateTimeDelta(
                        TimeDelta.create(
                            currentTime,
                            activityEndTime,
                            mTvStartCountDown?.isShowDay ?: false
                        )
                    )
                    mTvStartCountDown?.visibility = View.VISIBLE
                }
                CountDownCommodityUtils.ACTIVITY_STATUS_FINISH -> {
                    //活动结束，不需要展示了，注销
                    CountDownManager.getInstance()
                        .removeOnTickListener(this@RecommendCommodityViewHolder)
                    //更新 UI
                    mTvStartCountDown?.visibility = View.GONE
                }
                else -> {
                    //预热中及其他状态，不处理，继续倒计时
                    mTvStartCountDown?.visibility = View.GONE
                }
            }
        }

    }

    override fun getCountDownTag(): Any {
        return AppUtil.getActivity(itemView.context) ?: this
    }

    /**
     * 是否是需要展示倒计时的状态
     */
    fun isShowCountDownStatus(activityStatus: Int): Boolean {
        return activityStatus == CountDownCommodityUtils.ACTIVITY_STATUS_PREHEATING || activityStatus == CountDownCommodityUtils.ACTIVITY_STATUS_IN_PROGRESS
    }

}