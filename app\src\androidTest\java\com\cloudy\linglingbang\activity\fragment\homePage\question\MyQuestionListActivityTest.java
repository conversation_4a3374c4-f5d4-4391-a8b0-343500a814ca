package com.cloudy.linglingbang.activity.fragment.homePage.question;

import android.content.Intent;

import com.cloudy.linglingbang.BaseInstrumentedTest;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.fragment.homePage.ColumnListActivity;

/**
 * 问答提问测试
 *
 * <AUTHOR>
 * @date 1/21/21
 */
public class MyQuestionListActivityTest extends BaseInstrumentedTest {

    @Override
    public void test() {
        super.test();

        Intent intent = IntentUtils.createStartIntent(getActivity(), HotQuestionListActivity.class, IntentUtils.INTENT_EXTRA_COMMON, 1);
        intent.putExtra(ColumnListActivity.INTENT_EXTRA_COLUMN_NAME, "热门问题");
        intent.putExtra(ColumnListActivity.INTENT_EXTRA_EVENT_PAGE, "问答");
        intent.putExtra(IntentUtils.INTENT_EXTRA_FROM, 4);
        IntentUtils.startActivity(getActivity(), intent);
    }
}