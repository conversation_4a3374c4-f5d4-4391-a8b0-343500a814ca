package com.cloudy.linglingbang.activity.store.commodity.adapter.holder

import android.text.TextUtils
import android.view.View
import android.widget.TextView
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.app.widget.banner.AdImageView
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder
import com.cloudy.linglingbang.model.postcard.PostCardItem
import com.cloudy.linglingbang.model.server.Ad.AdJumpUtil2

/**
 * 商品详情 图文
 *
 * <AUTHOR>
 * @date 2022/9/26
 */
class ImageAndTextViewHolder(itemView: View?) : BaseRecyclerViewHolder<Any>(itemView) {
    var tvText: TextView? = null
    var adImageView: AdImageView? = null

    override fun initItemView(itemView: View?) {
        super.initItemView(itemView)
        tvText = itemView?.findViewById(R.id.tv_text)
        adImageView = itemView?.findViewById(R.id.iv_image)
    }

    override fun bindTo(bean: Any?, position: Int) {
        super.bindTo(bean, position)
        if (bean is PostCardItem) {

            //20231207需求：根据linkType跳转（同帖子）
            val listen = View.OnClickListener {
                AdJumpUtil2.goToActivityForWeb(
                    itemView.context,
                    bean.linkTypeOrZero,
                    bean.linkUrl,
                    null
                )
            }

            if (TextUtils.isEmpty(bean.text)) {
                tvText?.visibility = View.GONE
            } else {
                tvText?.visibility = View.VISIBLE
                tvText?.text = bean.text
                //配置有跳转则设置监听
                if (bean.linkTypeOrZero > 0) tvText?.setOnClickListener(listen)
            }

            if (TextUtils.isEmpty(bean.img)) {
                adImageView?.visibility = View.GONE
            } else {
                adImageView?.visibility = View.VISIBLE
                //如果服务器返回的宽高存在，在按照宽高，缩放imageView，如果没有返回，则加载图片之后获得宽高，在设置一次
                val widthString: String = bean.width
                val heightString: String = bean.height
                var widthFloat = 0f
                var heightFloat = 0f
                if (!TextUtils.isEmpty(widthString) && !TextUtils.isEmpty(heightString)) {
                    try {
                        widthFloat = widthString.toFloat()
                    } catch (e: NumberFormatException) {
                        e.printStackTrace()
                    }
                    try {
                        heightFloat = heightString.toFloat()
                    } catch (e: NumberFormatException) {
                        e.printStackTrace()
                    }
                }
                if (heightFloat > 0 && widthFloat > 0) {
                    adImageView?.setWhRate(widthFloat / heightFloat)
                } else {
                    adImageView?.setWhRate(0f)
                }
                adImageView?.createImageLoad(bean.img, R.drawable.ic_common_place_holder)
                    ?.load()
                //配置有跳转则设置监听
                if (bean.linkTypeOrZero > 0) adImageView?.setOnClickListener(listen)
            }
        }
    }
}