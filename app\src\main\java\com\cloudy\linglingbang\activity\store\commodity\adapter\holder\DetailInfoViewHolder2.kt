package com.cloudy.linglingbang.activity.store.commodity.adapter.holder

import android.annotation.SuppressLint
import android.view.View
import android.widget.ProgressBar
import android.widget.TextView
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.app.util.AppUtil
import com.cloudy.linglingbang.app.util.timer.CountDownManager
import com.cloudy.linglingbang.app.util.timer.TimeDelta
import java.text.SimpleDateFormat
import kotlin.math.pow


/**
 * 商品详情名称、销量、价格等信息
 *
 * <AUTHOR>
 * @date 2022/9/26
 */
class DetailInfoViewHolder2(itemView: View?) : BaseCommodityHolder<Any>(itemView),
    CountDownManager.OnTickListener {
    private var mTvName: TextView? = null
    private var mTvLabel: TextView? = null
    private var mTvProgress: TextView? = null
    private var mTvTime: TextView? = null
    private var mPbBar:ProgressBar? = null

    private var width:Int? = null;

    init {
        mTvName = itemView?.findViewById(R.id.tv_name)
        mTvLabel = itemView?.findViewById(R.id.tv_label)
        mTvProgress = itemView?.findViewById(R.id.tv_progress)
        mTvTime = itemView?.findViewById(R.id.tv_time)
        mPbBar = itemView?.findViewById(R.id.pb_jindu)

        var dm = itemView?.context?.resources?.displayMetrics
        val density = itemView?.context?.resources?.displayMetrics?.density
        width = dm?.widthPixels!! - Math.round(30 * density!!)
    }

    @SuppressLint("SimpleDateFormat")
    override fun bindTo(bean: Any?, position: Int) {
        super.bindTo(bean, position)
        mCenterCommodity?.apply {
            mTvName?.text = commodityName
            mTvLabel?.text = commoditySecondName

            if (promotionActivity.activityInfoList.size>0){
                mTvProgress?.text = promotionActivity.activityInfoList[0].groupPurchaseInfo.percentage.toString()

                //mPbBar?.progress = promotionActivity.activityInfoList[0].groupPurchaseInfo.percentage.toInt()
                var layoutParams = mPbBar?.layoutParams;
                layoutParams?.width = width?.times(promotionActivity.activityInfoList[0].groupPurchaseInfo.percentage.toInt())?.div(100)
                mPbBar?.layoutParams = layoutParams
            }
            val sdf = SimpleDateFormat("yyyy.MM.dd")
            mTvTime?.text = sdf.format(promotionActivity.activityInfoList[0].promotionEndTime)
        }
    }

    override fun onTick(currentTime: Long) {
        mCenterCommodity?.apply {
            val tsl = currentTime.toString().length - promotionStartTime.toString().length
            val tel = currentTime.toString().length - promotionEndTime.toString().length
            if (currentTime < promotionStartTime * 10.0.pow(tsl.toDouble()).toLong()) {
                activityStatus = 4
            } else if (currentTime >= promotionEndTime * 10.0.pow(tel.toDouble()).toLong()) {
                activityStatus = 6
                bindTo(null, layoutPosition)
                //活动结束，不需要展示了，注销
                CountDownManager.getInstance().removeOnTickListener(this@DetailInfoViewHolder2)
            }
            if (activityStatus == 5 || activityStatus == 4) {
                if (activityStatus != 5 && currentTime > promotionStartTime) {
                    activityStatus = 5
                    bindTo(null, layoutPosition)
                }
                val times = StringBuilder()
                val timeDelta = if (activityStatus == 4) {
                    times.append(itemView.resources.getString(R.string.commodity_new_start_time))
                    TimeDelta.create(
                        promotionStartTime * 10.0.pow(tsl.toDouble()).toLong(),
                        currentTime
                    )
                } else {
                    times.append(itemView.resources.getString(R.string.commodity_new_end_time))
                    TimeDelta.create(
                        currentTime,
                        promotionEndTime * 10.0.pow(tel.toDouble()).toLong()
                    )
                }
                if (timeDelta.days > 0) {
                    times.append(timeDelta.days)
                    times.append("天")
                    times.append(" ")
                } else {
                    times.append(" ")
                }

                if (timeDelta.hours > 0) {
                    if (timeDelta.hours < 10) {
                        times.append("0")
                    }
                    times.append(timeDelta.hours)
                    times.append(":")
                } else {
                    times.append("00:")
                }

                if (timeDelta.minutes < 10) {
                    times.append("0")
                }
                times.append(timeDelta.minutes)
                times.append(":")

                if (timeDelta.seconds < 10) {
                    times.append("0")
                }
                times.append(timeDelta.seconds)
            }
        }

    }

    override fun getCountDownTag(): Any {
        return AppUtil.getActivity(itemView.context)
    }

}