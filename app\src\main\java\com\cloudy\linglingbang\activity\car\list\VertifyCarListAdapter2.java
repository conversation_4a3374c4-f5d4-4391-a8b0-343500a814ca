package com.cloudy.linglingbang.activity.car.list;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.baidu.mapapi.model.LatLng;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.car.connected.CarControlActivity;
import com.cloudy.linglingbang.activity.service.newenergy.map.BaiduMapViewActivity;
import com.cloudy.linglingbang.activity.service.newenergy.map.EocoderUtils;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.car.home.MyCarType;
import com.cloudy.linglingbang.model.car.map.BaiduEocoderResult;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BackgroundSubscriber;
import com.cloudy.linglingbang.model.server.DefaultCarInfo;
import com.cloudy.linglingbang.web.CommonWebActivity;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 被授权车辆列表的adapter
 *
 * <AUTHOR>
 * @date 2020/11/25
 */
class VertifyCarListAdapter2 extends BaseRecyclerViewAdapter<MyCarType> {

    public VertifyCarListAdapter2(Context context, List<MyCarType> data) {
        super(context, data);
    }

    @Override
    protected BaseRecyclerViewHolder<MyCarType> createViewHolder(View itemView) {
        ViewHolder viewHolder = new ViewHolder(itemView);
        viewHolder.setOnItemClickListener(new BaseRecyclerViewHolder.OnItemClickListener() {
            @Override
            public void onItemClick(View itemView, int position) {
                //如果是车联网卡片，则跳转车控页面
                if (mData.get(position).getCarType() == 1) {
                    CarControlActivity.startActivity(mContext, mData.get(position).getVin(), CarControlActivity.AuthRole.ROLE_AUTHORIZATION);
                }
            }
        });
        return viewHolder;
    }

    @Override
    protected int getItemLayoutRes(int viewType) {
        return R.layout.item_vertify_car_list;
    }

    class ViewHolder extends BaseRecyclerViewHolder<MyCarType> {

        /**
         * 新能源
         */
        @BindView(R.id.ll_new_energy)
        LinearLayout mLlNewEnergy;

        @BindView(R.id.iv_open)
        ImageView mIvOpen;
        /**
         * 爱车名称
         */
        @BindView(R.id.tv_car_name)
        TextView mTvCarName;

        @BindView(R.id.ll_car_content)
        LinearLayout mLlCarContent;

        /**
         * 爱车名vin
         */
        @BindView(R.id.tv_car_vin)
        TextView mTvCarVin;
        /**
         * 车主
         */
        @BindView(R.id.tv_car_owner_name)
        TextView mTvCarOwnerName;
        /**
         * 授权时间
         */
        @BindView(R.id.tv_car_authorization_time)
        TextView mTvCarAuthorizationTime;
        /**
         * 车图
         */
        @BindView(R.id.iv_car_photo)
        ImageView mIvCarPhoto;
        /**
         * 续航里程数字
         */
        @BindView(R.id.tv_endurance_mileage)
        TextView mTvEnduranceMileage;

        /**
         * 剩余电量
         */
        @BindView(R.id.tv_surplus_electricity)
        TextView mTvSurplusElectricity;

        /**
         * 爱车位置
         */
        @BindView(R.id.tv_car_position)
        TextView mTvCarPosition;

        /**
         * 爱车位置
         */
        @BindView(R.id.rl_car_position)
        RelativeLayout mRlCarPosition;
        @BindView(R.id.tv_recharge)
        TextView mTvRecharge;

        /**
         * 车联网布局
         */
        @BindView(R.id.ll_car_internet)
        LinearLayout mLlCarInternet;

        @BindView(R.id.tv_car_internet_car_name)
        TextView mTvCarInternetCarName;
        @BindView(R.id.tv_car_internet_car_style)
        TextView mTvCarInternetCarStyle;
        @BindView(R.id.tv_car_internet_car_vin)
        TextView mTvCarInternetCarVin;

        public ViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            ButterKnife.bind(this, itemView);
        }

        @Override
        public void bindTo(MyCarType myCarType, int position) {
            super.bindTo(myCarType, position);
            //公共部分
            mTvCarName.setText(myCarType.getCarNickname());
            mTvCarVin.setText(myCarType.getVin());
            mTvCarOwnerName.setText(myCarType.getCarOwnerMobile());
            mTvCarAuthorizationTime.setText(AppUtil.formatDate(myCarType.getLicenseDate(), "yyyy/MM/dd"));
            //设置爱车图片
            new ImageLoad(mContext, mIvCarPhoto, myCarType.getCarImages(), ImageLoad.LoadMode.URL)
                    .setPlaceholderAndError(R.drawable.bg_default_car)
                    .load();
            if (myCarType.getCarType() == 2) {//新能源
                mLlNewEnergy.setVisibility(View.VISIBLE);
                mLlCarInternet.setVisibility(View.GONE);
                //先查询数据库，如果有数据就显示，没有再请求接口。一分钟内不重复请求
                DefaultCarInfo defaultCarInfo = VertifyCarListInfoManager.getInstance().loadData(myCarType.getVin());
                if (defaultCarInfo != null) {
                    updateUi(defaultCarInfo);
                    //如果大于一分钟，则重新请求数据
                    if (System.currentTimeMillis() - defaultCarInfo.getSaveTime() > 60000) {
                        getBasicCarInfo(myCarType.getVin());
                    }
                } else {
                    getBasicCarInfo(myCarType.getVin());
                }
            } else if (myCarType.getCarType() == 1) {//车联网
                mLlNewEnergy.setVisibility(View.GONE);
                mLlCarInternet.setVisibility(View.VISIBLE);
                mTvCarInternetCarName.setText(myCarType.getCarStyleName());
                mTvCarInternetCarStyle.setText(myCarType.getCarType());
                mTvCarInternetCarVin.setText(myCarType.getVin());
            }

            mIvOpen.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    if (mLlCarContent.getVisibility() == View.VISIBLE) {
                        mLlCarContent.setVisibility(View.GONE);
                        mIvOpen.setRotation(180);
                    } else {
                        mLlCarContent.setVisibility(View.VISIBLE);
                        mIvOpen.setRotation(0);
                    }
                }
            });

        }

        private void updateUi(DefaultCarInfo defaultCarInfo) {
            //车辆位置的点击事件
            mRlCarPosition.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    if (!AppUtil.checkLogin(mContext)) {
                        return;
                    }
                    if (AppUtil.isFastClick()) {
                        if (defaultCarInfo != null) {
                            SensorsUtils.sensorsClickBtn("查看爱车位置按钮", "我的-被授权车辆");
                            LatLng latLng = new LatLng(defaultCarInfo.getLatitude(), defaultCarInfo.getLongitude());
                            BaiduMapViewActivity.IntentExtra intentExtra = new BaiduMapViewActivity.IntentExtra(latLng.longitude, latLng.latitude, System.currentTimeMillis(), defaultCarInfo == null ? "" : defaultCarInfo.getCarTypeName(), defaultCarInfo == null ? "" : defaultCarInfo.getCollectTime(), defaultCarInfo.getVin());
                            BaiduMapViewActivity.startActivity((Activity) mContext, intentExtra);
                        } else {
                            ToastUtil.showMessage(mContext, "获取车辆信息失败，请稍后再试~");
                        }
                    }
                }
            });
            //一键充电的点击事件
            mTvRecharge.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    if (AppUtil.isFastClick()) {
                        SensorsUtils.sensorsClickBtn("一键充电", "我的-被授权车辆");
                        if (defaultCarInfo != null && !TextUtils.isEmpty(defaultCarInfo.getChargeUrl())) {
                            CommonWebActivity.startActivity(mContext, defaultCarInfo.getChargeUrl());
                        } else {
                            ToastUtil.showMessage(mContext, "请稍候再试");
                        }
                    }
                }
            });
            if (defaultCarInfo != null) {
                mTvEnduranceMileage.setText(String.valueOf(defaultCarInfo.getRemainMileage()));
                int colorRes = defaultCarInfo.getBatSoc() > 10 ? R.color.common_blue_384967 : R.color.color_ec0000;
                mTvSurplusElectricity.setTextColor(mContext.getResources().getColor(colorRes));
                mTvSurplusElectricity.setText(String.valueOf(defaultCarInfo.getBatSoc()));
                //显示车辆位置
                mRlCarPosition.setVisibility(View.VISIBLE);
                if(!TextUtils.isEmpty(defaultCarInfo.getAddr())){
                    mTvCarPosition.setText(defaultCarInfo.getAddr());
                }else{
                    getCarLocation(defaultCarInfo.getLatitude(), defaultCarInfo.getLongitude());
                }

            }

        }

        /**
         * 获取新能源汽车信息
         */
        private void getBasicCarInfo(String vin) {
            L00bangRequestManager2.getServiceInstance()
                    .getCarBasicInfo(vin)
                    .compose(L00bangRequestManager2.setSchedulers())
                    .subscribe(new BackgroundSubscriber<DefaultCarInfo>(mContext) {
                        @Override
                        public void onSuccess(DefaultCarInfo defaultCarInfo) {
                            super.onSuccess(defaultCarInfo);
                            defaultCarInfo.setSaveTime(System.currentTimeMillis());
                            updateUi(defaultCarInfo);
                            //插入或者替换
                            VertifyCarListInfoManager.getInstance().insertOrReplace(defaultCarInfo);
                        }

                        @Override
                        public void onFailure(Throwable e) {
                            super.onFailure(e);

                        }
                    });
        }

        /**
         * 根据经纬度获取位置信息
         */
        private void getCarLocation(double lat, double lng) {
            //根据经纬度获取地址信息
            EocoderUtils.getAddressInfo(mContext, lat, lng, new EocoderUtils.OnResultListener() {
                @Override
                public void onSuccess(BaiduEocoderResult.EocodeAddressInfo result) {
                    if (!TextUtils.isEmpty(result.getFormatted_address())) {
                        mTvCarPosition.setText(result.getFormatted_address());
                    } else {
                        String address = mContext.getResources().getString(R.string.txt_location_failure);
                        mTvCarPosition.setText(address);
                    }
                }

                @Override
                public void onFailure(String message) {
                    String address = mContext.getResources().getString(R.string.txt_location_failure);
                    mTvCarPosition.setText(address);
                }
            });

        }

    }

}
