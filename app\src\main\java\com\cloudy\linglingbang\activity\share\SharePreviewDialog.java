package com.cloudy.linglingbang.activity.share;

import android.content.Context;
import android.content.DialogInterface;
import android.text.Html;
import android.text.InputFilter;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.im.ImConfig;
import com.cloudy.linglingbang.app.util.DensityUtil;
import com.cloudy.linglingbang.app.util.DeviceUtil;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.InputLengthLimitFilter;
import com.cloudy.linglingbang.app.widget.dialog.alert.AlertController;
import com.cloudy.linglingbang.app.widget.dialog.alert.NewCommonAlertDialog;
import com.cloudy.linglingbang.app.widget.image.ImageLoadUtils;
import com.cloudy.linglingbang.model.chat.PostShareMessage;
import com.cloudy.linglingbang.model.postcard.PostCard;

import androidx.lifecycle.MediatorLiveData;
import io.rong.imlib.model.Conversation;
import io.rong.imlib.model.MessageContent;
import io.rong.message.TextMessage;

/**
 * 分享预览
 *
 * <AUTHOR>
 * @date 2022/4/14
 */
public class SharePreviewDialog extends NewCommonAlertDialog {
    /**
     * 视频icon标志
     */
    private View videoMark;
    /**
     * 帖子消息标题
     */
    private TextView mTitle;
    /**
     * 帖子消息图片
     */
    private ImageView mImg;
    /**
     * 帖子消息内容
     */
    private TextView mContent;
    /**
     * 消息来源
     */
    private TextView mSource;

    private EditText mEdContent;
    private ImageView mIvHeadImg;
    private TextView mTvName;
    /**
     * 帖子消息
     */
    private PostShareMessage mShareMessage;
    private MediatorLiveData<Integer> liveData;
    /**
     * 用户id或者群聊id
     */
    private String targetId;
    /**
     * 用户昵称或者群聊名称
     */
    private String title;
    /**
     * 用户头像或者群聊头像
     */
    private String image;
    private Conversation.ConversationType mConversationType;

    public SharePreviewDialog(Context context) {
        super(context);
        AlertController alertController = getAlertController();
        alertController.setButton(DialogInterface.BUTTON_POSITIVE, context.getString(R.string.commit_send), new OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (mShareMessage != null) {
                    sendMessage(mShareMessage, liveData);
                }
                //文本消息
                String text = mEdContent.getText().toString();
                if (!TextUtils.isEmpty(text)) {
                    sendMessage(TextMessage.obtain(text), null);
                }
            }
        }, null);
        setCanceledOnTouchOutside(false);
    }

    private void sendMessage(MessageContent messageContent, MediatorLiveData<Integer> liveData) {
        if (mConversationType == Conversation.ConversationType.GROUP) {
            ImConfig.sendMessageToGroup(messageContent, targetId, liveData);
        } else {
            ImConfig.sendMessage(messageContent, targetId, liveData);
        }
    }

    @Override
    protected void initView() {
        super.initView();

        mTitle = findViewById(R.id.tv_title);
        mContent = findViewById(R.id.tv_content);
        mSource = findViewById(R.id.tv_source);
        mImg = findViewById(R.id.iv_img);
        videoMark = findViewById(R.id.iv_video_mark);
        mEdContent = findViewById(R.id.ed_content);
        mTvName = findViewById(R.id.tv_nickName);
        mIvHeadImg = findViewById(R.id.iv_head);
        mEdContent.setFilters(new InputFilter[]{new InputLengthLimitFilter(5000, true, "字数超出上限")});
        if (!TextUtils.isEmpty(mShareMessage.getImageUrl())) {
            mImg.setVisibility(View.VISIBLE);
            ImageLoadUtils.createImageLoad(mImg, mShareMessage.getImageUrl())
                    .setCircle(true)
                    .setRadius(DensityUtil.dip2px(mImg.getContext(), 4f))
                    .setDoNotLoadWebp().load();
            if (mShareMessage.getPostTypeId() == PostCard.PostType.SHORT_VIDEO || mShareMessage.getPostTypeId() == PostCard.PostType.VIDEO_POST) {
                videoMark.setVisibility(View.VISIBLE);
            } else {
                videoMark.setVisibility(View.GONE);
            }
        } else {
            videoMark.setVisibility(View.GONE);
            mImg.setVisibility(View.GONE);
        }
        mTitle.setText(mShareMessage.getTitle());
        if (TextUtils.isEmpty(mShareMessage.getSource())) {
            mSource.setText(R.string.app_name);
        } else {
            mSource.setText(mShareMessage.getSource());
        }
        mContent.setText(mShareMessage.getContent());
        SpannableString stringBuilder = new SpannableString(Html.fromHtml(title));
        stringBuilder.setSpan(new ForegroundColorSpan(mTvName.getCurrentTextColor()), 0, stringBuilder.length(), SpannableString.SPAN_INCLUSIVE_INCLUSIVE);
        mTvName.setText(stringBuilder);
        ImageLoad.LoadUtils.loadAvatar(getContext(), mIvHeadImg, image);
    }

    public void setShareMessage(PostShareMessage shareMessage) {
        mShareMessage = shareMessage;
    }

    public void setLiveData(MediatorLiveData<Integer> liveData) {
        this.liveData = liveData;
    }

    public void setTargetId(String targetId) {
        this.targetId = targetId;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public void setConversationType(Conversation.ConversationType conversationType) {
        mConversationType = conversationType;
    }

    @Override
    protected int getDefaultLayoutResId() {
        return R.layout.dialog_share_preview;
    }

    @Override
    public void show() {
        super.show();
        Window window = getWindow();
        WindowManager.LayoutParams params = window.getAttributes();
        params.width = Math.max(DensityUtil.dip2px(mContext, 320f), DeviceUtil.getScreenWidth() * 310 / 375);
        window.setAttributes(params);
    }
}