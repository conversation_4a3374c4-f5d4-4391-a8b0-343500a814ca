package com.cloudy.linglingbang.activity.car.energy.bluetooth;

/**
 * 蓝牙连接失败枚举
 */
public enum ConnectFailEnum {

    AUTH_FAIL(1, "蓝牙连接失败，请下拉刷新重试", "鉴权失败"),
    BLUE_KEY_ERROR(2, "连接蓝牙失败", "蓝牙钥匙异常"),
    TIME_OUT(3, "连接蓝牙超时", "连接蓝牙超时");

    /**
     * @param code  蓝牙连接错误代码
     * @param desc  蓝牙连接错误描述
     * @param desc  蓝牙连接错误埋点描述
     */
    ConnectFailEnum(int code, String desc, String pointDesc) {
        this.code = code;
        this.desc = desc;
        this.pointDesc = pointDesc;
    }

    private final int code;
    private final String desc;
    private final String pointDesc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getPointDesc() {
        return pointDesc;
    }
}
