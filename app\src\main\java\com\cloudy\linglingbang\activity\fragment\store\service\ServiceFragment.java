package com.cloudy.linglingbang.activity.fragment.store.service;

import android.content.Context;
import android.view.View;
import android.view.animation.TranslateAnimation;
import android.widget.CompoundButton;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseScrollTabViewPagerFragment;
import com.cloudy.linglingbang.activity.fragment.store.home.model.StoreHomeConstant;
import com.cloudy.linglingbang.activity.fragment.store.rights.VipRightsFragment;
import com.cloudy.linglingbang.activity.fragment.store.youpin.SuperiorProductFragment;
import com.cloudy.linglingbang.app.util.DeviceUtil;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.PagerSlidingTabStrip;
import com.cloudy.linglingbang.app.widget.textview.CenterDrawableTextView;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;
import butterknife.BindView;
import butterknife.OnClick;

/**
 * 服务fragment
 *
 * <AUTHOR>
 * @date 2021/12/15
 */
public class ServiceFragment extends BaseScrollTabViewPagerFragment<Fragment> {
    @BindView(R.id.tv_indicator)
    View mTvIndicator;
    @BindView(R.id.tv_left)
    CenterDrawableTextView mTvLeft;
    @BindView(R.id.tv_right)
    CenterDrawableTextView mTvRight;
    @BindView(R.id.tv_life_payment)
    CenterDrawableTextView mTvLifePayment;
    private float mTvIndicatorWidth;

    public static Fragment newInstance() {
        return new VipRightsFragment();
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_service;
    }

    @Override
    protected List<Fragment> createAdapterData() {
        List<Fragment> fragmentList = new ArrayList<>();
        fragmentList.add(SuperiorProductFragment.newInstance(StoreHomeConstant.PAGE_CODE_GOODS_SERVICE, "服务-推荐", false));
        fragmentList.add(ServiceStoreFragment.newInstance(StoreHomeConstant.PAGE_CODE_SERVICE_GOODS));
        fragmentList.add(SelectPartsFragment.newInstance(StoreHomeConstant.PAGE_CODE_SERVICE_GOODS));

        return fragmentList;
    }

    @Override
    protected void initViews() {
        super.initViews();
        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) mTvIndicator.getLayoutParams();
        mTvIndicatorWidth = DeviceUtil.getScreenWidth() / 3;
        layoutParams.width = (int) mTvIndicatorWidth;
        mTvIndicator.setLayoutParams(layoutParams);
        tabs.setShouldExpand(true);
        LinearLayout.LayoutParams leftParam = (LinearLayout.LayoutParams) mTvLeft.getLayoutParams();
        leftParam.width = (int) mTvIndicatorWidth;
        mTvLeft.setLayoutParams(leftParam);
        LinearLayout.LayoutParams rightParam = (LinearLayout.LayoutParams) mTvRight.getLayoutParams();
        rightParam.width = (int) mTvIndicatorWidth;
        mTvRight.setLayoutParams(rightParam);
        LinearLayout.LayoutParams lifePaymentParam = (LinearLayout.LayoutParams) mTvLifePayment.getLayoutParams();
        lifePaymentParam.width = (int) mTvIndicatorWidth;
        mTvLifePayment.setLayoutParams(lifePaymentParam);
        mTvLeft.setText(titles[0]);
        mTvRight.setText(titles[1]);
        mTvLifePayment.setText(titles[2]);
        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                if (position == 0) {
                    mTvLeft.setChecked(true);
                    mTvLeft.setEnabled(false);
                    mTvRight.setChecked(false);
                    mTvRight.setEnabled(true);
                    mTvLifePayment.setChecked(false);
                    mTvLifePayment.setEnabled(true);
                } else if (position == 1) {
                    mTvLeft.setChecked(false);
                    mTvLeft.setEnabled(true);
                    mTvRight.setChecked(true);
                    mTvRight.setEnabled(false);
                    mTvLifePayment.setChecked(false);
                    mTvLifePayment.setEnabled(true);
                } else {
                    mTvLeft.setChecked(false);
                    mTvRight.setChecked(false);
                    mTvLeft.setEnabled(true);
                    mTvRight.setEnabled(true);
                    mTvLifePayment.setChecked(true);
                    mTvLifePayment.setEnabled(false);
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
    }

    @Override
    protected PagerAdapter createViewPagerAdapter(List<Fragment> data, String[] titles) {
        tabs.setListener(new PagerSlidingTabStrip.SingleListener() {
            @Override
            public TextView createTextTab(Context context, int position) {
                TextView textView = new CompoundButton(context) {
                    @Override
                    public void toggle() {
//                        super.toggle();
                    }
                };
                textView.setBackgroundResource(R.drawable.select_refit_txt2);
                return textView;
            }
        });
        return new FragmentStatePagerAdapter(getChildFragmentManager()) {

            @Override
            public Fragment getItem(int position) {
                return data.get(position);
            }

            @Override
            public int getCount() {
                return data.size();
            }

            @Nullable
            @Override
            public CharSequence getPageTitle(int position) {
                return titles[position];
            }
        };
    }

    @OnClick({R.id.tv_left, R.id.tv_right, R.id.tv_life_payment})
    void clicks(View view) {
        switch (view.getId()) {
            case R.id.tv_left:
                onItemSelected(0);
                break;
            case R.id.tv_right:
                onItemSelected(1);
                break;
            case R.id.tv_life_payment:
                onItemSelected(2);
                break;
        }
    }

    @Override
    protected void onItemSelected(int position) {
        super.onItemSelected(position);
        TranslateAnimation animation = new TranslateAnimation(Math.abs(mTvIndicatorWidth * (position - 1)), mTvIndicatorWidth * (position), 0, 0);
        animation.setDuration(200);
        animation.setFillAfter(true);
        mTvIndicator.startAnimation(animation);
        mViewPager.setCurrentItem(position);
        SensorsUtils.sensorsClickBtn("点击服务-" + titles[position], "好物", "好物");

    }

    @Override
    protected String[] getTitles() {
        return new String[]{"推荐", "选服务", "选配件"};
    }
}
