package com.cloudy.linglingbang.activity.car.add.fragment;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import com.cloudy.linglingbang.ApplicationLLB;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.activity.basic.BaseFragment;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.car.add.AddCarActivity;
import com.cloudy.linglingbang.activity.car.add.AddCarController;
import com.cloudy.linglingbang.app.util.InputMethodUtils;
import com.cloudy.linglingbang.app.util.ValidatorUtils;
import com.cloudy.linglingbang.app.widget.BaseOnClickListener;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

/**
 * 添加爱车基类 Fragment
 *
 * <AUTHOR>
 * @date 2019/3/19
 */
public class BaseAddCarFragment extends BaseFragment {
    /**
     * 当前步数
     * 用于判断展示，所以由 Fragment 持有，而不让 Activity 持有当前步数
     */
    private int mStep;
    /**
     * 下一步
     */
    protected Button mBtnNext;

    private List<ValidatorUtils.Validator> mValidatorList;

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_base_add_car;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mStep = IntentUtils.getFragmentIntArgument(this);
    }

    @Override
    protected void initViews() {
        super.initViews();
        FragmentActivity activity = getActivity();
        if (activity instanceof BaseActivity) {
            ((BaseActivity) activity).setMiddleTitle(getTitle(mStep).toString());
        }
        mBtnNext = mRootView.findViewById(R.id.btn_next);
        if (mBtnNext != null) {
            mBtnNext.setOnClickListener(new BaseOnClickListener() {
                @Override
                public void onClick(View v) {
                    super.onClick(v);
                    onClickNextStep();
                }
            });
        }
        mValidatorList = new ArrayList<>();
        initValidatorList(mValidatorList);
    }

    protected void bindEditTextNextAction(View... views) {
        for (View view : views) {
            if (view instanceof EditText) {
                EditText editText = (EditText) view;
                editText.setImeOptions(EditorInfo.IME_ACTION_NEXT);
                editText.setOnEditorActionListener(new TextView.OnEditorActionListener() {
                    @Override
                    public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                        if (actionId == EditorInfo.IME_ACTION_NEXT) {
                            onClickNextStep();
                            return true;
                        }
                        return false;
                    }
                });
            }
        }
    }

    /**
     * 注意如果用到 view 要放在 initViews 之后
     */
    protected void initValidatorList(List<ValidatorUtils.Validator> validatorList) {

    }

    /**
     * 获取标题
     */
    private CharSequence getTitle(int step) {
        int titleResId = 0;
        switch (step) {
            case AddCarController.STEP_FILL_CAR_INFO:
                titleResId = R.string.title_add_car_step_fill_car_info;
                break;
            case AddCarController.STEP_REAL_PERSON_AUTH:
                titleResId = R.string.title_add_car_step_show_authentication_info;
                break;
            case AddCarController.STEP_SET_SAFE_CODE:
                titleResId = R.string.title_add_car_step_set_safe_code;
                break;
            case AddCarController.STEP_SCAN_ACTIVE:
                titleResId = R.string.title_add_car_step_scan_active;
                break;
            case AddCarController.STEP_ACTIVE_FINISH:
                titleResId = R.string.title_add_car_step_add_finish;
                break;
            default:
                break;
        }
        if (titleResId != 0) {
            Context context = getContext();
            if (context != null) {
                return context.getText(titleResId);
            }
        }
        return "";
    }

    /**
     * 点击下一步
     */
    protected void onClickNextStep() {
        if (checkGoNextStep()) {
            prepareAndGoNextStep();
        }
    }

    /**
     * 检查是否可以跳到下一步
     */
    protected boolean checkGoNextStep() {
        if (mValidatorList != null) {
            for (ValidatorUtils.Validator validator : mValidatorList) {
                if (!validator.isValid()) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 检查完，准务跳转
     */
    protected void prepareAndGoNextStep() {
        goNextStep();
    }

    protected void goNextStep() {
        AddCarController addCarController = getAddCarController();
        if (addCarController != null) {
            addCarController.goNextStep(mStep);
        }
        Context context = getContext();
        if (context != null) {
            InputMethodUtils.hideInputMethod(context, null);
        }
    }

    public AddCarController getAddCarController() {
        FragmentActivity activity = getActivity();
        if (activity instanceof AddCarActivity) {
            return ((AddCarActivity) activity).getAddCarController();
        }
        return null;
    }

    public int getStep() {
        return mStep;
    }

    /**
     * 获取传入的 vin
     */
    @NonNull
    public String getVin() {
        AddCarController addCarController = getAddCarController();
        if (addCarController != null) {
            return addCarController.getAddCarInfo().getVin();
        } else {
            return "";
        }
    }

    protected void openScan() {
        FragmentActivity activity = getActivity();
        if (activity instanceof AddCarActivity) {
            ((AddCarActivity) activity).openScan();
        }
    }

    /**
     * 总是有为 null 警告，心烦
     */
    public Context getNotNullContext() {
        Context context = super.getContext();
        if (context != null) {
            return context;
        } else {
            if (mRootView != null) {
                return mRootView.getContext();
            } else {
                Activity activity = ApplicationLLB.currentActivity;
                if (activity != null) {
                    return activity;
                } else {
                    return ApplicationLLB.getInstance();
                }
            }
        }
    }

    /**
     * 得到扫码结果
     */
    public void onGetScanResult(String scanResult) {}
}
