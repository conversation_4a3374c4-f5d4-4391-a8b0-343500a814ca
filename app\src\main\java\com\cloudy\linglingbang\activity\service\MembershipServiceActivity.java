package com.cloudy.linglingbang.activity.service;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.ApplicationLLB;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.user.AccountInfoActivity;
import com.cloudy.linglingbang.activity.welfare.CarInfoEvent;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.DeprecatedUtils;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.widget.banner.BaseBannerView;
import com.cloudy.linglingbang.app.widget.dialog.CommonPromptDialog;
import com.cloudy.linglingbang.app.widget.imagetext.ImageTextViewHolder;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.entrance.FunctionEntrance;
import com.cloudy.linglingbang.model.entrance.FunctionEntranceEnum;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BackgroundSubscriber;
import com.cloudy.linglingbang.model.server.MyCar;
import com.cloudy.linglingbang.model.user.User;

import java.util.ArrayList;
import java.util.List;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**
 * 会员服务
 *
 * <AUTHOR> create at 2016/12/13 12:24
 */
public class MembershipServiceActivity extends BaseActivity {
    private static final int SPAN_COUNT = 4;
    /**
     * 带有立刻认证按钮的布局
     */
    @BindView(R.id.ll_authentication_button)
    LinearLayout mllAuthenticationButton;
    /**
     * 车辆轮播图
     */
    @BindView(R.id.rl_banner)
    RelativeLayout mRlBanner;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_membership_service);
    }

    @Override
    protected void initialize() {
        /*Glide.with(this).load(R.drawable.bg_membership_service).into(new SimpleTarget<GlideDrawable>() {
            @Override
            public void onResourceReady(GlideDrawable resource, GlideAnimation<? super GlideDrawable> glideAnimation) {
                RelativeLayout rlTop = (RelativeLayout) findViewById(R.id.rl_top);
                rlTop.setBackgroundDrawable(resource);
            }
        });*/
        findViewById(R.id.iv_back).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
        initServices();
        getMyCarInfo();
    }

    /**
     * 获取我的爱车的信息
     */
    private void getMyCarInfo() {
        if (UserUtils.isAuthenticationUser()) {
            mRlBanner.setVisibility(View.VISIBLE);
            mllAuthenticationButton.setVisibility(View.GONE);
            //如果是认证车主
            L00bangRequestManager2
                    .getServiceInstance()
                    .getMyCarsInfo()
                    .compose(L00bangRequestManager2.setSchedulers())
                    .subscribe(new BackgroundSubscriber<List<MyCar>>(this) {
                        @Override
                        public void onSuccess(List<MyCar> myCars) {
                            super.onSuccess(myCars);
                            initMyCar(myCars);
                        }
                    });
        } else {
            //如果不是认证车主，则加载去认证按钮
            mRlBanner.setVisibility(View.GONE);
            mllAuthenticationButton.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 初始化下方的各服务
     */
    private void initServices() {
        RecyclerView recyclerView = findViewById(R.id.recycler_view);
        if (recyclerView == null) {
            return;
        }
        recyclerView.setLayoutManager(new GridLayoutManager(this, SPAN_COUNT));
//        recyclerView.addItemDecoration(new GridItemDecoration(getResources().getDrawable(R.drawable.item_divider_vertical_line), getResources().getDrawable(R.drawable.item_divider), SPAN_COUNT));
        recyclerView.setAdapter(new MembershipServiceAdapter(this, getMembershipServiceData()));
    }

    /**
     * 创建服务
     */
    private List<FunctionEntrance> getMembershipServiceData() {
        List<FunctionEntrance> serviceList = new ArrayList<>();
        serviceList.add(FunctionEntrance.create(this, FunctionEntranceEnum.DAILY_SIGN, R.string.membership_service_sign, R.drawable.ic_membership_service_sign_in));
        serviceList.add(FunctionEntrance.create(this, FunctionEntranceEnum.SERVICE_MM, R.string.membership_service_customer_service, R.drawable.ic_membership_service_customer_service, 7));
        serviceList.add(FunctionEntrance.create(this, FunctionEntranceEnum.ELECTRONIC_MANUAL, R.string.membership_service_electronic_manual, R.drawable.ic_function_entrance_electronic_manual, 142));
        serviceList.add(FunctionEntrance.create(this, FunctionEntranceEnum.SHOP_4S, R.string.membership_service_4s, R.drawable.ic_function_entrance_4s, 142));
        serviceList.add(FunctionEntrance.create(this, FunctionEntranceEnum.SUBSTITUTION, R.string.membership_service_car_replacement, R.drawable.ic_function_entrance_substitution, 119));
        serviceList.add(FunctionEntrance.create(this, FunctionEntranceEnum.MAINTENANCE_RECORD, R.string.membership_service_maintenance_record, R.drawable.ic_function_entrance_maintenance_record, 115));
        serviceList.add(FunctionEntrance.create(this, FunctionEntranceEnum.DISCOUNT_INFO, R.string.membership_service_news, R.drawable.ic_function_entrance_discount_info, 0));
//        serviceList.add(FunctionEntrance.create(this, FunctionEntranceEnum.TECH, R.string.membership_service_ask_technician, R.drawable.ic_membership_service_ask_technician, 6));
        serviceList.add(FunctionEntrance.create(this, FunctionEntranceEnum.APPOINTMENT_SERVICE, R.string.membership_service_maintenance, R.drawable.ic_membership_service_maintenance, 9));
//        serviceList.add(FunctionEntrance.create(this, FunctionEntranceEnum.SERVICE_COMMENT, R.string.membership_service_comment, R.drawable.ic_membership_service_comment, 0));
//        serviceList.add(FunctionEntrance.create(this, FunctionEntranceEnum.ILLEGAL_QUERY, R.string.membership_service_violation_query, R.drawable.ic_membership_service_violation_query, 11));
//        serviceList.add(FunctionEntrance.create(this, FunctionEntranceEnum.CREDITS_EXCHANGE, R.string.membership_service_shop, R.drawable.ic_membership_service_shop, 101));
//        serviceList.add(FunctionEntrance.create(this, FunctionEntranceEnum.LIFE_SERVICE, R.string.function_entrance_life_service, R.drawable.ic_function_entrance_life_service, 142));
        serviceList.add(FunctionEntrance.create(this, FunctionEntranceEnum.DISCOUNT_GAS, R.string.membership_service_fuel, R.drawable.ic_function_entrance_discount_gas, 10));
//        serviceList.add(FunctionEntrance.create(this, FunctionEntranceEnum.COUPON_CENTER, R.string.membership_service_coupon_center, R.drawable.ic_function_entrance_coupon_list, 142));
        serviceList.add(FunctionEntrance.create(this, FunctionEntranceEnum.EMERGENCY_RESCUE, R.string.membership_service_emergency_assistance, R.drawable.ic_function_entrance_emergency_rescue, 102));
        serviceList.add(FunctionEntrance.create(this, FunctionEntranceEnum.SECOND_HAND_CAR, R.string.membership_service_second_hand_car, R.drawable.ic_function_second_hand_car));
//        serviceList.add(FunctionEntrance.create(this, FunctionEntranceEnum.RECOMMEND_AWARD, R.string.membership_service_recommend_award, R.drawable.ic_function_entrance_recommend_award, 118));
//        serviceList.add(FunctionEntrance.create(this, FunctionEntranceEnum.MEMBER_CHANNEL, R.string.membership_service_community, R.drawable.ic_function_entrance_member_channel, 14));
        return serviceList;
    }

    /**
     * 根据车初始化banner
     */
    private void initMyCar(List<MyCar> carList) {
        if (carList == null || carList.size() <= 0) {
            return;
        }
        MyCarHolder mMyCarHolder = new MyCarHolder();
        BaseBannerView<MyCar> bannerView = new BaseBannerView<MyCar>(this) {

            @Override
            public int[] getIndicatorImageResId() {
                return new int[]{R.drawable.ic_my_car_indicator_n, R.drawable.ic_my_car_indicator_s};
            }

            @Override
            protected View createAdView(Context context, ViewGroup container, int position) {
                return mMyCarHolder.createView(context);
            }

            @Override
            protected void bindAdView(View view, int position) {
                if (position >= 0 && position < carList.size()) {
                    mMyCarHolder.UpdateUI(view.getContext(), position, carList.get(position));
                }
            }
        };
        bannerView.setAdList(carList);
        bannerView.refresh();
        if (mRlBanner != null) {
            mRlBanner.removeAllViews();
            mRlBanner.addView(bannerView, new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, RelativeLayout.LayoutParams.MATCH_PARENT));
        }
    }

    /**
     * 点击去认证按钮
     */
    @OnClick(R.id.bt_authentication_right_now)
    public void goAuthentication() {
        if (!AppUtil.checkLogin(this)) {
            return;
        }
        if (UserUtils.isAuthenticationUser()) { // 认证通过
            //如果已经认证通过,进入认证编辑页面（防止用户一直停留在该页面，没有刷新数据）
            IntentUtils.startActivity(ApplicationLLB.currentActivity, AccountInfoActivity.class);
        } else {
            checkAuthenticationStatus(false);
        }
    }

    /**
     * 检查认证状态
     */
    private void checkAuthenticationStatus(boolean showDialog) {
        if (UserUtils.isGoingAuthentication()) { // 认证中
            JumpPageUtil.goAuthenticationPage(this);
        } else if (User.shareInstance().getCheckStatus() == User.checkStatusFlag.USER_AUTH_FAILED_AUTH) { // 认证失败
            JumpPageUtil.goAuthenticationPage(this);
        } else if (showDialog) { // 未认证 显示对话框
            Dialog dialog = new CommonPromptDialog.Builder(this)
                    .setButtonTextAndStrokeColor(Color.parseColor("#519BFE"))
                    .setIcon(R.drawable.ic_dialog_car_owner_verify_fail)
                    .setMessage("您还没有认证车主，不能预约哦！")
                    .setPositiveButton("立刻认证车主", new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            JumpPageUtil.goAuthenticationPage(MembershipServiceActivity.this);
                        }
                    })
                    .create();
            dialog.show();
        } else { // 未认证 进入认证页面
            JumpPageUtil.goAuthenticationPage(this);
        }
    }

    class MyCarHolder /*implements Holder<MyCar>*/ {

        @BindView(R.id.iv_image)
        ImageView iv_image;
        @BindView(R.id.tv_car_name)
        TextView tv_car_name;
        @BindView(R.id.tv_car_type)
        TextView tv_car_type;
        @BindView(R.id.ll_maintenance)
        LinearLayout ll_maintenance;
        @BindView(R.id.tv_maintenance_day)
        TextView tv_maintenance_day;
        @BindView(R.id.ll_insurance)
        LinearLayout ll_insurance;
        @BindView(R.id.tv_insurance_day)
        TextView tv_insurance_day;
        @BindView(R.id.ll_notice)
        LinearLayout ll_notice;

        //        @Override
        public View createView(Context context) {
            //root是banner
            View view = DeprecatedUtils.inflateWithNullRoot(LayoutInflater.from(context), R.layout.item_membership_service_my_car);
            ButterKnife.bind(this, view);
            return view;
        }

        //        @Override
        public void UpdateUI(Context context, int position, MyCar data) {
            if (data == null) {
                return;
            }
            String url = data.getIcon();
            new ImageLoad(context, iv_image, url, ImageLoad.LoadMode.URL)
                    .setPlaceholderAndError(R.drawable.bg_default_car)
                    .load();
            tv_car_name.setText(data.getFullName());
            tv_car_type.setText(data.getName());
            boolean show = false;
            if (data.getMaintainDiffdays() != null && data.getMaintainDiffdays() >= 0) {
                show = true;
                ll_maintenance.setVisibility(View.VISIBLE);
                tv_maintenance_day.setText(getString(R.string.membership_service_maintenance_day, data.getMaintainDiffdays()));
            } else {
                ll_maintenance.setVisibility(View.GONE);
            }
            if (data.getInsuranceDiffdays() != null && data.getInsuranceDiffdays() >= 0) {
                show = true;
                ll_insurance.setVisibility(View.VISIBLE);
                tv_insurance_day.setText(getString(R.string.membership_service_insurance_day, data.getInsuranceDiffdays()));
            } else {
                ll_insurance.setVisibility(View.GONE);
            }
            ll_notice.setVisibility(show ? View.VISIBLE : View.GONE);
        }
    }

    class MembershipServiceAdapter extends BaseRecyclerViewAdapter<FunctionEntrance> {

        public MembershipServiceAdapter(Context context, List<FunctionEntrance> data) {
            super(context, data);
        }

        @Override
        protected BaseRecyclerViewHolder<FunctionEntrance> createViewHolder(View itemView) {
            ImageTextViewHolder<FunctionEntrance> viewHolder = new ImageTextViewHolder<>(itemView);
            viewHolder.setOnItemClickListener(new BaseRecyclerViewHolder.OnItemClickListener() {
                @Override
                public void onItemClick(View itemView, int position) {
                    JumpPageUtil.goHomeFunctionEntrance(mContext, mData.get(position), "会员服务",new CarInfoEvent(""));
                }
            });
            return viewHolder;
        }

        @Override
        protected int getItemLayoutRes(int viewType) {
            return R.layout.item_membership_service;
        }
    }
}
