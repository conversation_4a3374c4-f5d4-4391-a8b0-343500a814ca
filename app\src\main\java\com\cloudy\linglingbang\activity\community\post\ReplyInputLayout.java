package com.cloudy.linglingbang.activity.community.post;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ChooseImageController;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.InputMethodUtils;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.UploadImageController;
import com.cloudy.linglingbang.app.util.file.FileUtils;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.ExpressionViewPager;
import com.cloudy.linglingbang.app.widget.SoftKeyBoardListener;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.Comment;
import com.cloudy.linglingbang.model.postcard.QuickReply;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.user.User;

import java.util.ArrayList;
import java.util.List;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**
 * 回复帖子及回复的布局
 * <br/>回复有，回复帖子，回复楼层，回复楼中楼
 * <br/>①表情流程，初始化{@linkplain ExpressionViewPager}，调用{@linkplain ExpressionViewPager#setEditText(EditText)}
 * <br/>点击表情按钮时，切换显示
 *
 * <AUTHOR> create at 2016/10/19 14:53
 */
public class ReplyInputLayout extends RelativeLayout {

    /**
     * 图片上限
     */
    private static final int MAX_IMAGE_COUNT = 9;

    private String eventPage;

    /**
     * The post id.
     */
    private long mPostId;
    /**
     * The comment id.
     */
    private long mCommentId;

    /**
     * 当前可提交的comment信息
     */
    private Comment currentComment;
    /**
     * 发送按钮
     */
    @BindView(R.id.btn_send)
    Button btn_send;
    /**
     * 编辑框
     */
    @BindView(R.id.et_content)
    EditText et_content;

    @BindView(R.id.iv_expression)
    ImageView iv_expression;

    @BindView(R.id.expression_view_pager)
    ExpressionViewPager viewPager;

    /**
     * 选择照片按钮: 只有在回复楼主的时候才显示
     */
    @BindView(R.id.iv_add_photo)
    ImageView iv_add_photo;

    @BindView(R.id.rl_input)
    RelativeLayout rl_input;

    @BindView(R.id.rl_root)
    RelativeLayout rl_root;

    @BindView(R.id.fl_chosen_images)
    FrameLayout fl_chosen_images;

    @BindView(R.id.recycler_view_chosen_images)
    RecyclerView recycler_view_chosen_images;

    @BindView(R.id.ll_cool)
    LinearLayout ll_cool;

    private ChooseImageController mChooseImageController;

    private UploadImageController mUploadImageController;

    private ReplyInputCallBack mReplyInputCallBack;
    /**
     * 如果回复主题，赋为-1
     */
    private int mCommentPosition;
    /**
     * 最大输入字数
     */
    private int mMaxLength;
    private TextWatcher mTextWatcher;

    /**
     * 是否展示图片和表情
     */
    private boolean mIsShowExpressAndPic = true;
    /**
     * 是否展示图片和表情
     */
    private boolean mIsShowQuickReply = true;

    /**
     * 快捷回复短语列表
     */
    private final List<QuickReply> mQuickReplies = new ArrayList<>();

    /**
     * 当前快捷回复的索引
     */
    private int mQuickReplyIndex;
    /**
     * 帖子评论提示文案
     */
    private String mReplyPostHint;
    /**
     * 回复评论提示文案
     */
    private String mReplyPostCommentHint;

    public ReplyInputLayout(Context context) {
        super(context);
        initView();
    }

    public ReplyInputLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public ReplyInputLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    public void showInput(long postId) {
        showInput(postId, -1, 0, null, null);
    }

    /**
     * Show input.
     *
     * @param postId          帖子id，用来回复
     * @param commentPosition 评论位置，用来回复成功后更新
     * @param commentId       回复id，用来标记是否相同，相同时保留上一次输入的内容，否则清容
     * @param commentParentId 回复的parent id，用来回复楼层和楼中楼
     * @param replyUser       回复的用户
     */
    public void showInput(long postId, int commentPosition, long commentId, String commentParentId, User replyUser) {
        mCommentPosition = commentPosition;
        if (mCommentId != commentId) {
            //如果2个id不相等，或是由回复帖子转为回复楼层，清除，如果相等可以保留
            et_content.getText().clear();
            if (mChooseImageController != null) {
                List<String> chosenImageList = mChooseImageController.getChosenImageList();
                if (chosenImageList != null) {
                    chosenImageList.clear();
                }
            }
            updateChosenImage();
        }
        mPostId = postId;
        mCommentId = commentId;
        //是否显示选择图片图片
        if (commentId == 0) {
            et_content.setHint(mReplyPostHint);
            if (mIsShowExpressAndPic) {
                iv_add_photo.setVisibility(View.VISIBLE);
            } else {
                iv_add_photo.setVisibility(View.GONE);
            }
        } else {
            if (replyUser != null) {
                et_content.setHint("回复" + replyUser.getNickname() + mReplyPostCommentHint);
            }
            iv_add_photo.setVisibility(View.GONE);
            //不需要关闭显示图片，上面的id不同会调用
        }
        if (!mIsShowExpressAndPic) {
            iv_expression.setVisibility(GONE);
        }
        ll_cool.setVisibility(mIsShowQuickReply ? VISIBLE : GONE);
        this.setVisibility(View.VISIBLE);
        showKeyboard();

        //初始化评论，post id在提交时传，commentContent在点按钮时赋值，图片在上传成功时赋值
        //可以复用
        if (currentComment == null) {
            currentComment = new Comment();
        }
        if (commentId == 0) {
            currentComment.setReplyUserId(null);
            currentComment.setCommentParentId(null);
        } else {
            if (replyUser != null) {
                currentComment.setReplyUserId(replyUser.getUserIdStr());
            }
            currentComment.setCommentParentId(commentParentId);
            currentComment.setImages(null);
        }
    }

    private void initView() {
        mMaxLength = getResources().getInteger(R.integer.post_detail_reply_max_length);
        mReplyPostHint = "回复楼主";
        mReplyPostCommentHint = "";
        LayoutInflater.from(getContext()).inflate(R.layout.layout_reply_input, this, true);
        ButterKnife.bind(this);
        viewPager.setFromReply(true);
        mTextWatcher = new TextWatcher() {
            private int mStart;
            private int mAfter;

            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                mStart = start;
                mAfter = after;
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                int over = s.length() - mMaxLength;
                if (over > 0) {
                    //如果有超出
                    //防止循环，这里如果不删除，虽然不会循环，但会多调用一次
                    et_content.removeTextChangedListener(mTextWatcher);
                    s.delete(mStart + mAfter - over, mStart + mAfter);
                    ToastUtil.showMessage(getContext(), getContext().getString(R.string.post_detail_input_reply_max_length, mMaxLength));
                    et_content.addTextChangedListener(mTextWatcher);
                }
            }
        };
        et_content.addTextChangedListener(mTextWatcher);
        viewPager.setEditText(et_content, mMaxLength);

        //监听键盘状态，键盘弹出时关闭表情键盘的显示
        Activity activity = AppUtil.getActivity(getContext());
        if (activity != null) {
            SoftKeyBoardListener.setListener(AppUtil.getActivity(getContext()), new SoftKeyBoardListener.OnSoftKeyBoardChangeListener() {
                @Override
                public void keyBoardShow(int height) {
                    if (viewPager.getVisibility() == View.VISIBLE) {
                        viewPager.setVisibility(View.GONE);
                    }
                }

                @Override
                public void keyBoardHide(int height) {
                }
            });
        }

    }

    @OnClick({R.id.rl_root, R.id.btn_send, R.id.iv_expression, R.id.iv_add_photo, R.id.ll_cool, R.id.tv_cancel})
    public void onClick(View v) {
        switch (v.getId()) {
            //case R.id.rl_root:
            case R.id.tv_cancel:
                if (!TextUtils.isEmpty(eventPage)) {
                    SensorsUtils.sensorsClickBtn("点击取消", eventPage, "回复评论弹窗");
                }
                cancelInput();
                break;
            case R.id.ll_cool:
                fillInQuickReply();
                break;
            case R.id.btn_send://点击发送按钮
                if (!TextUtils.isEmpty(eventPage)) {
                    SensorsUtils.sensorsClickBtn("点击发送", eventPage, "回复评论弹窗");
                }
                if (AppUtil.checkLogin(getContext(), AppUtil.RegisterChannel.CHANNEL_POST)) {
                    btn_send.setEnabled(false);
                    et_content.setEnabled(false);
                    btn_send.setTextColor(getResources().getColor(R.color.white));
                    currentComment.setCommentContent(et_content.getText().toString());
                    if (TextUtils.isEmpty(et_content.getText().toString())) {
                        ToastUtil.showMessage(getContext(), "请输入回复内容");
                        btn_send.setEnabled(true);
                        et_content.setEnabled(true);
                        return;
                    }
                    //有图
                    if (mChooseImageController != null && mChooseImageController.hasChosenImage()) {
                        if (mUploadImageController == null) {
                            mUploadImageController = new UploadImageController(AppUtil.getActivity(getContext()), true);
                            mUploadImageController.setOnUploadSuccessListener(new UploadImageController.OnUploadImageListSuccessListener() {
                                @Override
                                public void onUploadSuccess(List<String> result) {
                                    if (result != null && !result.isEmpty()) {
                                        currentComment.setImages(TextUtils.join(",", result));
                                        doReplay(currentComment);
                                    } else {
                                        resetUploadButton();
                                    }
                                }
                            });
                            mUploadImageController.setOnUploadFailListener(new UploadImageController.OnUploadFailListener() {
                                @Override
                                public boolean onUploadFail(Throwable e) {
                                    resetUploadButton();
                                    return false;
                                }

                                @Override
                                public boolean onBackPress() {
                                    resetUploadButton();
                                    return false;
                                }
                            });
                        }
                        mUploadImageController.upload(mChooseImageController.getChosenImageList());
                    } else {
                        //无图有内容
                        doReplay(currentComment);
                    }
                }
                break;
            case R.id.iv_expression:
                //点击表情
                if (viewPager.getVisibility() == View.VISIBLE) {
                    //如果表情可见状态，则隐藏
                    showKeyboard();
                    viewPager.setVisibility(View.GONE);
                } else {
                    hideKeyboard();
                    //这个延时用于平滑一些，否则会马上闪出来，再往下移
                    viewPager.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            viewPager.setVisibility(View.VISIBLE);
                        }
                    }, 100);
                }
                break;
            case R.id.iv_add_photo:
                getOrCreateChooseImageController().showChooseTypeDialog();
                break;
            default:
                break;
        }
    }

    /**
     * 在点击输入外部和回复成功时调用
     */
    private void cancelInput() {
        hideKeyboard();
        this.setVisibility(View.GONE);
        if (mReplyInputCallBack != null) {
            mReplyInputCallBack.onCancel();
        }
    }

    /**
     * 隐藏键盘
     */
    protected void hideKeyboard() {
        InputMethodUtils.hideInputMethod(getContext(), this);
    }

    /**
     * 显示键盘
     */
    protected void showKeyboard() {
        et_content.setFocusable(true);
        et_content.setFocusableInTouchMode(true);
        et_content.requestFocus(FOCUS_DOWN);
        //这里先请求焦点，再显示，测试时如果没有焦点好像无法调起
        InputMethodUtils.showInputMethod(et_content);
    }

    /**
     * 网络请求：提交comment
     *
     * @param comment 评论对象
     */
    private void doReplay(final Comment comment) {
        L00bangRequestManager2
                .setSchedulers(L00bangRequestManager2.getInstance().getService().commentCard(String.valueOf(mPostId), comment))
                .subscribe(new ProgressSubscriber<Comment>(getContext()) {
                    @Override
                    public void onSuccess(Comment resultComment) {
                        super.onSuccess(resultComment);
                        btn_send.setEnabled(true);
                        et_content.setEnabled(true);
                        currentComment = new Comment();
                        et_content.setText("");
                        if (mChooseImageController != null && mChooseImageController.hasChosenImage()) {
                            mChooseImageController.getChosenImageList().clear();
                        }
                        updateChosenImage();
                        iv_add_photo.setVisibility(View.VISIBLE);

                        cancelInput();

                        ToastUtil.showMessage(getContext(), "回复成功！");
                        //这里是否要刷新帖子
                        if (mReplyInputCallBack != null) {
                            mReplyInputCallBack.onReplyResult(resultComment, mCommentPosition);
                        }
                        mQuickReplies.clear();
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                        btn_send.setEnabled(true);
                        et_content.setEnabled(true);
                    }
                });
    }

    /**
     * 请求快捷回复短语列表
     */
    private void getQuickReply() {
        L00bangRequestManager2.getServiceInstance()
                .getQuickReplyList()
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<List<QuickReply>>(getContext()) {
                    @Override
                    public void onSuccess(List<QuickReply> quickReplies) {
                        super.onSuccess(quickReplies);
                        if (quickReplies != null && quickReplies.size() > 0) {
                            mQuickReplies.clear();
                            mQuickReplies.addAll(quickReplies);
                            mQuickReplyIndex = 0;
                            fillInQuickReply();
                        } else {
                            ToastUtil.showMessage(getContext(), "快捷回复内容为空~");
                        }
                    }
                });
    }

    /**
     * 填入快捷回复的内容
     */
    private void fillInQuickReply() {
        if (mQuickReplies.size() > 0) {
            int index = mQuickReplyIndex % mQuickReplies.size();
            android.util.Log.e("quickrepky", index + "");
            if (!TextUtils.isEmpty(mQuickReplies.get(index).getReplyContent())) {
                et_content.setText(mQuickReplies.get(index).getReplyContent());
                et_content.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        et_content.requestFocus();
                        et_content.setSelection(et_content.getText().length());
                    }
                }, 200);

                mQuickReplyIndex++;
            }

        } else {
            getQuickReply();
        }
    }

    /**
     * 取消上传时重置按钮
     */
    private void resetUploadButton() {
        btn_send.setEnabled(true);
        et_content.setEnabled(true);
    }

    /**
     * 更新选择的图片显示
     */
    private void updateChosenImage() {
        //背景颜色只用来区分，没有点击事件，点击的时候还是调用的隐藏
        int size = 0;
        //因为后面要用到 data 的数据，所以这里需要确保不为 null
        if (getOrCreateChooseImageController().hasChosenImage()) {
            //有图片赋值，无图片默认 0
            size = mChooseImageController.getChosenImageList().size();
        }
        if (size <= 0) {
            //取消阴影
            rl_root.setBackgroundColor(0);
            fl_chosen_images.setVisibility(View.GONE);
        } else {
            //阴影
            rl_root.setBackgroundColor(Color.parseColor("#66000000"));
            fl_chosen_images.setVisibility(View.VISIBLE);
            //需要减小 spanCount 否则宽度
            int spanCount = size < 3 ? size : 3;
            recycler_view_chosen_images.setLayoutManager(new GridLayoutManager(getContext(), spanCount));
            recycler_view_chosen_images.setAdapter(new ChosenImageAdapter(getContext(), mChooseImageController.getChosenImageList()));
        }
    }

    public ChooseImageController getOrCreateChooseImageController() {
        if (mChooseImageController == null) {
            mChooseImageController = new ChooseImageController((BaseActivity) AppUtil.getActivityOrContext(this));
            mChooseImageController.setOnChooseImageListListener(new ChooseImageController.OnChooseImageListListener() {
                @Override
                public void onAddImageList(List<String> image) {
                    updateChosenImage();
                }
            });
            mChooseImageController.setMaxCount(MAX_IMAGE_COUNT);
        }
        return mChooseImageController;
    }

    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (mChooseImageController != null) {
            mChooseImageController.onActivityResult(requestCode, resultCode, data);
        }
    }

    public void onPermissionResult(boolean isGranted, int requestCode) {
        if (mChooseImageController != null) {
            mChooseImageController.onPermissionResult(isGranted, requestCode);
        }
    }

    public void setReplyInputCallBack(ReplyInputCallBack replyInputCallBack) {
        mReplyInputCallBack = replyInputCallBack;
    }

    public interface ReplyInputCallBack {
        void onCancel();

        //回复的结果
        void onReplyResult(Comment resultComment, int commentPosition);
    }

    class ChosenImageAdapter extends BaseRecyclerViewAdapter<String> {
        ChosenImageAdapter(Context context, List<String> data) {
            super(context, data);
        }

        @Override
        protected BaseRecyclerViewHolder<String> createViewHolder(View itemView) {
            return new ChosenImageViewHolder(itemView);
        }

        @Override
        protected int getItemLayoutRes(int viewType) {
            return R.layout.item_select_photo;
        }

        void onDeleteImage(int position) {
            //remove 之后 data 及选中的图片都会变，引用的同一个对象
            mData.remove(position);
            updateChosenImage();
        }

        class ChosenImageViewHolder extends BaseRecyclerViewHolder<String> {
            ImageView ivChosenImage;
            ImageView ivDelete;

            ChosenImageViewHolder(View itemView) {
                super(itemView);
            }

            @Override
            protected void initItemView(View itemView) {
                super.initItemView(itemView);
                ivChosenImage = itemView.findViewById(R.id.iv_select_photo);
                ivDelete = itemView.findViewById(R.id.delete_icon);
                ivDelete.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onDeleteImage(getPositionOfData());
                    }
                });
            }

            @Override
            public void bindTo(String s, int position) {
                super.bindTo(s, position);
                new ImageLoad(mContext, ivChosenImage, FileUtils.pathToUri(s))
                        .load();
            }
        }

    }

    public void setShowExpressAndPic(boolean showExpressAndPic) {
        mIsShowExpressAndPic = showExpressAndPic;
    }

    public void setShowQuickReply(boolean isShowQuickReply) {
        mIsShowQuickReply = isShowQuickReply;
    }

    public void setReplyPostHint(String applyPostHint) {
        mReplyPostHint = applyPostHint;
    }

    public void setReplyPostCommentHint(String replyPostCommentHint) {
        mReplyPostCommentHint = replyPostCommentHint;
    }

    public void setEventPage(String eventPage) {
        this.eventPage = eventPage;
    }
}
