package com.cloudy.linglingbang.activity.community.post;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.community.common.PostFlagsEnum;
import com.cloudy.linglingbang.adapter.newcommunity.PostAuthorInfoHolder;
import com.cloudy.linglingbang.app.log.LLBTextUtils;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.widget.ExpressionTextView;
import com.cloudy.linglingbang.app.widget.NoScrollListView;
import com.cloudy.linglingbang.app.widget.PraiseCountTextView;
import com.cloudy.linglingbang.app.widget.dialog.CommonListWithBottomCancelDialog;
import com.cloudy.linglingbang.app.widget.dialog.alert.BaseListDialog;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.Comment;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.NormalSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.user.User;

import java.util.ArrayList;
import java.util.List;

import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 帖子详情，评论
 *
 * <AUTHOR> create at 2016/10/25 10:33
 */
class PostDetailCommentViewHolder extends BaseRecyclerViewHolder<Comment> {

    private final BaseRecyclerViewAdapter<Comment> mCommentAdapter;
    private Context mContext;

    /**
     * 采纳答案
     */
    TextView mTvAccept;

    /**
     * 楼层
     */
    TextView mTvFloor;

    @BindView(R.id.tv_comment_content)
    ExpressionTextView tv_comment_content;//评论内容

    /**
     * 图片
     */
    @BindView(R.id.recycler_view_images)
    RecyclerView recycler_view_images;

    @BindView(R.id.lv_comment_reply)
    NoScrollListView lv_comment_reply;

    //底部
    @BindView(R.id.tv_time)
    TextView tv_time;//时间

    @BindView(R.id.ll_reply)
    LinearLayout ll_reply;//回复

    @BindView(R.id.tv_reply_count)
    TextView tv_reply_count;//回复

    @BindView(R.id.tv_praise_count)
    PraiseCountTextView tv_praise_count;//赞

    @BindView(R.id.ll_praise)
    LinearLayout ll_praise;//赞

    @BindView(R.id.iv_praise)
    ImageView iv_praise;//点赞的帖子图标

    private PostAuthorInfoHolder mPostCommentAuthorInfoHolder;

    public PostDetailCommentViewHolder(BaseRecyclerViewAdapter<Comment> commentAdapter, View itemView) {
        super(itemView);
        mCommentAdapter = commentAdapter;
        updateItemViewByPostCard();
    }

    /**
     * 在 initItemView 之后才赋值 mCommentAdapter，因此赋值后再更新
     */
    private void updateItemViewByPostCard() {
        if (mPostCommentAuthorInfoHolder != null) {
            //帖子详情中的评论，都展示楼主标识
            mPostCommentAuthorInfoHolder.addFlags(PostFlagsEnum.SHOW_FLOOR_HOST);
        }
    }

    /**
     * 第次都获取，是为了防止下拉刷新后postCard不一致
     */
    private PostCard getPostCard() {
        if (mCommentAdapter != null) {
            if (mCommentAdapter instanceof PostDetailNativeAdapter) {
                PostDetailNativeAdapter adapter = (PostDetailNativeAdapter) mCommentAdapter;
                return adapter.getPostCard();
            }
        }
        return null;
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        ButterKnife.bind(this, itemView);

        mContext = itemView.getContext();

        mPostCommentAuthorInfoHolder = new PostAuthorInfoHolder(itemView);
        //移除，再添加
        LinearLayout llRight = itemView.findViewById(R.id.ll_right);
        llRight.removeAllViews();
        LayoutInflater.from(llRight.getContext()).inflate(R.layout.item_comment_author_ll_right, llRight, true);
        mTvAccept = itemView.findViewById(R.id.tv_accept);
        mTvFloor = itemView.findViewById(R.id.tv_floor);

        //隐藏围观数
        itemView.findViewById(R.id.ll_read).setVisibility(View.GONE);

        ll_reply.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onClickReply();
            }
        });
        //赞
        ll_praise.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!AppUtil.checkLogin(mContext, AppUtil.RegisterChannel.CHANNEL_POST)) {
                    return;
                }
                int itemPosition = getAdapterPosition();
                if (itemPosition == -1) {
                    return;
                }
                Comment comment = mCommentAdapter.getData().get(itemPosition);
                if (comment == null || comment.getCommentId() == null) {
                    return;
                }
                if (comment.getIsPraise() == 1) {
                    ToastUtil.showMessage(mContext, "您已经赞过了");
                } else {
                    //先成功，失败后复原
                    //显示点赞数
                    tv_praise_count.addCount((int) (comment.getPraiseCount()));
                    comment.setPraiseCount(comment.getPraiseCount() + 1);
                    comment.setIsPraise(1);
                    //不调用notify方法，只更新局部的view
//                    mCommentAdapter.notifyItemChanged(getAdapterPosition());
                    if (comment.getIsPraise() == 1) {
//                        iv_praise.setImageDrawable(AppUtil.getTintDrawable(R.drawable.ic_post_praaise_press_new, R.color.color_384967, iv_praise.getContext()));
                        iv_praise.setImageResource(R.drawable.ic_home_post_like_checked);
                        //点赞动画
                        iv_praise.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.post_praise_anim));
                    } else {
                        iv_praise.setImageResource(R.drawable.ic_home_post_like_normal);
                    }
                    L00bangRequestManager2
                            .getServiceInstance()
                            .praiseComment(comment.getCommentId())
                            .compose(L00bangRequestManager2.setSchedulers())
                            .subscribe(new NormalSubscriber<String>(mContext) {
                                @Override
                                public void onSuccess(String s) {
                                    super.onSuccess(s);
                                    //点赞完成后，更新点赞，主要是置顶的评论
                                    updatePraiseStatus(getAdapterPosition());
                                }

                                @Override
                                public void onFailure(Throwable e) {
                                    super.onFailure(e);
                                    final Comment comment = mCommentAdapter.getData().get(getAdapterPosition());
                                    comment.setPraiseCount(comment.getPraiseCount() - 1);
                                    comment.setIsPraise(0);
//                                    mCommentAdapter.notifyItemChanged(getAdapterPosition());
                                    //显示点赞数
                                    tv_praise_count.setTextColor(mContext.getResources().getColor(R.color.black_b_8d8d8d));
                                    if (comment.getPraiseCount() == 0) {
                                        tv_praise_count.setText("点赞");//回复数
                                    } else {
//                                        延迟执行,防止+1的动画没有完成，又被刷新了数字
                                        tv_praise_count.postDelayed(new Runnable() {
                                            @Override
                                            public void run() {
                                                tv_praise_count.setText(AppUtil.getCommentDesc(comment.getPraiseCount()));
                                            }
                                        }, 500);

                                    }
                                    if (comment.getIsPraise() == 1) {
                                        iv_praise.setImageResource(R.drawable.ic_home_post_like_checked);
                                    } else {
                                        iv_praise.setImageResource(R.drawable.ic_home_post_like_normal);
                                    }
                                }
                            });
                }
            }

            /**
             * 更新点赞的状态
             * 当前 position 的已经改变了，只需要处理别的
             */
            private void updatePraiseStatus(int position) {
                List<Comment> commentList = mCommentAdapter.getData();
                if (commentList != null && position>=0 && position < commentList.size()) {
                    Comment anchorComment = commentList.get(position);
                    if (anchorComment != null) {
                        Long anchorCommentId = anchorComment.getCommentId();
                        if (anchorCommentId != null && anchorCommentId != 0) {
                            //遍历
                            for (int i = 0; i < commentList.size(); i++) {
                                if (i == position) {
                                    //不处理当前
                                    continue;
                                }
                                Comment comment = commentList.get(i);
                                if (comment != null) {
                                    Long commentId = comment.getCommentId();
                                    if (commentId != null && commentId.equals(anchorCommentId)) {
                                        comment.setPraiseCount(comment.getPraiseCount() + 1);
                                        comment.setIsPraise(1);
                                        mCommentAdapter.notifyItemChanged(i);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });
        //回复内容长按
        tv_comment_content.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                Comment comment = mCommentAdapter.getData().get(getAdapterPosition());
                PostCard postCard = getPostCard();
                List<String> list = new ArrayList<>();
                if (User.getsUserInstance().hasLogin() && !comment.getUser().getUserIdStr().equals(User.getsUserInstance().getUserIdStr())) {
                    list.add("回复");
                }
                list.add("复制");
                //如果帖子是本人的或者回复人是本人，则可以删除
                if (User.getsUserInstance().hasLogin() && comment.getTopStatus() == 0 && (comment.getUser().getUserIdStr().equals(User.getsUserInstance().getUserIdStr()) || postCard.getAuthor().getUserIdStr().equals(User.getsUserInstance().getUserIdStr()))) {
                    list.add("删除");
                }

                Dialog dialog = new CommonListWithBottomCancelDialog(mContext, list, new BaseListDialog.OnChoiceClickListener() {
                    @Override
                    public boolean onChoiceClick(int chosenIndex, String chosenText) {
                        switch (chosenText) {
                            case "回复":
                                onClickReply();
                                break;
                            case "复制":
                                final String copyText = mCommentAdapter.getData().get(getAdapterPosition()).getCommentContent();
                                if (!TextUtils.isEmpty(copyText)) {
                                    LLBTextUtils.copyText(mContext, copyText, true);
                                }
                                break;
                            case "删除":
                                Dialog dialog = new CommonAlertDialog(mContext, R.string.post_dialog_delete_comment_confirm, new DialogInterface.OnClickListener() {
                                    @Override
                                    public void onClick(DialogInterface dialog, int which) {
                                        deleteComment();
                                    }
                                });
                                dialog.show();

                        }
                        return false;
                    }

                    private void deleteComment() {
                        L00bangRequestManager2
                                .getServiceInstance()
                                .deleteComment(comment.getCommentId())
                                .compose(L00bangRequestManager2.setSchedulers())
                                .subscribe(new ProgressSubscriber<Boolean>(mContext) {
                                    @Override
                                    public void onSuccess(Boolean s) {
                                        super.onSuccess(s);
                                        if (s) {
                                            ToastUtil.showMessage(mContext, "删除成功");
                                            mCommentAdapter.removeListItem(mCommentAdapter.getData().get(getAdapterPosition()));
                                            //楼层数量更新
                                            int count = 1;
                                            if (comment.getSubWebComments() != null) {
                                                count += comment.getSubWebComments().size();
                                            }
                                            if (mContext instanceof PostDetailActivity) {
                                                ((PostDetailActivity) mContext).onDeleteComment(count);
                                            }
                                        } else {
                                            ToastUtil.showMessage(mContext, "删除失败，请重试~");
                                        }

                                    }

                                    @Override
                                    public void onFailure(Throwable e) {
                                        super.onFailure(e);

                                    }
                                });
                    }
                });
                dialog.show();

                return true;
            }
        });
        tv_comment_content.setMaxLines(Integer.MAX_VALUE);
        //图片点击
        //采纳按钮点击
        mTvAccept.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                PostCard postCard = getPostCard();
                if (postCard != null) {
                    if (postCard.getIsAccept() != 1) {
                        //未采纳
                        User author = postCard.getAuthor();
                        if (author != null && !TextUtils.isEmpty(author.getUserIdStr())) {
                            User user = User.getsUserInstance();
                            if (user != null && !TextUtils.isEmpty(user.getUserIdStr())) {
                                if (user.getUserIdStr().equals(author.getUserIdStr())) {
                                    //是自己的帖子
                                    Comment comment = mCommentAdapter.getData().get(getAdapterPosition());
                                    User commentUser = comment.getUser();
                                    if (commentUser != null && !user.getUserIdStr().equals(commentUser.getUserIdStr())) {
                                        //不是自己的回复
                                        showAcceptCommentDialog();
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });
    }

    private void onClickReply() {
        if (mContext instanceof PostDetailActivity) {
            int position = getAdapterPosition();
            Comment comment = mCommentAdapter.getData().get(position);
            ((PostDetailActivity) mContext).showReplyLayout(comment, position);
        }
    }

    private void showAcceptCommentDialog() {
        CommonAlertDialog dialog = new CommonAlertDialog(mContext, mContext.getString(R.string.post_detail_dialog_accept_confirm), new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                PostCard postCard = getPostCard();
                if (postCard != null) {
                    if (postCard.getIsAccept() != 1) {
                        acceptComment(getAdapterPosition());
                    }
                }
            }
        });
        dialog.show();
    }

    private void acceptComment(final int acceptCommentPosition) {
        Comment acceptComment = mCommentAdapter.getData().get(acceptCommentPosition);
        if (acceptComment == null || acceptComment.getCommentId() == null) {
            return;
        }
        L00bangRequestManager2
                .getServiceInstance()
                .acceptComment(acceptComment.getCommentId())
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<Comment>(mContext) {
                    @Override
                    public void onSuccess(Comment comment) {
                        super.onSuccess(comment);
                        ToastUtil.showMessage(mContext, R.string.post_detail_toast_accept_success);
                        Comment acceptComment = mCommentAdapter.getData().get(acceptCommentPosition);
                        if (acceptComment != null) {
                            acceptComment.setAccept(1);
                        }
                        PostCard postCard = getPostCard();
                        if (postCard != null) {
                            postCard.setIsAccept(1);
                        }
                        if (mContext instanceof PostDetailActivity) {
                            PostDetailActivity postDetailActivity = (PostDetailActivity) mContext;
                            //隐藏采纳技师答案提示栏
                            postDetailActivity.setRemindUserAdoptTipsVisible(View.GONE);
                            int firstCommentPosition = postDetailActivity.getFirstCommentItemPosition();
                            if (firstCommentPosition != acceptCommentPosition) {
                                //等于时直接更新
                                mCommentAdapter.getData().remove(acceptCommentPosition);
                                mCommentAdapter.notifyItemRemoved(acceptCommentPosition);
                                mCommentAdapter.getData().add(firstCommentPosition, acceptComment);
                                mCommentAdapter.notifyItemInserted(firstCommentPosition);
                                return;
                            }
                        }
                        mCommentAdapter.notifyDataSetChanged();
                    }
                });
    }

    @Override
    public void bindTo(Comment comment, int position) {
        super.bindTo(comment, position);
        if (comment == null) {
            return;
        }
        //作者
        mPostCommentAuthorInfoHolder.bindTo(comment.getUser());

        //楼层
        mTvFloor.setText(mContext.getString(R.string.post_detail_label_floor, comment.getFloor()));

        //帖子内容
        tv_comment_content.setText(comment.getCommentContent());

        //图片
        new PostDetailCommentImagesManager().bindImages(recycler_view_images, comment.getImagesArr());

        //底部内容

        //时间
        if (comment.getCreationDate() != null) {
            tv_time.setText(AppUtil.checkMessTimeNew(comment.getCreationDate()));
        }

        //删除，评论不删除

        //显示回复数
        List<Comment> subComments = comment.getSubWebComments();
        if (subComments == null || subComments.size() == 0) {
            tv_reply_count.setText("回复");
            lv_comment_reply.setVisibility(View.GONE);
        } else {
            tv_reply_count.setText(AppUtil.getCommentDesc(subComments.size()));
            lv_comment_reply.setVisibility(View.VISIBLE);

            CommentReplyAdapter commentReplyAdapter = new CommentReplyAdapter(mContext, comment.getSubWebComments(), position, getPostCard().getAuthor().getUserIdStr(), lv_comment_reply);
            lv_comment_reply.setAdapter(commentReplyAdapter);
            commentReplyAdapter.notifyDataSetChanged();
        }
        if (comment.getIsPraise() == 1) {
            iv_praise.setImageResource(R.drawable.ic_home_post_like_checked);
        } else {
            iv_praise.setImageResource(R.drawable.ic_home_post_like_normal);
        }

        //显示点赞数
        if (comment.getPraiseCount() == 0) {
            tv_praise_count.setText("点赞");//回复数
        } else {
            tv_praise_count.setText(AppUtil.getCommentDesc(comment.getPraiseCount()));
        }

        //是否是我问技师
        PostCard postCard = getPostCard();
        if (postCard != null) {
            if (String.valueOf(PostCard.PostType.ASK_TECHNICIAN).equals(postCard.getPostTypeId())) {
                //是技师帖
                if (postCard.getIsAccept() == 1) {
                    //已采纳，显示采纳按钮
                    if (comment.getAccept() == 1) {
                        mTvAccept.setEnabled(false);
                        mTvAccept.setText(mContext.getString(R.string.post_detail_answer_accepted));
                        mTvAccept.setVisibility(View.VISIBLE);
                    } else {
                        mTvAccept.setVisibility(View.GONE);
                    }
                } else {
                    //未采纳，可采纳
                    User author = postCard.getAuthor();
                    if (author != null && !TextUtils.isEmpty(author.getUserIdStr())) {
                        User user = User.getsUserInstance();
                        if (user != null && !TextUtils.isEmpty(user.getUserIdStr())) {
                            if (user.getUserIdStr().equals(author.getUserIdStr())) {
                                //是自己的帖子
                                User commentUser = comment.getUser();
                                if (commentUser != null && !user.getUserIdStr().equals(commentUser.getUserIdStr())) {
                                    //不是自己的回复
                                    mTvAccept.setEnabled(true);
                                    mTvAccept.setText(mContext.getString(R.string.post_detail_accept_answer));
                                    mTvAccept.setVisibility(View.VISIBLE);
                                } else {
                                    //自己的回复不可采纳
                                    mTvAccept.setVisibility(View.GONE);
                                }
                            }
                        }
                    }
                }
            }
        }

        //是否能回复
        boolean canReply = CommunityUtils.PostUtils.canReply(getPostCard());
        //显示
        ll_reply.setVisibility(canReply ? View.VISIBLE : View.GONE);
    }
}
