package com.cloudy.linglingbang.activity.service.newenergy;

import android.os.Bundle;
import android.os.Handler;
import android.widget.RelativeLayout;

import com.aspsine.swipetoloadlayout.OnRefreshListener;
import com.aspsine.swipetoloadlayout.SwipeToLoadLayout2;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseScrollTabViewPagerFragment;
import com.cloudy.linglingbang.app.widget.hoverview.HoverGroupLayout;
import com.cloudy.linglingbang.app.widget.hoverview.HoverRecyclerView;
import com.cloudy.linglingbang.app.widget.hoverview.OnGroupScrollListener;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.server.YesterdayLeaderBoard;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.viewpager.widget.PagerAdapter;
import butterknife.BindView;

/**
 * 排行榜
 *
 * <AUTHOR>
 * @date 2019-07-16
 */
public class LeaderboardFragment extends BaseScrollTabViewPagerFragment<Fragment> implements OnGroupScrollListener {

    /**
     * 头部布局
     */
    @BindView(R.id.rl_head)
    RelativeLayout mRlHead;

    /** 刷新控件 */
    @BindView(R.id.swipeToLoadLayout)
    SwipeToLoadLayout2 mSwipeToLoadLayout2;

    private int mRlHeadHeight;
    //头部是否已滑出
    private boolean isCardScrollToEnd;

    private PagerAdapter mPagerAdapter;
    @BindView(R.id.swipe_target)
    HoverGroupLayout mHoverGroupLayout;

    List<Fragment> fragmentList = new ArrayList<>();

    private MyDrivingRecordFragment mMyDrivingRecordFragment;
    private YesterdayLeaderBoardFragment mYesterdayLeaderBoardFragment;

    private LeaderBoardHeaderLoader mLeaderBoardHeaderLoader;

    private String vin;
    private int totalMileage;
    private String carTypeName;

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_leaderboard;
    }

    @Override
    protected void initViews() {
        super.initViews();
        vin = getArguments().getString(LeaderboardActivity.vinExtra);
        totalMileage = getArguments().getInt(LeaderboardActivity.totalMileageExtra);
        carTypeName = getArguments().getString(LeaderboardActivity.carTypeNameExtra);
        mLeaderBoardHeaderLoader = new LeaderBoardHeaderLoader(mRootView, vin, totalMileage, carTypeName);
        getYesterdayRankInfo();

        mHoverGroupLayout.setOnGroupScrollListener(this);
        //测量头部高度,若在onCreate直接测量高度为0，所以需要用post
        mRlHead.post(new Runnable() {
            @Override
            public void run() {
                mRlHeadHeight = mRlHead.getMeasuredHeight();
            }
        });

        mSwipeToLoadLayout2.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh() {
                //判断当前选择的是哪个fragment
                if (mViewPager.getCurrentItem() == 0) {
                    if (mMyDrivingRecordFragment != null) {
                        mMyDrivingRecordFragment.onRefresh();
                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                mSwipeToLoadLayout2.setRefreshing(false);
                            }
                        }, 500);
                    }
                } else if (mViewPager.getCurrentItem() == 1) {
                    if (mYesterdayLeaderBoardFragment != null) {
                        mYesterdayLeaderBoardFragment.onRefresh();
                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                mSwipeToLoadLayout2.setRefreshing(false);
                            }
                        }, 500);
                    }
                }
            }
        });
    }

    private void getYesterdayRankInfo() {
        L00bangRequestManager2.getServiceInstance()
                .getRankInfo(vin)
                .compose(L00bangRequestManager2.<YesterdayLeaderBoard>setSchedulers())
                .subscribe(new ProgressSubscriber<YesterdayLeaderBoard>(getActivity()) {
                    @Override
                    public void onSuccess(YesterdayLeaderBoard yesterdayLeaderBoard) {
                        super.onSuccess(yesterdayLeaderBoard);
                        if (yesterdayLeaderBoard != null) {
                            mLeaderBoardHeaderLoader.updateView(yesterdayLeaderBoard);
                        }
                    }
                });

    }

    @Override
    protected PagerAdapter createViewPagerAdapter(final List<Fragment> data, final String[] titles) {
        mPagerAdapter = new FragmentStatePagerAdapter(getChildFragmentManager()) {
            @Override
            public Fragment getItem(int position) {
                return data.get(position);
            }

            @Override
            public int getCount() {
                return data.size();
            }

            @Override
            public CharSequence getPageTitle(int position) {
                return titles[position];
            }

            @Override
            public int getItemPosition(@NonNull Object object) {
                return POSITION_NONE;
            }
        };
        return mPagerAdapter;
    }

    @Override
    protected List<Fragment> createAdapterData() {
        mMyDrivingRecordFragment = MyDrivingRecordFragment.newInstance(mHoverGroupLayout, getArguments().getString(LeaderboardActivity.vinExtra));
        fragmentList.add(mMyDrivingRecordFragment);
        mYesterdayLeaderBoardFragment = YesterdayLeaderBoardFragment.newInstance(mHoverGroupLayout);
        fragmentList.add(mYesterdayLeaderBoardFragment);
        return fragmentList;
    }

    @Override
    protected String[] getTitles() {
        return getResources().getStringArray(R.array.array_car_leader_board);
    }

    public static LeaderboardFragment newInstance(String vin, int totalMileage, String carTypeName) {
        LeaderboardFragment leaderboardFragment = new LeaderboardFragment();
        Bundle bundle = new Bundle();
        bundle.putString(LeaderboardActivity.vinExtra, vin);
        bundle.putInt(LeaderboardActivity.totalMileageExtra, totalMileage);
        bundle.putString(LeaderboardActivity.carTypeNameExtra, carTypeName);
        leaderboardFragment.setArguments(bundle);
        return leaderboardFragment;
    }

    @Override
    public boolean isGroupScroll() {
        int currentItem = mViewPager.getCurrentItem();
        switch (currentItem) {
            case 0:
                if (mMyDrivingRecordFragment != null && mMyDrivingRecordFragment.getRefreshController() != null) {
                    HoverRecyclerView recyclerView = (HoverRecyclerView) mMyDrivingRecordFragment.getRefreshController().getRecyclerView();
                    return recyclerView != null && recyclerView.isChildScrollToTop();
                }
            case 1:
                if (mYesterdayLeaderBoardFragment != null && mYesterdayLeaderBoardFragment.getRefreshController() != null) {
                    HoverRecyclerView recyclerView = (HoverRecyclerView) mYesterdayLeaderBoardFragment.getRefreshController().getRecyclerView();
                    return recyclerView != null && recyclerView.isChildScrollToTop();
                }
            default:
                break;
        }
        return false;
    }

    @Override
    public void onScrollChanged(int left, int top) {
        //控制外层的刷新
        if (top > 0) {
            mSwipeToLoadLayout2.setRefreshEnabled(false);
        } else {
            mSwipeToLoadLayout2.setRefreshEnabled(true);
        }

        if (top >= mRlHeadHeight) {
            isCardScrollToEnd = true;
        } else if (isCardScrollToEnd) {
            isCardScrollToEnd = false;
        }
    }
}
