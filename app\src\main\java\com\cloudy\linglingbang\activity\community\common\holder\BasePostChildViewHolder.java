package com.cloudy.linglingbang.activity.community.common.holder;

import android.view.View;

import com.cloudy.linglingbang.model.postcard.PostCard;

/**
 * 新建一个类，没有 ViewHolder 的逻辑，只有类似的方法
 * <p>
 * 如果作为基类，理应负责判空
 *
 * <AUTHOR>
 * @date 2018/6/25
 */
public class BasePostChildViewHolder {
    protected PostCard mPostCard;

    public BasePostChildViewHolder(View itemView) {
        initItemView(itemView);
    }

    protected void initItemView(View itemView) { }

    public void bindTo(PostCard postCard) {
        mPostCard = postCard;
    }
}
