package com.cloudy.linglingbang.activity.store.commodity.dialog

import android.content.Context
import android.view.View
import android.widget.CheckBox
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import butterknife.BindView
import butterknife.ButterKnife
import butterknife.OnClick
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.store.commodity.adapter.OneClickCleaningAdapter
import com.cloudy.linglingbang.activity.store.commodity.adapter.ShoppingCartSingleGiftAdapter
import com.cloudy.linglingbang.app.util.ToastUtil
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils
import com.cloudy.linglingbang.app.widget.dialog.alert.BaseAlertDialog
import com.cloudy.linglingbang.model.Message.PushExtra
import com.cloudy.linglingbang.model.store.commodity.CartCommodity

/**
 * 一键清理的弹窗
 *
 * <AUTHOR>
 * @date 2022/10/18
 */
class OneClickCleaningDialog : BaseAlertDialog {

    var list: MutableList<CartCommodity>? = null

    var oneClickCleaningAdapter: OneClickCleaningAdapter? = null


    /** 列表 */
    @JvmField
    @BindView(R.id.recycler_view_invalidity_commodity)
    var recyclerViewInvalidityCommodity: RecyclerView? = null

    /** 已选多少件 */
    @JvmField
    @BindView(R.id.tv_already_choose)
    var tvAlreadyChoose: TextView? = null

    /** 全选 */
    @JvmField
    @BindView(R.id.cb_check_all)
    var cbCheckAll: CheckBox? = null

    /** 全选 */
    @JvmField
    @BindView(R.id.ll_check_box)
    var llCheckBox: LinearLayout? = null

    /**
     * 点击删除
     */
    var onClickDeleteListener: (MutableList<Long>) -> Unit = {}

    constructor(
        context: Context?,
        cartCommodityList: MutableList<CartCommodity>?
    ) : super(context) {
        list = cartCommodityList
    }


    override fun isBottomDialog(): Boolean {
        return true
    }

    override fun getDefaultLayoutResId(): Int {
        return R.layout.dialog_one_click_cleaning
    }


    override fun initView() {
        super.initView()
        ButterKnife.bind(this)
        recyclerViewInvalidityCommodity?.layoutManager =
            GridLayoutManager(context, 3)
        oneClickCleaningAdapter = OneClickCleaningAdapter(
            recyclerViewInvalidityCommodity?.context,
            list
        )
        /**
         * 选择的时候回调
         */
        oneClickCleaningAdapter?.setCheckedOrCancelListener {
            var a = 0
            list?.forEach { item ->
                if (item.isCheckForOneClean) {
                    a++
                }
            }
            tvAlreadyChoose?.text = "已选 $a 件 "
            cbCheckAll?.isChecked = a == list?.size

        }
        recyclerViewInvalidityCommodity?.adapter = oneClickCleaningAdapter

        /**
         * 点击全选
         */
        llCheckBox?.setOnClickListener { _ ->
            cbCheckAll?.let {
                var count = 0
                it.isChecked = !it.isChecked
                list?.forEach { item ->
                    item.isCheckForOneClean = it.isChecked
                }
                count = if (it.isChecked) {
                    list?.size!!
                } else {
                    0
                }
                tvAlreadyChoose?.text = "已选 $count 件 "
                oneClickCleaningAdapter?.notifyDataSetChanged()
            }
        }
    }


    /**
     * 点击关闭
     */
    @OnClick(R.id.iv_close)
    fun onCloseClick(view: View) {
        SensorsUtils.sensorsClickBtn("点击关闭", "购物车", "一键清理弹窗")
        dismiss()
    }


    /**
     * 点击删除
     */
    @OnClick(R.id.btn_delete)
    fun onDeleteClick() {
        SensorsUtils.sensorsClickBtn("点击删除", "购物车", "一键清理弹窗")
        if (list.isNullOrEmpty()) {
            ToastUtil.showMessage(context, "没有任何商品")
            return
        }
        var deleteList = mutableListOf<Long>()
        list?.forEach { it ->
            if (it.isCheckForOneClean) {
                deleteList.add(it.cartId)
            }
        }
        onClickDeleteListener(deleteList)
    }


    fun setOnClickDelete(e: (MutableList<Long>) -> Unit) {
        this.onClickDeleteListener = e
    }

}