package com.cloudy.linglingbang.activity.community.post;

import android.app.Activity;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.GradientDrawable;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.adapter.ChooseImageAdapter;
import com.cloudy.linglingbang.app.util.ChooseImageController;
import com.cloudy.linglingbang.app.util.DeprecatedUtils;
import com.cloudy.linglingbang.app.util.DeviceUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.UploadImageController;
import com.cloudy.linglingbang.app.widget.dialog.alert.BaseListDialog;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonSelectListDialog;
import com.cloudy.linglingbang.model.ReportBean;
import com.cloudy.linglingbang.model.ReportTypeBean;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;

import java.util.ArrayList;
import java.util.List;

import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import butterknife.OnClick;

/**
 * 帖子举报详情
 *
 * <AUTHOR>
 * @date 2018/9/19
 */
public class PostReportActivity extends BaseActivity implements TextWatcher {

    /** 输入框可输入最大长度 **/
    private static final int INPUT_MAX_LENGTH = 100;
    /** 输入框可输入最小长度 **/
    private static final int INPUT_MIN_LENGTH = 5;

    /**
     * 每行展示的图片数
     */
    private static final int COLUMN_NUMBER = 3;
    /**
     * 最新可选择照片数量
     */
    private static final int MAX_IMAGE = 5;
    @BindView(R.id.recycler_view)
    RecyclerView mRecyclerView;
    /** 申请理由输入框 **/
    @BindView(R.id.ed_content)
    EditText mEditContent;
    /** 举报理由 **/
    @BindView(R.id.tv_report_label)
    TextView mTvReportType;
    /** 配图数量 **/
    @BindView(R.id.tv_report_image_count)
    TextView mTvReportImageCount;

    /** 图片存放 **/
    private List<String> mChooseImageList;
    private int mImageSize;
    private ChooseImageAdapter mChooseImageAdapter;
    private ChooseImageController mChooseImageController;
    private UploadImageController mUploadImageController;
    /** 举报理由选择框 **/
    private CommonSelectListDialog mChoosePostReportTypeDialog;

    /** 帖子举报理由类型id **/
    private Long mReportTypeId = -1L;
    /** 帖子举报类型 **/
    private List<ReportTypeBean> mReportTypeBeanList;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_post_report);
    }

    @Override
    protected void initialize() {
        setMiddleTitle(getString(R.string.title_post_report));
        mChooseImageList = new ArrayList<>();
        mChooseImageList.add(null);
        mRecyclerView.setLayoutManager(new GridLayoutManager(this, COLUMN_NUMBER));
        initChooseImageAdapter();
        mRecyclerView.setAdapter(mChooseImageAdapter);
        initRecyclerView();
        int strokeColor = getResources().getColor(R.color.gray_divider_e2e2e2);
        GradientDrawable gd = new GradientDrawable();
        gd.setStroke(getResources().getDimensionPixelSize(R.dimen.normal_1), strokeColor);
        gd.setCornerRadius(getResources().getDimensionPixelSize(R.dimen.normal_1));
        DeprecatedUtils.setBackgroundDrawable(mEditContent, gd);

        mEditContent.addTextChangedListener(this);
    }

    @Override
    public boolean onPrepareOptionsMenu(Menu menu) {
        menu.clear();
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(R.menu.menu_commit, menu);
        mToolbar.setOnMenuItemClickListener(new Toolbar.OnMenuItemClickListener() {
            @Override
            public boolean onMenuItemClick(MenuItem item) {
                if (item.getItemId() == R.id.next_step) {
                    onSubmit();
                    return true;
                }
                return false;
            }
        });
        return true;
    }

    /**
     * 点击提交按钮
     */
    private void onSubmit() {
        if (checkReportText()) {
            if (mChooseImageController != null && mChooseImageController.hasChosenImage()) {
                if (mUploadImageController == null) {
                    mUploadImageController = new UploadImageController(this)
                            .setNeedCompress(true)
                            .setOnUploadSuccessListener(new UploadImageController.OnUploadImageListSuccessListener() {
                                @Override
                                public void onUploadSuccess(List<String> result) {
                                    submit(result);
                                }
                            });
                }
                mUploadImageController.upload(mChooseImageController.getChosenImageList());
            } else {
                submit(null);
            }
        }
    }

    /**
     * 检查必填项是否已填写
     */
    private boolean checkReportText() {
        if (mReportTypeId == -1L) {
            ToastUtil.showMessage(this, R.string.toast_post_report_type_not_choose);
            return false;
        }
        String content = mEditContent.getText().toString().trim();
        if (TextUtils.isEmpty(content) || content.length() < INPUT_MIN_LENGTH) {
            ToastUtil.showMessage(this, getString(R.string.toast_post_report_content_length, INPUT_MIN_LENGTH));
            return false;
        }
        return true;
    }

    /**
     * 提交
     *
     * @param imageList 已上传的图片数组，如果没有图片，则为 null
     */
    private void submit(List<String> imageList) {
        final String targetId = (String) IntentUtils.getExtra(getIntent(), "null");
        if (TextUtils.isEmpty(targetId)) {
            return;
        }
        String content = mEditContent.getText().toString().trim();
        ReportBean bean = new ReportBean();
        bean.setTargetId(Long.valueOf(targetId));
        bean.setProsecutionDesc(content);
        bean.setProsecutionTypeId(mReportTypeId.intValue());
        if (imageList != null) {
//            bean.setProsecutionPic(new Gson().toJson(imageList));
            bean.setProsecutionPic(imageList);
        }
        L00bangRequestManager2.getInstance().getService().prosecutionPost(bean)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<Object>(this) {
                    @Override
                    public void onSuccess(Object o) {
                        super.onSuccess(o);
                        ToastUtil.showMessage(PostReportActivity.this, R.string.toast_post_report_commit_success);
                        //关闭进度条弹出框
                        super.onRequestCompleted();
                        Intent intent = new Intent();
                        intent.putExtra("postId", targetId);
                        setResult(Activity.RESULT_OK, intent);
                        PostReportActivity.this.finish();
                    }
                });
    }

    private void initRecyclerView() {
        int count = mChooseImageList.size() - 1;
        //123 为1
        int recyclerViewHeight = mImageSize * (count / COLUMN_NUMBER + 1);
        ViewGroup.LayoutParams layoutParams = mRecyclerView.getLayoutParams();
        layoutParams.height = recyclerViewHeight;
        mRecyclerView.setLayoutParams(layoutParams);
        setReportText(count);
    }

    /**
     * 设置举报配图文案图片数量
     */
    private void setReportText(int count) {
        if (count < 0) {
            count = 0;
        } else if (count > MAX_IMAGE) {
            count = MAX_IMAGE;
        }
        mTvReportImageCount.setText(getString(R.string.label_report_image_count, count, MAX_IMAGE));
    }

    /**
     * 初始化图片适配器
     */
    private void initChooseImageAdapter() {
        if (mChooseImageAdapter == null) {
            mChooseImageAdapter = new ChooseImageAdapter(this, mChooseImageList);
            mChooseImageAdapter.setFromType(1);
        }
        int screenWidth = DeviceUtil.getScreenWidth();
        int contentWidth = screenWidth - mRecyclerView.getPaddingLeft() - mRecyclerView.getPaddingRight();
        mImageSize = contentWidth / COLUMN_NUMBER;
        mChooseImageAdapter.setImageSize(mImageSize);
        mChooseImageAdapter.setChooseImageController(getOrCreateChooseImageController());

        mChooseImageAdapter.setOnImageDeleteListener(new ChooseImageAdapter.OnImageDeleteListener() {
            @Override
            public void removeImage(int index) {
                PostReportActivity.this.removeImage(index);
            }
        });
    }

    private ChooseImageController getOrCreateChooseImageController() {
        if (mChooseImageController == null) {
            mChooseImageController = new ChooseImageController(this)
                    .setMaxCount(MAX_IMAGE)
                    .setOnChooseImageListListener(new ChooseImageController.OnChooseImageListListener() {
                        @Override
                        public void onAddImageList(List<String> image) {
                            mChooseImageList.clear();
                            mChooseImageList.addAll(mChooseImageController.getChosenImageList());
                            mChooseImageList.add(null);
                            mChooseImageAdapter.notifyDataSetChanged();
                            initRecyclerView();
                        }
                    });
        }
        return mChooseImageController;
    }

    private void removeImage(int position) {
        mChooseImageList.remove(position);
        //因为多第一张
        mChooseImageController.getChosenImageList().remove(position);
        mChooseImageAdapter.notifyItemRemoved(position);
        initRecyclerView();
    }

    /**
     * 点击选择举报理由
     */
    @OnClick(R.id.ll_report_type)
    public void clickChooseReportType() {
        if (mChoosePostReportTypeDialog != null) {
            mChoosePostReportTypeDialog.show();
        } else {
            //获取举报类型
            L00bangRequestManager2.getInstance().getService().getProsecutionType()
                    .compose(L00bangRequestManager2.<List<ReportTypeBean>>setSchedulers())
                    .subscribe(new ProgressSubscriber<List<ReportTypeBean>>(this) {
                        @Override
                        public void onSuccess(List<ReportTypeBean> data) {
                            super.onSuccess(data);
                            mReportTypeBeanList = data;
                            if (data == null || data.isEmpty()) {
                                ToastUtil.showMessage(getApplicationContext(), R.string.dialog_choose_report_type_toast_empty);
                            } else {
                                showReportTypeDialog();
                            }
                        }
                    });
        }
    }

    /**
     * 弹出举报类型弹出框
     */
    private void showReportTypeDialog() {
        if (mChoosePostReportTypeDialog == null) {
            ArrayList<String> reportTypeData = new ArrayList<>();
            for (ReportTypeBean bean : mReportTypeBeanList) {
                reportTypeData.add(bean.getProsecutionTypeDesc());
            }
            mChoosePostReportTypeDialog = new CommonSelectListDialog(PostReportActivity.this, reportTypeData, mOnChoiceClickListener) {
                @Override
                protected void initView() {
                    super.initView();
                    //设置标题和颜色
                    TextView titleView = mChoosePostReportTypeDialog.getAlertController().getTitleView();
                    if (titleView != null) {
                        titleView.setTextColor(getResources().getColor(R.color.color_9092a5));
                    }
                    //设置完成按钮
                    Button button = getAlertController().getButton(DialogInterface.BUTTON_POSITIVE);
                    if (button != null) {
                        button.setTextColor(getResources().getColor(R.color.color_3a444a));
                        DeprecatedUtils.setBackgroundDrawable(button, new ColorDrawable(Color.TRANSPARENT));
                        button.setGravity(Gravity.RIGHT | Gravity.CENTER_VERTICAL);
                        button.setText(R.string.commit_done);
                    }
                }

                @Override
                protected int getDefaultItemLayoutResId() {
                    return R.layout.item_dialog_post_report_list_select;
                }
            };
            //取消按钮置空
            mChoosePostReportTypeDialog.getAlertController().setTitle(getResources().getString(R.string.title_report_dialog));
            mChoosePostReportTypeDialog.getAlertController().setButton(DialogInterface.BUTTON_NEGATIVE, null, null);
            mChoosePostReportTypeDialog.setShowItemCount(7);
        }
        if (mChoosePostReportTypeDialog != null) {
            mChoosePostReportTypeDialog.show();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (mChooseImageController != null) {
            mChooseImageController.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    protected void onPermissionResult(boolean isGranted, int requestCode) {
        super.onPermissionResult(isGranted, requestCode);
        if (mChooseImageController != null) {
            mChooseImageController.onPermissionResult(isGranted, requestCode);
        }
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable editable) {
        int count = editable.length();
        if (count > INPUT_MAX_LENGTH) {
            ToastUtil.showMessage(getApplicationContext(), getString(R.string.toast_post_report_content_max_length, INPUT_MAX_LENGTH));
            editable.delete(INPUT_MAX_LENGTH, count);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mEditContent.removeTextChangedListener(this);
    }

    /**
     * 举报类型弹出框选择项完成监听
     */
    private BaseListDialog.OnChoiceClickListener mOnChoiceClickListener = new BaseListDialog.OnChoiceClickListener() {
        @Override
        public boolean onChoiceClick(int chosenIndex, String chosenText) {
            if (chosenIndex < 0) {
                ToastUtil.showMessage(getApplicationContext(), R.string.toast_post_report_type_not_choose);
                return true;
            }
            mReportTypeId = mReportTypeBeanList.get(chosenIndex).getProsecutionTypeId();
            mTvReportType.setText(chosenText);
            return false;
        }
    };
}
