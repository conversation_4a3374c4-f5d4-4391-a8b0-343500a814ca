package com.cloudy.linglingbang.activity.store.commodity

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.graphics.Rect
import android.text.TextUtils
import android.view.View
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.basic.IRefreshContext
import com.cloudy.linglingbang.activity.basic.RefreshController
import com.cloudy.linglingbang.activity.fragment.mynew.util.SelfUserInfoLoader
import com.cloudy.linglingbang.activity.store.commodity.adapter.CommodityDetailAdapter
import com.cloudy.linglingbang.app.util.AppUtil
import com.cloudy.linglingbang.constants.AppConstants
import com.cloudy.linglingbang.model.evaluation.EvaluationContent
import com.cloudy.linglingbang.model.evaluation.EvaluationLabel
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BackgroundSubscriber
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber
import com.cloudy.linglingbang.model.store.commodity.CenterCommodity
import com.cloudy.linglingbang.model.store.home.StoreElementCommodity
import io.reactivex.rxjava3.core.Observable

/**
 * 商品详情
 * <AUTHOR>
 * @date 2022/9/21
 */
class CommodityDetailRefreshController(refreshContext: IRefreshContext<Any>) :
    RefreshController<Any>(refreshContext) {
    var mCenterCommodityDetail: CenterCommodity? = null
    var attributes: String? = null
        set(value) {
            mCenterCommodityDetail?.localAttributeName = value
            field = value
        }
    var mRlTitleView: View? = null
    var mRlTitleView2: View? = null
    var mRlTitleView3: View? = null

    var mRlBottom: RelativeLayout? = null
    var mRlCrowdfunding: RelativeLayout? = null

    var mTvStatus: TextView? = null
    var btnShoppingCar: TextView? = null
    var btnBuyNow: TextView? = null
    var mTvCrowdFundingOrder: TextView? = null
    var mTvPrice: TextView? = null

    var mllCarInternet: View? = null
    var btnCart: TextView? = null
    var commodityId: Long? = null
    val header = arrayListOf<Any>()

    private fun queryCommodityDetail() {
        val map = HashMap<String, String>(1)
        map["commodityId"] = commodityId.toString()
        map["channelCode"] = AppConstants.CHANNEL_SOURCE_ID.toString()
        L00bangRequestManager2.getServiceInstance()
            .getCommodityDetail(map)
            .compose(L00bangRequestManager2.setSchedulers())
            .subscribe(object : ProgressSubscriber<CenterCommodity>(context) {
                override fun onSuccess(t: CenterCommodity?) {
                    super.onSuccess(t)
                    setCommodity(t)
                    onRequestSuccess(mData, 1, LOAD_TYPE_NET)
                }

                override fun onError(e: Throwable) {
                    super.onError(e)
                    onLoadFail(1, e)
                }
            })
    }

    fun queryCommodityDetailByUserChange() {
        queryCommodityDetail()
    }

    @SuppressLint("SetTextI18n")
    private fun setCommodity(t: CenterCommodity?) {
        mCenterCommodityDetail = t
        (getAdapter() as CommodityDetailAdapter).mCenterCommodity = mCenterCommodityDetail
        mCenterCommodityDetail?.apply {
            localAttributeName = attributes
            mTvStatus?.visibility = View.GONE
            //首先判断是传统商城，还是新的众筹，然后改变显示底部栏
            if (promotionActivity.activityInfoList.size > 0 && promotionActivity.activityInfoList[0].promotionType == 5) {
                mRlBottom?.visibility = View.GONE
                mRlCrowdfunding?.visibility = View.VISIBLE
                mRlTitleView?.visibility = View.GONE
                mRlTitleView2?.visibility = View.VISIBLE
                mRlTitleView3?.visibility = View.GONE

                mTvCrowdFundingOrder?.text =
                    promotionActivity.activityInfoList[0].groupPurchaseInfo.buySuccessAmount.toString()
                //判断两个价格的字符串不是null 和 空字符
                if (maxPrice != null && !maxPrice.equals("") && minPrice != null && !minPrice.equals(
                        ""
                    )
                ) {
                    // 字符串转数值比较
                    var maxPriceDouble: Double = maxPrice.toDouble()
                    var minPriceDouble: Double = minPrice.toDouble()
                    //判断最大最小一致 那就直接显示最小价格，如果不一致就是最小（起）
                    if (maxPriceDouble == minPriceDouble) {
                        mTvPrice?.text = minPrice
                    } else {
                        mTvPrice?.text = minPrice + "起"
                    }
                }
            } else {
                mRlBottom?.visibility = View.VISIBLE
                mRlCrowdfunding?.visibility = View.GONE
                mRlTitleView?.visibility = View.VISIBLE
                mRlTitleView2?.visibility = View.GONE
                mRlTitleView3?.visibility = View.VISIBLE
            }

            mllCarInternet?.visibility = if (commodityClassifyId == 0) View.VISIBLE else View.GONE
            if (commodityClassifyId == 0) { //车商品
                btnShoppingCar?.setText(R.string.store_phone_me_dirver)
                btnCart?.visibility = View.GONE
            } else {
                btnShoppingCar?.setText(R.string.store_add_to_shopping_car)
                btnCart?.visibility = View.VISIBLE
            }

            if (isSpecialButton == 1) { //2023.5.12龙卡需求:特殊处理为询价，优先级比过往字段高
                mllCarInternet?.findViewById<View>(R.id.iv_test_drive)?.visibility = View.GONE
                btnShoppingCar?.visibility = View.GONE
                btnBuyNow?.text = specialButtonConfig
            } else {
                btnShoppingCar?.visibility = if (inputCartFlag == 1) View.VISIBLE else View.GONE
            }

            if (commodityPublishStatus != 1) {
                mTvStatus?.visibility = View.VISIBLE
                mTvStatus?.setText(R.string.store_has_been_removed)
            } else {
                if (medalCondition != 1) {
                    mTvStatus?.visibility = View.VISIBLE
                    mTvStatus?.setText(R.string.store_medal_non_conformance)
                }
            }

            getCommoditySizeOffset()
            data?.clear()
            data.addAll(header)
            adapter.notifyDataSetChanged()
        }

        requestEvaluationList(commodityId.toString())
        requestEvaluationLabelList(commodityId.toString())
    }

    override fun initViews(rootView: View) {
        super.initViews(rootView)
        mRlTitleView = rootView.findViewById(R.id.rl_title)
        mRlTitleView2 = rootView.findViewById(R.id.rl_title2)
        mRlTitleView3 = rootView.findViewById(R.id.title_layout)
        mRlBottom = rootView.findViewById(R.id.rl_bottom)
        mRlCrowdfunding = rootView.findViewById(R.id.rl_crowdfunding)
        mTvStatus = rootView.findViewById(R.id.tv_status)
        mllCarInternet = rootView.findViewById(R.id.ll_car_internet)
        btnCart = rootView.findViewById(R.id.btn_cart)
        btnShoppingCar = rootView.findViewById(R.id.btn_add_to_shopping_car)
        btnBuyNow = rootView.findViewById(R.id.buy_now)
        swipeToLoadLayout.setBackgroundColor(Color.TRANSPARENT)
        mTvCrowdFundingOrder = rootView.findViewById(R.id.tv_crowdfunding_order)
        mTvPrice = rootView.findViewById(R.id.tv_price)
        //加粗订单数太粗 调整
        var mCrowdfundingOrderLeft: TextView =
            rootView.findViewById(R.id.tv_crowdfunding_order_left)
        mCrowdfundingOrderLeft.paint.isFakeBoldText = true
        var mCrowdfundingOrderRight: TextView =
            rootView.findViewById(R.id.tv_crowdfunding_order_right)
        mCrowdfundingOrderRight.paint.isFakeBoldText = true
    }


    private fun getCommoditySizeOffset() {
        header.clear()
        mEvaluation.commodityId = commodityId
        mEvaluation.carTypeId = null
        mEvaluation.isCar = false
        mCenterCommodityDetail?.apply {
            mEvaluation.isCar = commodityClassifyId == 0
            if (mEvaluation.isCar) {
                mEvaluation.carTypeId = carTypeId
            }
            //顶部banner
            header.add(R.layout.item_commodity_detail_banner)
            //商品信息,promotionType = 5 是众筹
            if (promotionActivity.activityInfoList.size > 0 && promotionActivity.activityInfoList[0].promotionType == 5) {
                header.add(R.layout.item_commodity_detail_info2)
            } else {
                header.add(R.layout.item_commodity_detail_info)
            }
            //勋章
            if (lmCommodityAsLimit?.limitMedalFlag == 1 && commodityMedalList?.isNotEmpty() == true) {
                header.add(R.layout.item_commodity_detail_medal)
            }
            //sku规格选择、参数、服务 - 2023/4/19需求：车商品页面不显示服务
            header.add(R.layout.item_commodity_detail_middle_info)

            //商品详情
            lmCommodityAsDetail?.apply {
                if (imgTexts != null) {
                    header.addAll(imgTexts)
                }
            }
//            //评价
//            header.add(mEvaluation)
//
//            //推荐商品
//            header.add(R.layout.item_commodity_recommond_title)
        }
    }

    override fun getListDataFromNet(
        service2: L00bangService2,
        pageNo: Int,
        pageSize: Int
    ): Observable<BaseResponse<List<Any>>> {
        val map = HashMap<String, Any>()
        map["type"] = 1
        map["pageNo"] = if (pageNo > 1) pageNo - 1 else 1
        map["pageSize"] = pageSize
        return service2.getRecommendCommodityList(map)
            .map { listBaseResponse: BaseResponse<List<StoreElementCommodity>> ->
//                if (listBaseResponse.data == null || listBaseResponse.data.isEmpty()) {
//                    listBaseResponse.cloneWithData(emptyList())
//                } else {
//                    listBaseResponse.cloneWithData(listBaseResponse.data)
//                }
                listBaseResponse.cloneWithData(emptyList())
            }
    }

    override fun getListData(page: Int) {
        if (page <= 1) {
            mPageNo = 1
            isFirstLoad = false
            queryCommodityDetail()
            //请求购物车信息
            SelfUserInfoLoader.getInstance().getUserCartInfo(context)
        } else {
            super.getListData(page)
        }
    }

    override fun createItemDecoration(context: Context): RecyclerView.ItemDecoration {
        val px30 = context.resources.getDimensionPixelOffset(R.dimen.normal_30)
        val px16 = context.resources.getDimensionPixelOffset(R.dimen.normal_16)
        return object : RecyclerView.ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                super.getItemOffsets(outRect, view, parent, state)
                val data = data ?: return
                val position = parent.getChildAdapterPosition(view)
                if (position < 0 || position >= data.size) {
                    return
                }
                if (mData[position] is StoreElementCommodity) {
                    var spanIndex = -1
                    val tempSize = mData.size
                    for (i in 0 until tempSize) {
                        if (mData[position] is StoreElementCommodity) {
                            spanIndex++
                        }
                    }
                    if (spanIndex == -1) {
                        return
                    }
                    if (spanIndex % 2 == 0) {
                        outRect.left = px30
                        outRect.right = px16
                    } else {
                        outRect.left = px16
                        outRect.right = px30
                    }
                }

            }
        }
    }

    override fun createLayoutManager(context: Context): RecyclerView.LayoutManager {
        val spanCount = 2
        val manager = StaggeredGridLayoutManager(spanCount, StaggeredGridLayoutManager.VERTICAL)
        manager.spanCount
        //防止 item 交换位置
        manager.gapStrategy = StaggeredGridLayoutManager.GAP_HANDLING_NONE
        return manager
    }

    /**
     * 请求获取精选列表
     */
    private fun requestEvaluationList(waresId: String) {
        val map: MutableMap<String, Any> = java.util.HashMap()
        map["waresId"] = waresId
        map["type"] = -1
        map["size"] = -1
        map["currentPage"] = 1
//        map["commentType"] = 8.toString()
        if (mCenterCommodityDetail?.commodityClassifyId == 0) {
            map["commentType"] = 1.toString()
            map["carId"] = mCenterCommodityDetail?.carTypeId.toString()
        } else {
            map["commentType"] = 8.toString()
        }
        L00bangRequestManager2
            .getServiceInstance()
            .getCommodityEvaluateShowList(map)
            .compose(L00bangRequestManager2.setSchedulers())
            .subscribe(object :
                BackgroundSubscriber<EvaluationContent>(AppUtil.getActivity(context)) {
                override fun onSuccess(evaluationContent: EvaluationContent) {
                    super.onSuccess(evaluationContent)
                    mEvaluation.mEvaluationContent = evaluationContent
                    notifyItemChangedByViewType(R.layout.item_commodity_detail_evaluation)
                }
            })
    }

    /**
     * 获取评价标签以及好评率
     */
    private fun requestEvaluationLabelList(waresId: String) {
        if (TextUtils.isEmpty(waresId)) {
            return
        }
        val map: MutableMap<String, String> = java.util.HashMap()
        map["waresId"] = waresId
        if (mCenterCommodityDetail?.commodityClassifyId == 0) {
            map["commentType"] = 1.toString()
            mCenterCommodityDetail?.carTypeId?.let {
                map["carId"] = it.toString()
            }
        } else {
            map["commentType"] = 8.toString()
        }
        L00bangRequestManager2.getServiceInstance()
            .getCommodityEvaluateLabelList(map)
            .compose(L00bangRequestManager2.setSchedulers())
            .subscribe(object : BackgroundSubscriber<EvaluationLabel>(context) {
                override fun onSuccess(evaluationLabel: EvaluationLabel) {
                    super.onSuccess(evaluationLabel)
                    mEvaluation.mEvaluationLabel = evaluationLabel
                    notifyItemChangedByViewType(R.layout.item_commodity_detail_evaluation)
                }
            })
    }

    val mEvaluation: Evaluation = Evaluation()

    class Evaluation {
        var commodityId: Long? = null
        var isCar: Boolean = false
        var carTypeId: Long? = null
        var mEvaluationContent: EvaluationContent? = null
        var mEvaluationLabel: EvaluationLabel? = null
    }


    override fun onLoadSuccess(loadPage: Int, list: MutableList<Any>?, loadType: Int) {
        if (loadPage == 1) {
            isLoadMoreEnable = true
            swipeToLoadLayout.isRefreshing = false
            getListData(2)
        } else {
            super.onLoadSuccess(loadPage, list, loadType)
        }
    }

    override fun onLoadMoreComplete(size: Int) {
        if (size > 0) {
            notifyItemChangedByViewType(R.layout.item_commodity_recommond_title)
            adapter.notifyItemRangeInserted(data.size - size, size)
        }
        swipeToLoadLayout.setLoadingMore(false, true)
    }

    override fun onLoadFail(loadPage: Int, e: Throwable?) {
        super.onLoadFail(loadPage, e)
        if (loadPage > 1) {
            notifyItemChangedByViewType(R.layout.item_commodity_recommond_title)
        }
    }

    private fun notifyItemChangedByViewType(viewType: Int) {
        if (viewType <= 0) {
            return
        }
        val count = adapter?.itemCount ?: 0
        for (i in 0 until count) {
            if (adapter?.getItemViewType(i) == viewType) {
                adapter?.notifyItemChanged(i)
                break
            }
        }
    }
}