package com.cloudy.linglingbang.activity.store.commodity

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.app.util.DensityUtil
import com.cloudy.linglingbang.app.util.DeprecatedUtils
import com.cloudy.linglingbang.app.util.DeviceUtil
import com.cloudy.linglingbang.app.widget.popup.BasePopupWindow
import com.cloudy.linglingbang.model.store.commodity.ShoppingCart2

/**
 * 购物车金额明细弹窗
 *
 * <AUTHOR>
 * @date 2022/10/16
 */
class ShoppingCartAmountDetailsPopupWindow(context: Context) :
    BasePopupWindow(context) {

    var ivClose: ImageView? = null
    var llDialog: LinearLayout? = null
    var tvTotalPrice: TextView? = null
    var tvTotalMinus: TextView? = null
    var tvSingleDiscount: TextView? = null
    var tvFullDiscount: TextView? = null
    var tvCoupon: TextView? = null
    var tvFinalAmount: TextView? = null


    override fun initViews() {
        super.initViews()
        ivClose = contentView.findViewById(R.id.iv_close)
        llDialog = contentView.findViewById(R.id.ll_dialog)
        tvTotalPrice = contentView.findViewById(R.id.tv_total_price)
        tvTotalMinus = contentView.findViewById(R.id.tv_total_minus)
        tvSingleDiscount = contentView.findViewById(R.id.tv_single_discount)
        tvFullDiscount = contentView.findViewById(R.id.tv_full_discount)
        tvCoupon = contentView.findViewById(R.id.tv_coupon)
        tvFinalAmount = contentView.findViewById(R.id.tv_final_amount)
        ivClose?.setOnClickListener {
            dismiss()
        }
        llDialog?.setOnClickListener {
            //设置点击事件，点击白色区域不关闭
        }
    }


    override fun getPopAnimationStyle(): Int {
        return R.style.popwin_anim_style_from_bottom
    }


    override fun showAtLocation(parent: View?, gravity: Int, x: Int, y: Int) {
        width = ViewGroup.LayoutParams.MATCH_PARENT
        height = DeviceUtil.getScreenHeight() - DensityUtil.dip2px(context, 71F)
        isFocusable = true
        setBackgroundDrawable(DeprecatedUtils.createEmptyBitmapDrawable())
        //点击空白处关闭pop
        contentView.setOnClickListener { dismiss() }
        super.showAtLocation(parent, gravity, x, y)
    }


    override fun getDefaultLayoutResId(): Int {
        return R.layout.popupwindow_shopping_cart_amount_detail
    }

    fun setShoppingCart(shoppingCart: ShoppingCart2?) {
        shoppingCart?.apply {
            tvTotalPrice?.text = "¥$cartAmountStr"
            tvTotalMinus?.text = "¥$discountAmountStr"
            tvSingleDiscount?.text = "-¥$commodityOffStr"
            tvFullDiscount?.text = "-¥$combineOffStr"
            tvCoupon?.text = "-¥$couponOffStr"
            tvFinalAmount?.text = "¥$payAmountStr"

        }


    }


}