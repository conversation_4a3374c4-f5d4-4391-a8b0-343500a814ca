package com.cloudy.linglingbang.activity.welcome;

import com.cloudy.linglingbang.BaseInstrumentedTest;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;

/**
 * 欢迎页
 *
 * <AUTHOR>
 * @date 2018/8/10
 */
public class WelcomeActivityTest extends BaseInstrumentedTest {
    private String mOpenUrl = "llb://web.CommonWeb/DealerActivityDetail/1";

    @Override
    public void before() {
        super.before();
        mActivity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mActivity.getTextView().setAllCaps(false);
                mActivity.getTextView().setText(mOpenUrl);
            }
        });
    }

    @Override
    public void test() {
        super.test();
        JumpPageUtil.goCommonWeb(getContext(), mOpenUrl);
        ToastUtil.showMessage(getContext(), "打开" + mOpenUrl);
    }
}