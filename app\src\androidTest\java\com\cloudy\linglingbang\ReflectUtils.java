package com.cloudy.linglingbang;

import java.lang.reflect.Field;

/**
 * 反射工具
 *
 * <AUTHOR>
 * @date 2019/8/23
 */
public class ReflectUtils {
    public static void setFieldValue(Object object, String fieldName, Object fieldValue) {
        Class clazz = object.getClass();
        try {
            Field field = clazz.getDeclaredField(fieldName);
            boolean accessible = field.isAccessible();
            if (!accessible) {
                field.setAccessible(true);
            }
            field.set(object, fieldValue);
            if (!accessible) {
                field.setAccessible(false);
            }
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
    }
}
