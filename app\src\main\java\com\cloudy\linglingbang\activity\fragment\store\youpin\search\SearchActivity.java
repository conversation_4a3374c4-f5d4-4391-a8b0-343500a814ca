package com.cloudy.linglingbang.activity.fragment.store.youpin.search;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.app.util.FastClickUtils;
import com.cloudy.linglingbang.app.util.InputMethodUtils;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.util.timer.CountDownManager;
import com.cloudy.linglingbang.constants.FinalSensors;

import butterknife.OnClick;

/**
 * 商城-搜索商品
 *
 * <AUTHOR>
 * @date 2020/5/6
 */
public class SearchActivity extends BaseActivity implements TextWatcher {

    private EditText et_content;
    private HotWordController mHotWordController;
    private SearchController mSearchController;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_search_store_home);
    }

    @Override
    protected void initialize() {
        SensorsUtils.StoreHomeAnchor storeHomeAnchor = (SensorsUtils.StoreHomeAnchor) getIntentExtra(null);
        et_content = findViewById(R.id.et_content);
        FrameLayout frameLayout = findViewById(R.id.fl_container);
        mSearchController = new SearchController(this, frameLayout, storeHomeAnchor);
        mSearchController.setStoreHomeAnchor(storeHomeAnchor);
        //热词
        mHotWordController = new HotWordController(this, new HotWordController.OnSearchListener() {
            @Override
            public void search(String text) {
                onClickHotWord(text);
                mHotWordController.hide();
            }

            @Override
            public void showInputMethod() {
                et_content.postDelayed(new Runnable() {

                    @Override
                    public void run() {
                        InputMethodUtils.showInputMethod(et_content);
                    }
                }, 100);
            }
        });
        mHotWordController.show();
        mHotWordController.queryHotWord();
        //先show才能addView，否则为null
        frameLayout.addView(mHotWordController.getSearchHotWordView());

        //设置
        et_content.setHint(getText(R.string.search_commodity_input_hint1));
        et_content.setImeOptions(EditorInfo.IME_ACTION_SEARCH);
        et_content.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                boolean handled = false;
                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                    checkAndSearch(true);
                    handled = true;
                }
                return handled;
            }
        });
        et_content.addTextChangedListener(this);
    }

    /**
     * 点击热词搜索
     */
    public void onClickHotWord(String history) {
        et_content.setText(history);
        //点击历史后，光标移到最后
        et_content.setSelection(et_content.getText().length());
        checkAndSearch(true);
    }

    /**
     * 点击取消
     */
    @OnClick(R.id.tv_search)
    public void onClickCancelButton() {
        onBack();
    }

    /**
     * 检查是否为空，进行搜索
     *
     * @param fromSearch 是表示点击搜索按钮或键盘上的搜索，false表示点击类型切换
     * @return true表示进行了搜索，false表示为空
     */
    private boolean checkAndSearch(boolean fromSearch) {
        if (FastClickUtils.isFastClick()) {
            return false;
        }
        String text = et_content.getText().toString().trim();
        if (TextUtils.isEmpty(text)) {
            if (fromSearch) {
                ToastUtil.showMessage(this, getString(R.string.search_input_hint));
            }
            return false;
        }
        mSearchController.search(text, fromSearch);
        //隐藏输入法
        InputMethodUtils.hideInputMethod(this, et_content);
        mHotWordController.hide();
        return true;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        CountDownManager.getInstance().onDestroy(this);
        et_content.removeTextChangedListener(this);
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {
        if (s.length() == 0) {
            mSearchController.hide();
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        SensorsUtils.sensorsViewStart(FinalSensors.STORE_SEARCH_PAGE);
    }

    @Override
    protected void onStop() {
        super.onStop();
        SensorsUtils.sensorsViewEndNew("搜索页", FinalSensors.STORE_SEARCH_PAGE, "浏览好物搜索页");
    }

}
