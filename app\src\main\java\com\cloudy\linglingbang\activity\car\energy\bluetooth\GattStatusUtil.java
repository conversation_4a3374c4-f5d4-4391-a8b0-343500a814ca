package com.cloudy.linglingbang.activity.car.energy.bluetooth;

import java.util.HashMap;
import java.util.Map;

/**
 * 蓝牙连接status状态码
 * Created by LiYeWen on 2025/05/07
 */
public class GattStatusUtil {

    private static GattStatusUtil instance;
    //蓝牙连接status状态码集合
    private Map<Integer, String> mStatusMap = new HashMap<>();

    private GattStatusUtil(){
        mStatusMap.put(0, "(GATT操作成功完成)"); //GATT_SUCCESS
        mStatusMap.put(8, "(设备端已经断开连接、HCI层检测到连接超时)"); //BLE_HCI_CONNECTION_TIMEOUT
        mStatusMap.put(19, "(远程用户终止连接)"); //BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION
        mStatusMap.put(62, "(蓝牙硬件未能成功建立连接)"); //BLE_HCI_CONN_FAILED_TO_BE_ESTABLISHED
        mStatusMap.put(133, "(可以是任何原因，从设备不在范围内到随机错误)"); //GATT_ERROR
        mStatusMap.put(257, "(GATT操作失败，除上述错误之外的其他错误)"); //GATT_FAILURE
    }

    /**
     * 获取单例对象
     * @return
     */
    public static GattStatusUtil getInstance() {
        if (instance == null) {
            synchronized (GattStatusUtil.class) {
                instance = new GattStatusUtil();
            }
        }
        return instance;
    }

    /**
     * 获取蓝牙连接status状态描述
     * @param status 蓝牙回调状态码
     * @return
     */
    public String getDesc(int status){
        return " status = " +  status + mStatusMap.get(status) ;
    }

}
