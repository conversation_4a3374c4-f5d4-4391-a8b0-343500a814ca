package com.cloudy.linglingbang.activity.community.post.holder;

import android.content.Context;
import android.view.View;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.community.common.PostFlagsEnum;
import com.cloudy.linglingbang.activity.community.common.holder.square.PostAuthorWithAttentionViewHolder;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.util.ViewHolderUtils;
import com.cloudy.linglingbang.app.widget.BaseOnClickListener;
import com.cloudy.linglingbang.constants.WebUrlConfigConstant;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.user.Author;

/**
 * 帖子详情的作者
 *
 * <AUTHOR>
 * @date 2018/8/26
 */
public class PostDetailAuthorViewHolder extends PostAuthorWithAttentionViewHolder {
    private TextView mTvApplyAuthor;

    public PostDetailAuthorViewHolder(View itemView) {
        super(itemView);
        //展示关注
        addFlags(PostFlagsEnum.SHOW_ATTENTION);
        //删除后 finish
        setFinishAfterDelete(true);
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        //将标签赋值给申请作者，标签置为 null 父类不处理，申请作者按钮单独处理
        mTvApplyAuthor = mTvLabel;
//        mTvApplyAuthor.setText(R.string.post_detail_apply_author);
//        mTvApplyAuthor.setTextColor(itemView.getContext().getResources().getColor(R.color.color_00b0ff));
//        mTvApplyAuthor.setOnClickListener(new BaseOnClickListener(true) {
//            @Override
//            public void onClick(Context context) {
//                super.onClick(context);
//                onClickApplyAuthor(context);
//            }
//        });
        //置为 null 不展示
        mTvColumn = null;
        mTvLabel = null;
    }

    @Override
    public void bindTo(PostCard postCard) {
        super.bindTo(postCard);
//        Author author;
//        ViewHolderUtils.setVisibility(!UserUtils.isAuthor()
//                        && postCard != null
//                        && (author = postCard.getAuthor()) != null
//                        && author.getAudit() == 1
//                , mTvApplyAuthor);
    }

    /**
     * 申请成为作者
     */
    private void onClickApplyAuthor(Context context) {
        JumpPageUtil.goCommonWeb(context, WebUrlConfigConstant.APPLY_AUTHOR);
    }
}
