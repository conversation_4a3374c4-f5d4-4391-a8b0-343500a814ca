package com.cloudy.linglingbang.activity.car.list;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseScrollTabViewPagerFragment;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.viewpager.widget.PagerAdapter;

/**
 * 授权车辆的列表页面
 *
 * <AUTHOR>
 * @date 2020-02-27
 */
public class VertifyCarListFragment extends BaseScrollTabViewPagerFragment<Fragment> {

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_vertify_car_list;
    }

    @Override
    protected PagerAdapter createViewPagerAdapter(List<Fragment> data, String[] titles) {
        return new FragmentStatePagerAdapter(getChildFragmentManager()) {
            @Override
            public Fragment getItem(int position) {
                return data.get(position);
            }

            @Override
            public int getCount() {
                return data.size();
            }

            @Nullable
            @Override
            public CharSequence getPageTitle(int position) {
                return titles[position];
            }
        };
    }

    @Override
    protected List<Fragment> createAdapterData() {
        List<Fragment> fragmentList = new ArrayList<>();
        NewEnergyCarListFragment newEnergyCarListFragment = new NewEnergyCarListFragment();
        fragmentList.add(newEnergyCarListFragment);
        InternetOfVehiclesCarListFragment internetOfVehiclesCarListFragment = new InternetOfVehiclesCarListFragment();
        fragmentList.add(internetOfVehiclesCarListFragment);
        return fragmentList;
    }

    @Override
    protected String[] getTitles() {
        return getResources().getStringArray(R.array.vertify_car_list_title_array);
    }
}
