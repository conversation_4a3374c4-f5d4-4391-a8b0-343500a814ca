package com.cloudy.linglingbang.activity.store.commodity.adapter

import android.content.Context
import android.content.Intent
import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.ScanImageActivity
import com.cloudy.linglingbang.app.widget.image.ImageLoadUtils
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder
import com.cloudy.linglingbang.model.postcard.PostCardItem
import com.cloudy.linglingbang.model.store.commodity.CommoditySku
import java.io.Serializable


class AttributeValueWithImageAdapter(context: Context?, list: List<CommoditySku.AttributeValue>?) :
    BaseRecyclerViewAdapter<CommoditySku.AttributeValue>(context, list) {
    override fun createViewHolder(itemView: View?): BaseRecyclerViewHolder<CommoditySku.AttributeValue> {
        return AttributeValueViewHolder(itemView)
    }

    override fun getItemLayoutRes(viewType: Int): Int {
        return R.layout.item_commodity_attribute_value1_with_image
    }

    class AttributeValueViewHolder(itemView: View?) :
        BaseRecyclerViewHolder<CommoditySku.AttributeValue>(itemView) {
        var mImageView: ImageView? = null
        var mTextView: TextView? = null
        var attribute: CommoditySku.AttributeValue? = null

        override fun initItemView(itemView: View) {
            super.initItemView(itemView)
            mImageView = itemView.findViewById<View>(R.id.iv_image) as ImageView
            mTextView = itemView.findViewById<View>(R.id.tv_text) as TextView

            itemView.findViewById<View>(R.id.iv_show)?.setOnClickListener {
                attribute?.apply {
//                    if (TextUtils.isEmpty(attributeValueImage)) {
//                        return@apply
//                    }
                    val intent = Intent(
                        itemView.context,
                        ScanImageActivity::class.java
                    )
                    val list: ArrayList<PostCardItem> = arrayListOf()
                    list.add(PostCardItem())
                    list[0].imgDesc = attributeValueName
                    list[0].img =
                        if (TextUtils.isEmpty(attributeValueImage)) "empty" else attributeValueImage
                    intent.putExtra(
                        ScanImageActivity.EXTRA_IMAGE_DESC_LIST,
                        list as Serializable?
                    )
                    intent.putExtra("image_index", 0)
                    intent.putExtra(ScanImageActivity.EXTRA_SHOW_IMAGE_INDEX, false)
                    intent.putExtra(
                        ScanImageActivity.EXTRA_IMAGE_ERROR,
                        R.drawable.ic_sku_attr_place1
                    )
                    itemView.context.startActivity(intent)
                }

            }
        }

        override fun bindTo(bean: CommoditySku.AttributeValue?, position: Int) {
            super.bindTo(bean, position)
            attribute = bean
            bean?.apply {
                mTextView?.text = attributeValueName
                ImageLoadUtils.createImageLoad(
                    mImageView,
                    attributeValueImage,
                    R.drawable.ic_sku_attr_place
                ).load()
                if (bean.checkState == 2) {
                    itemView.visibility = View.GONE
                    itemView.isEnabled = false
                    itemView.setBackgroundResource(R.drawable.shape_bg_solid_f2f2f2_stroke_2px_f2f2f2)
                    mTextView?.setTextColor(itemView.resources.getColor(R.color.color_3d1D1E23))
                } else {
                    itemView.visibility = View.VISIBLE
                    mTextView?.setTextColor(itemView.resources.getColor(if (bean.checkState == 1) R.color.color_ea0029 else R.color.color_1D1E23))
                    itemView.isEnabled = true
                    if (bean.checkState == 1) {
                        itemView.setBackgroundResource(R.drawable.shape_bg_solid_fce5e9_stroke_2px_ea0029)
                    } else {
                        itemView.setBackgroundResource(R.drawable.shape_bg_solid_f2f2f2_stroke_2px_f2f2f2)
                    }
                }
            }
        }
    }

}