package com.cloudy.linglingbang.activity.car.energy.bluetooth;

/**
 * 开启通知阶段枚举
 */
public enum DescriptorWriteEnum {

    DEFAULT(0, "默认值无效阶段"),
    AUTH_ONE(1, "第一步鉴权阶段"),
    AUTH_TWO(2, "第二步鉴权阶段"),
    CONTROL(3, "注册控制指令通道阶段");

    /**
     * @param flag   鉴权阶段
     * @param desc   描述
     */
    DescriptorWriteEnum(int flag, String desc) {
        this.flag = flag;
        this.desc = desc;
    }

    private final int flag;
    private final String desc;

    public int getFlag() {
        return flag;
    }

    public String getDesc() {
        return desc;
    }

}
