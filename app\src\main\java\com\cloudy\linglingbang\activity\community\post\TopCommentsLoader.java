package com.cloudy.linglingbang.activity.community.post;

import android.content.Context;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.model.Comment;
import com.cloudy.linglingbang.model.postcard.CommentTitle;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BackgroundSubscriber;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 置顶评论加载
 *
 * <AUTHOR>
 * @date 2018/5/8
 */
public class TopCommentsLoader {
    public static final int TOP_STATUS_TOP = 1;
    private Context mContext;
    private long mPostId;
    private List<Comment> mTopComments;
    private BaseRecyclerViewAdapter mPostDetailNativeAdapter;
    private boolean mIsNeedAddTitle = true;//是否需要添加标题（只在帖子中需要添加置顶评论几个字）

    public TopCommentsLoader(Context context, long postId, BaseRecyclerViewAdapter postDetailNativeAdapter) {
        mContext = context;
        mPostId = postId;
        mPostDetailNativeAdapter = postDetailNativeAdapter;
    }

    /**
     * 构造方法
     *
     * @param isNeedAddTitle 设置是否添加标题
     */
    public TopCommentsLoader(Context context, long postId, BaseRecyclerViewAdapter postDetailNativeAdapter, boolean isNeedAddTitle) {
        mContext = context;
        mPostId = postId;
        mPostDetailNativeAdapter = postDetailNativeAdapter;
        mIsNeedAddTitle = isNeedAddTitle;
    }

    /**
     * 加载
     */
    public void load() {
        L00bangRequestManager2.getServiceInstance()
                .getTopComments(mPostId, TOP_STATUS_TOP)
                .compose(L00bangRequestManager2.<List<Comment>>setSchedulers())
                .subscribe(new BackgroundSubscriber<List<Comment>>(mContext) {
                    @Override
                    public void onSuccess(List<Comment> comments) {
                        super.onSuccess(comments);
                        onLoadTopComments(comments);
                    }
                });
    }

    /**
     * 加载完成
     */
    private void onLoadTopComments(List<Comment> comments) {
        if (comments != null && !comments.isEmpty()) {
            //这里不能用 topStatus 来标记移除，因为下方的全部评论，服务器返回的 topStatus 也是 1
            for (Comment comment : comments) {
                comment.setFromTop(true);
            }
            //添加前后的 title
            if (mIsNeedAddTitle) {
                comments.add(0, new CommentTitle(mContext.getString(R.string.post_detail_top_comments), true));
            }
        }
        mTopComments = comments;

        List<Comment> data = mPostDetailNativeAdapter.getData();
        if (data != null) {
            //移除置顶
            Iterator<Comment> iterator = data.iterator();
            while (iterator.hasNext()) {
                Comment comment = iterator.next();
                if (comment != null) {
                    if (comment.isFromTop()) {
                        iterator.remove();
                    }
                }
            }
        }
        //添加置顶
        addTopComments(data);
        mPostDetailNativeAdapter.notifyDataSetChanged();
    }

    /**
     * 添加置顶评论
     */
    public void addTopComments(List<Comment> data) {
        if (mTopComments == null) {
            //不用添加
            return;
        }
        if (data == null) {
            //初始化，要添加
            data = new ArrayList<>();
        }
        //查找插入位置
        int index = 0;
        for (; index < data.size(); index++) {
            if (data.get(index) != null) {
                break;
            }
        }
        //index 为0 ,或者是不为 null 的位置
        data.addAll(index, mTopComments);
    }

    public List<Comment> getTopComments() {
        return mTopComments;
    }
}
