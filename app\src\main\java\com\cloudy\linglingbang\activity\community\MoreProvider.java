package com.cloudy.linglingbang.activity.community;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;

import com.cloudy.linglingbang.R;

import androidx.core.view.ActionProvider;
import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 社区详情页更多按钮
 * Created by han<PERSON><PERSON> on 16/4/13.
 */
public class MoreProvider extends ActionProvider {
    private Context mContext;
    @BindView(R.id.iv_message_icon1)
    ImageView mIvMessageIcon1;
    @BindView(R.id.iv_red_point)
    ImageView mIvRedPoint;

    //定义一个接口，以便回调函数调用
    public interface OnListener {
        void execute();
    }

    //声明接口
    private OnListener callBack;

    //外部进行接口实现
    public void setCallBackListener(OnListener listener) {
        this.callBack = listener;
    }

    public MoreProvider(Context context) {
        super(context);
        this.mContext = context;
    }

    @Override
    public View onCreateActionView() {
        LayoutInflater inflater = LayoutInflater.from(mContext);
        final View view = inflater.inflate(R.layout.layout_menu_more, null);
        ButterKnife.bind(this, view);
        view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                callBack.execute();
            }
        });
        return view;
    }

    public void setIcon(int res) {
        mIvMessageIcon1.setImageDrawable(getContext().getResources().getDrawable(res));
    }

    /**
     * 设置红点是否可见
     *
     * @param unreadPointVisible
     */
    public void setUnreadPointVisible(int unreadPointVisible) {
        if (mIvRedPoint != null) {
            mIvRedPoint.setVisibility(unreadPointVisible);
        }
    }

}
