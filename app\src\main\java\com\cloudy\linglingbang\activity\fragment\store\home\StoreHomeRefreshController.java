package com.cloudy.linglingbang.activity.fragment.store.home;

import android.content.Context;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.IRefreshContext;
import com.cloudy.linglingbang.activity.fragment.store.home.model.StoreHomeNavElementWrapper;
import com.cloudy.linglingbang.activity.fragment.store.youpin.StoreNavScrollListener;

import java.util.List;

import androidx.recyclerview.widget.RecyclerView;

/**
 * 商城的各 tab 数据加载
 *
 * <AUTHOR>
 * @date 2020/5/11
 */
public class StoreHomeRefreshController extends StoreHomeBaseRefreshController {

    private View mIvFmFloatView;
    private StoreNavScrollListener mNavScrollListener;

    public StoreHomeRefreshController(IRefreshContext<Object> refreshContext, String pageCode) {
        super(refreshContext, pageCode);
    }

    @Override
    protected void onLoadSuccess(int loadPage, List<Object> list, int loadType) {
        super.onLoadSuccess(loadPage, list, loadType);
        if (mIvFmFloatView != null) {
            boolean hasNav = false;
            recyclerView.removeOnScrollListener(mNavScrollListener);
            for (int i = 0; i < mData.size(); i++) {
                Object o = mData.get(i);
                if (o instanceof StoreHomeNavElementWrapper) {
                    hasNav = true;
                    mNavScrollListener.setNavViewHolder(mIvFmFloatView, (StoreHomeNavElementWrapper) o, i);
                    break;
                }
            }
            if (hasNav) {
                recyclerView.addOnScrollListener(mNavScrollListener);
            } else {
                if (mIvFmFloatView.getVisibility() == View.VISIBLE) {
                    mIvFmFloatView.setVisibility(View.GONE);
                }
            }
        }
    }

    @Override
    protected RecyclerView.ItemDecoration createItemDecoration(final Context context) {
        return new StoreHomeElementItemDecoration(context);
    }

    @Override
    public void initViews(View rootView) {
        super.initViews(rootView);
        if (hasNavTab()) {
            mIvFmFloatView = rootView.findViewById(R.id.ll_search_condition);
            if (mIvFmFloatView != null) {
                mNavScrollListener = new StoreNavScrollListener(recyclerView);
                mIvFmFloatView.setVisibility(View.GONE);
                mIvFmFloatView.setPadding(-getContext().getResources().getDimensionPixelOffset(R.dimen.normal_8),
                        mIvFmFloatView.getPaddingTop(),
                        mIvFmFloatView.getPaddingRight(),
                        mIvFmFloatView.getPaddingBottom());
            }
        }
        recyclerView.setPadding(0, recyclerView.getPaddingTop(), 0, recyclerView.getPaddingBottom());
    }
}
