package com.cloudy.linglingbang.activity.user.share.partner;

import com.cloudy.linglingbang.BaseInstrumentedTest;
import com.cloudy.linglingbang.app.util.AppUtil;

/**
 * 合伙人分享排行榜
 *
 * <AUTHOR>
 * @date 2019-09-23
 */
public class PartnerLederboardShareActivityTest extends BaseInstrumentedTest {

    @Override
    public void test() {
        super.test();
        if (AppUtil.checkLogin(getActivity())) {
            PartnerLederboardShareUtils.Extra extra = new PartnerLederboardShareUtils.Extra();
            extra.setDesc("全公司排名NO.x");
            extra.setNumber("100");
            extra.setShareProgCodeImg("http://news.workercn.cn/html/files/2019-05/13/20190513140317796750819.jpg");

//            extra.setShareImag("https://b-ssl.duitang.com/uploads/item/201210/04/20121004224008_rPJkL.jpeg");
//            extra.setShareImag("https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1569402169823&di=2d63e9b7bf9e70c70d9d2b0a0c9dd65b&imgtype=0&src=http%3A%2F%2Fb-ssl.duitang.com%2Fuploads%2Fitem%2F201609%2F12%2F20160912182224_YHmfZ.thumb.700_0.jpeg");
//            extra.setShareImag("https://ss0.bdstatic.com/70cFvHSh_Q1YnxGkpoWK1HF6hhy/it/u=109785448,1218225440&fm=26&gp=0.jpg");
//            IntentUtils.startActivity(getActivity(), PartnerLederboardShareActivity.class, extra);

            PartnerLederboardShareUtils.getInstance().startShare(getActivity(), extra);
        }
    }

}