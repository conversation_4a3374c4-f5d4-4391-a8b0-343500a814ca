package com.cloudy.linglingbang.activity.store.commodity.adapter.holder

import android.view.View
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.store.EvaluationActivity
import com.cloudy.linglingbang.activity.store.commodity.CommodityDetailRefreshController
import com.cloudy.linglingbang.adapter.store.evaluation.ChooseEvaluationAdapter
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils

/**
 * 评价标题
 *
 * <AUTHOR>
 * @date 2022/9/26
 */
class EvaluationTitleViewHolder(itemView: View?) : BaseCommodityHolder<Any>(itemView) {
    private var mTvEvaluationCount: TextView? = null
    private var mTvAllEvaluation: TextView? = null
    private var recyclerView: RecyclerView? = null
    var evaluation: CommodityDetailRefreshController.Evaluation? = null

    override fun initItemView(itemView: View?) {
        super.initItemView(itemView)
        itemView?.apply {
            mTvEvaluationCount = findViewById(R.id.tv_evaluation_count)
            mTvAllEvaluation = findViewById(R.id.tv_all_evaluation)
            recyclerView = findViewById(R.id.recyclerView)
            findViewById<View>(R.id.rl_evaluation).setOnClickListener {
                var eventName = "点击(评价)"
                mCenterCommodity?.apply {
                    eventName = " 点击($commodityId)($commodityName)"
                }
                val type =
                    if (mCenterCommodity?.commodityClassifyId == 0) "(整车)" else "(优品)"

                val applauseRate = evaluation?.mEvaluationLabel?.applauseRate ?: -1f

                SensorsUtils.sensorsClickBtn(
                    eventName + if (applauseRate > 0f) "好评率" else "暂无评价",
                    "商品详情页$type",
                    "查看全部"
                )
                val intentExtra = EvaluationActivity.IntentExtra(evaluation?.commodityId.toString())
                if (evaluation?.carTypeId != null) {
                    intentExtra.carTypeId = evaluation?.carTypeId.toString()
                }
                intentExtra.classType = if (evaluation?.isCar == true) 1 else 0
                intentExtra.evaluationLabel = evaluation?.mEvaluationLabel
                intentExtra.openFrom = EvaluationActivity.OPEN_FROM_NEW_COMMODITY_DETAIL
                EvaluationActivity.startActivity(
                    itemView.context,
                    intentExtra
                )
            }
        }
    }

    override fun bindTo(bean: Any?, position: Int) {
        super.bindTo(bean, position)
        evaluation = null
        recyclerView?.visibility = View.GONE
        if (bean is CommodityDetailRefreshController.Evaluation) {
            evaluation = bean
            mTvEvaluationCount?.text = itemView.resources.getString(
                R.string.commodity_evaluation_count,
                (bean.mEvaluationContent?.count ?: 0)
            )
            mTvAllEvaluation?.setTextColor(itemView.context.resources.getColor(R.color.color_ea0029))
            mTvAllEvaluation?.text = itemView.context.resources.getString(
                R.string.store_evaluation_applause_rate,
                (bean.mEvaluationLabel?.applauseRateToInt ?: 0).toString() + "%"
            )
            val chooseEvaluationAdapter = object : ChooseEvaluationAdapter(
                itemView.context,
                bean.mEvaluationContent?.data
            ) {
                override fun getItemLayoutRes(viewType: Int): Int {
                    return R.layout.item_evaluation_commodity_detail_new
                }
            }
            chooseEvaluationAdapter.setOnItemClickListener { itemView, _ ->
                var eventName = "点击精选评价"
                mCenterCommodity?.apply {
                    eventName = " 点击($commodityId)($commodityName)精选评价"
                }
                val type =
                    if (mCenterCommodity?.commodityClassifyId == 0) "(整车)" else "(优品)"

                SensorsUtils.sensorsClickBtn(
                    eventName,
                    "商品详情页$type",
                    "精选评价内容"
                )
                val intentExtra = EvaluationActivity.IntentExtra(bean.commodityId.toString())
                if (bean.carTypeId != null) {
                    intentExtra.carTypeId = bean.carTypeId.toString()
                }
                intentExtra.classType = if (evaluation?.isCar == true) 1 else 0
                intentExtra.evaluationLabel = bean.mEvaluationLabel
                intentExtra.openFrom = EvaluationActivity.OPEN_FROM_NEW_COMMODITY_DETAIL
                EvaluationActivity.startActivity(
                    itemView.context,
                    intentExtra
                )
            }
            recyclerView?.visibility =
                if (chooseEvaluationAdapter.itemCount > 0) View.VISIBLE else View.GONE
            recyclerView?.adapter = chooseEvaluationAdapter
        } else {
            recyclerView?.visibility = View.GONE
            mTvEvaluationCount?.text =
                itemView.resources.getString(R.string.commodity_evaluation_count, 0)
            mTvAllEvaluation?.setTextColor(itemView.context.resources.getColor(R.color.color_99A1B2))
            mTvAllEvaluation?.setText(R.string.commodity_no_have_evaluation)
            recyclerView?.adapter = null
        }
    }
}