package com.cloudy.linglingbang.activity.chat;

import android.Manifest;
import android.content.Context;
import android.graphics.Bitmap;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.MD5;
import com.cloudy.linglingbang.app.util.PermissionUtils;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.util.chat.GetContactsTask;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.app.widget.textview.PressEffectiveButton;
import com.cloudy.linglingbang.model.chat.Contacts;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.NormalSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.user.User;
import com.umeng.analytics.MobclickAgent;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 联系人好友
 *
 * <AUTHOR> create at 2016/12/29 13:06
 */
public class ContactsFriendActivity extends BaseRecyclerViewRefreshActivity<Object> {
    private HashMap<String, Contacts> contactsHashMap;

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<Object> list) {
        return new ContactsFriendAdapter(this, list);
    }

    @Override
    public Observable<BaseResponse<List<Object>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return null;
    }

    /**
     * 读取联系人
     */
    private void getContacts() {

        new GetContactsTask(this, new GetContactsTask.GetContactsObserver() {
            @Override
            public void notifyContentData(ArrayList<String> names, ArrayList<String> numbers, ArrayList<Bitmap> icons) {
                if (numbers == null || numbers.size() <= 0) {
                    ToastUtil.showMessage(ContactsFriendActivity.this, R.string.contacts_friend_toast_empty);
                    getRefreshController().getSwipeToLoadLayout().setRefreshing(false);
                    return;
                }
                contactsHashMap = new HashMap<>();
                String[] contactsNumberMd5Array = new String[numbers.size()];
                int size = numbers.size();
                for (int i = 0; i < size; i++) {
                    contactsNumberMd5Array[i] = MD5.getMD5Str(numbers.get(i));
                    Contacts contacts = new Contacts();
                    contacts.setIcon(icons.get(i));
                    contacts.setName(names.get(i));
                    contacts.setPhone(numbers.get(i));
                    contactsHashMap.put(numbers.get(i), contacts);
                }
                getContactsFriend(contactsNumberMd5Array);
            }
        }).execute();
    }

    private void getContactsFriend(String[] contactsNumberMd5Array) {
        Map<String, Object> map = new HashMap<>();
        map.put("mobiles", contactsNumberMd5Array);
        L00bangRequestManager2
                .getServiceInstance()
                .getInvitedList(map)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new NormalSubscriber<ArrayList<User>>(this) {
                    @Override
                    public void onSuccess(ArrayList<User> users) {
                        super.onSuccess(users);
                        //好友列表
                        getRefreshController().getData().clear();
                        getRefreshController().getData().addAll(users);
                        //联系人列表
                        for (int i = 0; i < users.size(); i++) {
                            contactsHashMap.remove(users.get(i).getMobile());
                        }
                        for (Contacts contacts : contactsHashMap.values()) {
                            getRefreshController().getData().add(contacts);
                        }
                        getRefreshController().getAdapter().notifyDataSetChanged();
                        getRefreshController().getSwipeToLoadLayout().setRefreshing(false);
                    }
                });
    }

    @Override
    public RefreshController<Object> createRefreshController() {
        return new RefreshController<Object>(this) {
            @Override
            public void getListData(int page) {
                //如果没有权限，则申请的时候先关闭刷新
                if (!PermissionUtils.checkPermission(ContactsFriendActivity.this, Manifest.permission.READ_CONTACTS)) {
                    getRefreshController().getSwipeToLoadLayout().setRefreshing(false);
                }
                checkPermissions(0, getString(R.string.permission_contacts_pre), getString(R.string.permission_contacts_setting), Manifest.permission.READ_CONTACTS);
            }
        };
    }

    @Override
    protected void onPermissionResult(boolean isGranted, int requestCode) {
        super.onPermissionResult(isGranted, requestCode);
        //如果权限允许
        if (isGranted) {
            getContacts();
        }//如果未放行权限
        else {
            getRefreshController().getSwipeToLoadLayout().setRefreshing(false);
        }
    }

    private class ContactsFriendAdapter extends BaseRecyclerViewAdapter<Object> {

        ContactsFriendAdapter(Context context, List<Object> data) {
            super(context, data);
        }

        @Override
        protected BaseRecyclerViewHolder<Object> createViewHolder(View itemView) {
            return new ContactsFriendViewHolder(itemView);
        }

        @Override
        protected int getItemLayoutRes(int viewType) {
            return R.layout.item_contacts_friend;
        }

        private class ContactsFriendViewHolder extends BaseRecyclerViewHolder<Object> {
            ImageView iv_icon;
            TextView tv_name;
            TextView tv_phone_number;
            PressEffectiveButton btn_add_friend;

            ContactsFriendViewHolder(View itemView) {
                super(itemView);
            }

            @Override
            protected void initItemView(final View itemView) {
                super.initItemView(itemView);
                iv_icon = itemView.findViewById(R.id.iv_icon);
                tv_name = itemView.findViewById(R.id.tv_name);
                tv_phone_number = itemView.findViewById(R.id.tv_phone_number);
                btn_add_friend = itemView.findViewById(R.id.btn_add_friend);
                btn_add_friend.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        Object o = mData.get(getAdapterPosition());
                        if (o instanceof User) {
                            final User user = (User) o;
                            if (!UserUtils.hasAttentionOther(user)) {
                                //友盟统计
                                MobclickAgent.onEvent(mContext, "32");
                                L00bangRequestManager2.getServiceInstance()
                                        .addFriend(user.getUserIdStr())
                                        .compose(L00bangRequestManager2.setSchedulers())
                                        .subscribe(new ProgressSubscriber<String>(ContactsFriendActivity.this) {
                                            @Override
                                            public void onSuccess(String s) {
                                                super.onSuccess(s);
                                                ToastUtil.showMessage(getApplicationContext(), R.string.user_homepage_attention_success);
                                                user.setFriendRelation(User.friendRelation.ATTENTION_OTHER);
                                                notifyDataSetChanged();
                                            }
                                        });
                            }
                        } else if (o instanceof Contacts) {
                            final Contacts contacts = (Contacts) o;
                            //友盟统计
                            MobclickAgent.onEvent(mContext, "33");
                            //发送短信
                            IntentUtils.startActivityBySendToIntent(mContext, contacts.getPhone(), getString(R.string.sms_content));
                            //通知服务器加金币
                            L00bangRequestManager2
                                    .getServiceInstance()
                                    .inviteRegister()
                                    .compose(L00bangRequestManager2.setSchedulers())
                                    .subscribe(new NormalSubscriber<String>(mContext) {
                                        @Override
                                        public void onSuccess(String s) {
                                            super.onSuccess(s);
                                            contacts.setIsInvited(true);
                                            notifyDataSetChanged();
                                        }
                                    });
                        }
                    }
                });
            }

            @Override
            public void bindTo(Object o, int position) {
                super.bindTo(o, position);
                if (o instanceof User) {
                    tv_phone_number.setVisibility(View.VISIBLE);
                    //注册用户
                    User user = (User) o;
                    tv_name.setText(user.getNickname());
                    tv_phone_number.setText(user.getMobile());
                    ImageLoad.LoadUtils.loadAvatar(mContext, iv_icon, user.getPhoto());
                    if (user.getAttentionDisplayStatus() == 1) {
                        btn_add_friend.setVisibility(View.GONE);
                    } else {
                        btn_add_friend.setVisibility(View.VISIBLE);
                        if (UserUtils.hasAttentionOther(user)) {
                            btn_add_friend.setText(mContext.getString(R.string.item_contacts_friend_attended));
                            setButtonEnable(false);
                        } else {
                            btn_add_friend.setText(mContext.getString(R.string.item_contacts_friend_attend));
                            setButtonEnable(true);
                        }
                    }
                } else if (o instanceof Contacts) {
                    tv_phone_number.setVisibility(View.GONE);
                    //联系人
                    Contacts contacts = (Contacts) o;
                    tv_name.setText(contacts.getName());
                    iv_icon.setImageBitmap(contacts.getIcon());
                    if (contacts.isInvited()) {
                        btn_add_friend.setText(mContext.getString(R.string.item_contacts_friend_invited));
                        setButtonEnable(false);
                    } else {
                        btn_add_friend.setText(mContext.getString(R.string.item_contacts_friend_invite));
                        setButtonEnable(true);
                    }
                }
            }

            /**
             * 设置按钮是否可用
             *
             * @param enable 可用
             */
            private void setButtonEnable(boolean enable) {
                if (enable) {
                    btn_add_friend.setNormalShapeType(PressEffectiveButton.ShapeType.FilledRoundedRectangle);
                    btn_add_friend.setPressedShapeColor(PressEffectiveButton.ShapeType.FilledRoundedRectangle);

                    btn_add_friend.setNormalTextColor(mContext.getResources().getColor(R.color.white));
                    btn_add_friend.setPressedShapeColor(mContext.getResources().getColor(R.color.white));

                    btn_add_friend.setNormalShapeColor(mContext.getResources().getColor(R.color.orange_ff7000));
                    btn_add_friend.setPressedShapeColor(mContext.getResources().getColor(R.color.orange_bt_press));

                    btn_add_friend.updateShapeBackgroundAndTextColor();

                    btn_add_friend.setEnabled(true);
                } else {
                    btn_add_friend.setNormalShapeType(PressEffectiveButton.ShapeType.StrokedRoundedRectangle);
                    btn_add_friend.setPressedShapeType(PressEffectiveButton.ShapeType.StrokedRoundedRectangle);

                    btn_add_friend.setNormalTextColor(mContext.getResources().getColor(R.color.gray_divider_e2e2e2));
                    btn_add_friend.setPressedTextColor(mContext.getResources().getColor(R.color.gray_divider_e2e2e2));

                    btn_add_friend.setNormalShapeColor(mContext.getResources().getColor(R.color.gray_divider_e2e2e2));
                    btn_add_friend.setPressedShapeColor(mContext.getResources().getColor(R.color.gray_divider_e2e2e2));

                    btn_add_friend.updateShapeBackgroundAndTextColor();

                    btn_add_friend.setEnabled(false);
                }
            }
        }
    }
}
