package com.cloudy.linglingbang.activity.store.commodity.adapter.holder

import android.view.View
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.store.commodity.adapter.CommodityDetailAdapter
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder

/**
 * 帖子详情-商品推荐标题
 *
 * <AUTHOR>
 * @date 2022/9/26
 */
class RecommendTitleViewHolder(itemView: View?) : BaseRecyclerViewHolder<Any>(itemView) {

    var ll_empty_view: View? = null
    override fun initItemView(itemView: View?) {
        super.initItemView(itemView)
        ll_empty_view = itemView?.findViewById(R.id.ll_empty_view);
    }

    override fun bindTo(bean: Any?, position: Int) {
        super.bindTo(bean, position)
        ll_empty_view?.visibility =
            if (mAdapter != null && position + 1 < mAdapter.itemCount
                && mAdapter.getItemViewType(position + 1) == R.layout.item_commodity_recommond_commodity
            ) {
                View.GONE
            } else {
                View.VISIBLE
            }
    }
}