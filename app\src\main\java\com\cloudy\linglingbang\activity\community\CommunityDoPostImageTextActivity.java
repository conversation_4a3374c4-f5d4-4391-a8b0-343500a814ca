package com.cloudy.linglingbang.activity.community;

import android.Manifest;
import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.text.InputFilter;
import android.text.InputType;
import android.text.Spanned;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.baidu.mapapi.model.LatLng;
import com.baidu.mapapi.search.core.PoiInfo;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.HomeActivity;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.club.CarClubDetailActivity;
import com.cloudy.linglingbang.activity.community.post.ChooseDrivingHabitDialog;
import com.cloudy.linglingbang.activity.community.post.ChooseQuestionTypeDialog;
import com.cloudy.linglingbang.activity.community.post.PostDetailActivity;
import com.cloudy.linglingbang.activity.community.post.detail.GoodsStickyDialog;
import com.cloudy.linglingbang.activity.fragment.community.EngineerAndTechnicianQuestionTypeFragment;
import com.cloudy.linglingbang.activity.fragment.mynew.util.SelfUserInfoLoader;
import com.cloudy.linglingbang.activity.my.follow.MyAttentionActivity;
import com.cloudy.linglingbang.activity.newcommunity.PublishGoodsExchangeInfoActivity;
import com.cloudy.linglingbang.app.log.LogUtils;
import com.cloudy.linglingbang.app.receiver.UserInfoChangedHelper;
import com.cloudy.linglingbang.app.util.ActivityController;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ChooseImageController;
import com.cloudy.linglingbang.app.util.DeviceUtil;
import com.cloudy.linglingbang.app.util.FileUtil;
import com.cloudy.linglingbang.app.util.ImageUtil;
import com.cloudy.linglingbang.app.util.InputLengthLimitFilter;
import com.cloudy.linglingbang.app.util.InputMethodUtils;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.LogUploadUtils;
import com.cloudy.linglingbang.app.util.NetworkUtil;
import com.cloudy.linglingbang.app.util.PermissionUtils;
import com.cloudy.linglingbang.app.util.PreferenceUtil;
import com.cloudy.linglingbang.app.util.ThreadManager;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.UpLoadUtil;
import com.cloudy.linglingbang.app.util.ViewHolderUtils;
import com.cloudy.linglingbang.app.util.file.FileUtils;
import com.cloudy.linglingbang.app.util.image.compress.CompressImageHelper;
import com.cloudy.linglingbang.app.util.image.compress.CompressListener;
import com.cloudy.linglingbang.app.util.image.compress.CompressResult;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.ExpressionViewPager;
import com.cloudy.linglingbang.app.widget.SoftKeyBoardListener;
import com.cloudy.linglingbang.app.widget.TextAndImageView;
import com.cloudy.linglingbang.app.widget.dialog.ChooseCarTypeDialog;
import com.cloudy.linglingbang.app.widget.dialog.ChooseCityChannelDialog;
import com.cloudy.linglingbang.app.widget.dialog.alert.AlertController;
import com.cloudy.linglingbang.app.widget.dialog.alert.BaseListDialog;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonListDialog;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonSelectListDialog;
import com.cloudy.linglingbang.app.widget.item.CommonItem;
import com.cloudy.linglingbang.app.widget.item.CommonItemWithLeftIcon;
import com.cloudy.linglingbang.app.widget.item.CommonItemWithRightInput;
import com.cloudy.linglingbang.constants.ConfigConstant;
import com.cloudy.linglingbang.db.GreenDaoManager;
import com.cloudy.linglingbang.greendao.PostCardItemDao;
import com.cloudy.linglingbang.model.CarType;
import com.cloudy.linglingbang.model.ImageModel;
import com.cloudy.linglingbang.model.ProvinceModel;
import com.cloudy.linglingbang.model.community.ColumnInfo;
import com.cloudy.linglingbang.model.community.ContentGroupInfo;
import com.cloudy.linglingbang.model.postcard.DriverHabitType;
import com.cloudy.linglingbang.model.postcard.EditPostCard;
import com.cloudy.linglingbang.model.postcard.PersonalCard;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.postcard.PostCardItem;
import com.cloudy.linglingbang.model.postcard.PostCardManager;
import com.cloudy.linglingbang.model.postcard.QuestionType;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.server.MyCar;
import com.cloudy.linglingbang.model.user.MyDefaultCar;
import com.cloudy.linglingbang.model.user.User;
import com.google.gson.Gson;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import butterknife.BindView;
import butterknife.OnClick;
import butterknife.Optional;

import static com.cloudy.linglingbang.R.string.community_do_post_dialog_save_no_exit;
import static com.cloudy.linglingbang.R.string.community_do_post_dialog_save_yes;
import static com.cloudy.linglingbang.constants.ConfigConstant.CHOOSE_COMMUNITY_REQUEST_CODE;
import static com.cloudy.linglingbang.constants.ConfigConstant.MAX_PIC_COUNT;

/**
 * 社区发帖页面 fromType=1
 * Created by hanfei on 16/6/3.
 */
public class CommunityDoPostImageTextActivity extends BaseActivity implements View.OnClickListener {

    public final static String POI_INFO_KEY = "poiInfo";
    /**
     * 提车作业贴的模块
     */
    @Nullable
    @BindView(R.id.vs_car_buying_experience)
    ViewStub mVsCarBuying;

    @Nullable
    @BindView(R.id.ll_do_post_zone)
    LinearLayout mLlDoPostZone;

    @Nullable
    @BindView(R.id.rl_choose_community_zone)
    LinearLayout mRlChooseCommunityZone;
    /**
     * 表情的ViewPager
     */
    @Nullable
    @BindView(R.id.expression_view_pager)
    protected ExpressionViewPager expression_view_pager;
    /**
     * 选择社区的按钮
     */
    @Nullable
    @BindView(R.id.tv_select_community)
    CommonItemWithLeftIcon tv_select_community;

    /**
     * 去电脑上发帖
     */
    @Nullable
    @BindView(R.id.tv_post_by_computer)
    TextView tv_post_by_computer;

    /**
     * 图文编辑区
     */
    @BindView(R.id.ll_content)
    protected LinearLayout ll_content;

    /**
     * 话题名称
     */
    @Nullable
    @BindView(R.id.tv_topic_name)
    protected TextView tv_topic_name;

    /**
     * 插入图片、表情、拍照等功能栏
     */
    @Nullable
    @BindView(R.id.ll_function_view)
    LinearLayout ll_function_view;

    /**
     * 图文帖标题
     */
    @Nullable
    @BindView(R.id.ll_post_title)
    LinearLayout ll_post_title;

    /**
     * 图文帖标题编辑框
     */
    @Nullable
    @BindView(R.id.et_image_text_post_title)
    protected EditText et_post_title;

    /**
     * 抽屉布局
     */
    @Nullable
    @BindView(R.id.drawer_layout)
    DrawerLayout mDrawerLayout;

    /**
     * 图文帖标题允许输入的最大字符个数
     */
    private static final int MAX_INPUT_TITLE_LENGTH = 25;
    /**
     * 图文帖标题的KEY
     */
    public static final String KEY_IMAGE_TEXT_TITLE = "image_text_title";
    /**
     * 从广场发帖
     */
    public static final int OPEN_FROM_SQUARE = 1;
    /**
     * 从本地发帖
     */
    public static final int OPEN_FROM_LOCAL = 2;
    /**
     * 从车型发帖
     */
    public static final int OPEN_FROM_CAR_TYPE = 3;
    /**
     * 从车友会发帖
     */
    public static final int OPEN_FROM_CAR_CLUB = 4;
    /**
     * 从好货互通发帖
     */
    public static final int OPEN_FROM_GOODS = 5;
    /**
     * 从人车生活发帖
     */
    public static final int OPEN_FROM_PEOPLE_CAR_LIFT = 6;
    /**
     * 发帖入口
     */
    protected int mOpenFrom;
    /**
     * 选择本地栏目请求码
     */
    public static final int REQUEST_CODE_CHOOSE_LOCAL_COLUMN = 1001;
    /**
     * 好货互通选择封面
     */
    public static final int REQUEST_CODE_CHOOSE_GOODS_COVER_IMG = 1002;

    public static final int REQUEST_CODE_SELECT_LOCATION = 1009;

    public static final int REQUEST_CODE_SELECT_USER = 1010;

    /**
     * 城市社区栏目id
     */
    protected Long mLocalColumnId;

    /**
     * 城市
     */
    @Nullable
    @BindView(R.id.tv_city)
    protected CommonItemWithLeftIcon mTvCity;
    /**
     * 标签
     */
    @Nullable
    @BindView(R.id.tv_label)
    protected CommonItemWithLeftIcon mTvLabel;

    /**
     * 位置
     */
    @Nullable
    @BindView(R.id.ll_location)
    protected CommonItemWithLeftIcon mLlLocation;
    @Nullable
    @BindView(R.id.tv_location)
    protected TextView mTvLocation;
    protected PoiInfo poiInfo;

    /**
     * 提醒用户
     */
    @Nullable
    @BindView(R.id.ll_notify_user)
    protected CommonItemWithLeftIcon mLlNotifyUser;
    protected ArrayList<User> mNotifyUserList;

    public static final int REQUEST_CODE_CHOOSE_TYPE_LABEL = 1003;
    /**
     * 个人名片icon
     */
    @Nullable
    @BindView(R.id.iv_personal_card)
    protected ImageView mIvPersonalCard;
    /**
     * 个人名片布局
     */
    @Nullable
    @BindView(R.id.ll_personal_card)
    protected LinearLayout mLlPersonalCard;
    public static final int REQUEST_CODE_CHOOSE_PERSONAL_CARD = 1004;
    /**
     * 个人名片数据
     */
    protected PersonalCard mPersonalCard;
    protected String mGoodsCoverImagePath;
    protected PostCardItem mGoodsCoverImg;

    private PostCard mEditPostCard;
    /**
     * 车型
     */
    private CommonItem mItemCarType;
    private ChooseCarTypeDialog.ChooseCarTypeUtil mChooseCarTypeUtil;
    protected CarType mChosenCarType;
    /**
     * 问题类型
     */
    private CommonItem mItemQuestionType;
    private ChooseQuestionTypeDialog.ChooseQuestionTypeUtil mChooseQuestionTypeUtil;
    private QuestionType mChosenQuestionType;
    private String mChosenQuestionIds;//问题类型id组合
    /**
     * 驾驶习惯
     */
    private CommonItem mItemDrivingHabit;
    private ChooseDrivingHabitDialog.ChooseDrivingHabitUtil mChooseDrivingHabitUtil;
    private DriverHabitType mDriverHabitType;//驾驶习惯
    /**
     * 行驶里程
     */
    private CommonItemWithRightInput mItemMileage;
    private String mChosenMileage;//行驶里程

    /**
     * 是否已经提交
     */
    protected boolean isCommit = false;
    /**
     * 发帖类型，在intent中传过来。
     * 用于
     * <br/>①在{@linkplain #
     * ()}中设置标题及是否显示社区等初始化
     * <br/>②在{@linkplain #addFirstText()}中是否需要修改顶部的布局
     * <br/>③在{@linkplain #checkCommitInput()}中检查提交时要校验的信息
     * <br/>④在{@linkplain #needSaveDraft()}中判断是否需要保存草稿
     * <br/>⑤在{@linkplain TextAndImageView#newInstance(Context, int)}中设置hint等
     */
    protected int mPostType;

    private int tag = 0;
    protected EditText editText;
    private int currentTag;
    private int totalTextCount = 0;//总字数
    private int totalPicCount = 0;//总图片数
    private int totalOutCurrentText;//去除了当前文本框的剩余文字
    private List<PostCardItem> itemList;

    protected IntentExtra mIntentExtra;

    protected static PostCardItemDao mPostCardItemDao;

    private int mExperienceFromType;

    private final PostCard.RewardType[] mRewardTypes = PostCard.RewardType.values();
    private static final int READ_PIC = 0;
    private static final int TAKE_PIC = 1;
    Handler handler = new Handler();

    //菱感贴内容
    protected String postContent;
    protected String postTitle;
    //编辑菱感贴时候使用
    protected PostCard mPostCard;

    /**
     * 问题类型抽屉
     */
    private EngineerAndTechnicianQuestionTypeFragment mEngineerAndTechnicianDrawerFragment;
    private DrawerLayout.DrawerListener mDrawerListener;

    private ChooseImageController mChooseImageController;

    /**
     * 最少字数限制
     */
    private static class LeastWords {
        final static int SIGNED = 2;
        final static int IMAGE_TEXT = 10;
        final static int TEC = 10;
        final static int TOPIC = 10;
        final static int CAR_BUYING = 50;
    }

    /**
     * 保存帖子的定时任务runnable
     */
    Runnable runnable = new Runnable() {
        @Override
        public void run() {
            saveImageAndText();
            handler.postDelayed(this, ConfigConstant.SAVE_DRAFT_DELAY);
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        PostCardManager.getInstance().getUploadImgs().clear();

        mToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                makeSureBack();
            }
        });

        //监听键盘状态，键盘弹出时关闭表情键盘的显示a
        SoftKeyBoardListener.setListener(CommunityDoPostImageTextActivity.this, new SoftKeyBoardListener.OnSoftKeyBoardChangeListener() {
            @Override
            public void keyBoardShow(int height) {
                if (expression_view_pager != null && expression_view_pager.getVisibility() == View.VISIBLE) {
                    expression_view_pager.setVisibility(View.GONE);
                }
            }

            @Override
            public void keyBoardHide(int height) {
            }
        });
    }

    @Override
    public void loadViewLayout() {
        setContentView(R.layout.activity_do_post_image_drawer_layout);
    }

    /**
     * 打开小游戏分享发帖页面
     *
     * @param context  上下文
     * @param postType 发帖类型-{@link com.cloudy.linglingbang.model.postcard.PostCard.PostType}
     * @param res      h5图片资源
     */
    public static void startActivityToShareGameByPath(Context context, int postType, String res, int linkType, String linkUrl) {
        IntentExtra intentExtra = new IntentExtra(postType, res, linkType, linkUrl);
        intentExtra.setGroupInfo(new ContentGroupInfo(PostCard.PostSeries.CAR_FORUM));
        intentExtra.setOpenFrom(CommunityDoPostImageTextActivity.OPEN_FROM_CAR_TYPE);
        IntentUtils.startActivity(context, CommunityDoPostImageTextActivity.class, intentExtra);
    }

    private ChooseCityChannelDialog mChooseCityDialog;

    /**
     * 初始化右侧抽屉
     */
    private void initRightDrawer() {
        //避免重复初始化
        if (mEngineerAndTechnicianDrawerFragment != null) {
            return;
        }
        FrameLayout frameLayout = findViewById(R.id.fl_drawer);
        ViewGroup.LayoutParams params = frameLayout.getLayoutParams();
        params.width = DeviceUtil.getScreenWidth() * 59 / 75;
        frameLayout.setLayoutParams(params);

        if (mEngineerAndTechnicianDrawerFragment == null) {
            mEngineerAndTechnicianDrawerFragment = new EngineerAndTechnicianQuestionTypeFragment();
        }
        FragmentManager fragmentManager = getSupportFragmentManager();
        FragmentTransaction beginTransaction = fragmentManager.beginTransaction();
        beginTransaction.add(R.id.fl_drawer, mEngineerAndTechnicianDrawerFragment, "mEngineerAndTechnicianDrawerFragment");
        beginTransaction.commitAllowingStateLoss();
        mDrawerListener = new DrawerLayout.DrawerListener() {
            @Override
            public void onDrawerSlide(@NonNull View drawerView, float slideOffset) {

            }

            @Override
            public void onDrawerOpened(@NonNull View drawerView) {
                if (mEngineerAndTechnicianDrawerFragment != null) {
                    mEngineerAndTechnicianDrawerFragment.getData();
                }
            }

            @Override
            public void onDrawerClosed(@NonNull View drawerView) {
                if (mEngineerAndTechnicianDrawerFragment != null) {
                    mEngineerAndTechnicianDrawerFragment.reset();
                }
            }

            @Override
            public void onDrawerStateChanged(int newState) {

            }
        };
        mDrawerLayout.addDrawerListener(mDrawerListener);

    }

    /**
     * 关闭右侧抽屉
     */
    public void closeDrawer() {
        if (mDrawerLayout != null && mDrawerLayout.isDrawerOpen(Gravity.END)) {
            mDrawerLayout.closeDrawer(Gravity.END);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mDrawerLayout != null && mDrawerListener != null) {
            mDrawerLayout.removeDrawerListener(mDrawerListener);
        }
    }

    /**
     * 更新问题类型文案
     */
    public void upDataSelect(String content, String ids) {
        mItemQuestionType.getTvLeft().setText(content);
        mChosenQuestionIds = ids;
    }

    /**
     * 是否需要关闭右侧抽屉
     */
    private Boolean isNeedCloseDrawer() {
        return mDrawerLayout != null && mDrawerLayout.isDrawerOpen(Gravity.END);
    }

    /**
     * 设置标题最大字数
     */
    public int getTitleMaxLength() {
        return MAX_INPUT_TITLE_LENGTH;
    }

    /**
     * 展示图文帖标题
     */
    protected void showImageTextPostTitle() {
        ll_post_title.setVisibility(View.VISIBLE);
        et_post_title.setFilters(new InputFilter[]{new InputFilter() {
            @Override
            public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                for (int i = start; i < end; i++) {
                    if (!isCommonCharacter(source.charAt(i))) {
                        return "";
                    }
                }
                return null;
            }
        }, new InputFilter.LengthFilter(getTitleMaxLength())});
        et_post_title.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    ll_function_view.setVisibility(View.GONE);
                } else {
                    ll_function_view.setVisibility(View.VISIBLE);
                }
            }
        });
    }

    /**
     * 判断是否是普通字符(排除emoji、特殊字符等)
     *
     * @param codePoint 比较的单个字符
     */
    private boolean isCommonCharacter(char codePoint) {
        return (codePoint == 0x0) || (codePoint == 0x9) || (codePoint == 0xA) ||
                (codePoint == 0xD) || ((codePoint >= 0x20) && (codePoint <= 0xD7FF)) ||
                ((codePoint >= 0xE000) && (codePoint <= 0xFFFD)) || ((codePoint >= 0x10000)
                && (codePoint <= 0x10FFFF));
    }

    /**
     * 0点~7点技师或者工程师休眠时间提示弹窗
     */
    private void showTechnicianRestTime(String message) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(AppUtil.getServerCurrentTime());
        int hour = calendar.get(Calendar.HOUR_OF_DAY);//获取小时
        int minute = calendar.get(Calendar.MINUTE);//获取分钟
        int minuteOfDay = hour * 60 + minute;//从0:00到目前为止的分钟数
        if (minuteOfDay >= 0 && minuteOfDay <= 7 * 60) {//0点~7点
            CommonAlertDialog dialog = new CommonAlertDialog(this,
                    message,
                    getString(R.string.feedback_dialog_success_button), null, null, null);
            AlertController alertController = dialog.getAlertController();
            if (alertController != null) {
                alertController.setTitle(getString(R.string.technician_rest_time_title));
            }
            dialog.show();
            if (alertController != null && alertController.getTitleView() != null) {
                alertController.getTitleView().setTextSize
                        (TypedValue.COMPLEX_UNIT_PX, getResources().getDimension(R.dimen.activity_set_text_36));
            }
        }
    }

    /**
     * 设置提车作业帖相关背景
     */
    private void setCarBuyingPostBackground() {
        mVsCarBuying.inflate();
        mLlDoPostZone.setBackgroundColor(Color.WHITE);
        mRlChooseCommunityZone.setVisibility(View.GONE);
    }

    /**
     * 展示添加位置
     */
    protected void showLocation() {
        if (mLlLocation == null) {
            return;
        }
        mLlLocation.setVisibility(View.GONE);
        mLlLocation.setOnClickListener(null);
        if (mOpenFrom == OPEN_FROM_CAR_TYPE || mIntentExtra.getOpenFrom() == OPEN_FROM_GOODS) {
            if ((mPostType == PostCard.PostType.IMAGE_TEXT || mPostType == PostCard.PostType.SHORT_VIDEO || mPostType == PostCard.PostType.SHARE_GAME)) {
//                mTvLocation.setText("添加位置");
                mLlLocation.setVisibility(View.VISIBLE);
                String postTitle = et_post_title.getText().toString();
                if (!TextUtils.isEmpty(postTitle)) {
                    String json = PreferenceUtil.getStringPreference(this, POI_INFO_KEY, null);
                    if (!TextUtils.isEmpty(json)) {
                        poiInfo = new Gson().fromJson(json, PoiInfo.class);
                        mLlLocation.getTvLeft().setText(poiInfo.getName());
                        return;
                    }
                }
                mLlLocation.setOnClickListener(this::selectLocation);
            }
        }
    }

    protected void selectLocation(View v) {

        SensorsUtils.sensorsClickBtn("点击发长文-添加位置", "长文发布页", "添加位置");

        IntentUtils.startActivityForResult(this, SelectLocationActivity.class, REQUEST_CODE_SELECT_LOCATION);
    }

    /**
     * 展示草稿箱的内容
     */
    protected void showDraft() {
        if (mPostType == PostCard.PostType.IMAGE_TEXT) {
            String draftTitle = PreferenceUtil.getStringPreference(this, KEY_IMAGE_TEXT_TITLE, "");
            if (!TextUtils.isEmpty(draftTitle)) {
                et_post_title.setText(draftTitle);
            }
        }
        List<PostCardItem> postCardItems = null;
        try {
            postCardItems = mPostCardItemDao.queryBuilder()
                    .where(PostCardItemDao.Properties.Type.eq(mPostType))
                    .orderAsc(PostCardItemDao.Properties.Index)
                    .list();
        }catch (NullPointerException e){
            e.printStackTrace();
        }
        if (postCardItems != null && postCardItems.size() > 0) {
            addSavedImageAndTextView(postCardItems);
        } else {
            addFirstText();
        }
    }

    /**
     * 添加分享文字
     */
    private void addShareText(String shareText) {
        TextAndImageView textAndImageView = TextAndImageView.newInstance(this, mIntentExtra.getPostType());
        PostCardItem postCardItem = new PostCardItem();
        postCardItem.setText(shareText);
        textAndImageView.setTag(tag);
        textAndImageView.setObject(postCardItem);
        ll_content.addView(textAndImageView);
        tag++;
        setTextAndImageListener(textAndImageView);//监听文本框的变化
    }

    @Override
    public void initialize() {
        mPostCardItemDao = GreenDaoManager.getInstance().getDaoSession().getPostCardItemDao();
        mIntentExtra = (IntentExtra) getIntentExtra(null);
        if (mIntentExtra == null) {
            String json = getIntent().getStringExtra("p");
            if (!TextUtils.isEmpty(json)) {
                mIntentExtra = new Gson().fromJson(json, IntentExtra.class);
            }
        }

        if (mIntentExtra == null) {
            onIntentExtraError();
            return;
        }
        //禁止抽屉滑动出来
        if (mDrawerLayout != null) {
            mDrawerLayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED, Gravity.END);
        }
        //根据发帖类型进行初始化
        mPostType = mIntentExtra.getPostType();
        mExperienceFromType = mIntentExtra.getFromType();
        mOpenFrom = mIntentExtra.getOpenFrom();
        if (mPostType == PostCard.PostType.NEW_VEHICLE_ZONE_IMAGE_TEXT) {
            mPostType = PostCard.PostType.IMAGE_TEXT;
        }
        //设置标题
        //如果是问题帖
        if (mPostType == PostCard.PostType.QUESTION) {
            setLeftTitle(getString(R.string.title_post_type_question));
            showDraft();
        } else if (mPostType == PostCard.PostType.ASK_TECHNICIAN || mPostType == PostCard.PostType.ASK_ENGINEER) {//如果是金牌技师帖或者问工程师
            if (mPostType == PostCard.PostType.ASK_ENGINEER) {
                setLeftTitle(getString(R.string.title_post_type_ask_engineer));
                //工程师休眠时间提示弹窗
                showTechnicianRestTime(getString(R.string.engineer_rest_time_message));
            } else {
                setLeftTitle(getString(R.string.title_post_type_ask_technician));
                //技师休眠时间提示弹窗
                showTechnicianRestTime(getString(R.string.technician_rest_time_message));
            }
            ViewStub stub = findViewById(R.id.view_stub_divider_20);
            stub.inflate();
            initRewardViews();
            //添加第一个文本框
            addFirstText();
            //初始化右侧抽屉
            initRightDrawer();
            ViewHolderUtils.goneViews(mRlChooseCommunityZone);
        } else if (mPostType == PostCard.PostType.ASK_SOPHISTICATED_DRIVER) {//如果是问老司机
            setLeftTitle(getString(R.string.title_post_type_ask_sophisticated_driver));
            //添加第一个文本框
            addFirstText();
        } else if (mPostType == PostCard.PostType.EDIT_POST_CARD) {//如果是编辑帖子
            if (mOpenFrom == OPEN_FROM_GOODS) {//设置发布好货时候的toolbar显示样式
                toolbar_title_left.setText(getString(R.string.tv_publish_goods_back));
                toolbar_title_left.setVisibility(View.VISIBLE);
                toolbar_title_left.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        finish();
                    }
                });
                mToolbar.setNavigationIcon(null);
                setLeftTitle("编辑好货");
            } else {
                setLeftTitle("编辑帖子");
            }
            mRlChooseCommunityZone.setVisibility(View.GONE);
            //如果是编辑帖子，则根据帖子的id获取帖子详情
            requestPostDetail();
            /*if (!TextUtils.isEmpty(mIntentExtra.getChannelId())) {
                //如果是编辑帖子，则根据帖子的id获取帖子详情
                requestPostDetail();
            }*/
        } else if (mPostType == PostCard.PostType.EDIT_TOPIC) {//编辑话题帖
            setMiddleTitle("编辑帖子");
            mRlChooseCommunityZone.setVisibility(View.GONE);
            requestPostDetail();
        } else if (mPostType == PostCard.PostType.SHARE_GAME) {//游戏分享帖
            setLeftTitle(getString(R.string.title_post_type_image_text));
            tv_post_by_computer.setVisibility(View.VISIBLE);
            tv_post_by_computer.getPaint().setUnderlineText(true);
            showImageTextPostTitle();
            addShareText("");
//            insertWebImage();
            insertWebImageForPath();
            mOpenFrom = OPEN_FROM_CAR_TYPE;
        } else if (mPostType == PostCard.PostType.CAR_BUYING_EXPERIENCE) {//发提车作业帖
            setLeftTitle(getString(R.string.title_post_type_car_buying_experience));
            setCarBuyingPostBackground();
            showDraft();
        } else if (mPostType == PostCard.PostType.EDIT_CAR_BUYING_POST_CARD) {//编辑提车作业帖
            setLeftTitle(getString(R.string.title_post_type_edit_car_buying));
            setCarBuyingPostBackground();
            PostCard postCard = mIntentExtra.getPostCard();//获取车辆信息页传递的帖子对象
            if (postCard != null) {
                List<PostCardItem> postCardItem = postCard.getImgTexts();
                if (postCardItem != null && postCardItem.size() > 0) {
                    addSavedImageAndTextView(postCardItem);
                } else {
                    addFirstText();
                }
            }
        } else if (mPostType == PostCard.PostType.SIGN_IN) {//签到帖
            setLeftTitle(getString(R.string.title_post_type_sign_in));
            addFirstText();
        } else if (mPostType == PostCard.PostType.TOPIC) {//话题帖
            setLeftTitle(getString(R.string.title_post_type_topic));
            tv_topic_name.setVisibility(View.VISIBLE);
            String topicName = mIntentExtra.getTopicName();
            tv_topic_name.setText(getString(R.string.community_do_post_topic_name, topicName));
            mRlChooseCommunityZone.setVisibility(View.GONE);
            showDraft();
        } else if (mOpenFrom == OPEN_FROM_CAR_CLUB) {
            setLeftTitle(getString(R.string.title_post_type_image_text));
            tv_post_by_computer.setVisibility(View.VISIBLE);
            tv_post_by_computer.getPaint().setUnderlineText(true);
            showDraft();
        } else if (mOpenFrom == OPEN_FROM_GOODS) {//设置发布好货时候的toolbar显示样式
            toolbar_title_left.setText(getString(R.string.tv_publish_goods_back));
            toolbar_title_left.setVisibility(View.VISIBLE);
            toolbar_title_left.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    finish();
                }
            });
            mToolbar.setNavigationIcon(null);
            setLeftTitle(getString(R.string.title_publish_goods_title));
            et_post_title.setHint(getString(R.string.tv_publish_goods_post_title));
            tv_post_by_computer.setVisibility(View.VISIBLE);
            tv_post_by_computer.getPaint().setUnderlineText(true);
            showImageTextPostTitle();
            showDraft();
        } else {//否则，是普通图文帖
            setLeftTitle(getString(R.string.title_post_type_image_text));
            mLlNotifyUser.setVisibility(View.VISIBLE);
            //需求上只写了在“发图文页面”显示去电脑发帖
            tv_post_by_computer.setVisibility(View.VISIBLE);
            tv_post_by_computer.getPaint().setUnderlineText(true);
            showImageTextPostTitle();
            showDraft();
        }
        showLocation();

        //是否显示社区
        if (mPostType == PostCard.PostType.ASK_TECHNICIAN
                || mPostType == PostCard.PostType.ASK_ENGINEER //添加工程师判断
                || mPostType == PostCard.PostType.ASK_SOPHISTICATED_DRIVER
                || mPostType == PostCard.PostType.EDIT_POST_CARD
                || mPostType == PostCard.PostType.EDIT_TOPIC
                || mPostType == PostCard.PostType.CAR_BUYING_EXPERIENCE
                || mPostType == PostCard.PostType.EDIT_CAR_BUYING_POST_CARD
                || mPostType == PostCard.PostType.IMAGE_TEXT
                || mPostType == PostCard.PostType.SHARE_GAME
                || mPostType == PostCard.PostType.SIGN_IN
                || mPostType == PostCard.PostType.TOPIC
                || !TextUtils.isEmpty(mIntentExtra.getChannelId())) {
            //如果是我问技师，或者是问老司机，或者是编辑帖子，或者是（编辑）提车作业帖，或者有id，则不可见
            tv_select_community.setVisibility(View.GONE);
        } else {
            tv_select_community.setVisibility(View.VISIBLE);
        }

        setLabelViews();
    }

    /**
     * 初始化悬赏的view
     */
    private void initRewardViews() {
        //置于右侧
//        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) tv_select_community.getLayoutParams();
//        layoutParams.gravity = Gravity.RIGHT;
//        tv_select_community.setLayoutParams(layoutParams);
    }

    /**
     * 进入页面之后初始化第一个文本框
     */
    private void addFirstText() {
        if (mPostType == PostCard.PostType.ASK_ENGINEER || mPostType == PostCard.PostType.ASK_TECHNICIAN || mPostType == PostCard.PostType.ASK_SOPHISTICATED_DRIVER) {
            //我问技师，显示选择
            View choiceView = LayoutInflater.from(this).inflate(R.layout.layout_post_ask_technician_choice, ll_content, true);
            mItemCarType = choiceView.findViewById(R.id.item_cart_type);
            mItemCarType.setOnClickListener(this);
            mItemQuestionType = choiceView.findViewById(R.id.item_question_type);
            mItemQuestionType.setOnClickListener(this);
            mItemQuestionType.getTvRight().setMaxLines(Integer.MAX_VALUE);

            mItemDrivingHabit = choiceView.findViewById(R.id.item_driving_habit);
            mItemDrivingHabit.setOnClickListener(this);
            mItemMileage = choiceView.findViewById(R.id.item_enter_mileage);
            mItemMileage.setInputType(InputType.TYPE_CLASS_NUMBER);

            //设置右边文本颜色
            mItemMileage.getTvRight().setTextColor(getResources().getColor(R.color.color_fea000));
            mItemCarType.getTvRight().setTextColor(getResources().getColor(R.color.color_fea000));
            mItemQuestionType.getTvRight().setTextColor(getResources().getColor(R.color.color_fea000));
            mItemDrivingHabit.getTvRight().setTextColor(getResources().getColor(R.color.color_fea000));
            //设置行驶里程最多输入6位数
            final int maxLength = 6;
            mItemMileage.getTvRight().setFilters(new InputFilter[]{new InputLengthLimitFilter(maxLength, true, getResources().getString(R.string.toast_input_mileage_tip, maxLength))});
            if (mPostType == PostCard.PostType.ASK_SOPHISTICATED_DRIVER) {
                //问老司机，只显示类型
                mItemCarType.setVisibility(View.GONE);
                mItemMileage.setVisibility(View.GONE);
                mItemDrivingHabit.setVisibility(View.GONE);
            } else {
                //如果是问技师或者工程师，请求默认车型
                requestDefaultCarType();
            }
        }
        TextAndImageView textAndImageView = TextAndImageView.newInstance(this, mIntentExtra.getPostType());
        PostCardItem postTest = new PostCardItem();
        textAndImageView.setTag(tag);
        textAndImageView.setObject(postTest);
        if (mPostType == PostCard.PostType.ASK_ENGINEER || mPostType == PostCard.PostType.ASK_TECHNICIAN) {
            textAndImageView.getEt_text().setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimensionPixelSize(R.dimen.activity_set_text_24));
        }
        ll_content.addView(textAndImageView);
        tag++;
        setTextAndImageListener(textAndImageView);//监听文本框的变化
    }

    /**
     * 请求车型信息
     */
    private void requestDefaultCarType() {
        L00bangRequestManager2.getServiceInstance()
                .getDefaultUserFavoriteCarAndCount()
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<MyDefaultCar>(this) {
                    @Override
                    public void onSuccess(MyDefaultCar myDefaultCar) {
                        super.onSuccess(myDefaultCar);
                        if (myDefaultCar != null && myDefaultCar.getDefaultCar() != null) {
                            mChosenCarType = new CarType((long) myDefaultCar.getDefaultCar().getCarTypeId(), myDefaultCar.getDefaultCar().getFullName());
                            if (mChosenCarType != null) {
                                mItemCarType.getTvLeft().setText(mChosenCarType.getFullName());
                            }
                        }
                    }
                });

    }

    /**
     * 添加图文视图
     *
     * @param mSelectedList 选择的图片
     */
    private void addImageAndTextView(List<String> mSelectedList) {
        String startText = "";
        String endText = "";
        if (editText != null) {
            //如果是在文字的中间
            String content = editText.getText().toString();
            startText = content.substring(0, editText.getSelectionStart());
            endText = content.substring(editText.getSelectionStart());
        }
        for (int g = 0; g < mSelectedList.size(); g++) {
            final TextAndImageView textAndImageView = TextAndImageView.newInstance(this, mIntentExtra.getPostType());
            PostCardItem postCardItem = new PostCardItem();
            postCardItem.setPath(mSelectedList.get(g));
            textAndImageView.setTag(tag);
            textAndImageView.setObject(postCardItem);
            tag++;
            setTextAndImageListener(textAndImageView);
//            if (editText != null && !editText.getText().toString().equals("")) {//如果有文本
            for (int i = 0; i < ll_content.getChildCount(); i++) {
                View child = ll_content.getChildAt(i);
                if (child instanceof TextAndImageView) {
                    int tag = (int) child.getTag();
                    if (tag == currentTag) {
                        PostCardItem item = ((TextAndImageView) ll_content.getChildAt(i)).getObject();
                        if (g == 0) {
                            //将截取的第一个值赋值给上一个（只有第一次的时候需要添加）
                            item.setText(startText);
                            ((TextAndImageView) ll_content.getChildAt(i)).refreshData();
                        }
                        if (g == mSelectedList.size() - 1) {
                            postCardItem.setText(endText);
                            //计算剩余字数，否则不准确
                            setResidueCount(textAndImageView);
                            textAndImageView.refreshData();
                            textAndImageView.requestTextFocus();
                        }
                        if (i >= ll_content.getChildCount()) {//防止图片删除，数组越界
                            ll_content.addView(textAndImageView, i);
                            //把currentTag设置成当前
                            currentTag = (int) (ll_content.getChildAt(i)).getTag();
                        } else {
                            ll_content.addView(textAndImageView, i + 1);
                            //把currentTag设置成当前
                            currentTag = (int) (ll_content.getChildAt(i + 1)).getTag();
                        }

                        break;
                    }
                }
            }

         /*   } else {//如果没有文本
                ll_content.addView(textAndImageView);
                textAndImageView.requestFocus();
            }*/
        }
    }

    /**
     * 添加上一次保存的或者发送出去的图文视图
     *
     * @param postCardItems 上次保存过的图文视图列表
     */
    private void addSavedImageAndTextView(List<PostCardItem> postCardItems) {
        //处理第一个文本框
        //如果是第一个，并且从数据库还原或者从编辑帖子进入的,第一个图片存在，则添加一个空的文本框，否则第一个图片上将无法插入视图
        if (tag == 0) {
            if (mPostType != PostCard.PostType.EDIT_POST_CARD
                    && mPostType != PostCard.PostType.EDIT_TOPIC
                    && mPostType != PostCard.PostType.EDIT_CAR_BUYING_POST_CARD
                    && !TextUtils.isEmpty(postCardItems.get(0).getPath())
                    && isExistFIle(postCardItems.get(0).getPath())) {
                addFirstText();
            } else if ((mPostType == PostCard.PostType.EDIT_POST_CARD
                    || mPostType == PostCard.PostType.EDIT_TOPIC
                    || mPostType == PostCard.PostType.EDIT_CAR_BUYING_POST_CARD)
                    && !TextUtils.isEmpty(postCardItems.get(0).getImg())
                    && postCardItems.get(0).getImg().startsWith("http")) {
                addFirstText();
            } else {
                //如果没有图片，但是第一个文本是空，也会添加一个空的文本框
                if (TextUtils.isEmpty(postCardItems.get(0).getText())) {
                    addFirstText();
                }
            }
        }

        //第一个文本框处理添加完毕，循环数据库中的数据，添加之前储存的图文视图
        for (int i = 0; i < postCardItems.size(); i++) {
            //初始化图片数量，如果含有图片，并且图片存在，则图片个数+1
            if (mPostType != PostCard.PostType.EDIT_POST_CARD
                    && mPostType != PostCard.PostType.EDIT_TOPIC
                    && mPostType != PostCard.PostType.EDIT_CAR_BUYING_POST_CARD
                    && !TextUtils.isEmpty(postCardItems.get(i).getPath())
                    && isExistFIle(postCardItems.get(i).getPath())) {
                totalPicCount++;
            } else if ((mPostType == PostCard.PostType.EDIT_POST_CARD
                    || mPostType == PostCard.PostType.EDIT_TOPIC
                    || mPostType == PostCard.PostType.EDIT_CAR_BUYING_POST_CARD)
                    && !TextUtils.isEmpty(postCardItems.get(i).getImg())
                    && postCardItems.get(i).getImg().startsWith("http")) {
                totalPicCount++;
            }
            //否则，则判断是不是有文字,如果也没有文字，则不添加
            else {
                if (TextUtils.isEmpty(postCardItems.get(i).getText())) {
                    //如果文字也为空（必须是非第一个视图，因为第一个视图是没有图片的固定视图），则不添加该视图，结束本次循环
                    continue;
                }
            }
            final TextAndImageView textAndImageView = TextAndImageView.newInstance(this, mPostType);
            textAndImageView.setTag(tag);
            textAndImageView.setObject(postCardItems.get(i));
            setTextAndImageListener(textAndImageView);
            ll_content.addView(textAndImageView);
            //把currentTag设置成当前
//            currentTag = tag;
            if (i == postCardItems.size() - 1) {
                textAndImageView.requestTextFocus();
            }
            tag++;
        }
    }

    /**
     * 监听文本框的变化
     *
     * @param textAndImageView the view
     */
    private void setTextAndImageListener(final TextAndImageView textAndImageView) {
        textAndImageView.setOnListener(new TextAndImageView.OnListener() {
            //当每个文本框获得焦点时
            @Override
            public void OnGetFocus(final EditText editText) {
                setResidueCount(textAndImageView);
                CommunityDoPostImageTextActivity.this.editText = editText;
                currentTag = Integer.parseInt(textAndImageView.getTag().toString());
                //如果不是我问技师并且不是从草稿进入，则保存一次到数据库
                if (needSaveDraft()) {
                    saveDraft();
                }
                //获得焦点的时候自动弹出键盘
                editText.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        InputMethodManager inputManager = (InputMethodManager) editText.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
                        if (inputManager != null) {
                            inputManager.showSoftInput(editText, 0);
                        }
                    }
                }, 200);

//                ToastUtil.showMessage(CommunityDoPostImageTextActivity.this, textAndImageView.getTag() + "----" + editText.getText().toString());
            }

            //需要移除这个view的时候
            @Override
            public void deleteView(View view) {
                //循环，让上一个文本框获得光标
                int deleteTag = Integer.parseInt(textAndImageView.getTag().toString());
                for (int i = 0; i < ll_content.getChildCount(); i++) {
                    View child = ll_content.getChildAt(i);
                    if (child.getTag() != null) {
                        int tag = Integer.parseInt(child.getTag().toString());
                        if (child instanceof TextAndImageView && tag == deleteTag && ll_content.getChildAt(i - 1) != null) {
                            ((TextAndImageView) ll_content.getChildAt(i - 1)).requestTextFocus();
                        }
                    }
                }
                ll_content.removeView(view);
            }

            //失去焦点时增加总字数
            @Override
            public void addTextCount(int count) {

            }

            //删除图片
            @Override
            public void deletePicList(String path) {
                totalPicCount--;
            }

            /**
             * 点击图片描述时
             */
            @Override
            public void onPictureDescriptionGetFocus(EditText editText) {
                //设置表情键盘属于这个文本框
                if (expression_view_pager != null) {
                    expression_view_pager.setVisibility(View.GONE);
                    expression_view_pager.setEditText(textAndImageView.getEt_expression_text());
                }
            }
        });
    }

    protected void setResidueCount(TextAndImageView textAndImageView) {
        totalTextCount = 0;
        totalTextCount = getTotalCount();
        totalOutCurrentText = totalTextCount - textAndImageView.getEt_text().getText().toString().length();
        int residueCount = ConfigConstant.MAX_TEXT_COUNT - (totalOutCurrentText);//这个文本框剩余的字数
        //设置表情键盘属于这个文本框
        if (expression_view_pager != null) {
            expression_view_pager.setVisibility(View.GONE);
            expression_view_pager.setEditText(textAndImageView.getEt_text(), residueCount);
        }
        textAndImageView.setResidueCount(residueCount);
    }

    /**
     * 统计总字数
     *
     * @return 字数
     */
    public int getTotalCount() {
        int count = 0;
        for (int i = 0; i < ll_content.getChildCount(); i++) {
            View child = ll_content.getChildAt(i);
            if (child instanceof TextAndImageView) {
                PostCardItem postCardItem = ((TextAndImageView) ll_content.getChildAt(i)).getObject();
                if (!TextUtils.isEmpty(postCardItem.getText())) {
                    String text = postCardItem.getText();
                    if (text != null) {
                        count += text.trim().length();
                    }
                }
            }
        }
        return count;
    }

    @Override
    public boolean onPrepareOptionsMenu(Menu menu) {
        menu.clear();
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(getMenu_publish(), menu);
        MenuItem item = menu.findItem(R.id.next_step);
        if (mOpenFrom == OPEN_FROM_LOCAL || mOpenFrom == OPEN_FROM_SQUARE) {
            item.setTitle(getString(R.string.car_buying_experience_next_step));
        }
        mToolbar.setOnMenuItemClickListener(onMenuItemClick);
        return true;
    }

    protected int getMenu_publish() {
        return R.menu.menu_publish;
    }

    /**
     * 增加Toolbar监听
     */
    protected Toolbar.OnMenuItemClickListener onMenuItemClick = new Toolbar.OnMenuItemClickListener() {
        @Override
        public boolean onMenuItemClick(MenuItem menuItem) {
            switch (menuItem.getItemId()) {
                //点击下一步
                case R.id.next_step:
                    onClickMenu();
                    break;
                //点击存草稿
                case R.id.item_draft:
                    onClickSaveDraft();

            }
            return true;
        }
    };

    protected void onClickSaveDraft() {
        //保存草稿操作，目前只有灵感贴支持，故此处不实现，字灵感贴中实现该方法
    }

    /**
     * 点击右上角提交或者下一步的操作
     */
    protected void onClickMenu() {
        if (!AppUtil.checkLogin(this)) {
            return;
        }
        //获取channelId，埋点使用，注意可能为null
        String channelId = mIntentExtra.getChannelId();
        if (null == channelId) {
            channelId = "";
        }
        if (mOpenFrom == OPEN_FROM_SQUARE || mOpenFrom == OPEN_FROM_LOCAL) {
            if (checkCommitInput()) {
                Intent intent = new Intent(CommunityDoPostImageTextActivity.this, CommunityDoPostChooseColumnActivity.class);
                intent.putExtra(CommunityDoPostChooseColumnActivity.KEY_OPEN_FROM, mOpenFrom);
                intent.putExtra(CommunityDoPostChooseColumnActivity.KEY_POST_TYPE_ID, mPostType);
                if (mOpenFrom == OPEN_FROM_LOCAL) {
                    intent.putExtra(CommunityDoPostChooseColumnActivity.KEY_CHANNEL_ID, mIntentExtra.getChannelId());
                }
                startActivityForResult(intent, REQUEST_CODE_CHOOSE_LOCAL_COLUMN);

//                SensorsChannelUtils.sensorsClickPostPublish(mPostType, User.getsUserInstance().getUserIdStr(), channelId, SensorsChannelUtils.PostEventType.EVENT_TYPE_NEXT_STEP, mLocalColumnId);
            }
        } else if (mOpenFrom == OPEN_FROM_GOODS) {//好货发帖
            if (mIntentExtra.getPostType() == PostCard.PostType.EDIT_POST_CARD) {
                //如果是编辑帖子
                mGoodsCoverImagePath = mIntentExtra.getCoverImg();
                if (TextUtils.isEmpty(mGoodsCoverImagePath)) {
                    //如果没有封面，说明没修改封面
                    doPost();
                } else {//有封面的情况还要上传封面
                    ArrayList<String> preCompress = new ArrayList<>();
                    preCompress.add(mGoodsCoverImagePath);
                    compressPic(preCompress);
                }

            } else {
                if (checkCommitInput()) {
                    mGoodsCoverImagePath = mIntentExtra.getCoverImg();
                    if (TextUtils.isEmpty(mGoodsCoverImagePath)) {
                        return;
                    }
                    ArrayList<String> preCompress = new ArrayList<>();
                    preCompress.add(mGoodsCoverImagePath);
                    compressPic(preCompress);
                }
            }

        } else {
            doPost();
            //点击发布的埋点
//            SensorsChannelUtils.sensorsClickPostPublish(mPostType, User.getsUserInstance().getUserIdStr(), channelId, SensorsChannelUtils.PostEventType.EVENT_TYPE_RELEASE, mLocalColumnId);
            //SensorsChannelUtils.sensorsClickPostPublish(mPostType, User.getsUserInstance().getUserIdStr(), mIntentExtra.getGroupInfo(), mIntentExtra.getChannelId(), SensorsChannelUtils.PostEventType.EVENT_TYPE_NEXT_STEP,"点击发布"+(mOpenFrom == OPEN_FROM_GOODS?"好货":"图文"));
            SensorsUtils.sensorsClickBtn("点击发布" + (mOpenFrom == OPEN_FROM_GOODS ? "好货" : "长文"), "发布", "发布");
        }
    }

    /**
     * 点击发布开始发帖
     */
    protected void doPost() {
        if (!isCommit) {
            if (checkCommitInput()) {
                //检查网络是否可用
                if (NetworkUtil.isNetworkAvailable(CommunityDoPostImageTextActivity.this)) {
                    isCommit = true;
                    dealImageAndText();//处理图文混排中的图片和文本
                } else {
                    ToastUtil.showMessage(CommunityDoPostImageTextActivity.this, getString(R.string.common_network_unavailable));
                }
            }
        } else {
            ToastUtil.showMessage(CommunityDoPostImageTextActivity.this, getString(R.string.community_do_post_toast_is_committing));
        }
    }

    /**
     * 插入web游戏分享图片
     */
    private void insertWebImageForPath() {
        String imgPath = mIntentExtra.getWebResPath();
        if (!TextUtils.isEmpty(imgPath)) {
            //插入图片
            TextAndImageView textAndImageView = TextAndImageView.newInstance(this, mPostType);
            PostCardItem postCardItem = new PostCardItem();
            postCardItem.setPath(imgPath);
            if (mIntentExtra.getLinkType() >= 0) {
                postCardItem.setLinkType(mIntentExtra.getLinkType());
                if (!TextUtils.isEmpty(mIntentExtra.getLinkUrl())) {
                    postCardItem.setLinkUrl(mIntentExtra.getLinkUrl());
                }
            }
            textAndImageView.setTag(tag);
            textAndImageView.setObject(postCardItem);
            ll_content.addView(textAndImageView);
            //图片存在则 +1
            if (FileUtil.isHaveFile(imgPath)) {
                totalPicCount++;
            }
            tag++;
            setTextAndImageListener(textAndImageView);//监听文本框的变化
            textAndImageView.requestTextFocus();
            LogUtils.d("--------小游戏图片分享-------->");
        }
    }

    //不传postId（适用于发帖子）
    public static void startActivity(Context context, int postType, String communityId, String communityName, ContentGroupInfo contentGroupInfo, int jumpAfterSuccess, int openFrom) {
        IntentExtra intentExtra = new IntentExtra(postType, communityId, null);
        intentExtra.setJumpAfterSuccess(jumpAfterSuccess);
        intentExtra.setChannelName(communityName);
        intentExtra.setGroupInfo(contentGroupInfo);
        intentExtra.setOpenFrom(openFrom);
        IntentUtils.startActivity(context, CommunityDoPostImageTextActivity.class, intentExtra);
    }

    /**
     * 检查输入文字限制
     */
    private boolean checkTotalWordsCount() {
        int leastCount = -1;
        //根据帖子类型设置最小字数
        if (mPostType == PostCard.PostType.IMAGE_TEXT || mPostType == PostCard.PostType.EDIT_POST_CARD || mPostType == PostCard.PostType.SHARE_GAME) {
            leastCount = LeastWords.IMAGE_TEXT;
        } else if (mPostType == PostCard.PostType.ASK_TECHNICIAN || mPostType == PostCard.PostType.ASK_ENGINEER) {//添加我问工程师
            leastCount = LeastWords.TEC;
        } else if (mPostType == PostCard.PostType.CAR_BUYING_EXPERIENCE
                || mPostType == PostCard.PostType.EDIT_CAR_BUYING_POST_CARD) {
            leastCount = LeastWords.CAR_BUYING;
        } else if (mPostType == PostCard.PostType.SIGN_IN) {
            leastCount = LeastWords.SIGNED;
        } else if (mPostType == PostCard.PostType.TOPIC || mPostType == PostCard.PostType.EDIT_TOPIC) {
            leastCount = LeastWords.TOPIC;
        }
        if (leastCount > 0 && getTotalCount() < leastCount) {
            ToastUtil.showMessage(this, getString(R.string.car_buying_do_post_input_more_word_toast, leastCount));
            return false;
        }
        return true;
    }

    /**
     * 点击返回时检查是否有内容
     *
     * @return 是否有内容
     */
    protected boolean checkBackInput() {
        if (mPostType == PostCard.PostType.IMAGE_TEXT || mPostType == PostCard.PostType.SHARE_GAME) {
            if (!TextUtils.isEmpty(et_post_title.getText())) {
                return true;
            }
        }

        if (totalPicCount != 0 || totalTextCount != 0) {
            //字数或者图片不为零
            return true;
        } else {
            //如果字数和图片都为0，则判断当前editText是否为零（当前焦点没离开，totalTextCount可能为0）
            return editText != null && editText.getText() != null && !TextUtils.isEmpty(editText.getText().toString());
        }
    }

    /**
     * 保存草稿
     */
    private void saveDraft() {
        handler.removeCallbacks(runnable);
        handler.postDelayed(runnable, 0);
    }

    /**
     * 保存未发完的帖子到数据库，以便下次继续编辑
     */
    protected void saveImageAndText() {
        if (mPostType == PostCard.PostType.IMAGE_TEXT) {
            String postTitle = et_post_title.getText().toString();
            PreferenceUtil.putPreference(CommunityDoPostImageTextActivity.this, KEY_IMAGE_TEXT_TITLE, postTitle);
            if (poiInfo != null && !TextUtils.isEmpty(postTitle)) {
                PreferenceUtil.putPreference(CommunityDoPostImageTextActivity.this, postTitle, new Gson().toJson(poiInfo));
            }
        }
        new Thread(new Runnable() {
            @Override
            public void run() {
                itemList = new ArrayList<>();
                for (int i = 0; i < ll_content.getChildCount(); i++) {
                    View child = ll_content.getChildAt(i);
                    if (child instanceof TextAndImageView) {
                        PostCardItem postCardItem = ((TextAndImageView) ll_content.getChildAt(i)).getObject();
                        if (!TextUtils.isEmpty(postCardItem.getPath()) || !TextUtils.isEmpty(postCardItem.getText())) {
                            postCardItem.setType(mPostType);
                            postCardItem.setIndex(i);
                            itemList.add(postCardItem);//统计所有的item，并添加到集合
                        }
                    }
                }
                if (itemList.size() > 0) {
                    //有id重复等问题，加synchronized尝试
                    synchronized (CommunityDoPostImageTextActivity.class) {
                        //先删除所有的同类型帖子，再添加
                        deleteByType(mPostType);
                        mPostCardItemDao.insertInTx(itemList);
                    }
                }
            }
        }).start();
    }

    @Override
    protected void onBack() {
        finish();
    }

    protected void deleteByType(int type) {
        //该方法不删除缓存，可能有问题，换用下面的尝试
//        mPostCardItemDao.queryBuilder()
//                .where(PostCardItemDao.Properties.Type.eq(type))
//                .buildDelete()
//                .executeDeleteWithoutDetachingEntities();
        mPostCardItemDao.deleteInTx(
                mPostCardItemDao.queryBuilder()
                        .where(PostCardItemDao.Properties.Type.eq(type))
                        .list()
        );
    }

    /**
     * 遍历图文混排的自定义view，得到list
     */
    private void dealImageAndText() {
        boolean isDelete = false;//是否有删除的图片
        ArrayList<String> preCompress = new ArrayList<>();
        itemList = new ArrayList<>();
        for (int i = 0; i < ll_content.getChildCount(); i++) {
            View child = ll_content.getChildAt(i);
            if (child instanceof TextAndImageView) {
                PostCardItem postCardItem = ((TextAndImageView) ll_content.getChildAt(i)).getObject();
                if (postCardItem.getPath() != null && postCardItem.getImg() == null) {//添加地址到集合，供下一步解压
                    if (isExistFIle(postCardItem.getPath())) {//如果图片存在，则添加到预处理文件中
                        preCompress.add(postCardItem.getPath());
                    } else {//如果提交之前有的照片被从图库删除
                        String text = ((TextAndImageView) ll_content.getChildAt(i)).getObject().getText();
                        if (text == null || "".equals(text)) {//如果删除该列的文字也为空，则移除该子view
                            ll_content.removeView(child);
                            i--;
                        } else {//如果文字不为空，则只删除该张图片
                            ((TextAndImageView) ll_content.getChildAt(i)).getObject().setPath(null);
                            ((TextAndImageView) ll_content.getChildAt(i)).refreshData();
                        }
                        totalPicCount--;
                        isDelete = true;
                    }
                }
                itemList.add(postCardItem);//统计所有的item，并添加到集合
            }
        }
        if (isDelete) {//如果有删除的图片，弹出确认框
            Dialog dialog = new CommonAlertDialog(this, R.string.community_do_post_dialog_delete_image_post_confirm, R.string.community_do_post_dialog_delete_image_post_yes, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    dealImageAndText();//再次处理图片，直到没有图片被删除
                }
            });
            dialog.show();
            isCommit = false;
        } else {
            if (preCompress.size() > 0) {//如果有要处理的图片，则处理图片
                compressPic(preCompress);
            } else {
                //如果没有要处理的图片，直接提交
                PostCard postCard = null;
                if (mPostType == PostCard.PostType.CAR_BUYING_EXPERIENCE
                        || mPostType == PostCard.PostType.EDIT_CAR_BUYING_POST_CARD) {
                    postCard = mIntentExtra.getPostCard();//提车作业贴使用上一个页面传递的postcard对象
                }
                if (postCard == null) {
                    postCard = new PostCard();
                }
                postCard.setImgTexts(itemList);
                commit(postCard);
            }
        }
    }

    //压缩图片
    protected void compressPic(final ArrayList<String> picList) {
        CompressImageHelper.compress(this, picList, new CompressListener() {
            @Override
            public void onCompressSuccess(List<CompressResult> compressResultList) {
                //图片压缩成功，上传照片
                List<ImageModel> imageModelList = new ArrayList<>();
                for (CompressResult result : compressResultList) {
                    ImageModel imageModel = new ImageModel(result.getPath(), result.getWidth(), result.getHeight());
                    imageModelList.add(imageModel);
                }
                doUploadPic(imageModelList);
            }

            @Override
            public void onCompressFail(Throwable e) {
                //图片压缩失败
                ToastUtil.showMessage(CommunityDoPostImageTextActivity.this, getString(R.string.community_do_post_toast_compress_image_fail));
                isCommit = false;
            }
        });
    }

    /**
     * 上传图片
     */
    private void doUploadPic(final List<ImageModel> imageModels) {
        //获得带有path的list
        List<String> imageList = new ArrayList<>();
        if (imageModels != null) {
            for (ImageModel imageModel : imageModels) {
                imageList.add(imageModel.getPath());
            }
        }
        UpLoadUtil.doUploadV2(this, imageList, new UpLoadUtil.OnListener() {
            @Override
            public void onLoadingSuccess(ArrayList<String> ImageList) {
                onUploadPicSuccess(ImageList, imageModels);
            }

            @Override
            public void onLoadingFailure(Throwable e, String s) {
                String error;
                if (s.contains("timed out") || s.contains("SocketTimeoutException")) {
                    error = getString(R.string.community_do_post_toast_upload_image_fail_because_of_network_unavailable);
                } else {
                    LogUploadUtils.saveNormalLog(ConfigConstant.LOG_TYPE_NETWORK, ConfigConstant.LOG_LEVEL_ERROR, "上传图片失败", s);
                    error = getString(R.string.community_do_post_toast_upload_image_fail);
                }
                ToastUtil.showMessage(CommunityDoPostImageTextActivity.this, error);
                isCommit = false;
            }

            @Override
            public void onBackPress() {
                isCommit = false;
                InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
                if (imm != null) {
                    imm.toggleSoftInput(0, InputMethodManager.HIDE_NOT_ALWAYS);
                }
                if (expression_view_pager != null){
                    expression_view_pager.setVisibility(View.GONE);
                }
            }
        });
    }

    /**
     * 设置标签、城市、车型、个人名片等
     */
    protected void setLabelViews() {
        if (mOpenFrom == OPEN_FROM_PEOPLE_CAR_LIFT || mOpenFrom == OPEN_FROM_CAR_TYPE) {
            //显示选择标签
            if (mOpenFrom == OPEN_FROM_CAR_TYPE) {
                if (mTvCity != null) {
                    mTvCity.setVisibility(View.GONE);
                    //                if (!TextUtils.isEmpty(mIntentExtra.getChannelName()) && !TextUtils.isEmpty(mIntentExtra.getChannelId())) {
//                    mTvCity.setText(mIntentExtra.getChannelName());
//                }

                }
                if (mTvLabel != null) {
                    mTvLabel.setTitle(getResources().getString(R.string.car_club_list_choose_car_type));
                }
            } /*else if (mOpenFrom == OPEN_FROM_GOODS) {
                //是否显示个人名片
                mLlPersonalCard.setVisibility(View.VISIBLE);
            }*/
            if (mTvLabel != null) {
                mTvLabel.setVisibility(View.VISIBLE);
                ContentGroupInfo contentGroupInfo = mIntentExtra.getGroupInfo();
                if (contentGroupInfo != null && !TextUtils.isEmpty(contentGroupInfo.getContentGroupName())) {
                    mTvLabel.getTvLeft().setText(contentGroupInfo.getContentGroupName());
                }
            }
        }
    }

    /**
     * 好货互通、车型发帖，检查标签、车型、城市是否填写
     */
    protected boolean checkCommitMessage() {
        if (mOpenFrom == OPEN_FROM_PEOPLE_CAR_LIFT) {
            //人车生活
            String label = mTvLabel.getTvLeft().getText().toString();
            if (mIntentExtra.getGroupInfo() == null || mIntentExtra.getGroupInfo().getContentGroupIdOrZero() == 0 || TextUtils.isEmpty(label) || getString(R.string.community_do_post_btn_select_label).equals(label)) {
                ToastUtil.showMessage(CommunityDoPostImageTextActivity.this,
                        R.string.community_do_post_btn_select_label);
                return false;
            }

//            if (mOpenFrom == OPEN_FROM_GOODS) {
//                //好货互通 - 个人名片是否是必须（待定）
//
//                //检查封面图是否已上传
//                return true;
//            }

        } else if (mOpenFrom == OPEN_FROM_CAR_TYPE) {
//            车型改为非必选
//            String label = mTvLabel.getText().toString();
//            if (TextUtils.isEmpty(label) || getString(R.string.community_do_post_btn_select_car_type).equals(label) || mIntentExtra.getGroupInfo() == null) {
//                ToastUtil.showMessage(CommunityDoPostImageTextActivity.this,
//                        R.string.community_do_post_item_choose_car_type_left);
//                return false;
//            }
//            去除城市社区的选择
//            String city = mTvCity.getText().toString();
//            if (TextUtils.isEmpty(city) || getString(R.string.community_do_post_btn_select_label_city).equals(city) || TextUtils.isEmpty(mIntentExtra.getChannelId()) || TextUtils.isEmpty(mIntentExtra.getChannelName())) {
//                ToastUtil.showMessage(CommunityDoPostImageTextActivity.this,
//                        R.string.community_do_post_btn_select_city_type);
//                return false;
//            }
        }
        return true;
    }

    /**
     * 提交问题
     */
    protected void commit(final PostCard postCard) {
        if (poiInfo != null) {
            postCard.setLocationName(poiInfo.getName());
            postCard.setLocationDetail(poiInfo.getAddress());
            postCard.setLatitude(poiInfo.location.latitude);
            postCard.setLongitude(poiInfo.location.longitude);
        }
        if (mIntentExtra != null) {
            postCard.setChannelId(mIntentExtra.getChannelId());
        }
        if (mPostType == PostCard.PostType.SHARE_GAME) {
            postCard.setPostTypeId(String.valueOf(PostCard.PostType.IMAGE_TEXT));
        } else {
            postCard.setPostTypeId(String.valueOf(mPostType));
        }
        //@用户
        if (mNotifyUserList != null && !mNotifyUserList.isEmpty()) {
            List<String> list = new ArrayList<>(mNotifyUserList.size());
            for (User user : mNotifyUserList) {
                list.add(user.getUserIdStr());
            }
            postCard.setMentionUsers(list);
        }
        if (mPostType == PostCard.PostType.ASK_TECHNICIAN || mPostType == PostCard.PostType.ASK_ENGINEER) {
            //我问技师或者我问工程师
//            int checkId = radio_group_reward.getCheckedRadioButtonId();
//            if (checkId != -1) {
//                int rewardTypeId = mRewardTypes[checkId].getTypeId();
//                postCard.setRewardId(String.valueOf(rewardTypeId));
//            }
            //问工程师类型和问技师一样
            postCard.setPostTypeId(String.valueOf(PostCard.PostType.ASK_TECHNICIAN));
            //车型
            if (mChosenCarType != null) {
                postCard.setCarTypeId(String.valueOf(mChosenCarType.getCarTypeId()));
            }
            //问题标签
            if (!TextUtils.isEmpty(mChosenQuestionIds)) {
//                提问技师或工程师选择问题类型id（如果为多个，中间用“，”分隔。
//                例如：“1，2，3，4”，注意问题级别顺序）
                postCard.setPostLabelIds(mChosenQuestionIds);
            }
            //驾驶风格
//            if (mDriverHabitType != null) {
//                postCard.setDrivingHabit(Long.valueOf(mDriverHabitType.getHabitId()).intValue());
//            }
            //行驶里程
            if (!TextUtils.isEmpty(mChosenMileage)) {
                postCard.setMileage(Integer.valueOf(mChosenMileage));
            }

//            techType 技师类型，1技师2工程师（默认为1）
            if (mPostType == PostCard.PostType.ASK_ENGINEER) {
                postCard.setTechType(2);
            } else {
                postCard.setTechType(1);
            }

        } else if (mPostType == PostCard.PostType.ASK_SOPHISTICATED_DRIVER) {
            //问题标签
            if (mChosenQuestionType != null) {
                postCard.setPostLabelIds(String.valueOf(mChosenQuestionType.getPostLabelId()));
            }
        }
        //如果不是编辑帖子
        if (mPostType != PostCard.PostType.EDIT_LING_SENSE && mPostType != PostCard.PostType.EDIT_POST_CARD && mPostType != PostCard.PostType.EDIT_TOPIC && mPostType != PostCard.PostType.EDIT_CAR_BUYING_POST_CARD) {
            //发帖操作
            if (mOpenFrom == OPEN_FROM_SQUARE || mOpenFrom == OPEN_FROM_PEOPLE_CAR_LIFT || mOpenFrom == OPEN_FROM_LOCAL || mOpenFrom == OPEN_FROM_GOODS || mOpenFrom == OPEN_FROM_CAR_TYPE || mPostType == PostCard.PostType.TOPIC) {
                if (mPostType == PostCard.PostType.IMAGE_TEXT || mPostType == PostCard.PostType.SHARE_GAME) {
                    postCard.setPostTitle(et_post_title.getText().toString());
                }
                if (mPostType == PostCard.PostType.TOPIC) {
                    postCard.setTopicId(mIntentExtra.getTopicId());
                }
                if (mOpenFrom == OPEN_FROM_SQUARE || mOpenFrom == OPEN_FROM_LOCAL) {
                    postCard.setLocalColumnId(mLocalColumnId);
                }
                if (mPostType == PostCard.PostType.LING_SENSE) {
                    postCard.setPostText(postContent);
                    postCard.setPostTitle(postTitle);
                }
                setPostCardParam(postCard);
                L00bangRequestManager2
                        .setSchedulers(L00bangRequestManager2.getServiceInstance().pubPostV3(postCard))
                        .subscribe(new ProgressSubscriber<PostCard>(CommunityDoPostImageTextActivity.this) {
                            @Override
                            public void onSuccess(PostCard postCard1) {
                                super.onSuccess(postCard);
                                isCommit = false;
                                PostCardManager.getInstance().getUploadImgs().clear();

                                //好货互通发帖，帖子发布成功后，弹窗确认是否需要置顶
                                /*if (showGoodsStickyDialog(postCard1)) {
                                    return;
                                }*/
                                if (needSaveDraft()) {
                                    //删除保存的帖子记录
                                    deleteCachePost();
                                    //帖子发布完成后跳转选择帖子标签页
                                    /*if (mOpenFrom != OPEN_FROM_CAR_TYPE && mOpenFrom != OPEN_FROM_PEOPLE_CAR_LIFT && mPostType == PostCard.PostType.IMAGE_TEXT && postCard1 != null) {
                                        // 跳转选择标签页
                                        IntentUtils.startActivity(CommunityDoPostImageTextActivity.this, CommunityDoPostSuccessActivity.class, postCard1.getPostId());
                                    }*/
                                }
                                ToastUtil.showMessage(CommunityDoPostImageTextActivity.this, R.string.community_do_post_post_success);
                                //跳转到我的关注列表
                                IntentUtils.startActivity(context, MyAttentionActivity.class, User.shareInstance().getUserIdStr());
                                finish();
                                //如果是发送好货贴，也需要销毁掉第一步填写信息的页面
                                if (mOpenFrom == OPEN_FROM_GOODS) {
                                    if (ActivityController.isContainActivity(PublishGoodsExchangeInfoActivity.class)) {
                                        ActivityController.finishActivityByName(PublishGoodsExchangeInfoActivity.class);
                                    }
                                }
                            }

                            @Override
                            public void onFailure(Throwable e) {
                                super.onFailure(e);
                                isCommit = false;
                            }
                        });
            } else {
                if (mPostType == PostCard.PostType.CAR_BUYING_EXPERIENCE) {//发提车作业贴
                    L00bangRequestManager2
                            .setSchedulers(L00bangRequestManager2.getServiceInstance().pubCarPosting(postCard))
                            .subscribe(new ProgressSubscriber<PostCard>(CommunityDoPostImageTextActivity.this) {
                                @Override
                                public void onSuccess(PostCard postCard) {
                                    super.onSuccess(postCard);
                                    isCommit = false;
                                    PostCardManager.getInstance().getUploadImgs().clear();

                                    //删除保存的帖子记录
                                    deleteCachePost();
                                    //删除Sp保存的标签记录
                                    PreferenceUtil.removePreference(CommunityDoPostImageTextActivity.this, CarBuyingExperienceFillInfoActivity.KEY_LABEL_DRAFT);
                                    onPostExperiencePostSuccess(postCard);

                                    //通知
                                    notifyCarInfoChanged(postCard);
                                }

                                @Override
                                public void onFailure(Throwable e) {
                                    super.onFailure(e);
                                    isCommit = false;
                                }
                            });
                } else {//普通发帖
                    L00bangRequestManager2
                            .setSchedulers(L00bangRequestManager2.getServiceInstance().pubPostV3(postCard))
                            .subscribe(new ProgressSubscriber<PostCard>(CommunityDoPostImageTextActivity.this) {
                                @Override
                                public void onSuccess(PostCard postCard1) {
                                    super.onSuccess(postCard);
                                    isCommit = false;
                                    PostCardManager.getInstance().getUploadImgs().clear();

                                    //好货互通发帖，帖子发布成功后，弹窗确认是否需要置顶
                                    /*if (showGoodsStickyDialog(postCard1)) {
                                        return;
                                    }*/
                                    if (needSaveDraft()) {
                                        //删除保存的帖子记录
                                        deleteCachePost();
                                        //如果是图文或问题，再执行判断跳转
                                        //如果是从社区进来的
                                        if (mIntentExtra != null && mIntentExtra.isJumpAfterSuccess() == 1) {
                                            //不是我问技师，跳转
                                            Intent intent = new Intent(CommunityDoPostImageTextActivity.this, CommunityDetailActivity.class);
//                                        intent.putExtra("data", postCard1);
                                            intent.putExtra("channelId", mIntentExtra.getChannelId());
                                            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                                            startActivity(intent);
                                        } else if (mIntentExtra != null && mIntentExtra.isJumpAfterSuccess() == 2) {
                                            //如果是从车友会进来的
                                            try {
                                                Intent intent = new Intent(CommunityDoPostImageTextActivity.this, CarClubDetailActivity.class);
//                                        intent.putExtra("data", postCard1);
                                                Long channelId = Long.valueOf(mIntentExtra.getChannelId());
                                                intent.putExtra("channelId", channelId);
                                                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                                                startActivity(intent);
                                            } catch (NumberFormatException e) {
                                                e.printStackTrace();
                                            }
                                        }
                                    }
                                    //如果是发送好货贴，也需要销毁掉第一步填写信息的页面
                                    if (mOpenFrom == OPEN_FROM_GOODS) {
                                        if (ActivityController.isContainActivity(PublishGoodsExchangeInfoActivity.class)) {
                                            ActivityController.finishActivityByName(PublishGoodsExchangeInfoActivity.class);
                                        }
                                    }
                                    if (mPostType == PostCard.PostType.LING_SENSE) {
                                        deleteCacheTagList();
                                        ToastUtil.showMessage(CommunityDoPostImageTextActivity.this, R.string.community_do_post_post_success);
                                        //如果是菱感贴，发布成功就跳转到我的关注列表
                                        IntentUtils.startActivity(context, MyAttentionActivity.class, User.shareInstance().getUserIdStr());
                                        finish();
                                    }
                                    ToastUtil.showMessage(CommunityDoPostImageTextActivity.this, R.string.community_do_post_post_success);
                                    finish();

                                }

                                @Override
                                public void onFailure(Throwable e) {
                                    super.onFailure(e);
                                    isCommit = false;
                                }
                            });
                }
            }
        } else if (mPostType == PostCard.PostType.EDIT_LING_SENSE || mPostType == PostCard.PostType.EDIT_POST_CARD || mPostType == PostCard.PostType.EDIT_TOPIC) {//如果是编辑帖子
            EditPostCard editPostCard = new EditPostCard();
            editPostCard.setPostId(Long.valueOf(mIntentExtra.getPostId()));
            if (mPostType != PostCard.PostType.EDIT_LING_SENSE) {
                editPostCard.setChannelId(mIntentExtra.getChannelId());
            }
            editPostCard.setImgTexts(postCard.getImgTexts());
            //如果是从好货过来的，需要加上好货模板的内容
            if (mOpenFrom == OPEN_FROM_GOODS) {
                //好货互通
                editPostCard.setSeries(mIntentExtra.getSeries());
                editPostCard.setContentGroupId(String.valueOf(mIntentExtra.getGroupInfo().getContentGroupIdOrZero()));
                mPersonalCard = mIntentExtra.getPersonalCard();
                if (mPersonalCard != null && mPersonalCard.getCallingCardId() != 0) {
                    editPostCard.setCallingCardId(String.valueOf(mPersonalCard.getCallingCardId()));
                }
                //每一次都传mPersonalCard，在上一步控制有没有personCard
                editPostCard.setCallingCard(mPersonalCard);
                //mGoodsCoverImagePath为空是从编辑过来的，没有修改封面
                if (mGoodsCoverImg != null) {
                    editPostCard.setCoverImageText(mGoodsCoverImg);
                }
                editPostCard.setGoodStuffName(mIntentExtra.getGoodsName());
                editPostCard.setGoodNum(mIntentExtra.getGoodsNum());
                editPostCard.setGoodArea(mIntentExtra.getGoodsArea());
                editPostCard.setGoodCityId(mIntentExtra.getGoodCityId());
            }
            if (mPostType == PostCard.PostType.EDIT_POST_CARD) {
                editPostCard.setPostTypeId(String.valueOf(PostCard.PostType.IMAGE_TEXT));
                if (mEditPostCard != null && !TextUtils.isEmpty(mEditPostCard.getPostTitle())) {
                    editPostCard.setPostTitle(et_post_title.getText().toString());
                }
            } else if (mPostType == PostCard.PostType.EDIT_TOPIC) {
                editPostCard.setPostTypeId(String.valueOf(PostCard.PostType.TOPIC));
            } else if (mPostType == PostCard.PostType.EDIT_LING_SENSE) {//编辑菱感贴
                editPostCard.setPostTypeId(String.valueOf(PostCard.PostType.LING_SENSE));
                editPostCard.setPostTitle(postTitle);
            }
            L00bangRequestManager2
                    .setSchedulers(L00bangRequestManager2.getServiceInstance().editPost(editPostCard))
                    .subscribe(new ProgressSubscriber<PostCard>(CommunityDoPostImageTextActivity.this) {
                        @Override
                        public void onSuccess(PostCard postCard1) {
                            super.onSuccess(postCard);
                            isCommit = false;
                            PostCardManager.getInstance().getUploadImgs().clear();

                            if (mPostType == PostCard.PostType.EDIT_LING_SENSE) {
                                deleteCacheTagList();
                            }
                            if (mIntentExtra.getPostId() != null) {
                                Intent intent = new Intent();
                                intent.putExtra("postId", mIntentExtra.getPostId());
                                intent.putExtra(IntentUtils.INTENT_EXTRA_FROM, ConfigConstant.POST_OPEN_TYPE);
                                intent.putExtra("isFromEdit", true);
                                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                                intent.setClass(context, PostDetailActivity.class);
                                CommunityDoPostImageTextActivity.this.startActivity(intent);
                            }
                            finish();

                        }

                        @Override
                        public void onFailure(Throwable e) {
                            super.onFailure(e);
                            isCommit = false;
                        }
                    });
        } else {//编辑提车作业贴
            postCard.setPostTypeId(String.valueOf(PostCard.PostType.CAR_BUYING_EXPERIENCE));
            L00bangRequestManager2
                    .setSchedulers(L00bangRequestManager2.getServiceInstance().editPostCarByUser(postCard))
                    .subscribe(new ProgressSubscriber<PostCard>(CommunityDoPostImageTextActivity.this) {
                        @Override
                        public void onSuccess(PostCard postCard) {
                            super.onSuccess(postCard);
                            isCommit = false;
                            PostCardManager.getInstance().getUploadImgs().clear();

                            onPostExperiencePostSuccess(postCard);
                        }

                        @Override
                        public void onFailure(Throwable e) {
                            super.onFailure(e);
                            isCommit = false;
                        }
                    });
        }

    }

    /**
     * 提交前检查填写信息
     *
     * @return 是否检查通过
     */
    protected boolean checkCommitInput() {
        if (!AppUtil.checkLogin(this)) {
            return false;
        }
        //验证最小字数
        if (!checkTotalWordsCount()) {
            return false;
        }
        if (mPostType == PostCard.PostType.ASK_TECHNICIAN || mPostType == PostCard.PostType.ASK_ENGINEER) {
            //我问技师，校验类型
            //我问工程师，校验类型
            if (mChosenCarType == null) {
                ToastUtil.showMessage(this, R.string.community_do_post_item_choose_car_type);
                return false;
            }
            //行驶里程
            mChosenMileage = mItemMileage.getContent().trim();
            if (TextUtils.isEmpty(mChosenMileage)) {
                ToastUtil.showMessage(this, R.string.community_do_post_item_choose_enter_mileage);
                return false;
            }
            //驾驶习惯
//            if (mDriverHabitType == null) {
//                ToastUtil.showMessage(this, R.string.community_do_post_item_choose_driving_habit);
//                return false;
//            }
            if (TextUtils.isEmpty(mChosenQuestionIds)) {
                ToastUtil.showMessage(this, R.string.community_do_post_item_choose_question_type);
                return false;
            }
            //暂不限制
//            int totalWordsCount = getTotalCount();
//            if (totalWordsCount < 10) {
//                ToastUtil.showMessage(this, "问题描述请不要低于10字哦~");
//                return false;
//            }
        } else if (mPostType == PostCard.PostType.ASK_SOPHISTICATED_DRIVER) {
            if (mChosenQuestionType == null) {
                ToastUtil.showMessage(this, R.string.community_do_post_item_choose_question_type);
                return false;
            }
            //暂不限制
//            int totalWordsCount = getTotalCount();
//            if (totalWordsCount < 10) {
//                ToastUtil.showMessage(this, "问题描述请不要低于10字哦~");
//                return false;
//            }
        }
//        else if (mPostType == PostCard.PostType.CAR_BUYING_EXPERIENCE
//                || mPostType == PostCard.PostType.EDIT_CAR_BUYING_POST_CARD) {
//            /*if (getTotalCount() < 300) {//提车作业帖总字数不得少于300
//                ToastUtil.showMessage(this, R.string.car_buying_do_post_input_more_word_toast);
//                return false;
//            }*/
//            if (totalPicCount < 2) {
//                ToastUtil.showMessage(CommunityDoPostImageTextActivity.this,
//                        getString(R.string.car_buying_do_post_add_more_picture_toast, 2));
//                return false;
//            }
//        }
        else {
            //不是我问技师，也不是问老司机，也不是提车作业帖，才校验社区
            if (TextUtils.isEmpty(mIntentExtra.getChannelId()) && mPostType != PostCard.PostType.IMAGE_TEXT
                    && mPostType != PostCard.PostType.SHARE_GAME && mPostType != PostCard.PostType.SIGN_IN
                    && mPostType != PostCard.PostType.TOPIC && mPostType != PostCard.PostType.EDIT_TOPIC && mPostType != PostCard.PostType.CAR_BUYING_EXPERIENCE
                    && mPostType != PostCard.PostType.EDIT_CAR_BUYING_POST_CARD) {
                ToastUtil.showMessage(this, getString(R.string.community_do_post_toast_no_channel_id));
                return false;
            }
        }
        if ((mPostType == PostCard.PostType.IMAGE_TEXT || mPostType == PostCard.PostType.SHARE_GAME) && (mOpenFrom == OPEN_FROM_SQUARE || mOpenFrom == OPEN_FROM_GOODS
                || mOpenFrom == OPEN_FROM_LOCAL || mOpenFrom == OPEN_FROM_CAR_TYPE)
                && TextUtils.isEmpty(et_post_title.getText())) {
            ToastUtil.showMessage(this, getString(R.string.community_do_post_toast_no_title));
            return false;
        }
        if (mPostType == PostCard.PostType.EDIT_POST_CARD && mEditPostCard != null &&
                !TextUtils.isEmpty(mEditPostCard.getPostTitle()) &&
                TextUtils.isEmpty(et_post_title.getText())) {
            ToastUtil.showMessage(this, getString(R.string.community_do_post_toast_no_title));
            return false;
        }

        if (mPostType == PostCard.PostType.EDIT_POST_CARD
                || mPostType == PostCard.PostType.EDIT_TOPIC
                || mOpenFrom == OPEN_FROM_GOODS
                || mOpenFrom == OPEN_FROM_CAR_TYPE
                || mOpenFrom == OPEN_FROM_PEOPLE_CAR_LIFT) {
            if (totalPicCount < 1) {
                ToastUtil.showMessage(CommunityDoPostImageTextActivity.this,
                        getString(R.string.car_buying_do_post_add_more_picture_toast, 1));
                return false;
            }
            if (!checkCommitMessage()) {
                return false;
            }
        }

        //所有图片和除了当前文本框剩余文字个数如果不是0，则返回true
        if (totalPicCount != 0 || totalOutCurrentText != 0) {
            //字数或者图片不为零
            return true;
        } else {
            //如果字数和图片都为0，则判断当前editText是否为零（当前焦点没离开，totalOutCurrentText可能为0）
            if (editText != null && editText.getText() != null && !TextUtils.isEmpty(editText.getText().toString())) {
                return true;
            }
            ToastUtil.showMessage(this, getString(R.string.community_do_post_toast_no_content));
            return false;
        }
    }

    /**
     * 清除已经保存的帖子（只有退出和发送成功执行该方法）
     */
    protected void deleteCachePost() {
        //先停止定时保存，在删除
        handler.removeCallbacks(runnable);
        ThreadManager.execute(new Runnable() {
            @Override
            public void run() {
                deleteByType(mPostType);
            }
        }, false, true);
        if (mPostType == PostCard.PostType.IMAGE_TEXT || mPostType == PostCard.PostType.LING_SENSE) {
            String key = PreferenceUtil.getStringPreference(CommunityDoPostImageTextActivity.this, KEY_IMAGE_TEXT_TITLE, null);
            PreferenceUtil.removePreference(CommunityDoPostImageTextActivity.this, key);
            PreferenceUtil.removePreference(CommunityDoPostImageTextActivity.this, KEY_IMAGE_TEXT_TITLE);
        }
    }

    /**
     * 通知爱车信息变化
     */
    private void notifyCarInfoChanged(PostCard postCard) {
        //广播
        MyCar userDefaultCar = SelfUserInfoLoader.getInstance().getUserDefaultCar();
        if (userDefaultCar != null) {
            if (postCard != null) {
                //发送广播，爱车信息变化
                String postId = postCard.getPostId();
                try {
                    userDefaultCar.setPostCarId(Long.valueOf(postId));
                } catch (NumberFormatException ignored) {
                }
                userDefaultCar.setIsPublishPickupPost(1);
                UserInfoChangedHelper.sendCarInfoChangedBroadcast();
            }
        }
    }

    /**
     * 提车作业帖提交成功后进行跳转
     */
    private void onPostExperiencePostSuccess(PostCard postCard) {
        if (mPostType == PostCard.PostType.EDIT_CAR_BUYING_POST_CARD) {//编辑成功
            if (postCard != null && !TextUtils.isEmpty(postCard.getPostId())) {
                Intent intent = new Intent(this, PostDetailActivity.class);
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                intent.putExtra("isFromEdit", true);
                intent.putExtra("postId", postCard.getPostId());
                intent.putExtra(IntentUtils.INTENT_EXTRA_FROM, JumpPageUtil.OPEN_TYPE.APP);
                startActivity(intent);
            }
        } else {//发帖成功
            ToastUtil.showMessage(CommunityDoPostImageTextActivity.this, R.string.community_do_post_post_success);
            Intent intent;
            if (mExperienceFromType == CarBuyingExperienceFillInfoActivity.FROM_TYPE_HOME) {
                intent = new Intent(this, HomeActivity.class);
                startActivity(intent);
            } else {
                /*intent = new Intent(this, BindCarListActivity.class);
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);*/
                //新需求,提车作业贴发布完成需要跳转详情
                JumpPageUtil.goToPost(this, postCard.getPostId());
                setResult(RESULT_OK);
            }
        }
        finish();
    }

    /**
     * 上传图片成功执行的操作
     */
    protected void onUploadPicSuccess(ArrayList<String> ImageList, List<ImageModel> imageModels) {
        if (mOpenFrom == OPEN_FROM_GOODS && mGoodsCoverImagePath != null && isExistFIle(mGoodsCoverImagePath)) {
            // 开始发帖
            //视频贴只有一张封面图，只取第一张即可
            if (ImageList != null && ImageList.size() > 0 && imageModels.size() > 0) {//如果是多张图
                mGoodsCoverImagePath = "";
//                mCoverUrl = ImageList.get(0);
                ImageModel mCoverModel = imageModels.get(0);
                mGoodsCoverImg = new PostCardItem();
                mGoodsCoverImg.setImg(ImageList.get(0));
                mGoodsCoverImg.setPath(mCoverModel.getPath());
                mGoodsCoverImg.setHeight(String.valueOf(mCoverModel.getHeight()));
                mGoodsCoverImg.setWidth(String.valueOf(mCoverModel.getWidth()));
                doPost();
            }
            return;
        }
        PostCard postCard = null;
        if (mPostType == PostCard.PostType.CAR_BUYING_EXPERIENCE
                || mPostType == PostCard.PostType.EDIT_CAR_BUYING_POST_CARD) {
            postCard = mIntentExtra.getPostCard();//提车作业贴使用上一个页面传递的postcard对象
        }
        if (postCard == null) {
            postCard = new PostCard();
        }
        // 开始发帖
        //发布菱感贴或者编辑菱感贴
        if (mPostType == PostCard.PostType.LING_SENSE || mPostType == PostCard.PostType.EDIT_LING_SENSE) {
            doLingLabPost(postCard, ImageList, imageModels);
            /*List<PostCardItem> lingLabItemList = new ArrayList<>();
            postCard.setPostTitle(postTitle);
            postCard.setPostText(postContent);
            if (mPostType == PostCard.PostType.EDIT_LING_SENSE) {//编辑帖子时需要对图片进行排序
                //图片排序
                doImageSort(mPostCard, ImageList, imageModels);

                commit(mPostCard);
                return;
            } else {//发布帖子
                for (int i = 0; i < ImageList.size(); i++) {
                    PostCardItem postCardItem = new PostCardItem();
                    postCardItem.setImg(ImageList.get(i));
                    postCardItem.setHeight(String.valueOf(imageModels.get(i).getHeight()));
                    postCardItem.setWidth(String.valueOf(imageModels.get(i).getWidth()));
                    lingLabItemList.add(postCardItem);
                }

            }
            PostCardItem postCardItem = new PostCardItem();
            postCardItem.setText(postContent);
            lingLabItemList.add(postCardItem);
            postCard.setImgTexts(lingLabItemList);
            commit(postCard);*/
            return;

        }
        if (ImageList != null && ImageList.size() > 0) {//如果是多张图
            //将返回的值依次赋值给listItem
            int j = 0;
            for (PostCardItem postCardItem : itemList) {
                if (postCardItem.getPath() != null && postCardItem.getImg() == null) {
                    if (j < ImageList.size() && ImageList.get(j) != null) {
                        postCardItem.setImg(ImageList.get(j));
                        if (imageModels != null) {
                            if (j < imageModels.size()) {//判断防止数组溢出
                                postCardItem.setHeight(String.valueOf(imageModels.get(j).getHeight()));
                                postCardItem.setWidth(String.valueOf(imageModels.get(j).getWidth()));
                            }
                        }
                    }
                    j++;
                }
            }
        }
        postCard.setImgTexts(itemList);
        commit(postCard);
    }

    protected void doLingLabPost(PostCard postCard, ArrayList<String> imageList, List<ImageModel> imageModels) {

    }

    protected void doImageSort(PostCard postCard, ArrayList<String> imageList, List<ImageModel> imageModels) {

    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (intent != null) {
            //选中的图片
            final ArrayList<String> mSelectedList = intent.getStringArrayListExtra("data");
            if (mSelectedList != null && mSelectedList.size() > 0) {
                PostCardManager.getInstance().getUploadImgs().clear();
                PostCardManager.getInstance().getUploadImgs().addAll(mSelectedList);
                //imagePathList.addAll(mSelectedList);
                totalPicCount += mSelectedList.size();
                addImageAndTextView(mSelectedList);
            }
        }
    }

    @Override
    public void onBackPressed() {
        makeSureBack();
    }

    /**
     * 点击返回时的确认操作
     */
    private void makeSureBack() {
        if (isNeedCloseDrawer()) {
            closeDrawer();
            return;
        }
        if (checkBackInput()) {
            //如果有输入的内容
            if (mPostType == PostCard.PostType.CAR_BUYING_EXPERIENCE) {
                saveImageAndText();
                finish();
                return;
            }
            String message;
            String btnOkText;
            String btnCancelText;
            if (needSaveDraft()) {
                message = getString(R.string.community_do_post_dialog_save_confirm);
                btnOkText = getString(community_do_post_dialog_save_yes);
                btnCancelText = getString(community_do_post_dialog_save_no_exit);
            } else {
                message = getString(R.string.community_do_post_dialog_exit_confirm);
                btnOkText = getString(R.string.community_do_post_dialog_exit_yes_exit);
                btnCancelText = getString(R.string.community_do_post_dialog_exit_no_continue);
            }
            Dialog dialog = new CommonAlertDialog(this, message, btnOkText, btnCancelText, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    if (needSaveDraft()) {
                        saveImageAndText();
                    }
                    onExit();
                    finish();
                }
            }, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    //选择直接退出
                    if (needSaveDraft()) {
                        deleteCachePost();
                        finish();
                    } else {
                        dialog.dismiss();
                    }
                }
            });
            dialog.show();
        } else {
            finish();
        }
    }

    /**
     * 退出时执行的操作
     */
    protected void onExit() {}

    /**
     * 选择社区
     */
    @Optional
    @OnClick(R.id.tv_select_community)
    protected void ocClickSelectCommunity() {
        IntentUtils.startActivityForResult(this, ChooseCommunityActivity.class, ConfigConstant.CHOOSE_COMMUNITY_REQUEST_CODE);
    }

    /**
     * 用电脑发帖
     */
    @Optional
    @OnClick(R.id.tv_post_by_computer)
    protected void onClickPostByComputer() {

        SensorsUtils.sensorsClickBtn("点击发长文-去电脑发帖", "长文发布页", "去电脑发帖");

        IntentUtils.startActivity(this, CommunityPostByComputerActivity.class);
    }

    /**
     * 点击图片按钮，弹出相册
     */
    @Optional
    @OnClick(R.id.ll_add_photo)
    protected void onClickChooseImage() {
        //选完可能还需要压缩，所以读写权限一起请求
        PermissionUtils.checkStoragePermissions(null, this, READ_PIC, getString(R.string.permission_picture_pre), getString(R.string.permission_picture_setting));
    }

    /**
     * 点击拍照按钮
     */
    @OnClick(R.id.iv_camera)
    @Optional
    protected void onClickCamera() {
        PermissionUtils.checkStoragePermissions(null, this, TAKE_PIC, getString(R.string.permission_camera_pre), getString(R.string.permission_camera_setting), Manifest.permission.CAMERA);
    }

    /**
     * 点击键盘按钮
     */
    @Optional
    @OnClick(R.id.iv_key_control)
    protected void onClickKeyControl() {
        //显示键盘，隐藏表情
        InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null) {
            imm.toggleSoftInput(0, InputMethodManager.HIDE_NOT_ALWAYS);
        }
        if (expression_view_pager != null) {
            expression_view_pager.setVisibility(View.GONE);
        }
    }

    /**
     * 点击表情
     */
    @Optional
    @OnClick(R.id.ll_expression)
    protected void onClickExpression() {
        if (expression_view_pager != null) {
            if (expression_view_pager.getVisibility() == View.VISIBLE) {
                //如果表情可见状态，则隐藏
                showKeyBoard();
                expression_view_pager.setVisibility(View.GONE);
            } else {
                hideKeyboard();
                expression_view_pager.setVisibility(View.VISIBLE);
            }
        }
    }

    /**
     * 点击个人名片
     */
    @Optional
    @OnClick(R.id.ll_personal_card)
    protected void onClickPersonalCard() {
        if (AppUtil.checkLogin(this)) {
            IntentUtils.startActivityForResult(this, PostPersonalCardActivity.class, REQUEST_CODE_CHOOSE_PERSONAL_CARD);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == CHOOSE_COMMUNITY_REQUEST_CODE && resultCode == RESULT_OK) {
            //如果是选择社区返回
            BaseCommunityListActivity.IntentExtra intentExtra = (BaseCommunityListActivity.IntentExtra) IntentUtils.getExtra(data, null);
            if (intentExtra != null) {
                tv_select_community.getTvLeft().setText(intentExtra.getName());
//                tv_select_community.setTextColor(getResources().getColor(R.color.orange_ff7000));
//                tv_select_community.setBackgroundResource(R.drawable.bg_rectangle_circle_stroke_orange_ff7000);
                mIntentExtra.setChannelId(intentExtra.getId());
            }
        } else if (requestCode == REQUEST_CODE_CHOOSE_LOCAL_COLUMN && resultCode == RESULT_OK) {
            //选择完本地栏目后开始发帖
            mIntentExtra.setChannelId(data.getStringExtra(CommunityDoPostChooseColumnActivity.KEY_CHANNEL_ID));
            long longExtra = data.getLongExtra(CommunityDoPostChooseColumnActivity.KEY_LOCAL_COLUMN_ID, -1L);
            if (longExtra != -1L) {
                mLocalColumnId = longExtra;
            }
            doPost();
        } else if (requestCode == REQUEST_CODE_CHOOSE_TYPE_LABEL && resultCode == RESULT_OK) {
            //选择标签返回结果、
            ContentGroupInfo groupInfo = (ContentGroupInfo) IntentUtils.getExtra(data.getExtras(), null);
            mTvLabel.getTvLeft().setText(groupInfo.getContentGroupName());
            mIntentExtra.setGroupInfo(groupInfo);
        } else if (requestCode == REQUEST_CODE_SELECT_LOCATION && resultCode == RESULT_OK) {
            poiInfo = (PoiInfo) IntentUtils.getExtra(data, null);
            if (mLlLocation != null) {
                mLlLocation.getTvLeft().setText(poiInfo == null ? getString(R.string.community_do_post_select_location) : poiInfo.getName());
            }
            if (mTvLocation != null) {
                mTvLocation.setText(poiInfo == null ? "添加位置" : poiInfo.getName());
                if (poiInfo != null) {
                    mTvLocation.setTextColor(getResources().getColor(R.color.white));
                    mTvLocation.setBackgroundResource(R.drawable.bg_corner30_solid_384967);
                    mTvLocation.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_post_ling_lab_position_white, 0, 0, 0);
                } else {
                    mTvLocation.setTextColor(getResources().getColor(R.color.color_868990));
                    mTvLocation.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_post_location, 0, 0, 0);
                    mTvLocation.setBackgroundResource(R.drawable.bg_corner_32_solid_f8f8f8);
                }
            }
        } else if (requestCode == REQUEST_CODE_SELECT_USER && resultCode == RESULT_OK) {
            setUserNotify(data);
        }
        if (mChooseImageController != null) {
            mChooseImageController.onActivityResult(requestCode, resultCode, data);
        }
    }

    protected void setUserNotify(Intent data) {
        if (mLlNotifyUser == null || data == null) {
            return;
        }
        mNotifyUserList = (ArrayList<User>) data.getSerializableExtra(PostSelectNotifyUserListActivity.EXTRA_KEY);
        int size = mNotifyUserList.size();
        if (size > 0) {
            mLlNotifyUser.getTvLeft().setText(getResources().getString(R.string.community_do_post_notify_user_count, size));
        } else {
            mLlNotifyUser.getTvLeft().setText(R.string.community_do_post_notify_user);
        }
    }

    /**
     * 点击提醒谁看
     */
    @Optional
    @OnClick(R.id.ll_notify_user)
    void clickSelectUser() {
//        SensorsUtils.sensorsClickBtn("点击图文发布页-提醒谁看", "图文发布页", "图文发布页");
        Intent intent = new Intent(this, PostSelectNotifyUserListActivity.class);
        if (mNotifyUserList != null && !mNotifyUserList.isEmpty()) {
            intent.putExtra(PostSelectNotifyUserListActivity.EXTRA_KEY, mNotifyUserList);
        }
        startActivityForResult(intent, REQUEST_CODE_SELECT_USER);
    }

    /**
     * 点击选择城市
     */
    @Optional
    @OnClick(R.id.tv_city)
    protected void onClickCity() {
        if (!TextUtils.isEmpty(mIntentExtra.getChannelId()) && !TextUtils.isEmpty(mIntentExtra.getChannelName())) {
            return;
        }

        //选择城市社区弹窗
        if (mChooseCityDialog != null) {
            mChooseCityDialog.show();
        } else {
            L00bangRequestManager2
                    .setSchedulers(L00bangRequestManager2.getInstance().getService().getCityChannelList())
                    .subscribe(new ProgressSubscriber<List<ProvinceModel>>(this) {
                        @Override
                        public void onSuccess(List<ProvinceModel> provinceModels) {
                            super.onSuccess(provinceModels);
                            mChooseCityDialog = new ChooseCityChannelDialog(CommunityDoPostImageTextActivity.this, getString(R.string.dialog_choose_city_title), provinceModels, new ChooseCityChannelDialog.OnChooseCityListener() {
                                @Override
                                public boolean onChoseCity(ProvinceModel chosenProvinceModel, ProvinceModel.CityChannelModel chosenCityModel) {
                                    mIntentExtra.setChannelId(String.valueOf(chosenCityModel.getChannelId()));
                                    mIntentExtra.setChannelName(chosenCityModel.getCityName());
                                    mTvCity.getTvLeft().setText(chosenCityModel.getCityName());
                                    return false;
                                }
                            });
                            mChooseCityDialog.show();
                        }
                    });
        }
    }

    /**
     * 选择标签
     **/
    @Optional
    @OnClick(R.id.tv_label)
    protected void onLabel() {
        //人车生活、好货互通
        /*if (mOpenFrom == OPEN_FROM_GOODS || mOpenFrom == OPEN_FROM_PEOPLE_CAR_LIFT) {
            long id = mIntentExtra.getGroupInfo() == null ? 0 : mIntentExtra.getGroupInfo().getContentGroupIdOrZero();
            CommunityPostChooseLabelActivity.startActivity(this, mIntentExtra.getSeries(), id, REQUEST_CODE_CHOOSE_TYPE_LABEL);
        } */
        //现在发帖是图文贴展示标签且可以选择，好货贴是在进入发帖之前已经指定了标签且不展示
        if (mOpenFrom == OPEN_FROM_CAR_TYPE) {
            //车型论坛
            getLabelList();
            SensorsUtils.sensorsClickBtn("点击发长文-选择车型", "长文发布页", "选择车型");
        }
    }

    /**
     * 选择车型
     */
    protected void getLabelList() {
        L00bangRequestManager2.getServiceInstance()
                .getContentGroupInfo(mIntentExtra.getSeries())
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<ColumnInfo>(this) {
                    @Override
                    public void onSuccess(final ColumnInfo columnInfo) {
                        super.onSuccess(columnInfo);
                        if (columnInfo == null) {
                            return;
                        }
                        List<ContentGroupInfo> list;
                        if ((list = columnInfo.getContentGroupList()) == null || list.isEmpty()) {
                            return;
                        }
                        List<String> strings = new ArrayList<>(list.size());
                        for (ContentGroupInfo contentGroupInfo : list) {
                            strings.add(contentGroupInfo.getContentGroupName());
                        }
                        CommonListDialog dialog = new CommonSelectListDialog(CommunityDoPostImageTextActivity.this, strings, new BaseListDialog.OnChoiceClickListener() {
                            @Override
                            public boolean onChoiceClick(int chosenIndex, String chosenText) {
                                if (chosenIndex == -1) {
                                    ToastUtil.showMessage(getApplicationContext(), R.string.report_person_reason_no_choice);
                                    return true;
                                }
                                ContentGroupInfo groupInfo = columnInfo.getContentGroupList().get(chosenIndex);
                                if (mTvLabel != null) {
                                    mTvLabel.getTvLeft().setText(groupInfo.getContentGroupName());
                                }
                                mIntentExtra.setGroupInfo(groupInfo);
                                showCar();
                                return false;

                            }
                        });
                        dialog.show();

                    }
                });
    }

    protected void showCar() {

    }

    @Override
    protected void onPermissionResult(boolean isGranted, int requestCode) {
        super.onPermissionResult(isGranted, requestCode);
        if (mChooseImageController != null) {
            mChooseImageController.onPermissionResult(isGranted, requestCode);
        }
        if (isGranted) {
            if (requestCode == READ_PIC) {
                chooseImage(false);
            } else if (requestCode == TAKE_PIC) {
                chooseImage(true);
            }
        }
    }

    /**
     * 点击图片，权限允许之后执行的操作
     */
    private void chooseImage(boolean useOnlyCamera) {
        PostCardManager.getInstance().getUploadImgs().clear();//暂时清空
        if (useOnlyCamera && !DeviceUtil.hasSDCard()) {
            ToastUtil.showMessage(CommunityDoPostImageTextActivity.this, getResources().getString(R.string.no_sdcard));
            return;
        }
        //计算剩余图片数
        int residuePic = ConfigConstant.MAX_PIC_COUNT - totalPicCount;
        if (residuePic < 1) {
            ToastUtil.showMessage(CommunityDoPostImageTextActivity.this, getString(R.string.community_do_post_toast_image_count_limit, MAX_PIC_COUNT));
            return;
        }
        if (mChooseImageController == null) {
            mChooseImageController = new ChooseImageController(this);
        }
        mChooseImageController.setOnlyUseCamera(useOnlyCamera);
        mChooseImageController.setMaxCount(residuePic);
        mChooseImageController.getChosenImageList().clear();
        mChooseImageController.setOnChooseImageListListener(new ChooseImageController.OnChooseImageListListener() {
            @Override
            public void onAddImageList(List<String> mSelectedList) {
                if (mSelectedList != null && mSelectedList.size() > 0) {
                    PostCardManager.getInstance().getUploadImgs().clear();
                    PostCardManager.getInstance().getUploadImgs().addAll(mSelectedList);
                    //imagePathList.addAll(mSelectedList);
                    totalPicCount += mSelectedList.size();
                    addImageAndTextView(mSelectedList);
                }
            }
        });
        mChooseImageController.showChooseTypeDialog();
    }

    /**
     * 显示键盘
     */
    protected void showKeyBoard() {
        if (editText != null) {
            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            if (imm != null) {
                imm.showSoftInput(editText, InputMethodManager.SHOW_FORCED);
            }
        }
    }

    /**
     * 隐藏键盘
     */
    protected void hideKeyboard() {
        if (editText != null) {
            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            if (imm != null) {
                imm.hideSoftInputFromWindow(editText.getWindowToken(), 0);
            }
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.item_cart_type:
                showChooseCarTypeDialog();
                break;
            case R.id.item_question_type:
                InputMethodUtils.hideInputMethod(this, v);
                if (mDrawerLayout != null) {
                    mDrawerLayout.openDrawer(Gravity.END);
                    return;
                }

                showChooseQuestionTypeDialog();
                break;
            case R.id.item_driving_habit:
                //选择驾驶习惯
                showChooseDrivingHabitDialog();
                break;
            default:
                break;
        }
    }

    /**
     * 选择车型
     */
    private void showChooseCarTypeDialog() {
        if (mChooseCarTypeUtil == null) {
            mChooseCarTypeUtil = new ChooseCarTypeDialog.ChooseCarTypeUtil(this, new ChooseCarTypeDialog.OnChooseCarTypeListener() {
                @Override
                public boolean onChooseCarType(CarType carType) {
                    mChosenCarType = carType;
                    if (mChosenCarType != null) {
                        if (mItemCarType != null) {
                            mItemCarType.getTvRight().setText(mChosenCarType.getFullName());
                        }
                        if (mTvLabel != null) {
                            mTvLabel.getTvLeft().setText(carType.getFullName());
                        }
                    }
                    return false;
                }
            });
        }
        mChooseCarTypeUtil.showDialog();
    }

    /**
     * 选择问题类型
     */
    private void showChooseQuestionTypeDialog() {
        if (mChooseQuestionTypeUtil == null) {
            mChooseQuestionTypeUtil = new ChooseQuestionTypeDialog.ChooseQuestionTypeUtil(this, mPostType, new ChooseQuestionTypeDialog.OnChooseQuestionTypeListener() {
                @Override
                public boolean onChooseQuestionType(QuestionType questionType) {
                    mChosenQuestionType = questionType;
                    if (mChosenQuestionType != null) {
                        mItemQuestionType.getTvRight().setText(mChosenQuestionType.getLabelName());
                    }
                    return false;
                }
            });
        }
        mChooseQuestionTypeUtil.showDialog();
    }

    /**
     * 选择驾驶习惯
     */
    private void showChooseDrivingHabitDialog() {
        if (mChooseDrivingHabitUtil == null) {
            mChooseDrivingHabitUtil = new ChooseDrivingHabitDialog.ChooseDrivingHabitUtil(this, new ChooseDrivingHabitDialog.OnChooseDrivingHabitTypeListener() {
                @Override
                public boolean onChooseQuestionType(DriverHabitType questionType) {
                    mDriverHabitType = questionType;
                    if (mDriverHabitType != null) {
                        mItemDrivingHabit.getTvRight().setText(questionType.getHabitDesc());
                    }
                    return false;
                }
            });
        }
        mChooseDrivingHabitUtil.showDialog();
    }

    //不传postId（适用于发帖子）
    public static void startActivity(Context context, IntentExtra intentExtra) {
        IntentUtils.startActivity(context, CommunityDoPostImageTextActivity.class, intentExtra);
    }

    //不传postId（适用于发帖子）
    public static void startActivity(Context context, int postType, String communityId, int jumpAfterSuccess, int openFrom) {
        startActivity(context, postType, communityId, null, jumpAfterSuccess, openFrom);
    }

    /***
     * 好货互通发帖，帖子发布成功后，弹窗确认是否需要置顶
     * @param postCard1
     * @return
     * 说明：现在好货贴发帖成功也不需要弹窗提醒是否置顶
     */
    private boolean showGoodsStickyDialog(@NonNull PostCard postCard1) {
        if (mOpenFrom == OPEN_FROM_GOODS && postCard1 != null) {
            GoodsStickyDialog.GoodsStickyUtils goodsStickyUtils = new GoodsStickyDialog.GoodsStickyUtils(this, postCard1.getPostId(), true);
            goodsStickyUtils.setType(1);
            goodsStickyUtils.showDialog();
            if (needSaveDraft()) {
                //删除保存的帖子记录
                deleteCachePost();
            }
            return true;
        }
        return false;
    }

    //打开编辑帖子
    public static void startActivity(Context context, int postType, String communityId, String postId, int jumpAfterSuccess, int openFrom) {
        IntentExtra intentExtra = new IntentExtra(postType, communityId, postId);
        intentExtra.setJumpAfterSuccess(jumpAfterSuccess);
        intentExtra.setOpenFrom(openFrom);
        IntentUtils.startActivity(context, CommunityDoPostImageTextActivity.class, intentExtra);
    }

    /**
     * 编辑好货贴
     *
     * @param context
     * @param postType
     * @param postId
     * @param jumpAfterSuccess
     */
    public static void startActivity(Context context, int postType, String postId, String communityId, String coverImg, int jumpAfterSuccess, String goodsName, String goodsNum, String goodsArea, Long goodCityId, ContentGroupInfo contentGroupInfo, PersonalCard personalCard) {
        IntentExtra intentExtra = new IntentExtra(postType, postId, communityId, coverImg, jumpAfterSuccess, goodsName, goodsNum, goodsArea, goodCityId, contentGroupInfo, personalCard);
        //必须指定是从好货进入的
        intentExtra.setOpenFrom(OPEN_FROM_GOODS);
        IntentUtils.startActivity(context, CommunityDoPostImageTextActivity.class, intentExtra);
    }

    /**
     * 发布话题帖
     */
    public static void startActivityToPostTopic(Context context, int postType, Long topicId, String topicName) {
        IntentExtra intentExtra = new IntentExtra(postType, topicId, topicName);
        IntentUtils.startActivity(context, CommunityDoPostImageTextActivity.class, intentExtra);
    }

    /**
     * 发好货贴
     *
     * @param context
     * @param coverImg         封面图
     * @param goodsName        服务名称或者商品名
     * @param goodsNum         库存数量
     * @param goodsArea        服务地区
     * @param contentGroupInfo 标签内容组
     * @param personalCard     个人名片
     */
    public static void startActivity(Context context, String coverImg, String goodsName, String goodsNum, String goodsArea, Long goodCityId, ContentGroupInfo contentGroupInfo, PersonalCard personalCard) {
        IntentExtra intentExtra = new IntentExtra(coverImg, goodsName, goodsNum, goodsArea, goodCityId, contentGroupInfo, personalCard);
        //必须指定是从好货进入的
        intentExtra.setOpenFrom(OPEN_FROM_GOODS);
        IntentUtils.startActivity(context, CommunityDoPostImageTextActivity.class, intentExtra);
    }

    /**
     * 好货互通、人车生活、车型论坛
     */
    protected void setPostCardParam(PostCard postCard) {
        if (mOpenFrom == OPEN_FROM_GOODS) {
            //好货互通
            postCard.setSeries(mIntentExtra.getSeries());
            postCard.setContentGroupId(String.valueOf(mIntentExtra.getGroupInfo().getContentGroupIdOrZero()));
            mPersonalCard = mIntentExtra.getPersonalCard();
            if (mPersonalCard != null && mPersonalCard.getCallingCardId() != 0) {
                postCard.setCallingCardId(String.valueOf(mPersonalCard.getCallingCardId()));
            }
            //每一次都传mPersonalCard，在上一步控制有没有personCard
            postCard.setCallingCard(mPersonalCard);
            //mGoodsCoverImagePath为空是从编辑过来的，没有修改封面
            if (mGoodsCoverImg != null) {
                postCard.setCoverImageText(mGoodsCoverImg);
            }
            postCard.setGoodStuffName(mIntentExtra.getGoodsName());
            postCard.setGoodNum(mIntentExtra.getGoodsNum());
            postCard.setGoodArea(mIntentExtra.getGoodsArea());
            postCard.setGoodCityId(mIntentExtra.getGoodCityId());
        } else if (mOpenFrom == OPEN_FROM_CAR_TYPE) {
            //车型论坛
            postCard.setSeries(mIntentExtra.getSeries());
            //车型
            postCard.setContentGroupId(String.valueOf(mIntentExtra.getGroupInfo().getContentGroupIdOrZero()));
            //城市
            postCard.setChannelId(String.valueOf(mIntentExtra.getChannelId()));
        } else if (mOpenFrom == OPEN_FROM_PEOPLE_CAR_LIFT) {
            //分类1:人车生活、2:车型论坛、3:好货互通
            postCard.setSeries(mIntentExtra.getSeries());
            postCard.setContentGroupId(String.valueOf(mIntentExtra.getGroupInfo().getContentGroupIdOrZero()));
        }
    }

    /**
     * 发布提车作业贴
     *
     * @param context  context
     * @param postType 发帖类型-{@link com.cloudy.linglingbang.model.postcard.PostCard.PostType}
     * @param postCard 帖子对象
     */
    public static void startActivityToPostCarBuyingExperience(Activity context, int postType, PostCard postCard, int fromType) {
        IntentExtra intentExtra = new IntentExtra(postType, postCard, fromType);
        IntentUtils.startActivityForResult(context, CommunityDoPostImageTextActivity.class, fromType, intentExtra);
    }

    public static class IntentExtra implements Serializable {
        private static final long serialVersionUID = -9169028055201531402L;
        private int mPostType;
        private String mChannelId;
        private String mChannelName;
        private String mPostId;
        private String mWebRes;//H5资源
        private String mWebResPath;//H5资源路径
        private int mJumpAfterSuccess;//成功后跳转到哪 1.社区  2.车友会  3.帖子
        private int mFromType;//发提车作业帖跳转
        private PostCard mPostCard;//提车作业贴-车辆信息页传递的数据
        private Long mTopicId;//话题id
        private String mTopicName;//话题名称
        private int mOpenFrom;//发帖入口
        //发布好货的字段
        private String mCoverImg;
        private String mGoodsName;
        private String mGoodsNum;
        private String mGoodsArea;
        private Long mGoodCityId;
        private PersonalCard mPersonalCard;
        private int mLinkType;
        private String mLinkUrl;

        /**
         * 值从mGroupInfo中取
         * 默认值为1
         */
        public int getSeries() {
            return mGroupInfo == null ? 1 : mGroupInfo.getSeriesOrZero();
        }

        /**
         * 标签
         **/
        private ContentGroupInfo mGroupInfo;
        /**
         * 草稿id
         */
        private Long mDraftId;

        public ContentGroupInfo getGroupInfo() {
            return mGroupInfo;
        }

        public void setGroupInfo(ContentGroupInfo groupInfo) {
            mGroupInfo = groupInfo;
        }

        public IntentExtra(int postType, String channelId, String postId) {
            mPostType = postType;
            mChannelId = channelId;
            mPostId = postId;
        }

        public IntentExtra(int postType, PostCard postCard, int fromType) {
            mPostType = postType;
            mPostCard = postCard;
            mFromType = fromType;
        }

//        public IntentExtra(int postType, String res) {
//            mPostType = postType;
//            mWebRes = res;
//        }

        public IntentExtra(int postType, String path, int linkType, String linkUrl) {
            mPostType = postType;
            mWebResPath = path;
            mLinkType = linkType;
            mLinkUrl = linkUrl;

        }

        public IntentExtra(int postType, PostCard postCard) {
            mPostType = postType;
            this.mPostCard = postCard;
        }

        public IntentExtra(int postType, Long topicId, String topicName) {
            mPostType = postType;
            mTopicId = topicId;
            mTopicName = topicName;
        }

        /**
         * 发好货的数据
         *
         * @param coverImg
         * @param goodsName
         * @param goodsNum
         * @param goodsArea
         * @param goodCityId
         * @param contentGroupInfo
         * @param personalCard
         */
        public IntentExtra(String coverImg, String goodsName, String goodsNum, String goodsArea, Long goodCityId, ContentGroupInfo contentGroupInfo, PersonalCard personalCard) {
            mCoverImg = coverImg;
            mGoodsName = goodsName;
            mGoodsNum = goodsNum;
            mGoodsArea = goodsArea;
            mGoodCityId = goodCityId;
            mGroupInfo = contentGroupInfo;
            mPersonalCard = personalCard;
        }

        /**
         * 编辑好货的数据
         *
         * @param postType
         * @param postId
         * @param communityId
         * @param coverImg
         * @param jumpAfterSuccess
         * @param goodsName
         * @param goodsNum
         * @param goodsArea
         * @param goodCityId
         * @param contentGroupInfo
         * @param personalCard
         */
        public IntentExtra(int postType, String postId, String communityId, String coverImg, int jumpAfterSuccess, String goodsName, String goodsNum, String goodsArea, Long goodCityId, ContentGroupInfo contentGroupInfo, PersonalCard personalCard) {
            mPostType = postType;
            mPostId = postId;
            mChannelId = communityId;
            mCoverImg = coverImg;
            mJumpAfterSuccess = jumpAfterSuccess;
            mGoodsName = goodsName;
            mGoodsNum = goodsNum;
            mGoodsArea = goodsArea;
            mGoodCityId = goodCityId;
            mGroupInfo = contentGroupInfo;
            mPersonalCard = personalCard;
        }

        public String getChannelId() {
            return mChannelId;
        }

        public void setChannelId(String channelId) {
            mChannelId = channelId;
        }

        public int getPostType() {
            return mPostType;
        }

        public void setPostType(int postType) {
            mPostType = postType;
        }

        public String getPostId() {
            return mPostId;
        }

        public void setPostId(String postId) {
            mPostId = postId;
        }

        String getWebRes() {
            return mWebRes;
        }

        public void setWebRes(String webRes) {
            mWebRes = webRes;
        }

        public String getWebResPath() {
            return mWebResPath;
        }

        public void setWebResPath(String webResPath) {
            mWebResPath = webResPath;
        }

        int isJumpAfterSuccess() {
            return mJumpAfterSuccess;
        }

        public void setJumpAfterSuccess(int jumpAfterSuccess) {
            mJumpAfterSuccess = jumpAfterSuccess;
        }

        public PostCard getPostCard() {
            return mPostCard;
        }

        public void setPostCard(PostCard postCard) {
            mPostCard = postCard;
        }

        int getFromType() {
            return mFromType;
        }

        public void setFromType(int fromType) {
            mFromType = fromType;
        }

        Long getTopicId() {
            return mTopicId;
        }

        public void setTopicId(Long topicId) {
            mTopicId = topicId;
        }

        String getTopicName() {
            return mTopicName;
        }

        public void setTopicName(String topicName) {
            mTopicName = topicName;
        }

        public int getOpenFrom() {
            return mOpenFrom;
        }

        public void setOpenFrom(int openFrom) {
            mOpenFrom = openFrom;
        }

        public String getChannelName() {
            return mChannelName;
        }

        public void setChannelName(String channelName) {
            mChannelName = channelName;
        }

        public String getCoverImg() {
            return mCoverImg;
        }

        public void setCoverImg(String coverImg) {
            this.mCoverImg = coverImg;
        }

        public String getGoodsName() {
            return mGoodsName;
        }

        public void setGoodsName(String goodsName) {
            this.mGoodsName = goodsName;
        }

        public String getGoodsNum() {
            return mGoodsNum;
        }

        public void setGoodsNum(String goodsNum) {
            this.mGoodsNum = goodsNum;
        }

        public String getGoodsArea() {
            return mGoodsArea;
        }

        public void setGoodsArea(String goodsArea) {
            this.mGoodsArea = goodsArea;
        }

        public Long getGoodCityId() {
            return mGoodCityId;
        }

        public void setGoodCityId(Long goodCityId) {
            mGoodCityId = goodCityId;
        }

        public PersonalCard getPersonalCard() {
            return mPersonalCard;
        }

        public void setPersonalCard(PersonalCard personalCard) {
            this.mPersonalCard = personalCard;
        }

        public int getLinkType() {
            return mLinkType;
        }

        public void setLinkType(int linkType) {
            this.mLinkType = linkType;
        }

        public String getLinkUrl() {
            return mLinkUrl;
        }

        public void setLinkUrl(String linkUrl) {
            this.mLinkUrl = linkUrl;
        }

        public Long getDraftId() {
            return mDraftId;
        }

        public void setDraftId(Long draftId) {
            mDraftId = draftId;
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        if (needSaveDraft()) {
            saveDraft();
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        if (needSaveDraft()) {
            if (!AppUtil.isForeground(this)) {
                //当应用切换到后台时保存帖子数据
                saveImageAndText();
            }
            //停止定时器保存帖子
            handler.removeCallbacks(runnable);
        }
    }

    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        //当遇到问题，当前activity重建的时候，清空所有的布局，重新加载草稿箱中的数据，防止重建时数据发生混乱
        ll_content.removeAllViews();
        tag = 0;//将tag重置为0
        showDraft();//添加草稿箱视图
    }

    /**
     * 编辑帖子时请求帖子数据
     */
    private void requestPostDetail() {
        if (mIntentExtra.getPostId() == null) {
            return;
        }
        L00bangRequestManager2
                .getServiceInstance()
                .getPostDetails(Long.valueOf(mIntentExtra.getPostId()))
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<PostCard>(this) {
                    @Override
                    public void onSuccess(PostCard postCard) {
                        super.onSuccess(postCard);
                        if (postCard != null) {
                            mEditPostCard = postCard;
                            List<PostCardItem> postCardItem = postCard.getImgTexts();
                            if (postCardItem != null && postCardItem.size() > 0) {
                                addSavedImageAndTextView(postCardItem);
                            }
                            poiInfo = new PoiInfo();
                            poiInfo.setName(postCard.getLocationName());
                            poiInfo.setAddress(postCard.getLocationDetail());
                            poiInfo.setLocation(new LatLng(postCard.getLatitude(), postCard.getLongitude()));
                            if (mPostType == PostCard.PostType.EDIT_POST_CARD && !TextUtils.isEmpty(postCard.getPostTitle())) {
                                showImageTextPostTitle();
                                et_post_title.setText(postCard.getPostTitle());

                            } else if (mPostType == PostCard.PostType.EDIT_TOPIC) {
                                tv_topic_name.setVisibility(View.VISIBLE);
                                tv_topic_name.setText(getString(R.string.community_do_post_topic_name, postCard.getTopicName()));
                            }
                        }
                    }
                });
    }

    /**
     * 是否需要保存草稿，图文和问题保存，别的不保存
     *
     * @return true如果要保存
     */
    protected boolean needSaveDraft() {
        return (mPostType == PostCard.PostType.IMAGE_TEXT
                || mPostType == PostCard.PostType.QUESTION
                || mPostType == PostCard.PostType.CAR_BUYING_EXPERIENCE
                || mPostType == PostCard.PostType.TOPIC
                || mPostType == PostCard.PostType.LING_SENSE);
    }

    protected boolean isExistFIle(String path) {
        if (TextUtils.isEmpty(path)) {
            return false;
        }
        return FileUtils.isAndroidMediaUri(path) || ImageUtil.isExistFIle(path);
    }

    protected void deleteCacheTagList() {

    }
}
