package com.cloudy.linglingbang.activity.community.common;

import android.content.Context;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.community.common.holder.BasePostStaggeredGridViewHolder;
import com.cloudy.linglingbang.activity.community.common.holder.BasePostViewHolder;
import com.cloudy.linglingbang.model.postcard.PostCard;

import java.util.List;

/**
 * 交错帖子瀑布流的adapter
 *
 * <AUTHOR>
 * @date 2019-11-26
 */
public class BaseStaggeredGridPostAdapter extends BasePostAdapter {

    public BaseStaggeredGridPostAdapter(Context context, List<PostCard> data) {
        super(context, data);
        //展示关注等字段
        addFlags(PostFlagsEnum.SHOW_ATTENTION);
    }

    @Override
    protected int getItemLayoutRes(int viewType) {
//        switch (viewType) {
//            case PostCard.PostType.SHORT_VIDEO:
//                return getShortVideoItemLayoutRes();
//            case PostCard.PostType.VOTE:
//                return R.layout.item_vote_post;
//            default:
//                return getDefaultItemLayoutRes();
//        }
        return getDefaultItemLayoutRes();
    }

    @Override
    public int getItemViewType(int position) {
        int postTypeId = mData.get(position).getPostTypeIdOrNegative();
//        switch (postTypeId) {
//            case PostCard.PostType.SHORT_VIDEO:
//                return PostCard.PostType.SHORT_VIDEO;
//            case PostCard.PostType.VOTE:
//            case PostCard.PostType.QUESTIONNAIRE:
//                //投票或问卷均处理为投票特殊处理
//                return PostCard.PostType.VOTE;
//            default:
//                return PostCard.PostType.IMAGE_TEXT;
//        }
        return PostCard.PostType.IMAGE_TEXT;
    }

    @Override
    protected int getDefaultItemLayoutRes() {
        return R.layout.item_post_staggered_grid;
    }

    @Override
    protected BasePostViewHolder createDefaultViewHolderWithoutSetting(View itemView) {
        return new BasePostStaggeredGridViewHolder(itemView);
    }

}

