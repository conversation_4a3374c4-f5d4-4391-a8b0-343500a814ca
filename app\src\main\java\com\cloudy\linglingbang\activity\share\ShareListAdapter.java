package com.cloudy.linglingbang.activity.share;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.search.adapter.SearchUserAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.chat.GroupListBean;
import com.cloudy.linglingbang.model.chat.PostShareMessage;
import com.cloudy.linglingbang.model.user.User;

import java.util.List;

import androidx.lifecycle.MediatorLiveData;
import io.rong.imlib.model.Conversation;

/**
 * <AUTHOR>
 * @date 2022/4/12
 */
class ShareListAdapter extends BaseRecyclerViewAdapter<Object> {
    private final MediatorLiveData<Integer> resultLiveData;
    private final PostShareMessage postShareMessage;

    public ShareListAdapter(Context context, List<Object> data, MediatorLiveData<Integer> resultLiveData, PostShareMessage postShareMessage) {
        super(context, data);
        this.postShareMessage = postShareMessage;
        this.resultLiveData = resultLiveData;
        setOnItemClickListener((itemView, position) -> onItemClick(position));
    }

    private void onItemClick(int position) {
        Object obj = mData.get(position);
        String targetId = null;
        String name = null;
        String image = null;
        Conversation.ConversationType conversationType = null;
        if (obj instanceof User) {
            conversationType = Conversation.ConversationType.PRIVATE;
            targetId = ((User) obj).getUserIdStr();
            image = ((User) obj).getPhoto();
            name = ((User) obj).getNickname();
        } else if (obj instanceof GroupListBean) {
            conversationType = Conversation.ConversationType.GROUP;
            targetId = String.valueOf(((GroupListBean) obj).getGroupId());
            image = ((GroupListBean) obj).getGroupIcon();
            name = ((GroupListBean) obj).getGroupName();
        }
        if (!TextUtils.isEmpty(targetId)) {
            SharePreviewDialog sharePreviewDialog =
                    new SharePreviewDialog(mContext);
            sharePreviewDialog.setTargetId(targetId);
            sharePreviewDialog.setTitle(name);
            sharePreviewDialog.setImage(image);
            sharePreviewDialog.setConversationType(conversationType);
            sharePreviewDialog.setLiveData(resultLiveData);
            sharePreviewDialog.setShareMessage(postShareMessage);
            sharePreviewDialog.show();
        }
    }

    @Override
    protected BaseRecyclerViewHolder<Object> createViewHolder(View itemView) {
        return new UserViewHolder(itemView);
    }

    @Override
    protected BaseRecyclerViewHolder<Object> createViewHolderWithViewType(View itemView, int viewType) {
        if (viewType == R.layout.item_share_user_title) {
            return new TitleViewHolder(itemView);
        }
        return super.createViewHolderWithViewType(itemView, viewType);
    }

    @Override
    public int getItemViewType(int position) {
        if (mData.get(position) instanceof User) {
            return R.layout.item_share_search_user;
        } else if (mData.get(position) instanceof String) {
            return R.layout.item_share_user_title;
        }
        return R.layout.item_share_search_user;
    }

    @Override
    protected int getItemLayoutRes(int viewType) {
        return viewType;
    }

    static class UserViewHolder extends BaseRecyclerViewHolder<Object> {
        SearchUserAdapter.ViewHolder mViewHolder;

        public UserViewHolder(View itemView) {
            super(itemView);
            mViewHolder = new SearchUserAdapter.ViewHolder(itemView);
            initItemView(itemView);
        }

        @Override
        public void bindTo(Object o, int position) {
            super.bindTo(o, position);
            if (o instanceof User) {
                mViewHolder.bindTo((User) o, position);
            } else if (o instanceof GroupListBean) {
                User user = new User();
                user.setPhoto(((GroupListBean) o).getGroupIcon());
                user.setNickname(((GroupListBean) o).getGroupName());
                mViewHolder.bindTo(user, position);
                user = null;
            }
        }
    }

    static class TitleViewHolder extends BaseRecyclerViewHolder<Object> {
        TextView mTvTitle;

        public TitleViewHolder(View itemView) {
            super(itemView);
            mTvTitle = itemView.findViewById(R.id.tv_title);
        }

        @Override
        public void bindTo(Object o, int position) {
            super.bindTo(o, position);
            if (o != null) {
                mTvTitle.setText(o.toString());
            }
        }
    }
}
