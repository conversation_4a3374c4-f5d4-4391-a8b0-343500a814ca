package com.cloudy.linglingbang.activity.community.common.holder.square;

import android.view.View;

import com.cloudy.linglingbang.activity.community.common.holder.BasePostViewHolder;
import com.cloudy.linglingbang.activity.community.common.holder.PostBottomInfoViewHolder;
import com.cloudy.linglingbang.activity.community.common.holder.PostContentViewHolder;
import com.cloudy.linglingbang.activity.community.common.holder.PostExperienceViewHolder;
import com.cloudy.linglingbang.activity.community.common.holder.PostImageViewHolder;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.header.HeaderAndFooterWrapperAdapter;
import com.cloudy.linglingbang.app.widget.recycler.wrapper.BaseWrapperItemAdapter;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.user.Author;

import java.util.ArrayList;
import java.util.List;

import androidx.recyclerview.widget.RecyclerView;

/**
 * 带关注
 *
 * <AUTHOR>
 * @date 2018/8/19
 */
public class PostWithAttentionViewHolder extends BasePostViewHolder implements PostAuthorWithAttentionViewHolder.OnAttentionListener {
    public PostWithAttentionViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    protected void initChildViewHolder(View itemView) {
        mChildViewHolderList = new ArrayList<>();
        //替换了带关注的 header
        mChildViewHolderList.add(new PostAuthorWithAttentionViewHolder(itemView)
                .setOnAttentionListener(this));
        mChildViewHolderList.add(new PostExperienceViewHolder(itemView));
        mChildViewHolderList.add(new PostContentViewHolder(itemView));
        mChildViewHolderList.add(new PostImageViewHolder(itemView));
        mChildViewHolderList.add(new PostBottomInfoViewHolder(itemView));
    }

    @Override
    public boolean needSetAdapter() {
        return true;
    }

    @Override
    public void onAttention(String userIdStr, boolean attention) {
        updateAttention(mAdapter, userIdStr, attention);
    }

    public static void updateAttention(RecyclerView.Adapter<? extends RecyclerView.ViewHolder> adapter, String userIdStr, boolean attention) {
        //取出adapter
        RecyclerView.Adapter<? extends RecyclerView.ViewHolder> innerAdapter;
        if (adapter instanceof HeaderAndFooterWrapperAdapter) {
            innerAdapter = ((HeaderAndFooterWrapperAdapter) adapter).getInnerAdapter();
        } else {
            innerAdapter = adapter;
        }
        //取出数据
        List data = null;
        if (innerAdapter instanceof BaseRecyclerViewAdapter) {
            data = ((BaseRecyclerViewAdapter) innerAdapter).getData();
        } else if (innerAdapter instanceof BaseWrapperItemAdapter) {
            data = ((BaseWrapperItemAdapter) innerAdapter).getData();
        }
        if (data != null) {
            for (Object o : data) {
                if (o instanceof PostCard) {
                    Author author = ((PostCard) o).getAuthor();
                    if (author != null) {
                        if (userIdStr.equals(author.getUserIdStr())) {
                            UserUtils.updateUserAttention(author, attention);
                        }
                    }
                }
            }
            //调外部的 adapter
            adapter.notifyDataSetChanged();
        }
    }
}
