package com.cloudy.linglingbang.activity.fragment.store.home.model;

import com.cloudy.linglingbang.model.store.home.StoreLayoutComponent;
import com.cloudy.linglingbang.model.wrapper.WrapperModelWithType;

import java.util.List;

/**
 * 商城导航栏元素
 *
 * <AUTHOR>
 * @date 2020/5/8
 */
public class StoreHomeNavElementWrapper extends WrapperModelWithType<List<StoreLayoutComponent>> {

    private StoreHomeElementEnum mComponentEnum;

    public StoreHomeNavElementWrapper(List<StoreLayoutComponent> original, int type) {
        super(original, type);
        mComponentEnum = StoreHomeElementEnum.valueOf(type);
        if (mComponentEnum == null) {
            //为 null 赋一个默认值
            mComponentEnum = StoreHomeElementEnum.MID_BANNER;
        }
    }

    public int getType() {
        return mComponentEnum.getType();
    }

    public float getSpanRatio() {
        return mComponentEnum.getSpanRatio();
    }

    public int getLayoutResId() {
        return mComponentEnum.getLayoutResId();
    }

}
