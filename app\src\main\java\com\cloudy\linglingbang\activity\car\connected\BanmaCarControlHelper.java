package com.cloudy.linglingbang.activity.car.connected;

import android.content.Context;
import android.text.TextUtils;

import com.bangcle.CryptoTool;
import com.cloudy.linglingbang.activity.travel.model.CarStatus;
import com.cloudy.linglingbang.activity.travel.model.UnifyControlBtnStat;
import com.cloudy.linglingbang.activity.travel.model.banma.BluetoothKey;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.PreferenceUtil;
import com.cloudy.linglingbang.constants.EnvironmentConstant;
import com.cloudy.linglingbang.db.GreenDaoManager;
import com.cloudy.linglingbang.greendao.BluetoothKeyDao;
import com.cloudy.linglingbang.model.Message.PushExtra;
import com.cloudy.linglingbang.model.car.control.CarBindInfo;

import java.util.Map;

/**
 * 车控帮助类
 *
 * <AUTHOR>
 * @date 2019/3/22
 */
public class BanmaCarControlHelper {

    public static final int AIR_CONDITIONER_TIME = 10 * 1000 * 60;//空调制热时间

    public static final String COOL_KEY = "cool";
    public static final String HEAT_KEY = "heat";

    //最后一次更新时间
    public static final String LAST_UPDATE_TIME_KEY = "last_update";

    /**
     * 空调类型
     */
    public static class AcType {
        //空调制冷操作
        public static final int AC_TYPE_COOL = 1;
        //空调制热操作
        public static final int AC_TYPE_HEAT = 2;

    }

    /**
     * 保存的sp key
     */
    public static class ControlPreference {
        //安全码（最后一次更新时间，以及安全码的值，用逗号隔开）
        public static final String SP_KEY_SAFE_CODE = "control_safe_code";
        //安全码时间间隔
        public static final String SP_KEY_SAFE_CODE_TIME_INTERVAL = "safe_code_time_interval";
        //是否设置不再提醒
        public static final String SP_KEY_IS_NO_REMIND = "is_no_remind";

    }

    /**
     * 错误码
     */
    public static class ControlErrorCode {
        //安全码不正确
        public static final int SAFE_CODE_ERROR = 2400105;
        //安全码错误超过上限，需要重置
        public static final int SAFE_CODE_NEED_RESET = 2400108;
        //请更新车机版本，详情咨询客服
        public static final int SAFE_CODE_NEED_UPDATECARVERSION = 2402118;
    }

    /**
     * 操作的值
     */
    public static class ControlCode {
        /** 制冷 */
        public static final int COOLING = 1;
        /** 制热 */
        public static final int HEATING = 2;
        /** 关空调 */
        public static final int OFF_AC = 3;
        /** 开锁 */
        public static final int UNLOCK = 4;
        /** 上锁 */
        public static final int LOCK = 5;
        /** 开后备箱 */
        public static final int TRUNK = 6;
        /** 寻车 */
        public static final int FIND = 7;
    }

    /**
     * 用车人身份跳转信息
     */
    public static class CarUserJump {
        /** 跳转添加爱车页面 */
        public static final int JUMP_ADD_CAR = 0;
        /** 跳转爱车列表页面 */
        public static final int JUMP_LIST = 1;
        /** 用车人正常，可以车控 */
        public static final int JUMP_CAR_CONTROL = 2;

    }

    /**
     * 获取上次输入的安全码
     * 先获取安全码间隔时间，并获取上次安全码的校验时间=
     * 如果时间间隔小于设定的时间间隔，则返回上次的安全码信息
     */
    public static String getLastSafeCode(Context context, String vin) {
        //如果上次获取时间是0，则直接让用户输入安全码
        String safeCodeString = PreferenceUtil.getStringPreference(context, ControlPreference.SP_KEY_SAFE_CODE + vin, null);
        if (safeCodeString != null) {
            if (safeCodeString == null || !safeCodeString.contains(",")) {
                return null;
            }
            String[] safeCodeStringArray = safeCodeString.split(",");
            //上次获取时间
            long lastTime = Long.valueOf(safeCodeStringArray[0]);
            String lastSafeCode = safeCodeStringArray[1];
            //时间间隔,默认是5分钟
            long interval = PreferenceUtil.getIntPreference(context, ControlPreference.SP_KEY_SAFE_CODE_TIME_INTERVAL, 5) * 60 * 1000;
            //如果上次获取时间大于时间间隔
            if (AppUtil.getServerCurrentTime() - lastTime <= interval) {
                return lastSafeCode;
            } else {
                return null;
            }
        } else {
            return null;
        }

    }

    /**
     * 保存安全码
     */
    public static void saveSafeCode(Context context, String safeCode, String vin) {
        if (!TextUtils.isEmpty(safeCode)) {
            String safeCodeSp = AppUtil.getServerCurrentTime() + "," + safeCode;
            PreferenceUtil.putPreference(context, ControlPreference.SP_KEY_SAFE_CODE + vin, safeCodeSp);
        } else {
            PreferenceUtil.removePreference(context, ControlPreference.SP_KEY_SAFE_CODE + vin);
        }
    }

    /**
     * 清空安全码
     */
    public static void clearSafeCode(Context context, String vin) {
        saveSafeCode(context, null, vin);
    }

    /**
     * 保存安全码输入时间间隔
     */
    public static void saveSafeCodeInterval(Context context, int interval) {
        if (interval > 0) {
            PreferenceUtil.putPreference(context, ControlPreference.SP_KEY_SAFE_CODE_TIME_INTERVAL, interval);
        }
    }

    /**
     * 设置不再提醒
     *
     * @param type 类型，0为不在提醒刷新，1为不在提醒蓝牙钥匙开锁
     */
    public static void setNoRemind(Context context, int type) {
        PreferenceUtil.putPreference(context, ControlPreference.SP_KEY_IS_NO_REMIND + type, true);
    }

    /**
     * 是否不再提醒
     * true：不提醒
     */
    public static boolean isNeedNoRemind(Context context, int type) {
        return PreferenceUtil.getBooleanPreference(context, ControlPreference.SP_KEY_IS_NO_REMIND + type, false);
    }

    public static BluetoothKeyDao getBluetoothDao() {
        return GreenDaoManager.getInstance().getDaoSession().getBluetoothKeyDao();
    }

    /**
     * 保存蓝牙钥匙
     */
    public static void saveBluetoothKey(BluetoothKey bluetoothKey) {
        if (bluetoothKey == null) {
            return;
        }
        //如果存在，则修改保存的蓝牙钥匙
        BluetoothKeyDao bluetoothKeyDao = getBluetoothDao();
        BluetoothKey bluetoothKeyOld = getBluetoothDao().queryBuilder().where(BluetoothKeyDao.Properties.Vin.eq(bluetoothKey.getVin())).build().unique();
        if (bluetoothKeyOld != null) {
            bluetoothKeyOld.setAesKeyEncryption(CryptoTool.aesEncryptStringWithBase64(bluetoothKey.getAesKey(), EnvironmentConstant.AES_EKEY, null));
            bluetoothKeyOld.setKeyReferenceEncryption(CryptoTool.aesEncryptStringWithBase64(String.valueOf(bluetoothKey.getKeyReference()), EnvironmentConstant.AES_EKEY, null));
            bluetoothKeyOld.setSecretKeyEncryption(CryptoTool.aesEncryptStringWithBase64(String.valueOf(bluetoothKey.getSecretKey()), EnvironmentConstant.AES_EKEY, null));
            bluetoothKeyOld.setTboxMac(bluetoothKey.getTboxMac());
            bluetoothKeyOld.setTspUserIdEncryption(CryptoTool.aesEncryptStringWithBase64(bluetoothKey.getTspUserId(), EnvironmentConstant.AES_EKEY, null));
            bluetoothKeyDao.update(bluetoothKeyOld);
        } else {
            //否则则添加新的蓝牙钥匙
            bluetoothKeyDao.insertInTx(bluetoothKey);
        }
    }

    /**
     * 根据vin获取蓝牙钥匙
     */
    public static BluetoothKey findBluetoothKeyByVin(String vin) {
        if (vin != null) {
            BluetoothKey bluetoothKey = getBluetoothDao().queryBuilder().where(BluetoothKeyDao.Properties.Vin.eq(vin)).build().unique();
            //如果为空，则说明上次的字段不完整，或者上次是从上个版本迁移过来，重新请求
            if (bluetoothKey == null || TextUtils.isEmpty(bluetoothKey.getAesKeyEncryption())) {
                return null;
            }
            return bluetoothKey;
        }
        return null;
    }

    /**
     * 根据vin删除蓝牙钥匙
     */
    public static void deleteBluetoothKey(String vin) {
        if (vin == null) {
            return;
        }
        BluetoothKey bluetoothKey = findBluetoothKeyByVin(vin);
        if (bluetoothKey != null) {
            getBluetoothDao().delete(bluetoothKey);
        }
    }

    /**
     * 获取用车人状态
     *
     * @return Type：0，不是用车人，并且没有绑定的爱车；1，不是用车人，有绑定的爱车，或者是用车人，但是没有当前爱车（vin为空）；2，是用车人，有当前爱车，可以车控
     * 跳转：0，跳转添加爱车页面；1,跳转爱车列表页面；2，用车人正常，可以车控
     */
    public static int getCarUserStatus(CarBindInfo carBindInfo) {
//        if (carBindInfo == null) {
//            return CarUserJump.JUMP_ADD_CAR;
//        }
//        //如果是用车人
//        if (carBindInfo.getIsCarUser() == 1 || carBindInfo.getIsCommonCarUser() == 1) {
//            //如果vin不为空，则表示可以车控
//            if (!TextUtils.isEmpty(carBindInfo.getVin())) {
//                return CarUserJump.JUMP_CAR_CONTROL;
//            } else {
//                //如果vin为空，则说明有车辆，进入列表页面
//                return CarUserJump.JUMP_LIST;
//            }
//        } else {
//            if (carBindInfo.getBindingStatus() == 1 || carBindInfo.getWaitActiveGrantAuth() == 1) {
//                return CarUserJump.JUMP_LIST;
//            } else {
//                return CarUserJump.JUMP_ADD_CAR;
//            }
//        }
        return -1;
    }

    /**
     * 是否有默认爱车
     *
     * @param carBindInfo
     * @return
     */
    public static int getCarUserStStatus(CarBindInfo carBindInfo) {
        //如果是用车人
//        if (carBindInfo == null) {
//            return CarUserJump.JUMP_ADD_CAR;
//        }
//        if ((carBindInfo.getIsCarUser() == 1 || carBindInfo.getIsCommonCarUser() == 1)&&!TextUtils.isEmpty(carBindInfo.getVin())) {
//            //如果vin不为空，则表示可以车控
//            return CarUserJump.JUMP_CAR_CONTROL;
//        }else {
//            return CarUserJump.JUMP_ADD_CAR;
//        }
        return -1;
    }

    /**
     * 获取空调状态
     *
     * @return 0：关闭；1：制冷；2：制热
     */
    public static int getAirConditioningStatus(Context context, String vin) {
        if (AppUtil.getServerCurrentTime() <= getAcWorkStartTime(context, vin, AcType.AC_TYPE_COOL) + AIR_CONDITIONER_TIME) {
            return 1;
        } else if (AppUtil.getServerCurrentTime() <= getAcWorkStartTime(context, vin, AcType.AC_TYPE_HEAT) + AIR_CONDITIONER_TIME) {
            return 2;
        } else {
            return 0;
        }
    }

    /**
     * 获取空调制冷或者制热开始时间
     *
     * @return 开始时间
     */
    public static Long getAcWorkStartTime(Context context, String vin, int acType) {
        String[] startTimeAndCommandId = getStartTimeAndCommandId(context, vin, acType);
        if (startTimeAndCommandId != null) {
            return Long.valueOf(startTimeAndCommandId[0]);
        } else {
            return 0L;
        }
    }

    /**
     * 获取空调车控开始时间和commandId
     *
     * @return 数组第一位为开始时间的String，第二位为commandId
     */
    public static String[] getStartTimeAndCommandId(Context context, String vin, int acType) {
        String key = getAcKey(vin, acType);
        String startValue = PreferenceUtil.getStringPreference(context, key, null);
        if (startValue != null && startValue.contains(",")) {
            return startValue.split(",");
        } else {
            return null;
        }
    }

    /**
     * 保存空调开启时间（包含制冷和制热）
     */
    public static void saveStartAcTime(Context context, String vin, String commandId, int acType) {
        PreferenceUtil.putPreference(context, getAcKey(vin, acType), AppUtil.getServerCurrentTime() + "," + commandId);
    }

    /**
     * 清除空调时间
     */
    public static void clearAcTime(Context context, String vin, int acType) {
        PreferenceUtil.removePreference(context, getAcKey(vin, acType));
    }

    /**
     * 根据commandId清除空调计时操作
     */
    public static void clearAcTimeByCommandId(Context context, PushExtra pushExtra) {
//        if (pushExtra == null || pushExtra.getExtend() == null) {
//            return;
//        }
//        Map<String, String> carControlMap = pushExtra.getExtend();
//        String vin = carControlMap.get("vin");
//        String commandId = carControlMap.get("commandId");
//        String[] heatTimeAndCommandId = getStartTimeAndCommandId(context, vin, AcType.AC_TYPE_HEAT);
//        //获取制热的时间以及判断commandId是否相同,如果相同，则清除
//        if (heatTimeAndCommandId != null && heatTimeAndCommandId.length > 1 && heatTimeAndCommandId[1].equals(commandId)) {
//            if (pushExtra.getPushType() == PushExtra.PUSH_TYPE.PUSH_TYPE_POP_BY_BUSINESS && getAcWorkStartTime(context, vin, AcType.AC_TYPE_HEAT) + AIR_CONDITIONER_TIME > CommonUtil.getServerCurrentTime()) {
//                //弹窗
//                ReceiverMsgLab.showPushDialog(pushExtra);
//            }
//            clearAcTime(context, vin, AcType.AC_TYPE_HEAT);
//            return;
//        }
//        String[] coolTimeAndCommandId = getStartTimeAndCommandId(context, vin, AcType.AC_TYPE_COOL);
//        //获取制冷的时间以及判断commandId是否相同,如果相同，则清除
//        if (coolTimeAndCommandId != null && coolTimeAndCommandId.length > 1 && coolTimeAndCommandId[1].equals(commandId)) {
//            if (pushExtra.getPushType() == PushExtra.PUSH_TYPE.PUSH_TYPE_POP_BY_BUSINESS && getAcWorkStartTime(context, vin, AcType.AC_TYPE_COOL) + AIR_CONDITIONER_TIME > CommonUtil.getServerCurrentTime()) {
//                //弹窗
//                ReceiverMsgLab.showPushDialog(pushExtra);
//            }
//            clearAcTime(context, vin, AcType.AC_TYPE_COOL);
//        }
    }

    /**
     * 获取空调操作时间对应的key
     */
    public static String getAcKey(String vin, int acType) {
        return acType == AcType.AC_TYPE_HEAT ? getHeatKey(vin) : getCoolKey(vin);
    }

    /**
     * 设置最后一次更新时间
     */
    public static void setLastUpdateTime(Context context, long time) {
        PreferenceUtil.putPreference(context, LAST_UPDATE_TIME_KEY, time);
    }

    /**
     * 设置最后一次更新时间
     */
    public static long getLastUpdateTime(Context context) {
        return PreferenceUtil.getLongPreference(context, LAST_UPDATE_TIME_KEY, 0L);
    }

    /**
     * 获取加热的key
     */
    public static String getHeatKey(String vin) {
        return vin + HEAT_KEY;
    }

    /**
     * 获取制冷的的key
     */
    public static String getCoolKey(String vin) {
        return vin + COOL_KEY;
    }

    /**
     * 斑马车联网和博泰车联网的转换
     *
     * @param status
     * @return
     */
    public static CarStatus transformCarStatus(Map<String, String> status) {
        CarStatus carStatus = new CarStatus();
        if (status != null) {
            if (!TextUtils.isEmpty(status.get(UnifyControlBtnStat.ServiceNameCodeForBanma.SERVICE_AC))) {
                carStatus.setShowAir(true);
                carStatus.setAirState(Integer.parseInt(status.get(UnifyControlBtnStat.ServiceNameCodeForBanma.SERVICE_AC)));
            }
            if (!TextUtils.isEmpty(status.get(UnifyControlBtnStat.ServiceNameCodeForBanma.SERVICE_ENGINE))) {
                carStatus.setShowEngine(true);
                carStatus.setEngineState(Integer.parseInt(status.get(UnifyControlBtnStat.ServiceNameCodeForBanma.SERVICE_ENGINE)));
            }
            if (!TextUtils.isEmpty(status.get(UnifyControlBtnStat.ServiceNameCodeForBanma.SERVICE_DOOR))) {
                carStatus.setShowDoor(true);
                carStatus.setDoorState("0".equals(status.get(UnifyControlBtnStat.ServiceNameCodeForBanma.SERVICE_DOOR)) ? 1 : 0);

            }
            if (!TextUtils.isEmpty(status.get(UnifyControlBtnStat.ServiceNameCodeForBanma.SERVICE_TAIL_DOOR))) {
                carStatus.setShowTrunk(true);
                carStatus.setTrunkState(Integer.parseInt(status.get(UnifyControlBtnStat.ServiceNameCodeForBanma.SERVICE_TAIL_DOOR)));
            }
            if (!TextUtils.isEmpty(status.get(UnifyControlBtnStat.ServiceNameCodeForBanma.SERVICE_TOP_WINDOW))) {
                carStatus.setShowSkylight(true);
                carStatus.setSkylightState("0".equals(status.get(UnifyControlBtnStat.ServiceNameCodeForBanma.SERVICE_TOP_WINDOW)) ? 1 : 0);
            }
            if (!TextUtils.isEmpty(status.get(UnifyControlBtnStat.ServiceNameCodeForBanma.SERVICE_WINDOW))) {
                carStatus.setShowWindow(true);
                carStatus.setWindowState("0".equals(status.get(UnifyControlBtnStat.ServiceNameCodeForBanma.SERVICE_WINDOW)) ? 1 : 0);
            }
        }
        return carStatus;

    }

}
