package com.cloudy.linglingbang.activity.store.commodity.dialog;

import android.content.Context;
import android.view.WindowManager;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.widget.banner.AdRoundImageView;
import com.cloudy.linglingbang.app.widget.dialog.alert.BaseAlertDialog;

/**
 * sku弹窗 属性备注说明
 *
 * <AUTHOR>
 * @date 2022/10/19
 */
public class SkuImagePreDialog extends BaseAlertDialog {
    private String imageUrl;
    private AdRoundImageView adRoundImageView;

    public SkuImagePreDialog(Context context, String url) {
        super(context);
        imageUrl = url;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    @Override
    protected int getDefaultLayoutResId() {
        return R.layout.dialog_sku_pre_image;
    }

    @Override
    protected void initView() {
        super.initView();
        setCanceledOnTouchOutside(false);
        setCancelable(false);
        adRoundImageView = findViewById(R.id.iv_image);
        findViewById(R.id.iv_close).setOnClickListener(v -> dismiss());

    }

    @Override
    public void show() {
        super.show();
        WindowManager.LayoutParams params = getWindow().getAttributes();
        params.height = WindowManager.LayoutParams.MATCH_PARENT;
        params.width = WindowManager.LayoutParams.MATCH_PARENT;
        getWindow().setAttributes(params);
        adRoundImageView.createImageLoad(imageUrl)
                .load();
    }

    @Override
    protected boolean isNeedSetButton() {
        return false;
    }
}
