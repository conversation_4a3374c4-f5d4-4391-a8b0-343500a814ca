package com.cloudy.linglingbang.activity.service.newenergy;

import android.app.Dialog;
import android.content.Context;
import android.util.TypedValue;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog2;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.server.CarHealthyCheckInfo;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 车辆体检里边的item的adapter
 *
 * <AUTHOR>
 * @date 2019-07-16
 */
public class CarHealthyItemAdapter extends BaseRecyclerViewAdapter<CarHealthyCheckInfo.ItemListBean> {

    public CarHealthyItemAdapter(Context context, List<CarHealthyCheckInfo.ItemListBean> data) {
        super(context, data);
    }

    @Override
    protected BaseRecyclerViewHolder<CarHealthyCheckInfo.ItemListBean> createViewHolder(View itemView) {
        return new ViewHolder(itemView);
    }

    @Override
    protected int getItemLayoutRes(int viewType) {
        return R.layout.item_car_healthy_item;
    }

    class ViewHolder extends BaseRecyclerViewHolder<CarHealthyCheckInfo.ItemListBean> {

        @BindView(R.id.tv_left)
        TextView mTvLeft;

        @BindView(R.id.tv_right)
        TextView mTvRight;

        @BindView(R.id.iv_help)
        ImageView mIvHelp;

        public ViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            ButterKnife.bind(this, itemView);
        }

        @Override
        public void bindTo(CarHealthyCheckInfo.ItemListBean itemListBean, int position) {
            super.bindTo(itemListBean, position);
            if (position == 0) {
                mTvLeft.setTextColor(mContext.getResources().getColor(R.color.black));
                mTvLeft.setTextSize(TypedValue.COMPLEX_UNIT_PX, mContext.getResources().getDimension(R.dimen.activity_set_text_32));
                mTvRight.setTextSize(TypedValue.COMPLEX_UNIT_PX, mContext.getResources().getDimension(R.dimen.activity_set_text_32));
            } else {
                mTvLeft.setTextColor(mContext.getResources().getColor(R.color.color_929292));
                mTvLeft.setTextSize(TypedValue.COMPLEX_UNIT_PX, mContext.getResources().getDimension(R.dimen.activity_set_text_24));
                mTvRight.setTextSize(TypedValue.COMPLEX_UNIT_PX, mContext.getResources().getDimension(R.dimen.activity_set_text_24));
            }
            if (1 == itemListBean.getItemStatus()) {
                mTvRight.setTextColor(mContext.getResources().getColor(R.color.color_384967));
            } else if (0 == itemListBean.getItemStatus()) {
                mTvRight.setTextColor(mContext.getResources().getColor(R.color.color_384967));
            } else {
                mTvRight.setTextColor(mContext.getResources().getColor(R.color.black));
            }
            mTvLeft.setText(itemListBean.getItemName());
            mTvRight.setText(itemListBean.getItemStatusStr());
            if (itemListBean.getItemHint() != null) {
                mIvHelp.setVisibility(View.VISIBLE);
                mIvHelp.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        Dialog dialog = new CommonAlertDialog2(mContext, itemListBean.getItemHint().getTitle(), itemListBean.getItemHint().getContent(), itemListBean.getItemHint().getButtonDesc(), null, null, null);
                        dialog.show();
                    }
                });
            } else {
                mIvHelp.setVisibility(View.GONE);
            }
        }
    }
}
