package com.cloudy.linglingbang.activity.share;

import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.app.util.InputMethodUtils;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.model.chat.PostShareMessage;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;

import java.util.ArrayList;
import java.util.List;

import androidx.lifecycle.MediatorLiveData;
import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 分享:用户搜索页面
 *
 * <AUTHOR>
 * @date 2022/4/13
 */
public class ShareSearchUserActivity extends BaseRecyclerViewRefreshActivity<Object> {
    private MediatorLiveData<Integer> mResultLiveData;

    private EditText et_content;
    private String mSearchTxt;
    /**
     * 正常关闭页面
     */
    public final static int RESULT_FINISH = 0x99;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_search_share_user);
    }

    @Override
    protected void initialize() {
        super.initialize();
        if (getIntentExtra(null) == null) {
            onIntentExtraError();
            return;
        }
        mResultLiveData = new MediatorLiveData<>();
        mResultLiveData.observe(this, status -> {
            if (status == null) {
                return;
            }
            if (status == 0) {
                //发送成功 分享成功
                setResult(RESULT_OK);
                finish();
            } else if (status == 1) {
                // 取消分享
                setResult(RESULT_CANCELED);
                finish();
            } else {
                ToastUtil.showMessage(getApplicationContext(), "分享失败");
            }
        });

        et_content = findViewById(R.id.et_content);
        //设置
        et_content.setHint(getText(R.string.txt_share_search_user));
        et_content.setImeOptions(EditorInfo.IME_ACTION_SEARCH);
        et_content.setOnEditorActionListener((v, actionId, event) -> {
            boolean handled = false;
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                checkAndSearch();
                handled = true;
            }
            return handled;
        });
        InputMethodUtils.showInputMethodByPostDelayed(et_content);
        findViewById(R.id.tv_search).setOnClickListener(v -> onBack());
    }

    @Override
    protected void onBack() {
        setResult(RESULT_FINISH);
        super.onBack();
    }

    private void checkAndSearch() {
        mSearchTxt = et_content.getText().toString();
        if (TextUtils.isEmpty(mSearchTxt)) {
            return;
        }
        InputMethodUtils.hideInputMethod(this, et_content);
        if (getRefreshController() != null) {
            getRefreshController().manualRefresh();
        }
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<Object> list) {
        return new ShareListAdapter(this, list, mResultLiveData, (PostShareMessage) getIntentExtra(null));
    }

    @Override
    public Observable<BaseResponse<List<Object>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return service2.searchUser(3, mSearchTxt, pageNo, pageSize)
                .map(listBaseResponse -> {
                    if (listBaseResponse.getData() == null) {
                        return listBaseResponse.cloneWithData(new ArrayList<>());
                    } else {
                        List<Object> list = new ArrayList<>(listBaseResponse.getData());
                        return listBaseResponse.cloneWithData(list);
                    }
                });
    }

    @Override
    public RefreshController<Object> createRefreshController() {
        return new RefreshController<Object>(this) {
            @Override
            public void initViews(View rootView) {
                setEmptyBackgroundColorId(R.color.transparent);
                setEmptyString(rootView.getResources().getString(R.string.txt_share_list_empty_tip));
                super.initViews(rootView);
                swipeToLoadLayout.setBackgroundColor(Color.TRANSPARENT);
            }

            @Override
            public int getErrorImageResId() {
                return R.drawable.ic_post_share_place;
            }

            @Override
            public int getEmptyImageResId() {
                return getErrorImageResId();
            }

            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return null;
            }

            @Override
            protected boolean loadDataAfterInitViews() {
                return false;
            }
        };
    }
}
