package com.cloudy.linglingbang.activity.fragment.store.home;

import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.fragment.store.youpin.StoreHomeJumpPageUtils;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.image.ImageLoadUtils;
import com.cloudy.linglingbang.app.widget.imagetext.ImageTextAdapter;
import com.cloudy.linglingbang.app.widget.imagetext.ImageTextBean;
import com.cloudy.linglingbang.app.widget.imagetext.ImageTextViewHolder;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.app.widget.recycler.DividerItemDecoration;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.store.home.StoreLayoutElement;

import java.util.ArrayList;
import java.util.List;

import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 优品优惠活动列表
 *
 * <AUTHOR>
 * @date 2020/5/9
 */
public class StoreHomeDiscountActivity extends BaseRecyclerViewRefreshActivity<Object> {
    List<StoreLayoutElement> mList;
    private String title;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        title = getIntent().getStringExtra(IntentUtils.INTENT_EXTRA_TITLE);
        setMiddleTitle(title);
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List list) {
        Adapter adapter = new Adapter(this, list);
        adapter.setOnItemClickListener(new BaseRecyclerViewHolder.OnItemClickListener() {
            @Override
            public void onItemClick(View itemView, int position) {
                if (mList.isEmpty()) {
                    return;
                }
                StoreHomeJumpPageUtils.goToPageByElement(StoreHomeDiscountActivity.this, mList.get(position), new SensorsUtils.StoreHomeAnchor(title + "更多", title + "更多"));
            }
        });
        return adapter;
    }

    @Override
    public RefreshController<Object> createRefreshController() {
        return new RefreshController<Object>(this) {
            @Override
            public void initViews(View rootView) {
                super.initViews(rootView);
                int px40 = getContext().getResources().getDimensionPixelOffset(R.dimen.normal_40);
                recyclerView.setBackgroundColor(Color.TRANSPARENT);
                swipeToLoadLayout.setBackgroundColor(Color.TRANSPARENT);
                recyclerView.setPadding(px40, px40 / 2, px40, px40);
                setShowBottomToast(false);
            }

            @Override
            protected boolean showRefreshingWhenLoadDataAfterInitViews() {
                return false;
            }

            @Override
            protected boolean isRefreshEnable() {
                return false;
            }

            @Override
            protected boolean isLoadMoreEnable() {
                return false;
            }

            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return new DividerItemDecoration(context, DividerItemDecoration.VERTICAL_LIST, R.drawable.item_divider_40);
            }

            @Override
            public void getListData(int page) {
                super.getListData(page);
                mData.clear();
                Object object = IntentUtils.getExtra(getIntent(), null);
                if (object instanceof List) {
                    List list = (List) object;
                    if (mList == null) {
                        mList = new ArrayList<>();
                    }
                    for (Object o : list) {
                        if (o instanceof StoreLayoutElement) {
                            mList.add((StoreLayoutElement) o);
                            mData.add(new ImageTextBean(((StoreLayoutElement) o).getTitle(), ((StoreLayoutElement) o).getImage()));
                        }
                    }
                }
                onRefreshComplete();
            }
        };
    }

    @Override
    public Observable<BaseResponse<List<Object>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return null;
    }

    private static class Adapter extends ImageTextAdapter {

        public Adapter(Context context, List data) {
            super(context, data);
        }

        @Override
        protected int getItemLayoutRes(int viewType) {
            return R.layout.item_store_home_element_slide;
        }

        @Override
        protected BaseRecyclerViewHolder createViewHolder(View itemView) {
            return new Adapter.ViewHolder(itemView);
        }

        static class ViewHolder extends ImageTextViewHolder {

            public ViewHolder(View itemView) {
                super(itemView);
            }

            @Override
            public void bindTo(ImageTextBean imageTextBean, int position) {
                ImageLoadUtils.createImageLoad(mImageView, imageTextBean.getImageUrl(), R.drawable.ic_common_place_holder)
                        .load();
            }
        }
    }
}
