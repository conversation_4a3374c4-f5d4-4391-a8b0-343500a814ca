package com.cloudy.linglingbang.activity.fragment.store.hotel.order;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.aspsine.swipetoloadlayout.OnRefreshListener;
import com.aspsine.swipetoloadlayout.SwipeToLoadLayout2;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.fragment.store.hotel.HotelDetailActivity;
import com.cloudy.linglingbang.app.widget.dialog.hotel.HotelUtil;
import com.cloudy.linglingbang.app.widget.item.CommonItem;
import com.cloudy.linglingbang.model.hotel.OrderInfo;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 订单详情
 *
 * <AUTHOR>
 * @date 2022/3/18
 */
public class OrderDetailActivity extends BaseActivity implements OnRefreshListener {
    private Long mOrderId;
    @BindView(R.id.tv_order_num)
    TextView mTvOrderNum;
    @BindView(R.id.tv_order_status)
    TextView mTvOrderStatus;
    @BindView(R.id.item_order_total_price)
    CommonItem mItemOrderTotalPrice;
    @BindView(R.id.item_order_pay_price)
    CommonItem mItemOrderPayPrice;
    @BindView(R.id.item_order_deposit_price)
    CommonItem mItemOrderDepositPrice;
    @BindView(R.id.tv_hotel_name)
    TextView mTvHotelName;
    @BindView(R.id.item_room_style)
    CommonItem mItemRoomStyle;
    @BindView(R.id.item_in_room_data)
    CommonItem mItemInRoomData;
    @BindView(R.id.item_out_room_data)
    CommonItem mItemOutRoomData;
    @BindView(R.id.item_in_room_name)
    CommonItem mItemInRoomName;
    @BindView(R.id.item_phone)
    CommonItem mItemPhone;
    @BindView(R.id.item_order_num)
    CommonItem mItemOrderNum;
    @BindView(R.id.item_order_data)
    CommonItem mItemOrderData;
    @BindView(R.id.tv_cancel_rule)
    TextView mTvCancelRule;
    @BindView(R.id.tv_tip)
    TextView mTvTip;
    @BindView(R.id.ll_cancel_rule)
    LinearLayout mLlCancelRule;
    @BindView(R.id.ll_tip)
    LinearLayout mLlTip;
    @BindView(R.id.tv_cancel_order)
    TextView mTvCancelOrder;

    @BindView(R.id.swipeToLoadLayout)
    SwipeToLoadLayout2 mSwipeToLoadLayout2;

    private OrderInfo mOrderInfo;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_jinjiang_order_detail);
    }

    @Override
    protected void initialize() {
        mOrderId = (Long) getIntentExtra(null);
        if (mOrderId == null) {
            return;
        }
        requestOrderDetail();
        mSwipeToLoadLayout2.setOnRefreshListener(this);

    }

    private void requestOrderDetail() {
        L00bangRequestManager2.getServiceInstance()
                .hotelOrderDetail(mOrderId)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<OrderInfo>(this) {
                    @Override
                    public void onSuccess(OrderInfo orderInfo) {
                        super.onSuccess(orderInfo);
                        if (mSwipeToLoadLayout2 != null && mSwipeToLoadLayout2.isRefreshing()) {
                            mSwipeToLoadLayout2.setRefreshing(false);
                        }
                        mOrderInfo = orderInfo;
                        mTvOrderNum.setText(getString(R.string.order_detail_order_num, orderInfo.getOrderCode()));
                        mTvOrderStatus.setText(orderInfo.getOrderStatusName());
                        mItemOrderTotalPrice.getTvRight().setText(getResources().getString(R.string.store_commodity_price, orderInfo.getOrderPriceStr()));
                        mItemOrderPayPrice.getTvRight().setText(getResources().getString(R.string.store_commodity_price, orderInfo.getReachStorePriceStr()));

                        if (orderInfo.getPayType().equals(1)) {//1是现金支付
                            mItemOrderDepositPrice.getTvRight().setText(orderInfo.getDepositPrice());
                        } else {
                            mItemOrderDepositPrice.getTvRight().setText(getResources().getString(R.string.order_detail_ling_value, orderInfo.getLingValueStr()));
                        }
                        mTvHotelName.setText(orderInfo.getInnName());
                        mItemRoomStyle.getTvRight().setText(orderInfo.getRoomTypeName() + " " + orderInfo.getRoomCount() + " 间" +
                                "");
                        mItemInRoomData.getTvRight().setText(orderInfo.getDtArrorig());
                        mItemOutRoomData.getTvRight().setText(orderInfo.getDtDeporig());
                        mItemInRoomName.getTvRight().setText(orderInfo.getGuestName());
                        mItemPhone.getTvRight().setText(orderInfo.getGuestMobile());
                        mItemOrderNum.getTvRight().setText(orderInfo.getOrderCode());
                        mItemOrderData.getTvRight().setText(orderInfo.getOrderTime());
                        if (TextUtils.isEmpty(orderInfo.getCancelRule())) {
                            mLlCancelRule.setVisibility(View.GONE);
                        } else {
                            mLlCancelRule.setVisibility(View.VISIBLE);
                            mTvCancelRule.setText(orderInfo.getCancelRule());
                        }
                        if (TextUtils.isEmpty(orderInfo.getTip())) {
                            mLlTip.setVisibility(View.GONE);
                        } else {
                            mLlTip.setVisibility(View.VISIBLE);
                            mTvTip.setText(orderInfo.getTip());
                        }
                        if (orderInfo.getOrderStatus() == 2 || orderInfo.getOrderStatus() == 1) {
                            mTvCancelOrder.setVisibility(View.VISIBLE);
                        } else {
                            mTvCancelOrder.setVisibility(View.GONE);
                        }

                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                        if (mSwipeToLoadLayout2 != null && mSwipeToLoadLayout2.isRefreshing()) {
                            mSwipeToLoadLayout2.setRefreshing(false);
                        }
                    }
                });

    }

    public static void startActivity(Context context, Long orderId) {
        IntentUtils.startActivity(context, OrderDetailActivity.class, orderId);
    }

    @OnClick(R.id.tv_cancel_order)
    void click() {
        requestCancelOrder(mOrderId);
    }

    @OnClick(R.id.tv_hotel_detail)
    void clickHotelDetail() {
        HotelDetailActivity.startActivity(this, mOrderInfo.getInnId(), 0, 0);
    }

    @OnClick(R.id.tv_hotel_phone)
    void clickHotelPhone() {
        HotelUtil.showCallDialogOrToCall(mOrderInfo.getInnPhone(), this);

    }

    private void requestCancelOrder(Long orderId) {
        L00bangRequestManager2.getServiceInstance()
                .cancelOrder(orderId)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<Boolean>(this) {
                    @Override
                    public void onSuccess(Boolean b) {
                        super.onSuccess(b);
                        //刷新数据
                        requestOrderDetail();
                    }
                });
    }

    @Override
    public void onRefresh() {
        requestOrderDetail();
    }
}
