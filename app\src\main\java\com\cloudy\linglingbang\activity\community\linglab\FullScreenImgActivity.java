package com.cloudy.linglingbang.activity.community.linglab;

import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.activity.community.linglab.viewmodel.ChooseImgViewModel;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.PreferenceUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.widget.tag.RandomDragTagLayout;
import com.cloudy.linglingbang.model.community.Topic;
import com.cloudy.linglingbang.model.postcard.PostCommodity;
import com.cloudy.linglingbang.model.tag.LingLabImageBean;
import com.cloudy.linglingbang.model.user.User;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.lifecycle.ViewModelProvider;
import androidx.viewpager.widget.ViewPager;

/**
 * 删除图片的弹窗
 *
 * <AUTHOR>
 * @date 2021/9/6
 */
public class FullScreenImgActivity extends BaseActivity {

    public static final String EXTRA_IMAGE_INDEX = "image_index";
    public static final String EXTRA_IMAGE_URLS = "image_urls";
    public static final String EXTRA_TOPIC = "topic";

    private ViewPager mViewPager;
    private ImageView mIvBack;
    private TextView mTitle;

    private ArrayList<LingLabImageBean> mList = new ArrayList<>();
    private int currentPosition;
    private ImagePagerAdapter imgPagerAdapter;

    public int tagNum = 0;

    private ChooseImgViewModel mChooseImgViewModel;

    private Topic mTopic;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_full_screen_img);
    }

    @Override
    protected void initialize() {
        mChooseImgViewModel = new ViewModelProvider(this, new ViewModelProvider.NewInstanceFactory()).get(ChooseImgViewModel.class);
        currentPosition = getIntent().getIntExtra(EXTRA_IMAGE_INDEX, 0);
        mList = getIntent().getParcelableArrayListExtra(EXTRA_IMAGE_URLS);
        mTopic = (Topic) getIntent().getSerializableExtra(EXTRA_TOPIC);
        mViewPager = findViewById(R.id.viewPager);
        mViewPager.setOffscreenPageLimit(4);
        mIvBack = findViewById(R.id.iv_back);
        mTitle = findViewById(R.id.tv_title);

        mTitle.setText(currentPosition + 1 + "/" + mList.size());
        imgPagerAdapter = new ImagePagerAdapter(getSupportFragmentManager(), mList);
        mViewPager.setAdapter(imgPagerAdapter);
        mViewPager.setCurrentItem(currentPosition);


        mIvBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                onBackPressed();
            }
        });
        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                mTitle.setText(position + 1 + "/" + mList.size());
            }

            @Override
            public void onPageSelected(int position) {
                imgPagerAdapter.getFragment(position).tagLayout.cancelEditState(imgPagerAdapter.getFragment(position).tagLayout);
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
        if (mList != null) {
            for (LingLabImageBean lingLabImageBean : mList) {
                String json = PreferenceUtil.getStringPreference(this, lingLabImageBean.getImgPath() + "_" + User.shareInstance().getUserIdStr() + "_" + lingLabImageBean.getTag(), null);
                if (TextUtils.isEmpty(json)) {
                    continue;
                }
                try {
                    List<PostCommodity> list = new Gson().fromJson(json, new TypeToken<List<PostCommodity>>() {}.getType());
                    tagNum += list.size();
                } catch (Exception ignored) {
                }
            }
        }
    }

    private static class ImagePagerAdapter extends FragmentPagerAdapter {

        public List<LingLabImageBean> fileList;
        private final Map<Integer, FullScreenImgFragment> map = new HashMap<>();

        public ImagePagerAdapter(FragmentManager fm, List<LingLabImageBean> fileList) {
            super(fm);
            this.fileList = fileList;
        }

        @Override
        public int getCount() {
            return fileList == null ? 0 : fileList.size();
        }

        @Override
        public Fragment getItem(int position) {
            FullScreenImgFragment fullScreenImgFragment = FullScreenImgFragment.newInstance(fileList.get(position).getImgPath(), String.valueOf(fileList.get(position).getTag()));
            map.put(position, fullScreenImgFragment);
            return fullScreenImgFragment;
        }

        @NonNull
        @Override
        public Object instantiateItem(@NonNull ViewGroup container, int position) {
            return super.instantiateItem(container, position);
        }

        @Override
        public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
            super.destroyItem(container, position, object);
            //map.remove(position);
        }

        public FullScreenImgFragment getFragment(int key) {
            return map.get(key);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        imgPagerAdapter.getFragment(mViewPager.getCurrentItem()).onActivityResult(requestCode, resultCode, data);
        super.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    public void onBackPressed() {
        for (int i = 0; i < imgPagerAdapter.getCount(); i++) {
            if (imgPagerAdapter.getFragment(i) == null) {
                continue;
            }
            RandomDragTagLayout randomDragTagLayout = imgPagerAdapter.getFragment(i).tagLayout;
            int count = imgPagerAdapter.getFragment(i).tagLayout.getChildCount();
            if (count > 1) {
                if (randomDragTagLayout.isHasOver()) {
                    ToastUtil.showMessage(FullScreenImgActivity.this, "标记不要重叠哦~");
                    return;
                }
            }
            List<PostCommodity> list = imgPagerAdapter.getFragment(i).saveTag();
            PreferenceUtil.putPreference(this, mList.get(i).getImgPath() + "_" + User.shareInstance().getUserIdStr() + "_" + mList.get(i).getTag(), new Gson().toJson(list));
        }
        if (!AppUtil.isExistMainActivity(CommunityDoPostLingLabActivity.class, this)) {
            CommunityDoPostLingLabActivity.startActivity(this, mList, mTopic);
        }
        finish();
        super.onBackPressed();
    }

}
