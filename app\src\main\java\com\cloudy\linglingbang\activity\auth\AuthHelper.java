package com.cloudy.linglingbang.activity.auth;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;

import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.model.auth.AuthClient;
import com.cloudy.linglingbang.model.auth.AuthCode;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;

/**
 * 认证工具
 *
 * <AUTHOR>
 * @date 2019/3/4
 */
public class AuthHelper {
    /**
     * 取消
     */
    private static final String ERROR_CODE_USER_CANCEL = "161021";
    /**
     * 参数错误
     */
    private static final String ERROR_CODE_PARAM_ERROR = "113200";

    /**
     * 从 intent 解析的客户端信息
     */
    private AuthClient mSourceAuthClient;
    private AuthCallback mAuthCallback;

    public AuthHelper(Activity activity, AuthCallback authCallback) {
        mSourceAuthClient = AuthHelper.parseIntent(activity.getIntent());
        mAuthCallback = authCallback;
        if (mSourceAuthClient == null) {
            authFail(activity, ERROR_CODE_PARAM_ERROR, "请求参数不合法，请检查参数");
        }
    }

    public static AuthClient parseIntent(Intent intent) {
        if (intent == null) {
            return null;
        }

        String action = intent.getAction();
        if (!Intent.ACTION_VIEW.equals(action)) {
            return null;
        }

        Uri uri = intent.getData();
        if (uri == null) {
            return null;
        }

        String host = uri.getHost();
        if (host == null || host.length() == 0) {
            return null;
        }

        if (!host.equalsIgnoreCase("authLogin")) {
            return null;
        }

        String redirectUri = uri.getQueryParameter("redirect_uri");
        if (TextUtils.isEmpty(redirectUri)) {
            return null;
        }
        String clientId = uri.getQueryParameter("client_id");
        if (TextUtils.isEmpty(clientId)) {
            return null;
        }
        String state = uri.getQueryParameter("state");
        return new AuthClient(clientId, redirectUri, state);
    }

    /**
     * 获取客户端信息
     */
    public void getAuthClientInfo(Context context) {
        if (mSourceAuthClient != null) {
            String clientId = mSourceAuthClient.getClientId();
            if (!TextUtils.isEmpty(clientId)) {
                //获取信息
                L00bangRequestManager2.getServiceInstance()
                        .getAuthClientInfo(clientId)
                        .compose(L00bangRequestManager2.<AuthClient>setSchedulers())
                        .subscribe(new ProgressSubscriber<AuthClient>(context) {
                            @Override
                            public void onSuccess(AuthClient authClient) {
                                super.onSuccess(authClient);
                                if (mAuthCallback != null) {
                                    mAuthCallback.onGetAuthClientInfo(authClient);
                                }
                            }

                            @Override
                            public void onFailure(Throwable e) {
                                super.onFailure(e);
                            }
                        });
            }
        }
    }

    /**
     * 点击授权，不需要等待信息返回，可以直接点击授权
     */
    public void authorize(final Activity activity) {
        if (!UserUtils.hasLogin()) {
            //登录
            AppUtil.goLogin(activity);
            return;
        }
        L00bangRequestManager2.getServiceInstance()
                .getAuthCode(mSourceAuthClient.getResponseType(), mSourceAuthClient.getClientId(), mSourceAuthClient.getState(), mSourceAuthClient.getRedirectUri())
                .compose(L00bangRequestManager2.<AuthCode>setSchedulers())
                .subscribe(new ProgressSubscriber<AuthCode>(activity) {
                    @Override
                    public void onSuccess(AuthCode authCode) {
                        super.onSuccess(authCode);
                        authSuccess(activity, authCode.getCode());
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                    }
                });
    }

    //授权相关

    /**
     * 授权成功
     */
    public void authSuccess(Activity activity, String code) {
        setDataAndReturn(activity, code, null, null);
    }

    /**
     * 取消授权
     */
    public void authCancel(Activity activity) {
        authFail(activity, ERROR_CODE_USER_CANCEL, "用户取消授权");
    }

    /**
     * 认证失败
     */
    public void authFail(Activity activity, String errorCode, String errorMessage) {
        setDataAndReturn(activity, null, errorCode, errorMessage);
    }

    public void setDataAndReturn(Activity activity, String code, String errorCode, String errorMessage) {
        //先执行 finish 使 SingleTop 等有效
        activity.finish();
        if (mSourceAuthClient != null) {
            String redirectUri = mSourceAuthClient.getRedirectUri();
            if (redirectUri != null) {
                Uri.Builder builder = Uri.parse(redirectUri)
                        .buildUpon();
                if (code != null) {
                    builder.appendQueryParameter("code", code);
                }
                if (errorCode != null) {
                    builder.appendQueryParameter("errorCode", errorCode);
                }
                if (errorMessage != null) {
                    builder.appendQueryParameter("errorMessage", errorMessage);
                }
                builder.appendQueryParameter("state", mSourceAuthClient.getState());

                Intent intent = new Intent(Intent.ACTION_VIEW, builder.build());
                try {
                    activity.startActivity(intent);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public interface AuthCallback {
        void onGetAuthClientInfo(AuthClient authClient);
    }
}
