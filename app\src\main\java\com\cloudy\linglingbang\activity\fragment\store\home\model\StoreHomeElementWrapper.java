package com.cloudy.linglingbang.activity.fragment.store.home.model;

import com.cloudy.linglingbang.model.WrapperModel;
import com.cloudy.linglingbang.model.store.home.StoreLayoutElement;

/**
 * 商城组件元素
 *
 * <AUTHOR>
 * @date 2018/10/18
 */
public class StoreHomeElementWrapper extends WrapperModel<StoreLayoutElement> {
    /**
     * 在组件中的位置
     * 该 index 可以用来区分在同一组件中不同的位置，有展示的区别
     */
    private int mIndexInComponent;
    private StoreHomeElementEnum mComponentEnum;
    /**
     * 流水号
     */
    private long layoutComponentId;

    public StoreHomeElementWrapper(StoreLayoutElement original) {
        this(original, 0);
    }

    public StoreHomeElementWrapper(StoreLayoutElement original, int indexInComponent) {
        super(original);
        mIndexInComponent = indexInComponent;
        mComponentEnum = StoreHomeElementEnum.valueOf(original.getShowModule());
        if (mComponentEnum == null) {
            //为 null 赋一个默认值
            mComponentEnum = StoreHomeElementEnum.MID_BANNER;
        }
    }

    public void setIndexInComponent(int indexInComponent) {
        mIndexInComponent = indexInComponent;
    }

    public int getType() {
        return mComponentEnum.getType();
    }

    public float getSpanRatio() {
        return mComponentEnum.getSpanRatio();
    }

    public int getLayoutResId() {
        return mComponentEnum.getLayoutResId();
    }

    public int getIndexInComponent() {
        return mIndexInComponent;
    }

    public long getLayoutComponentId() {
        return layoutComponentId;
    }

    public void setLayoutComponentId(long layoutComponentId) {
        this.layoutComponentId = layoutComponentId;
    }
}
