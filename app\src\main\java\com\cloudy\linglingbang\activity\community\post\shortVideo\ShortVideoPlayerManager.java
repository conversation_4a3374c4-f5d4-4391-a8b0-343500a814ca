package com.cloudy.linglingbang.activity.community.post.shortVideo;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;

import com.aliyun.player.source.VidSts;
import com.cloudy.aliyunshortvideo.widget.AliyunVodPlayerView;
import com.cloudy.aliyunshortvideo.widget.ShortVideoPostPlayer;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.shortvideo.ShortVideoUtil;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.model.shortvideo.AliSTSTokenInfo;

/**
 * 视频帖播放器管理类
 *
 * <AUTHOR>
 * @date 2018/11/16
 */
public class ShortVideoPlayerManager {
    private ShortVideoPostPlayer mPlayerView;
    private Context mContext;
    private boolean mIsLoadPlayer = true;
    private VidSts vidSts;
    //上次申请mLastRequestVidStsTime的时间
    private long mLastRequestVidStsTime;

    private ShortVideoPostPlayer.OnVideoOperateListener mOnVideoOperateListener;

    public void setOnVideoOperateListener(ShortVideoPostPlayer.OnVideoOperateListener onVideoOperateListener) {
        mOnVideoOperateListener = onVideoOperateListener;
    }

    public static ShortVideoPlayerManager getInstance(Context context) {
        return new ShortVideoPlayerManager(context);
    }

    public ShortVideoPlayerManager(Context context) {
        this.mContext = context;
        initPlayer();
    }

    public ShortVideoPostPlayer getPlayerView() {
        return mPlayerView;
    }

    public void play(final String videoId, final String cover) {
        if (!mIsLoadPlayer) {
            return;
        }
        if (mPlayerView == null) {
            initPlayer();
        } else {
            mPlayerView.showCover(cover);
            mPlayerView.stop();
            mPlayerView.reset();
            removeSelfFromParent(mPlayerView);
        }
        /*if ((!TextUtils.isEmpty(videoId))) {
            //设置视频资源url
            mPlayerView.setVideoSource(postCardItem.getVideoUrl(), postCardItem.getImg());
            //准备开始播放
            mPlayerView.start();
        }*/
        if (!TextUtils.isEmpty(videoId) && (vidSts == null || needGetSts())) {
            requestPermission(videoId, cover);

        } else {
            vidSts.setVid(videoId);//视频vid
            if (mPlayerView != null) {
                mPlayerView.setVideoSource(vidSts, cover, "");
                mPlayerView.start();
            }
        }

    }

    /**
     * 初始化播放器
     */
    private void initPlayer() {
        mPlayerView = new ShortVideoPostPlayer(mContext);
        mPlayerView.setOnVideoOperateListener(new ShortVideoPostPlayer.OnVideoOperateListener() {
            @Override
            public void onDoubleClick() {
                if (mOnVideoOperateListener != null) {
                    mOnVideoOperateListener.onDoubleClick();
                }
            }

            /**
             * 鉴权失败再次请求，更新鉴权信息
             */
            @Override
            public void onPermissionExpired() {
                resetPermission();
            }
        });
        mPlayerView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        //设置横竖屏方向
        mPlayerView.setFullScreenOrientation(AliyunVodPlayerView.FULL_ORIENTATION.SCREEN_PORTRAIT);
    }

    /**
     * 是否是重置
     */
    private void resetPermission() {
        requestPermission(null, null);

    }

    /**
     * 重新请求权限（鉴权信息）
     */
    private void requestPermission(final String videoId, final String cover) {
        //获取短视频播放凭证
        ShortVideoUtil.getAliSTSToken(mContext, new ShortVideoUtil.STSTokenCallback() {
            @Override
            public void onSuccess(AliSTSTokenInfo tokenInfo) {
                if (mPlayerView == null || tokenInfo == null) {
                    return;
                }
                mLastRequestVidStsTime = AppUtil.getServerCurrentTime();
                //使用vid+STS方式播放
                vidSts = new VidSts();
                vidSts.setAccessKeyId(tokenInfo.getAccessKeyId());//播放凭证id
                vidSts.setAccessKeySecret(tokenInfo.getAccessKeySecret());//播放凭证secret
                vidSts.setSecurityToken(tokenInfo.getSecurityToken());//播放凭证token
                vidSts.setTitle("");//隐藏视频标题
                //为空说明是更新鉴权信息
                if (videoId != null) {
                    vidSts.setVid(videoId);//视频vid
                    mPlayerView.setVideoSource(vidSts, cover, "");
                } else {
                    mPlayerView.reSetVidSts(vidSts);
                }
                mPlayerView.start();
            }

            @Override
            public void onFailure(Throwable e) {
                ToastUtil.showMessage(mContext, mContext.getString(R.string.play_video_failed));
                // TODO: [hanfei create at 2018/11/16] 播放失败的回调是否添加？
            }
        });
    }

    public void removeSelfFromParent(View child) {
        if (child != null) {
            ViewGroup parent = (ViewGroup) child.getParent();
            if (parent != null && parent instanceof ViewGroup) {
                parent.removeView(child);
            }
        }
    }

    /**
     * 当页面滑动时调用，停止加载视频
     */
    public void onPageScrolled() {
        mIsLoadPlayer = false;
    }

    /**
     * 页面滑动停止时可以加载
     */
    public void onPageSelect() {
        mIsLoadPlayer = true;
    }

    /**
     * 当位置释放时调用
     */
    public void onPositionRelease() {
        if (mPlayerView != null) {
            mPlayerView.stop();
        }
    }

    /**
     * 是否需要申请视频凭证
     */
    public boolean needGetSts() {
        return mLastRequestVidStsTime == 0 || AppUtil.getServerCurrentTime() - mLastRequestVidStsTime > 3000 * 1000;
    }

    public void release() {
        if (mPlayerView != null) {
            mPlayerView.release();
            mPlayerView = null;
        }
    }
}
