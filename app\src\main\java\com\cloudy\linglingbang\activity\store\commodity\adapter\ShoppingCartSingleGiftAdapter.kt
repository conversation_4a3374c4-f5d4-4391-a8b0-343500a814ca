package com.cloudy.linglingbang.activity.store.commodity.adapter

import android.content.Context
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import butterknife.BindView
import butterknife.ButterKnife
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.store.commodity.CommodityDetailActivity
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder
import com.cloudy.linglingbang.model.SourceModel
import com.cloudy.linglingbang.model.store.commodity.CartCommodity

/**
 * 单个商品赠品部分（附加商品，赠品，换购商品）adapter
 *
 * <AUTHOR>
 * @date 2022/10/13
 */
class ShoppingCartSingleGiftAdapter(context: Context?, data: List<CartCommodity>?) :
    BaseRecyclerViewAdapter<CartCommodity>(context, data) {


    override fun createViewHolder(itemView: View?): BaseRecyclerViewHolder<CartCommodity> {
        return ShoppingCarSingleGiftViewHolder(itemView)
    }

    override fun getItemLayoutRes(viewType: Int): Int {
        return R.layout.item_shopping_chart_single_gift;
    }


    /**
     * 赠品的ViewHolder
     */
    inner class ShoppingCarSingleGiftViewHolder(itemView: View?) :
        BaseRecyclerViewHolder<CartCommodity>(itemView) {

        /**
         * 赠品名称
         */
        @JvmField
        @BindView(R.id.tv_gift_name)
        var tvGiftName: TextView? = null

        /**
         * 赠品价格和数量
         */
        @JvmField
        @BindView(R.id.tv_gift_price_and_count)
        var tvGiftPriceAndCount: TextView? = null

        /**
         * 赠品类型
         */
        @JvmField
        @BindView(R.id.tv_gift_type)
        var tvGiftType: TextView? = null

        /**
         * 赠品类型
         */
        @JvmField
        @BindView(R.id.ll_root)
        var llRoot: LinearLayout? = null


        override fun initItemView(itemView: View) {
            super.initItemView(itemView)
            ButterKnife.bind(this, itemView)
        }

        override fun bindTo(bean: CartCommodity, position: Int) {
            super.bindTo(bean, position)
            bean.apply {
                when (bean.additionAttributeId) {
                    -1L -> {
                        tvGiftType?.text = "【赠品】"
                        tvGiftName?.context?.resources?.getColor(R.color.color_ea0029)
                            ?.let { tvGiftType?.setTextColor(it) }
                    }
                    -2L -> {
                        tvGiftType?.text = "【换购】"
                        tvGiftName?.context?.resources?.getColor(R.color.color_ea0029)
                            ?.let { tvGiftType?.setTextColor(it) }
                    }
                    else -> {
                        tvGiftType?.text = "【$additionAttributeName】"
                        tvGiftName?.context?.resources?.getColor(R.color.color_1D1E23)
                            ?.let { tvGiftType?.setTextColor(it) }
                    }
                }

                tvGiftName?.text = bean.commodityName
                tvGiftPriceAndCount?.text = "¥$sellPriceStr x$quantity"
                llRoot?.setOnClickListener {
                    SensorsUtils.sensorsClickBtn(
                        "点击查看(" + tvGiftType?.text + tvGiftName?.text + ")",
                        "购物车",
                        "赠换购附加商品"
                    )
                    CommodityDetailActivity.startActivity(llRoot?.context!!, commodityId,
                        SourceModel(
                            SourceModel.POSITION_TYPE.SHOPPING_CAR_TYPE,
                            SourceModel.POSITION_TYPE.SHOPPING_CAR_VALUE
                        )
                    )
                }
            }

        }
    }
}