package com.cloudy.linglingbang.activity.car.add.fragment;

import android.content.Context;
import android.net.Uri;
import android.text.InputFilter;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.car.add.AddCarController;
import com.cloudy.linglingbang.activity.car.add.util.NumberAndLetterFilter;
import com.cloudy.linglingbang.activity.car.add.util.TextViewEmptyToastHintValidator;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.ValidatorUtils;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.BaseOnClickListener;
import com.cloudy.linglingbang.app.widget.textview.GetVerificationHelper;
import com.cloudy.linglingbang.constants.FinalSensors;
import com.cloudy.linglingbang.model.car.add.AddCarInfo;
import com.cloudy.linglingbang.model.car.add.AuthResult;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.user.User;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Pattern;

import io.reactivex.rxjava3.core.Observable;

/**
 * 填写爱车信息
 *
 * <AUTHOR>
 * @date 2019/3/19
 */
public class AddCarFillInfoFragment extends BaseAddCarFragment {
    private static final int MAX_VIN_LENGTH = 17;

    /** 车架号(VIN) */
    private EditText mEtVin;
    /** 手机号 */
    private TextView mTvPhoneNo;
    /** 验证码 */
    private EditText mEtVerificationCode;

    /** 博泰的扫描地址，如果不为空，调用一步绑车接口 */
    private String mBotaiUrl;

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_add_car_fill_info;
    }

    @Override
    protected void initViews() {
        mEtVin = mRootView.findViewById(R.id.et_vin);
        mTvPhoneNo = mRootView.findViewById(R.id.tv_phone_no);
        mEtVerificationCode = mRootView.findViewById(R.id.et_verification_code);
        super.initViews();

        //如果限制输入，那么在扫码后整体替换时，如果有不合法字符，会被整体替换为空
        //也可以在 filter 中返回时过滤掉不合法字段，或者允许输入，由产品确定
        InputFilter[] inputFilters = {new InputFilter.LengthFilter(MAX_VIN_LENGTH), new NumberAndLetterFilter(), new InputFilter.AllCaps()};
        mEtVin.setFilters(inputFilters);
        //手机号
        mTvPhoneNo.setText(User.getsUserInstance().getMobile());
        //扫码
        mRootView.findViewById(R.id.iv_scan).setOnClickListener(new BaseOnClickListener() {
            @Override
            public void onClick(View v) {
                super.onClick(v);
                SensorsUtils.sensorsClickBtn("扫码框按钮", "绑车页面");
                openScan();
            }
        });
        //获取验证码
        new GetVerificationHelper((Button) mRootView.findViewById(R.id.btn_get_verification_code), mTvPhoneNo) {
            @Override
            protected Observable<BaseResponse<String>> getVerificationCode(L00bangService2 service, String phoneNumber) {
                return service.sendSmsCode(phoneNumber, POSITION_ADD_CAR);
            }
        };
        bindEditTextNextAction(mEtVerificationCode);
    }

    @Override
    protected void initValidatorList(List<ValidatorUtils.Validator> validatorList) {
        super.initValidatorList(validatorList);
        validatorList.add(new TextViewEmptyToastHintValidator(mEtVin));
        validatorList.add(new ValidatorUtils.ToastValidator(getContext(), getNotNullContext().getString(R.string.fill_car_info_car_info_invalid_vin)) {
            @Override
            public boolean isValidInner() {
                return isValidVin(mEtVin.getText().toString());
            }
        });
        validatorList.add(new TextViewEmptyToastHintValidator(mEtVerificationCode));
    }

    @Override
    public void onGetScanResult(String scanResult) {
        super.onGetScanResult(scanResult);
        scanResult = scanResult.trim();
        String vinFromBotaiUrl = getVinFromBotaiUrl(scanResult);
        if (!TextUtils.isEmpty(vinFromBotaiUrl)) {
            //博泰地址
            mBotaiUrl = scanResult;
            mEtVin.setText(vinFromBotaiUrl);
            mEtVin.setSelection(mEtVin.length());
        } else {
            // vin
            mBotaiUrl = null;
            if (isValidVin(scanResult)) {
                mEtVin.setText(scanResult);
                mEtVin.setSelection(mEtVin.length());
            } else {
                Context context = getContext();
                if (context != null) {
                    ToastUtil.showMessage(context, context.getString(R.string.fill_car_info_car_info_invalid_scan_vin));
                }
            }
        }
    }

    /**
     * 从扫描的结果中解析 vin
     * 如果扫出来的结果以 http 开头，同时能取出 vin，则认为是博泰的地址，执行一步到位。
     *
     * @return 如果不符合规则，返回 ""
     */
    public static String getVinFromBotaiUrl(String url) {
        String vin = "";
        if (url == null) {
            return vin;
        }
        try {
            Uri uri = Uri.parse(url);
            vin = uri.getQueryParameter("vin");
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (vin == null) {
            vin = "";
        }
        return vin;
    }

    /**
     * 是否是正确的 vin
     */
    public static boolean isValidVin(String vin) {
        //这里允许小写，扫码后自动填为大写
        return Pattern.matches(String.format(Locale.getDefault(), "^[a-zA-Z\\d]{%d}$", MAX_VIN_LENGTH), vin);
    }

    @Override
    protected void prepareAndGoNextStep() {
        //判断 vin 是否经过修改
        if (getVinFromBotaiUrl(mBotaiUrl).equalsIgnoreCase(mEtVin.getText().toString())) {
            //如果解析出的 vin 和输入框的一致，认为没有更改，走一步到位
            addBindInfoByBotaiUrl(mBotaiUrl);
        } else {
            checkVin(mEtVin.getText().toString());
        }
    }

    /**
     * 校验 vin
     */
    private void checkVin(String vin) {
        //先设置结果，需要记录相关信息
        AddCarController addCarController = getAddCarController();
        if (addCarController != null) {
            AddCarInfo addCarInfo = addCarController.getAddCarInfo();
            //设置 vin
            addCarInfo.setVin(vin);
        }
        Map<String, String> map = new HashMap<>();
        map.put("vin", vin);
        map.put("mobile", mTvPhoneNo.getText().toString());
        map.put("smsCode", mEtVerificationCode.getText().toString());
        L00bangRequestManager2.getServiceInstance()
                .preCheck(map)
                .compose(L00bangRequestManager2.<AuthResult>setSchedulers())
                .subscribe(new ProgressSubscriber<AuthResult>(getContext()) {
                    @Override
                    public void onSuccess(AuthResult authResult) {
                        super.onSuccess(authResult);
                        onGetAuthResult(authResult);
                    }
                });
    }

    /**
     * 获得了结果，转到下一步
     */
    private void onGetAuthResult(final AuthResult authResult) {
        //先设置结果，需要记录相关信息
        AddCarController addCarController = getAddCarController();
        if (addCarController != null) {
            AddCarInfo addCarInfo = addCarController.getAddCarInfo();
            addCarInfo.setAuthResult(authResult);
            if (authResult != null) {
                //channelId 也设置
                addCarInfo.setChannelId(authResult.getChannelIdOrZero());
            }
        }
        //设置完结果后，不再判断，直接跳到下一步
        goNextStep();
    }

    /**
     * 博泰一步到位
     */
    private void addBindInfoByBotaiUrl(String botaiUrl) {
        Map<String, String> map = new HashMap<>();
        map.put("vin", mEtVin.getText().toString());
        map.put("qrcode", botaiUrl);
        map.put("mobile", mTvPhoneNo.getText().toString());
        map.put("smsCode", mEtVerificationCode.getText().toString());
        L00bangRequestManager2.getServiceInstance()
                .addBindInfoAllInOne(map)
                .compose(L00bangRequestManager2.<String>setSchedulers())
                .subscribe(new ProgressSubscriber<String>(getContext()) {
                    @Override
                    public void onSuccess(String s) {
                        super.onSuccess(s);
                        AddCarController addCarController = getAddCarController();
                        if (addCarController != null) {
                            addCarController.goStep(AddCarController.STEP_ACTIVE_FINISH, mEtVin.getText().toString());
                        }
                    }
                });
    }

    @Override
    public void onStart() {
        super.onStart();
        SensorsUtils.sensorsViewStart(FinalSensors.BIND_VEHICLE_PAGE);
    }

    @Override
    public void onStop() {
        super.onStop();
        SensorsUtils.sensorsViewEndNew(FinalSensors.BIND_VEHICLE_PAGE_NAME, FinalSensors.BIND_VEHICLE_PAGE);
    }

}
