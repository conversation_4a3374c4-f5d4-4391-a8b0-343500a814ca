package com.cloudy.linglingbang.activity.car.home;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.IRefreshContext;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.fragment.homePage.today.TodayParseResultFunction;
import com.cloudy.linglingbang.app.util.LocationHelper;
import com.cloudy.linglingbang.app.util.StatusBarUtils;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.model.CityModel;
import com.cloudy.linglingbang.model.car.home.MyCarList;
import com.cloudy.linglingbang.model.community.HotType;
import com.cloudy.linglingbang.model.entrance.HomeFunctionResult;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BackgroundSubscriber;
import com.cloudy.linglingbang.model.server.Ad.Ad2;
import com.cloudy.linglingbang.model.server.Ad.AdRequestUtil2;
import com.cloudy.linglingbang.model.store.DealerInfo;
import com.cloudy.linglingbang.model.wrapper.FunctionResultWrapper;

import java.util.ArrayList;
import java.util.List;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.functions.Function;

/**
 * 我的爱车数据加载
 *
 * <AUTHOR>
 * @date 2020-02-17
 */
public class MyCarLoader extends RefreshController<Object> implements OnCityChangeListener {

    MyCarFragment mMyCarFragment;

    private int mUserRole;

    /**
     * 刷新的次数
     */
    private int mRefreshCount;

    /**
     * 设置请求总数，便于刷新（潜客）
     */
    private static final int MAX_REFRESH_COUNT_NORMAL = 7;
    /**
     * 设置请求总数，便于刷新（保客）
     */
    private static final int MAX_REFRESH_COUNT_RETENTIVE = 7;

    /**
     * 是否完成一次定位
     * 刚进页面，为false时，onRefresh不会请求定位地址，因为正在定位，等定位返回之后，在请求。
     * 定位一次成功之后，再次刷新，该值为true，则正常请求定位
     * 防止第一次进入页面请求两次4S店,4S店跳动的问题
     */
    private boolean mIsLocationFinish = false;
    private Double mLng;
    private Double mLat;
    private String mCityName;
    private Long mCityId;

    //顶部广告
    private MyCarModel.AdListTop mAdListTop;
    //中部推荐广告
    private List<Ad2> mMiddleAdList = new ArrayList<>();
    //福利社
    private MyCarModel.WelfareFunctionModel mWelfareFunction;
    //栏目列表
    private List<Object> mColumnList = new ArrayList<>();
    //4s店
    private DealerInfo mDealerInfo;

    //潜客-购车服务
    private MyCarModel.FunctionModel mBuyCarServiceFunction;
    //潜客-用车服务
    private MyCarModel.FunctionModel mUserCarServiceFunction;

    //保客-用车服务
    private MyCarModel.FunctionModel mUserCarServiceMemberFunction;
    //保客-其他服务
    private MyCarModel.FunctionModel mOtherServiceFunction;
    //保客-线上看车
    private MyCarModel.SeeCarOnlineFunctionModel mSeeCarOnLineFunction;
    //默认爱车列表
    private MyCarModel.MyCarTypeList mMyCarTypeList;

    //用户身份：潜客
    private static final int USER_ROLE_NORMAL = 0;
    //用户身份：保客
    private static final int USER_ROLE_RETENTIVE = 1;

    /**
     * 是否正在刷新
     */
    private boolean mIsRefreshing = false;

    private boolean mIsFirstRefresh = true;

    private boolean mRequestCarFinish = false;

    private boolean mRequestAdFinish = false;

    private boolean mIsNeedRefreshOilForRetentive;

    private boolean mIsNeedRefreshOilForNormal;

    /**
     * 是否需要强制刷新，当身份连续变化时，上一次刷新还没结束，又登录了其他的账号，这个时候需要调用强制刷新来刷新用户数据
     */
    private boolean mNeedForceRefresh;

    //定位之后，置为true，则说明，定位回来的时候，油价还没有初始化。在notify之后在调用油价刷新。
    private boolean mIsReadyRefreshOil;

    public static final String RESPONSE_CAR_REFRESH_ACTION = "RESPONSE_CAR_REFRESH_ACTION";
    public static final String INTENT_TYPE_CAR_LIST = "intent_type_car_list";

    public MyCarLoader(IRefreshContext<Object> refreshContext) {
        super(refreshContext);
    }

    @Override
    public void initViews(View rootView) {
        super.initViews(rootView);
        getRecyclerView().setBackgroundColor(getContext().getResources().getColor(R.color.white));
        //请求定位
        if (mMyCarFragment != null) {
            mMyCarFragment.goLocation();
        }
    }

    @Override
    public void onRefresh() {
//        super.onRefresh();
        if (!mIsRefreshing) {
            mIsRefreshing = true;
            mRefreshCount = 0;
            if (getAdapter() instanceof MyCarAdapter) {
                ((MyCarAdapter) getAdapter()).stopOilScroll();
            }
            //刷新时重新设置用户角色
            mUserRole = getUserRole();
            refreshStatusBar();
            getAllData();
        }

    }

    /**
     * 刷新状态栏颜色（保客和潜客颜色不一样）
     */
    private void refreshStatusBar() {
        if (mUserRole == USER_ROLE_NORMAL) {
            StatusBarUtils.setFullScreenTransparentStatusBarAndWhiteText(mMyCarFragment.getActivity());
        } else {
            StatusBarUtils.setFullScreenTransparentStatusBarAndBlackText(mMyCarFragment.getActivity());
        }
    }

    public void setFragment(MyCarFragment fragment) {
        mMyCarFragment = fragment;
    }

    /**
     * 检查是否刷新完成
     */
    private void checkRefreshComplete() {
        mRefreshCount++;
        int maxCount = mUserRole == USER_ROLE_NORMAL ? MAX_REFRESH_COUNT_NORMAL : MAX_REFRESH_COUNT_RETENTIVE;
        if (mRefreshCount >= maxCount) {
            onRefreshComplete();
            mIsRefreshing = false;
            if (getAdapter() instanceof MyCarAdapter) {
                ((MyCarAdapter) getAdapter()).startOilScroll();
            }
            //如果上次记录了强制刷新状态，则再次刷新
            if (mNeedForceRefresh) {
                mNeedForceRefresh = false;
                manualRefresh();
                Log.e("111111", "开始再次刷新");
            }
        }

    }

    void generateDataAndNotify() {
        if (mData != null) {
            mData.clear();
            mData.addAll(generateData());
            if (!mIsFirstRefresh) {
                if (mUserRole == USER_ROLE_RETENTIVE && mRefreshCount == MAX_REFRESH_COUNT_RETENTIVE) {
                    ((MyCarAdapter) getAdapter()).updateFragment();
                    adapter.notifyDataSetChanged();
                    Log.e("111111", "标志位1：调用updateFragment");
                } else if (mUserRole == USER_ROLE_NORMAL && mRefreshCount == MAX_REFRESH_COUNT_NORMAL) {
                    adapter.notifyDataSetChanged();
                }
            } else {
                //如果是第一次刷新
                if (mUserRole == USER_ROLE_RETENTIVE && mRequestCarFinish) {
                    adapter.notifyItemRangeChanged(1, mData.size() - 1);
                } else if (mUserRole == USER_ROLE_NORMAL && mRequestAdFinish) {
                    adapter.notifyItemRangeChanged(1, mData.size() - 1);
                } else {
                    adapter.notifyDataSetChanged();
                }

                int maxCount = mUserRole == USER_ROLE_RETENTIVE ? MAX_REFRESH_COUNT_RETENTIVE : MAX_REFRESH_COUNT_NORMAL;
                if (mRefreshCount >= maxCount) {
                    mIsFirstRefresh = false;
                    if (mIsReadyRefreshOil) {
                        if (adapter != null && adapter instanceof MyCarAdapter) {
                            if (mUserRole == USER_ROLE_NORMAL) {
                                ((MyCarAdapter) adapter).refreshOilPriceForNormal();
                            } else {
                                ((MyCarAdapter) adapter).refreshOilPriceForRetentive();
                            }
                            mIsReadyRefreshOil = false;

                        }
                    }
                }

            }

//            adapter.notifyDataSetChanged();

//                adapter.notifyItemRangeChanged(1,mData.size()-1);
//            }
//            if(mUserRole == USER_ROLE_RETENTIVE && mRequestCarFinish){
//                adapter.notifyItemRangeChanged(1,mData.size()-1);
//            } else if(mUserRole == USER_ROLE_NORMAL && mRequestAdFinish){
//                adapter.notifyItemRangeChanged(1,mData.size()-1);
//            } else{
//                adapter.notifyDataSetChanged();
//            }
        }
    }

    /**
     * 如果不是第一次刷新成功，则发送广播，通知车的fragment自己更新车的内容
     */
    private void sendBroadCastRefreshCarInfo() {
        Intent intent = new Intent(RESPONSE_CAR_REFRESH_ACTION);
        intent.putExtra(INTENT_TYPE_CAR_LIST, mMyCarTypeList);
        LocalBroadcastManager.getInstance(getContext()).sendBroadcast(intent);

    }

    /**
     * 生成数据
     */
    private List<Object> generateData() {
        //如果是潜客
        if (mUserRole == USER_ROLE_NORMAL) {
            return generateDataForNormalCustomer();
        } else {
            return generateDataForRetentiveCustomer();
        }
    }

    /**
     * 生成保客数据
     */
    private List<Object> generateDataForRetentiveCustomer() {
        List<Object> result = new ArrayList<>();
        //我的爱车列表
        if (mMyCarTypeList != null) {
            result.add(mMyCarTypeList);
        }
        //添加用车服务
        if (mUserCarServiceMemberFunction != null) {
            result.add(mUserCarServiceMemberFunction);
        }
        //添加4S店信息
        if (mDealerInfo != null) {
            result.add(mDealerInfo);
        }
        //添加栏目
        if (mColumnList != null && mColumnList.size() > 0) {
            result.addAll(mColumnList);
        }
        //线上看车
        if (mSeeCarOnLineFunction != null) {
            result.add(mSeeCarOnLineFunction);
        }
        //添加用车服务功能图标
        if (mOtherServiceFunction != null) {
            result.add(mOtherServiceFunction);
        }
        //添加福利社
        if (mWelfareFunction != null) {
            result.add(mWelfareFunction);
        }
        return result;
    }

    /**
     * 生成潜客数据
     */
    private List<Object> generateDataForNormalCustomer() {
        List<Object> result = new ArrayList<>();
        //加载顶部广告图
        if (mAdListTop != null) {
            result.add(mAdListTop);
        }
        //加载中部推荐车型广告图
        if (mMiddleAdList != null && mMiddleAdList.size() > 0) {
            result.add(new MyCarModel.AdListMiddle(mMiddleAdList));
        }
        //添加4S店信息
        if (mDealerInfo != null) {
            result.add(mDealerInfo);
        }
        //添加购车服务功能图标
        if (mBuyCarServiceFunction != null) {
            result.add(mBuyCarServiceFunction);
        }
        //添加栏目
        if (mColumnList != null && mColumnList.size() > 0) {
            result.addAll(mColumnList);
        }
        //添加用车服务功能图标
        if (mUserCarServiceFunction != null) {
            result.add(mUserCarServiceFunction);
        }
        //添加福利社
        if (mWelfareFunction != null) {
            result.add(mWelfareFunction);
        }
        return result;
    }

    /**
     * 获取数据
     */
    public void getAllData() {
        if (mUserRole == USER_ROLE_NORMAL) {
            //潜客
            mRequestAdFinish = false;
            getAllDataForNormalCustomer();
        } else {
            //保客
            mRequestCarFinish = false;
            getAllDataForRetentiveCustomer();
        }
    }

    /**
     * 获取潜客数据
     */
    public void getAllDataForNormalCustomer() {
        //获取车型广告(2个)
        loadAllAd();
        //获取功能图标（3个）
        getFunctionButton();
        //获取4S店（1个接口）
        if (mIsLocationFinish) {
            get4sData();
        }
        //获取栏目信息（1个接口）
        getColumnList();
    }

    /**
     * 获取保客数据
     */
    public void getAllDataForRetentiveCustomer() {
        //获取车列表（1个）
        getOwnerCarList(true);
        //获取功能图标（4个）
        getFunctionButton();
        //获取4S店（1个）
        if (mIsLocationFinish) {
            get4sData();
        }
        //获取栏目信息（1个）
        getColumnList();
    }

    /**
     * 获取拥有的车列表
     *
     * @param isAddCount 是否计算请求次数
     */
    private void getOwnerCarList(final boolean isAddCount) {
        L00bangRequestManager2.getServiceInstance()
                .getShowCarList()
                .compose(L00bangRequestManager2.<MyCarList>setSchedulers())
                .subscribe(new BackgroundSubscriber<MyCarList>(getContext()) {
                    @Override
                    public void onSuccess(MyCarList myCarList1) {
                        super.onSuccess(myCarList1);
                        if (myCarList1 != null) {
                            if (myCarList1.getCarListItems() != null && myCarList1.getCarListItems().size() > 0) {
                                myCarList1.setCountToChild();
                                mMyCarTypeList = new MyCarModel.MyCarTypeList(myCarList1.getCarListItems(), mIsNeedRefreshOilForRetentive);
                                mIsNeedRefreshOilForRetentive = false;
                            } else {
                                mMyCarTypeList = null;
                            }
                        }
                        if (isAddCount) {
                            mRequestCarFinish = true;
                            checkRefreshComplete();
                        }
                        generateDataAndNotify();
                    }

                    @Override
                    public void onError(Throwable e) {
                        if (isAddCount) {
                            checkRefreshComplete();
                        }
                        generateDataAndNotify();
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        if (isAddCount) {
                            checkRefreshComplete();
                        }
                        generateDataAndNotify();
                    }
                });

    }

    /**
     * 加载广告
     */
    public void loadAdByPosition(final int position) {
        AdRequestUtil2.getAdByPageCode(getContext(), position, false, new AdRequestUtil2.OnLoadAdListener() {
            @Override
            public void onLoadSuccess(List<Ad2> ads, int loadType) {
                checkRefreshComplete();
                if (ads != null && ads.size() > 0) {
                    if (position == Ad2.POSITION.CAR_TOP) {
                        mAdListTop = new MyCarModel.AdListTop(ads, mIsNeedRefreshOilForNormal);
                        mIsNeedRefreshOilForNormal = false;
                    } else {
                        mMiddleAdList.clear();
                        mMiddleAdList.addAll(ads);

                    }
                    generateDataAndNotify();
                    if (position == Ad2.POSITION.CAR_TOP) {
                        mRequestAdFinish = true;
                    }
                }
            }

            @Override
            public void onFailure(Throwable e, int loadType) {
                checkRefreshComplete();
                generateDataAndNotify();
            }
        });
    }

    /**
     * 获取4S店信息
     */
    private void get4sData() {
        L00bangRequestManager2.getServiceInstance()
                .getRecommendDealer(mLng, mLat, mCityId, mCityName)
                .compose(L00bangRequestManager2.<DealerInfo>setSchedulers())
                .subscribe(new BackgroundSubscriber<DealerInfo>(getContext()) {
                    @Override
                    public void onSuccess(DealerInfo dealerInfo) {
                        super.onSuccess(dealerInfo);
                        //设置城市和经纬度，一遍viewHolder跳转到H5传值
                        dealerInfo.setUserLat(mLat);
                        dealerInfo.setUserLng(mLng);
                        dealerInfo.setCityId(mCityId);
                        dealerInfo.setCityName(mCityName);
                        mDealerInfo = dealerInfo;
                        checkRefreshComplete();
                        generateDataAndNotify();
                    }

                    @Override
                    public void onError(Throwable e) {
                        checkRefreshComplete();
                        generateDataAndNotify();
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        checkRefreshComplete();
                        generateDataAndNotify();
                    }
                });

    }

    /**
     * 获取所有功能图标
     */
    private void getFunctionButton() {
        //如果是潜客
        if (mUserRole == USER_ROLE_NORMAL) {
            //购车服务
            getFunctionList(HomeFunctionResult.MY_CAR_BUY_CAR_NORMAL);
            //用车服务
            getFunctionList(HomeFunctionResult.MY_CAR_USER_CAR_SERVICE_NORMAL);
        } else {
            //用车服务
            getFunctionList(HomeFunctionResult.MY_CAR_USER_CAR_SERVICE_MEMBER);
            //线上看车
            getFunctionList(HomeFunctionResult.MY_CAR_SEE_CAR_ONLINE_MEMBER);
            //其他服务
            getFunctionList(HomeFunctionResult.MY_CAR_OTHER_SERVICE_MEMBER);
        }
        //请求福利社
        getFunctionList(HomeFunctionResult.MY_CAR_WELFARE);
    }

    private void getColumnList() {
        L00bangRequestManager2.getServiceInstance().getColumnsWithTypeAndPosition(HomeFunctionResult.POSITION_MY_CAR)
                .map(new Function<BaseResponse<List<HotType>>, BaseResponse<List<Object>>>() {
                    @Override
                    public BaseResponse<List<Object>> apply(BaseResponse<List<HotType>> listBaseResponse) {
                        if (listBaseResponse.getData() == null) {
                            return listBaseResponse.cloneWithData(null);
                        }
                        TodayParseResultFunction function = new TodayParseResultFunction();
                        List<Object> result = function.apply(listBaseResponse.getData());
                        return listBaseResponse.cloneWithData(result);
                    }
                })
                .compose(L00bangRequestManager2.<List<Object>>setSchedulers())
                .subscribe(new BackgroundSubscriber<List<Object>>(getContext()) {
                    @Override
                    public void onSuccess(List<Object> columnList) {
                        super.onSuccess(columnList);
                        mColumnList.clear();
                        mColumnList.addAll(columnList);
                        checkRefreshComplete();
                        generateDataAndNotify();
                    }

                    @Override
                    public void onError(Throwable e) {
                        checkRefreshComplete();
                        generateDataAndNotify();
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        checkRefreshComplete();
                        generateDataAndNotify();
                    }
                });
    }

    /**
     * 获取用户角色
     *
     * @return
     */
    private int getUserRole() {
        if (!UserUtils.isRetentiveCustomer()) {
            //潜客
            return USER_ROLE_NORMAL;
        } else {
            //保客
            return USER_ROLE_RETENTIVE;
        }

    }

    /**
     * 根据类型获取服务功能图标的内容
     */
    private void getFunctionList(final int type) {
        L00bangRequestManager2.getServiceInstance()
                .getHomeFunctionList(type)
                .compose(L00bangRequestManager2.<HomeFunctionResult>setSchedulers())
                .subscribe(new BackgroundSubscriber<HomeFunctionResult>(getContext()) {
                    @Override
                    public void onSuccess(HomeFunctionResult homeFunctionResult) {
                        super.onSuccess(homeFunctionResult);
                        checkRefreshComplete();
                        if (homeFunctionResult == null) {
                            return;
                        }
                        if (type == HomeFunctionResult.MY_CAR_BUY_CAR_NORMAL && homeFunctionResult.getFunctionEntranceList().size() > 0) { //潜客-购车服务
                            mBuyCarServiceFunction = new MyCarModel.FunctionModel(new FunctionResultWrapper(homeFunctionResult), "购车服务", R.drawable.ic_buy_car_service, type);
                        } else if (type == HomeFunctionResult.MY_CAR_USER_CAR_SERVICE_NORMAL && homeFunctionResult.getFunctionEntranceList().size() > 0) { //潜客-用车服务
                            mUserCarServiceFunction = new MyCarModel.FunctionModel(new FunctionResultWrapper(homeFunctionResult), "用车服务", R.drawable.ic_my_car_user_car_service, type);
                        } else if (type == HomeFunctionResult.MY_CAR_OTHER_SERVICE_MEMBER && homeFunctionResult.getFunctionEntranceList().size() > 0) {//保客-其他服务
                            mOtherServiceFunction = new MyCarModel.FunctionModel(new FunctionResultWrapper(homeFunctionResult), "其他服务", R.drawable.ic_buy_car_service, type);
                        } else if (type == HomeFunctionResult.MY_CAR_USER_CAR_SERVICE_MEMBER && homeFunctionResult.getFunctionEntranceList().size() > 0) {//保客-用车服务
                            mUserCarServiceMemberFunction = new MyCarModel.FunctionModel(new FunctionResultWrapper(homeFunctionResult), "用车服务", R.drawable.ic_my_car_user_car_service, type);
                        } else if (type == HomeFunctionResult.MY_CAR_SEE_CAR_ONLINE_MEMBER) {//保客-线上看车
                            if (homeFunctionResult.getFunctionEntranceList().size() > 0) {
                                mSeeCarOnLineFunction = new MyCarModel.SeeCarOnlineFunctionModel(new FunctionResultWrapper(homeFunctionResult));
                            } else {
                                mSeeCarOnLineFunction = null;
                            }
                        } else if (type == HomeFunctionResult.MY_CAR_WELFARE) {//福利社
                            if (homeFunctionResult.getFunctionEntranceList().size() > 0) {
                                mWelfareFunction = new MyCarModel.WelfareFunctionModel(new FunctionResultWrapper(homeFunctionResult));
                            } else {
                                mWelfareFunction = null;
                            }
                        }
                        generateDataAndNotify();
                    }

                    @Override
                    public void onError(Throwable e) {
                        checkRefreshComplete();
                        generateDataAndNotify();
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        checkRefreshComplete();
                        generateDataAndNotify();
                    }
                });
    }

    /**
     * 加载所有广告
     */
    private void loadAllAd() {
        //获取顶部广告
        loadAdByPosition(Ad2.POSITION.CAR_TOP);
        //加载中部推荐广告
        loadAdByPosition(Ad2.POSITION.CAR_MIDDLE);
    }

    @Override
    protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
        return null;
    }

    /**
     * 位置信息发生变化（定位成功或者定位失败）
     */
    public void onUpdateLocation() {
        mIsLocationFinish = true;
        LocationHelper.LocationEntity lastLocation = LocationHelper.getInstance().getLastLocation();
        if (lastLocation != null && lastLocation.latitude != 0 && lastLocation.longitude != 0 && !TextUtils.isEmpty(lastLocation.cityName)) {
            //有定位
            mLng = lastLocation.longitude == 0 ? null : lastLocation.longitude;
            mLat = lastLocation.latitude == 0 ? null : lastLocation.latitude;
            mCityName = lastLocation.cityName;
        } else {
            CityModel cityModel = OilPriceLoader.getCurrentCity();
            mCityName = cityModel.getCityName();
            mCityId = cityModel.getCityId();
        }

        boolean isRefresh = false;
        //刷新油价
        if (adapter instanceof MyCarAdapter) {
            if (mUserRole == USER_ROLE_NORMAL) {
                if (((MyCarAdapter) adapter).getCarBannerViewHolder() != null) {
                    ((MyCarAdapter) adapter).refreshOilPrice();
                    isRefresh = true;
                }
            } else {
                if (((MyCarAdapter) adapter).getMyCarCarListViewHolder() != null) {
                    ((MyCarAdapter) adapter).refreshOilPrice();
                    isRefresh = true;
                }
            }
            if (!isRefresh) {
                mIsReadyRefreshOil = true;
            } else {
                mIsReadyRefreshOil = false;
            }
        }
        //请求服务端4S店接口
        get4sData();
    }

    /**
     * 展示爱车发生变化，只刷新展示爱车
     */
    public void onUpdateShowCar() {
        if (getUserRole() == USER_ROLE_RETENTIVE) {
            getOwnerCarList(false);
        }
    }

    @Override
    public void onCityChange(Long cityId, String cityName) {
        mCityName = cityName;
        mCityId = cityId;
        get4sData();

    }

    /**
     * 重置城市，用于切换用户时使用
     */
    public void resetCity() {
        CityModel cityModel = OilPriceLoader.getCurrentCity();
        mCityName = cityModel.getCityName();
        mCityId = cityModel.getCityId();
        if (getAdapter() != null && getAdapter() instanceof MyCarAdapter) {
            ((MyCarAdapter) getAdapter()).resetCity();
        }
        //如果切换成保客,并且车列表不为空，则是第一次切换，则标识ViewHolder要刷新油价信息
        if (UserUtils.isRetentiveCustomer() && ((MyCarAdapter) getAdapter()).getMyCarCarListViewHolder() == null) {
            mIsNeedRefreshOilForRetentive = true;
        } else if (!UserUtils.isRetentiveCustomer() && ((MyCarAdapter) getAdapter()).getCarBannerViewHolder() == null) {
            //如果切换成潜客,并且车列表不为空，则是第一次切换，则标识ViewHolder要刷新油价信息
            mIsNeedRefreshOilForNormal = true;
        }
    }

    /**
     * 强制刷新。防止上一个刷新没结束，就又更换了用户状态，第二次刷新失败
     */
    public void forceRefresh() {
        //如果没在刷新，则直接刷新就好了
        if (!getSwipeToLoadLayout().isRefreshing()) {
            Log.e("111111", "manualRefresh");
            manualRefresh();
        } else {
            //否则记录需要刷新状态，本次刷新完成再次刷新一次
            mNeedForceRefresh = true;
            Log.e("111111", "需要再次刷新");
        }
    }

}
