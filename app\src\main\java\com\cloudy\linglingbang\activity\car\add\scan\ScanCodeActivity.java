package com.cloudy.linglingbang.activity.car.add.scan;

import android.app.Activity;
import android.content.Context;
import android.graphics.Point;
import android.graphics.Rect;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.view.View;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.car.add.AddCarController;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.widget.BaseOnClickListener;
import com.cloudy.zxing.CaptureActivity;
import com.cloudy.zxing.camera.CameraManager;

/**
 * 扫码
 *
 * <AUTHOR>
 * @date 2019/3/21
 */
public class ScanCodeActivity extends CaptureActivity {

    protected int mStep;
    protected String mVin;

    @Override
    public void onCreate(Bundle icicle) {
        super.onCreate(icicle);
        Bundle extras = getIntent().getExtras();
        mStep = (int) IntentUtils.getOrParseLongExtra(extras, IntentUtils.INTENT_EXTRA_COMMON, 0);
        mVin = (String) IntentUtils.getExtra(extras, AddCarController.INTENT_EXTRA_VIN, "");

        //返回
        View ivBack = findViewById(R.id.iv_back);
        if (ivBack != null) {
            ivBack.setOnClickListener(new BaseOnClickListener() {
                @Override
                public void onClick(Context context) {
                    super.onClick(context);
                    Activity activity = AppUtil.getActivity(context);
                    if (activity != null) {
                        activity.finish();
                    }
                }
            });
        }
        //标题
        TextView tvTitle = findViewById(R.id.tv_title);
        if (tvTitle != null) {
            tvTitle.setText(getTitle(mStep));
        }
        //介绍
        TextView textView = findViewById(R.id.status_view);
        if (textView != null) {
            textView.setText(getScanIntro(mStep));
        }
        //标题默认是透明，设为白色覆盖
        View viewTitle = findViewById(R.id.rl_title);
        if (viewTitle != null) {
            viewTitle.setBackgroundResource(R.color.white);
        }
    }

    private CharSequence getTitle(int step) {
        if (step == AddCarController.STEP_FILL_CAR_INFO) {
            return getString(R.string.title_add_car_scan_vin);
        } else if (step == AddCarController.STEP_SCAN_ACTIVE) {
            return getString(R.string.title_add_car_scan_active);
        }
        return "";
    }

    private CharSequence getScanIntro(int step) {
        if (step == AddCarController.STEP_FILL_CAR_INFO) {
            return getString(R.string.add_car_scan_vin_intro);
        } else if (step == AddCarController.STEP_SCAN_ACTIVE) {
            return getString(R.string.add_car_scan_active_intro);
        }
        return "";
    }

    /**
     * 转到下一步
     */
    protected void goNextStep() {
        AddCarController.startAddCarActivity(this, mVin, AddCarController.getNextStep(mStep));
        finish();
    }

    @Override
    protected CameraManager createCameraManager() {
        return new AddCarCameraManager(getApplicationContext());
    }

    private class AddCarCameraManager extends CameraManager {
        private int mFramingRectTop;

        public AddCarCameraManager(Context context) {
            super(context);
            mFramingRectTop = context.getResources().getDimensionPixelOffset(R.dimen.normal_220) + context.getResources().getDimensionPixelOffset(R.dimen.normal_50);
        }

        @Override
        public synchronized Rect getFramingRect() {
            if (framingRect == null) {
                if (camera == null) {
                    return null;
                }
                Point screenResolution = configManager.getScreenResolution();
                if (screenResolution == null) {
                    // Called early, before init even finished
                    return null;
                }

                //修改扫描框尺寸
                DisplayMetrics metrics = this.context.getResources().getDisplayMetrics();
                int width = (int) (metrics.widthPixels * 0.6D);
                int height = (int) (metrics.heightPixels * 0.336D);

                int leftOffset = (screenResolution.x - width) / 2;
                int topOffset = mFramingRectTop;
                framingRect = new Rect(leftOffset, topOffset, leftOffset + width, topOffset + height);
            }
            return framingRect;
        }
    }
}
