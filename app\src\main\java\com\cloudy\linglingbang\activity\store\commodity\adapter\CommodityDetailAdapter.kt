package com.cloudy.linglingbang.activity.store.commodity.adapter

import android.content.Context
import android.content.Intent
import android.text.TextUtils
import android.view.View
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.ScanImageActivity
import com.cloudy.linglingbang.activity.store.commodity.CommodityDetailRefreshController
import com.cloudy.linglingbang.activity.store.commodity.adapter.holder.*
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder
import com.cloudy.linglingbang.app.widget.recycler.header.WrapperUtils
import com.cloudy.linglingbang.model.SourceModel
import com.cloudy.linglingbang.model.postcard.PostCardItem
import com.cloudy.linglingbang.model.store.commodity.CenterCommodity
import com.cloudy.linglingbang.model.store.home.StoreElementCommodity

/**
 * 商品详情
 * <AUTHOR>
 * @date 2022/9/21
 */
class CommodityDetailAdapter(context: Context, data: List<Any>) :
    BaseRecyclerViewAdapter<Any>(context, data) {


    var mCenterCommodity: CenterCommodity? = null
    override fun createViewHolder(itemView: View): BaseRecyclerViewHolder<Any?>? {
        return null
    }

    override fun createViewHolderWithViewType(
        itemView: View,
        viewType: Int
    ): BaseRecyclerViewHolder<Any> {
        return when (viewType) {
            R.layout.item_commodity_detail_banner -> {
                HeaderBannerViewHolder(itemView)
            }

            R.layout.item_commodity_detail_info -> {    //详情title（价格，名称）
                DetailInfoViewHolder(itemView)
            }

            R.layout.item_commodity_detail_info2 -> {    //详情title（价格，名称）
                DetailInfoViewHolder2(itemView)
            }

            R.layout.item_commodity_detail_medal -> {   //兑换徽章
                MedalViewHolder(itemView)
            }

            R.layout.item_commodity_detail_middle_info -> { //选择配置
                DetailMiddleViewHolder(itemView)
            }

            R.layout.item_commodity_detail_evaluation -> {  //商品评价
                EvaluationTitleViewHolder(itemView)
            }

            R.layout.item_commodity_recommond_title -> {    //商品推荐
                val h = RecommendTitleViewHolder(itemView)
                h.setAdapter(this)
                h
            }

            R.layout.item_commodity_recommond_commodity -> {
                val h = RecommendCommodityViewHolder(itemView)
                h.orderSourceType = SourceModel.POSITION_TYPE.COMMODITY_DETAIL_TYPE
                h.orderSource = SourceModel.POSITION_TYPE.COMMODITY_DETAIL_VALUE
                h
            }

            R.layout.item_commodity_detail_text_img -> {    //详情主体列表
                val imageAndTextViewHolder = ImageAndTextViewHolder(itemView)
                imageAndTextViewHolder.onItemClickListener = listen()
                imageAndTextViewHolder
            }

            else -> BaseRecyclerViewHolder(itemView)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (mData[position] is CommodityDetailRefreshController.Evaluation) {
            //评价
            R.layout.item_commodity_detail_evaluation
        } else if (mData[position] is PostCardItem) {
            R.layout.item_commodity_detail_text_img
        } else if (mData[position] is Int || mData[position] is Integer) {
            mData[position] as Int
        } else if (mData[position] is StoreElementCommodity) {
            //商品推荐
            R.layout.item_commodity_recommond_commodity
        } else {
            R.layout.item_commodity_detail_text_img
        }
    }

    override fun getItemLayoutRes(viewType: Int): Int {
        return viewType
    }

    override fun onBindViewHolder(holder: BaseRecyclerViewHolder<Any>, position: Int) {
        if (holder is BaseCommodityHolder) {
            holder.mCenterCommodity = mCenterCommodity
        }
        super.onBindViewHolder(holder, position)
    }

    override fun onViewAttachedToWindow(holder: BaseRecyclerViewHolder<Any>) {
        super.onViewAttachedToWindow(holder)
        if (getItemViewType(holder.layoutPosition) != R.layout.item_commodity_recommond_commodity) {
            WrapperUtils.setFullSpan(holder)
        }
    }

    private fun listen(): BaseRecyclerViewHolder.OnItemClickListener {
        return BaseRecyclerViewHolder.OnItemClickListener { _, position ->
            if (mData[position] is PostCardItem) {
                val currentImg = (mData[position] as PostCardItem).img
                if (!TextUtils.isEmpty(currentImg)) {
                    val images: List<String> =
                        mData.filterIsInstance<PostCardItem>().mapNotNull { it.img }
                    val intent = Intent(mContext, ScanImageActivity::class.java)
                    intent.putExtra(ScanImageActivity.EXTRA_IMAGE_INDEX, images.indexOf(currentImg))
                    intent.putExtra(ScanImageActivity.EXTRA_IMAGE_URLS, images.toTypedArray())
                    mContext.startActivity(intent)
                }
            }
        }
    }
}