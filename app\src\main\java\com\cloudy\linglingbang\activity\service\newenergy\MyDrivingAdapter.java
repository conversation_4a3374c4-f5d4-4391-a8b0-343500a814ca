package com.cloudy.linglingbang.activity.service.newenergy;

import android.content.Context;
import android.view.View;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.server.MyDrivingRecord;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 昨日排行的adapter
 *
 * <AUTHOR>
 * @date 2019-07-16
 */
public class MyDrivingAdapter extends BaseRecyclerViewAdapter<MyDrivingRecord> {

    public MyDrivingAdapter(Context context, List<MyDrivingRecord> data) {
        super(context, data);
    }

    @Override
    protected BaseRecyclerViewHolder<MyDrivingRecord> createViewHolder(View itemView) {
        return new ViewHolder(itemView);
    }

    @Override
    protected int getItemLayoutRes(int viewType) {
        return R.layout.item_my_driving;
    }

    class ViewHolder extends BaseRecyclerViewHolder<MyDrivingRecord> {

        @BindView(R.id.tv_time)
        TextView mTvTime;

        @BindView(R.id.tv_mileage)
        TextView mTvMileage;

        @BindView(R.id.tv_save)
        TextView mTvSave;

        public ViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            ButterKnife.bind(this, itemView);
        }

        @Override
        public void bindTo(MyDrivingRecord myDrivingRecord, int position) {
            super.bindTo(myDrivingRecord, position);
            mTvMileage.setText(mContext.getResources().getString(R.string.txt_car_mileage, myDrivingRecord.getMileage()));
            mTvTime.setText(myDrivingRecord.getOccurDate());
            mTvSave.setText(mContext.getString(R.string.invoice_yuan, myDrivingRecord.getSaveCount()));
        }
    }
}
