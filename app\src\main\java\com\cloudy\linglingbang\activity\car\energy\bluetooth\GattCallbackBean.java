package com.cloudy.linglingbang.activity.car.energy.bluetooth;

/**
 * 蓝牙连接回调同步类
 * Created by LiYeWen on 2025/05/06
 */
public class GattCallbackBean {

    private int status;
    private int newState;
    private int mtu;

    public GattCallbackBean(){
    }

    public GattCallbackBean(int status){
        this.status = status;
    }

    public GattCallbackBean(int status, int newState){
        this.status = status;
        this.newState = newState;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getNewState() {
        return newState;
    }

    public void setNewState(int newState) {
        this.newState = newState;
    }

    public int getMtu() {
        return mtu;
    }

    public void setMtu(int mtu) {
        this.mtu = mtu;
    }

}
