package com.cloudy.linglingbang.activity.car.home;

import com.cloudy.linglingbang.model.WrapperModel;
import com.cloudy.linglingbang.model.car.home.MyCarType;
import com.cloudy.linglingbang.model.server.Ad.Ad2;
import com.cloudy.linglingbang.model.wrapper.FunctionResultWrapper;

import java.io.Serializable;
import java.util.List;

/**
 * 爱车（潜客）-模型
 *
 * <AUTHOR>
 * @date 2020-02-17
 */
public class MyCarModel {
    /**
     * 广告
     */
    public static class AdListTop {
        private List<Ad2> ad2s;
        private boolean isNeedRefresh;

        public AdListTop(List<Ad2> ad2s, boolean isNeedRefresh) {
            this.ad2s = ad2s;
            this.isNeedRefresh = isNeedRefresh;
        }

        public List<Ad2> getAd2s() {
            return ad2s;
        }

        public void setAd2s(List<Ad2> ad2s) {
            this.ad2s = ad2s;
        }

        public boolean isNeedRefresh() {
            return isNeedRefresh;
        }

        public void setNeedRefresh(boolean needRefresh) {
            isNeedRefresh = needRefresh;
        }
    }

    /**
     * 广告
     */
    public static class AdListMiddle extends WrapperModel<List<Ad2>> {
        public AdListMiddle(List<Ad2> ad2s) {
            super(ad2s);
        }
    }

    /**
     * 栏目标题
     */
    public static class ColumnTitle extends WrapperModel<Object> {
        public ColumnTitle(Object object) {
            super(object);
        }
    }

    /**
     * 福利社
     */
    public static class WelfareFunctionModel extends WrapperModel<FunctionResultWrapper> {
        public WelfareFunctionModel(FunctionResultWrapper object) {
            super(object);
        }
    }

    /**
     * 线上看车
     */
    public static class SeeCarOnlineFunctionModel extends WrapperModel<FunctionResultWrapper> {
        public SeeCarOnlineFunctionModel(FunctionResultWrapper object) {
            super(object);
        }
    }

    /**
     * 爱车列表
     */
    public static class MyCarTypeList implements Serializable {
        private List<MyCarType> carTypeList;
        private boolean isNeedRefreshOil;

        public MyCarTypeList(List<MyCarType> carTypeList, boolean isNeedRefreshOil) {
            this.carTypeList = carTypeList;
            this.isNeedRefreshOil = isNeedRefreshOil;
        }

        public MyCarTypeList(List<MyCarType> carTypeList) {
            this.carTypeList = carTypeList;
        }

        public List<MyCarType> getCarTypeList() {
            return carTypeList;
        }

        public void setCarTypeList(List<MyCarType> carTypeList) {
            this.carTypeList = carTypeList;
        }

        public boolean isNeedRefreshOil() {
            return isNeedRefreshOil;
        }

        public void setNeedRefreshOil(boolean needRefreshOil) {
            isNeedRefreshOil = needRefreshOil;
        }
    }


    /**
     * 功能图标
     */
    public static class FunctionModel {
        private FunctionResultWrapper mFunctionResultWrapper;
        private String functionName;
        private int functionIcon;
        private int functionType;

        public FunctionModel(FunctionResultWrapper functionResultWrapper, String functionName, int functionIcon) {
            mFunctionResultWrapper = functionResultWrapper;
            this.functionName = functionName;
            this.functionIcon = functionIcon;
        }

        public FunctionModel(FunctionResultWrapper functionResultWrapper, String functionName, int functionIcon, int functionType) {
            mFunctionResultWrapper = functionResultWrapper;
            this.functionName = functionName;
            this.functionIcon = functionIcon;
            this.functionType = functionType;
        }

        public FunctionResultWrapper getFunctionResultWrapper() {
            return mFunctionResultWrapper;
        }

        public void setFunctionResultWrapper(FunctionResultWrapper functionResultWrapper) {
            mFunctionResultWrapper = functionResultWrapper;
        }

        public String getFunctionName() {
            return functionName;
        }

        public void setFunctionName(String functionName) {
            this.functionName = functionName;
        }

        public int getFunctionIcon() {
            return functionIcon;
        }

        public void setFunctionIcon(int functionIcon) {
            this.functionIcon = functionIcon;
        }

        public int getFunctionType() {
            return functionType;
        }

        public void setFunctionType(int functionType) {
            this.functionType = functionType;
        }
    }

}
