package com.cloudy.linglingbang.activity.car.energy.bluetooth;

/**
 * 蓝牙连接失败埋点枚举
 */
public enum BleFailPointEnum {

    TIME_OUT(0, "连接蓝牙超时"),
    GATT_FAIL(1, "连接GATT失败"),
    GATT_TIME_OUT(2, "连接GATT超时"),
    SERVICES_FAIL(3, "发现服务失败"),
    SERVICES_TIME_OUT(4, "发现服务超时"),
    MTU_FAIL(5, "设置MTU失败"),
    MTU_TIME_OUT(6, "设置MTU超时"),
    AUTH_1_FAIL(7, "第一步鉴权写入通知描述符失败"),
    AUTH_1_TIME_OUT(8, "第一步鉴权写入通知描述符超时"),
    AUTH_2_FAIL(9, "第二步鉴权写入通知描述符失败"),
    AUTH_2_TIME_OUT(10, "第二步鉴权写入通知描述符超时"),
    AUTH_FAIL(11, "鉴权失败"),
    AUTH_TIME_OUT(12, "鉴权超时"),
    NOTIFY_CONTROL_FAIL(13, "写入通知注册控制指令通道失败"),
    NOTIFY_CONTROL_TIME_OUT(14, "写入通知注册控制指令通道超时");

    /**
     * @param code          蓝牙连接错误代码
     * @param pointDesc     蓝牙连接错误埋点描述
     */
    BleFailPointEnum(int code, String pointDesc) {
        this.code = code;
        this.pointDesc = pointDesc;
    }

    private final int code;
    private final String pointDesc;

    public int getCode() {
        return code;
    }

    public String getPointDesc() {
        return pointDesc;
    }
}
