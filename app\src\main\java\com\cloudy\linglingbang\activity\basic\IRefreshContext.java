package com.cloudy.linglingbang.activity.basic;

import android.content.Context;
import android.view.View;

import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;

import java.util.List;

import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 支持下拉刷新和加载更多的Activity或Fragment
 * <br/>使用时：实现{@linkplain #getListDataFormNet(L00bangService2, int, int)}获取数据
 * <br/>实现{@linkplain #createAdapter(List)} 创建一个Adapter
 * <br/>实现该接口后需要申明一个{@linkplain RefreshController}字段，并实现get和create方法
 * 然后，在适当的地方初始化{@linkplain RefreshController#initViews(View)}
 *
 * <AUTHOR> create at 2016/10/13 10:00
 */
public interface IRefreshContext<T> {
    /**
     * 获取context
     */
    Context getContext();

    /**
     * 创建一个recycler view的adapter
     */
    RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<T> list);

    /**
     * 获取数据
     *
     * @param pageNo 页数
     */
    Observable<BaseResponse<List<T>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize);

    /**
     * 在初始化时调用该方法，赋值给字段，可以重写以自定义
     */
    RefreshController<T> createRefreshController();

    /**
     * get，和{@linkplain #createRefreshController()}区分
     */
    RefreshController<T> getRefreshController();
}
