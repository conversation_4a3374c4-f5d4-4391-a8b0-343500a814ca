package com.cloudy.linglingbang.activity.club;

import android.app.Activity;
import android.app.Dialog;
import android.content.DialogInterface;
import android.content.Intent;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.adapter.club.ClubApplyListAdapter;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog;
import com.cloudy.linglingbang.model.club.ApplyJoinClubUser;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;

import java.util.List;

import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;

import static com.cloudy.linglingbang.activity.club.CarClubManageActivity.EXTRA_UNREAD_COUNT;

/**
 * 会员申请列表
 *
 * <AUTHOR>
 * @date 2017/11/20
 */
public class ClubApplyListActivity extends BaseRecyclerViewRefreshActivity<ApplyJoinClubUser> implements ClubApplyListAdapter.OnListener {
    private Long mChannelId;

    private ClubApplyListAdapter mAdapter;

    @Override
    protected void initialize() {
        super.initialize();
        setMiddleTitle(getResources().getString(R.string.club_member_apply));
        mChannelId = (Long) getIntentExtra(0L);
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<ApplyJoinClubUser> list) {
        mAdapter = new ClubApplyListAdapter(this, list);
        mAdapter.setCallBackListener(this);
        return mAdapter;
    }

    @Override
    public Observable<BaseResponse<List<ApplyJoinClubUser>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return service2.getApplyJoinList(mChannelId, pageNo, pageSize);
    }

    @Override
    public RefreshController<ApplyJoinClubUser> createRefreshController() {
        return super.createRefreshController()
                .setEmptyImageResId(R.drawable.ic_empty_car_club)
                .setEmptyStringResId(R.string.club_member_apply_empty);
    }

    @Override
    public void processApply(final int position, final boolean isPass) {
        String content;
        if (isPass) {
            content = getString(R.string.club_apply_pass);
        } else {
            content = getString(R.string.club_apply_refuse);
        }
        if (getData().size() > position && getData().get(position) != null) {
            final ApplyJoinClubUser applyJoinClubUser = getData().get(position);
            Dialog dialog = new CommonAlertDialog(this, content, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    if (getData().size() > position && getData().get(position) != null) {
                        processJoinApply(applyJoinClubUser, isPass);
                    }
                }
            });
            dialog.show();
        } else {
            ToastUtil.showMessage(this, "您操作的太快了~");
        }
    }

    /**
     * 通过或者拒绝加入车友会
     *
     * @param applyJoinClubUser
     * @param isPass
     */
    private void processJoinApply(final ApplyJoinClubUser applyJoinClubUser, boolean isPass) {
        L00bangRequestManager2.getServiceInstance()
                .processJoinApply(mChannelId, applyJoinClubUser.getChannelApplyId(), applyJoinClubUser.getApplyUserIdStr(), isPass)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<Object>(ClubApplyListActivity.this) {
                    @Override
                    public void onSuccess(Object o) {
                        super.onSuccess(o);
                        ToastUtil.showMessage(ClubApplyListActivity.this, getString(R.string.club_request_success));
                        mAdapter.removeListItem(applyJoinClubUser);
                        //如果删除之后列表为空，手动刷新列表
                        if (getData().size() == 0) {
                            getRefreshController().onRefresh();
                        }
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                    }
                });

    }

    @Override
    protected void onBack() {
        //退出前一个页面，更新小红点
        if (getData() != null) {
            Intent intent = new Intent();
            intent.putExtra(EXTRA_UNREAD_COUNT, getData().size());
            setResult(Activity.RESULT_OK, intent);
            finish();
        } else {
            super.onBack();
        }

    }
}
