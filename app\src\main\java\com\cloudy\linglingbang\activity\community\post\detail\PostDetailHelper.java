package com.cloudy.linglingbang.activity.community.post.detail;

import android.app.Activity;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.app.util.TitleGradientHelper;
import com.cloudy.linglingbang.app.widget.recycler.EmptySupportedRecyclerView;
import com.cloudy.linglingbang.model.postcard.PostCard;

import androidx.appcompat.widget.Toolbar;

/**
 * 帖子详情助手
 *
 * <AUTHOR>
 * @date 2018/11/21
 */
public class PostDetailHelper {
    /**
     * 按帖子调整布局，由于外部没有传帖子类型，先加载帖子详情再作调整
     * 如果外部传了帖子类型，也可以加载前就调整布局
     */
    public static void adjustLayoutByPostcard(Activity activity, PostCard postCard) {
        if (activity == null || postCard == null) {
            return;
        }
        String coverImage = postCard.getCoverImage();
        boolean hasCover = !TextUtils.isEmpty(coverImage);
        //有封面
        RelativeLayout rlContent = activity.findViewById(R.id.rl_content);
        ViewGroup.LayoutParams layoutParams = rlContent.getLayoutParams();
        if (layoutParams instanceof RelativeLayout.LayoutParams) {
            RelativeLayout.LayoutParams rlLayoutParams = (RelativeLayout.LayoutParams) layoutParams;
            if (hasCover) {
                /*
                有封面，under title，移除 below 规则
                原本应该是有正向有逆向的，考虑到帖子类型只要确定就不会再变，因此只处理正向改变
                 */
                rlLayoutParams.addRule(RelativeLayout.BELOW, 0);
                updateTitleBar(activity);
            }
        }
    }

    /**
     * 更新标题栏
     */
    private static void updateTitleBar(Activity activity) {
        if (activity instanceof BaseRecyclerViewRefreshActivity) {
            BaseRecyclerViewRefreshActivity refreshActivity = (BaseRecyclerViewRefreshActivity) activity;
            RefreshController refreshController = refreshActivity.getRefreshController();
            if (refreshController != null) {
                EmptySupportedRecyclerView recyclerView = refreshController.getRecyclerView();
                if (recyclerView != null) {
                    //取消标题
                    refreshActivity.setLeftTitle("");
                    TitleGradientHelper titleGradientHelper = new PostDetailTitleGradientHelper(activity.getWindow().getDecorView());
                    titleGradientHelper.initRecyclerView(recyclerView);
                }
            }
        }
    }

    private static class PostDetailTitleGradientHelper extends TitleGradientHelper {

        /**
         * 没有从 Activity 获取，重新 find 一次
         */
        private Toolbar mToolbar;

        public PostDetailTitleGradientHelper(View rootView) {
            super(rootView, 0);
        }

        @Override
        protected void initView(View rootView) {
            super.initView(rootView);
            View titleView = rootView.findViewById(R.id.ll_title);
            if (titleView != null) {
                setTitleOffsetHeight(titleView.getHeight());
            }

            mToolbar = rootView.findViewById(R.id.common_toolbar);
            if (mToolbar != null) {
                //新建颜色背景，同时设为透明
                mToolbar.setBackgroundDrawable(new ColorDrawable(Color.WHITE));
                updateBackgroundAlpha(mToolbar, 0);
                //设置正常状态
                onScrolledChanged(true);
            }
        }

        @Override
        protected void onScrolledChanged(float radio) {
            super.onScrolledChanged(radio);
            updateBackgroundAlpha(mToolbar, radio);
        }

        @Override
        protected void onScrolledChanged(boolean normalRadio) {
            super.onScrolledChanged(normalRadio);
            if (mToolbar != null) {
                mToolbar.setNavigationIcon(normalRadio ? R.drawable.ic_post_detail_back_normal : R.drawable.ic_post_detail_back_dark);
                MenuItem item = mToolbar.getMenu().findItem(R.id.action_right_text);
                if (item != null) {
                    item.setIcon(normalRadio ? R.drawable.ic_post_detail_share_normal : R.drawable.ic_post_detail_share_dark);
                    item.setTitle("");
                }
            }
        }
    }
}
