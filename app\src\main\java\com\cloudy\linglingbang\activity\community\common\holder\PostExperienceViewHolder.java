package com.cloudy.linglingbang.activity.community.common.holder;

import android.content.Context;
import android.text.format.DateFormat;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.model.postcard.ExperiencePostCar;
import com.cloudy.linglingbang.model.postcard.PostCard;

/**
 * 提车作业帖
 *
 * <AUTHOR>
 * @date 2018/6/27
 */
public class PostExperienceViewHolder extends BasePostChildViewHolder {
    private LinearLayout mLlBuyCarInfo;
    private TextView mTvCarName;
    private TextView mTvBuyCarTime;
    private TextView mTvBuyCarShop;

    public PostExperienceViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        mLlBuyCarInfo = itemView.findViewById(R.id.ll_buy_car_info);
        mTvCarName = itemView.findViewById(R.id.tv_car_name);
        mTvBuyCarTime = itemView.findViewById(R.id.tv_buy_car_time);
        mTvBuyCarShop = itemView.findViewById(R.id.tv_buy_car_shop);
    }

    @Override
    public void bindTo(PostCard postCard) {
        super.bindTo(postCard);
        Context context = mLlBuyCarInfo.getContext();
        //显示提车作业帖
        ExperiencePostCar experiencePostCar = postCard.getPostCarVo();
        if (experiencePostCar == null) {
            mLlBuyCarInfo.setVisibility(View.GONE);
        } else {
            mLlBuyCarInfo.setVisibility(View.VISIBLE);
            mTvCarName.setText(context.getString(R.string.item_post_my_car, experiencePostCar.getCarTypeName()));
            mTvBuyCarTime.setText(DateFormat.format(context.getString(R.string.item_post_buy_car_time), experiencePostCar.getPurchaseDateMillis()));
            mTvBuyCarShop.setText(context.getString(R.string.item_post_buy_car_shop, experiencePostCar.getShopName()));
        }
    }
}
