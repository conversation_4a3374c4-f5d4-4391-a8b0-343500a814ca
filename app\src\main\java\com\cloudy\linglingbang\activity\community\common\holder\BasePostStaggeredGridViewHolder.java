package com.cloudy.linglingbang.activity.community.common.holder;

import android.view.View;

import java.util.ArrayList;

/**
 * 瀑布流的viewHolder
 *
 * <AUTHOR>
 * @date 2019-11-26
 */
public class BasePostStaggeredGridViewHolder extends BasePostViewHolder {

    public BasePostStaggeredGridViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    protected void initChildViewHolder(View itemView) {
        mChildViewHolderList = new ArrayList<>();
        mChildViewHolderList.add(new PostAuthorStaggeredGridViewHolder(itemView));
        mChildViewHolderList.add(new PostContentViewHolder(itemView));
        mChildViewHolderList.add(new PostImageStaggeredGridViewHolder(itemView));
    }

}
