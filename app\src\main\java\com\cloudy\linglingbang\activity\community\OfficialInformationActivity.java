package com.cloudy.linglingbang.activity.community;

import android.content.Context;
import android.graphics.Color;
import android.view.View;
import android.widget.LinearLayout;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.adapter.postCard.PostCardListAdapter;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;

import java.util.List;

import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 官方资讯
 *
 * <AUTHOR>
 * @date 2022/2/16
 */
public class OfficialInformationActivity extends BaseRecyclerViewRefreshActivity<PostCard> {

    private String mUserIdStr;

    @Override
    protected void initialize() {
        super.initialize();
        setMiddleTitle(getString(R.string.official_information));
        mUserIdStr = getIntent().getStringExtra(IntentUtils.INTENT_EXTRA_COMMON);
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<PostCard> list) {
        PostCardListAdapter adapter = new PostCardListAdapter(getContext(), list);
        adapter.setOnItemClickListener((itemView, position) -> {
            JumpPageUtil.goToPost(OfficialInformationActivity.this, getData().get(position), "话题详情");
        });
        return adapter;
    }

    @Override
    public Observable<BaseResponse<List<PostCard>>> getListDataFormNet(L00bangService2 service2, final int pageNo, int pageSize) {
        return service2.getUserPost(pageNo, pageSize, mUserIdStr, 1);
    }

    @Override
    public RefreshController<PostCard> createRefreshController() {
        return new RefreshController<PostCard>(this) {

            @Override
            public void initViews(View rootView) {
                super.initViews(rootView);
                swipeToLoadLayout.setBackgroundColor(Color.WHITE);
                recyclerView.setBackgroundColor(Color.TRANSPARENT);
                //去掉列表和title直接的间隙
                LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) swipeToLoadLayout.getLayoutParams();
                params.topMargin = 0;
                swipeToLoadLayout.setLayoutParams(params);
            }

            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return null;
            }
        };
    }
}
