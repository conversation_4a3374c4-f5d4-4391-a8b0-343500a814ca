package com.cloudy.linglingbang.activity.service.newenergy;

import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.view.View;
import android.widget.ImageView;
import android.widget.ScrollView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.DensityUtil;
import com.cloudy.linglingbang.app.util.ShareUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.share.CarUsedShareUtils;
import com.cloudy.linglingbang.app.util.span.SpanUtils;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.NormalSubscriber;
import com.cloudy.linglingbang.model.server.CarUsedReportInfo;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.Group;
import butterknife.BindView;
import butterknife.OnClick;

/**
 * 用车报告
 *
 * <AUTHOR>
 * @date 2019-09-23
 */
public class CarUsedReportActivity extends BaseActivity {
    @BindView(R.id.scroll_view)
    ScrollView mScrollView;
    /**
     * 排名
     */
    @BindView(R.id.tv_text_car_driver)
    TextView tvMileageUp;
    @BindView(R.id.tv_car_driven_distance)
    TextView tvDrivenDistance;//行驶路程
    @BindView(R.id.iv_super_driver)
    ImageView ivSuperDriver;//勋章
    @BindView(R.id.tv_car_check_num)
    TextView tvCheckNum;//车辆体检次数
    @BindView(R.id.tv_car_motor_status)
    TextView tvMotorStatus;//电机状态
    @BindView(R.id.tv_car_batter_status)
    TextView tvBatterStatus;//电池状态
    @BindView(R.id.tv_save_money_count)
    TextView tvSaveMoneyCount;//节约钱数
    @BindView(R.id.group_detail)
    Group groupDetail;
    @BindView(R.id.group_default)
    Group groupDefault;
    private CarUsedReportInfo mCarUsedReportInfo;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_car_used_report);
    }

    @Override
    protected void initialize() {
        mScrollView.setBackgroundResource(R.color.white);
        setMiddleTitle(getString(R.string.title_my_used_car_report));
        String vin = getIntentStringExtra();
        requestUsedCarReport(vin);
    }

    /**
     * 请求用车报告
     */
    private void requestUsedCarReport(String vin) {
        L00bangRequestManager2.getServiceInstance()
                .getCarUserReport(vin)
                .compose(L00bangRequestManager2.<CarUsedReportInfo>setSchedulers())
                .subscribe(new NormalSubscriber<CarUsedReportInfo>(this) {
                    @Override
                    public void onSuccess(CarUsedReportInfo carUsedReportInfo) {
                        super.onSuccess(carUsedReportInfo);
                        mCarUsedReportInfo = carUsedReportInfo;
                        requestSuccess();
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                        ToastUtil.showMessage(CarUsedReportActivity.this, e.getMessage());
                    }
                });

    }

    /**
     * 请求用车报告接口成功
     */
    private void requestSuccess() {
        if (mCarUsedReportInfo == null) {
            return;
        }
        mScrollView.setBackgroundResource(R.drawable.bg_car_used_report_page);
        groupDefault.setVisibility(View.GONE);
        groupDetail.setVisibility(View.VISIBLE);

        //七日总里程排名
        if (mCarUsedReportInfo.getRankNoOrZero() > 0) {
            tvMileageUp.setVisibility(View.VISIBLE);
            AppUtil.setTypeface(this, tvMileageUp);
            String upUser = getString(R.string.txt_total_mileage_up, String.valueOf(mCarUsedReportInfo.getRankNoOrZero())).replace("\n", "");
            SpanUtils.setPartSpanText(tvMileageUp, upUser, getResources().getColor(R.color.color_fdad00),
                    getResources().getDimension(R.dimen.activity_set_text_28), 19, upUser.length() - 1);
        } else {
            tvMileageUp.setVisibility(View.GONE);
        }


        //行驶距离
        AppUtil.setTypeface(this, tvDrivenDistance);
        String totalMileage = getString(R.string.txt_total_mileage_des, String.valueOf(mCarUsedReportInfo.getMileCountOrZero()));
        SpanUtils.setPartSpanText(tvDrivenDistance, totalMileage, getResources().getColor(R.color.white),
                getResources().getDimension(R.dimen.activity_set_text_48), totalMileage.indexOf("k"), totalMileage.length());
        //根据行驶距离,设置不同的图片
        int totalMileages = mCarUsedReportInfo.getMileCountOrZero();
        Drawable drawable = getResources().getDrawable(R.drawable.ic_newenergy_city_elf);
        if (totalMileages <= 0) {
            drawable = getResources().getDrawable(R.drawable.ic_newenergy_invincible_house_cafe);
        } else if (totalMileages > 0 && totalMileages <= 350) {
            drawable = getResources().getDrawable(R.drawable.ic_newenergy_junior_driver);
        } else if (totalMileages > 350 && totalMileages <= 700) {
            drawable = getResources().getDrawable(R.drawable.ic_newenergy_intermediate_car_god);
        } else if (totalMileages > 700 && totalMileages <= 1050) {
            drawable = getResources().getDrawable(R.drawable.ic_newenergy_thunder_car_god);
        } else if (totalMileages > 1050 && totalMileages <= 1400) {
            drawable = getResources().getDrawable(R.drawable.ic_newenergy_city_elf);
        } else if (totalMileages > 1400) {
            drawable = getResources().getDrawable(R.drawable.ic_newenergyshuttle_wizard);
        }
        ivSuperDriver.setImageDrawable(drawable);

        //体检次数
        AppUtil.setTypeface(this, tvCheckNum);
        String checkNum = getString(R.string.txt_check_num, String.valueOf(mCarUsedReportInfo.getCheckCountOrZero()));
        SpannableString checkNumSpannableString = new SpannableString(checkNum);
        checkNumSpannableString.setSpan(new AbsoluteSizeSpan(DensityUtil.sp2px(this, 15)), checkNum.indexOf(" "), checkNum.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        tvCheckNum.setText(checkNumSpannableString);

        //电机状态
        if (!TextUtils.isEmpty(mCarUsedReportInfo.getEngineScore())) {
            tvMotorStatus.setText(mCarUsedReportInfo.getEngineScore());
            if ("优".equals(mCarUsedReportInfo.getEngineScore())) {
                tvMotorStatus.setTextColor(getResources().getColor(R.color.white));
            } else {
                tvMotorStatus.setTextColor(getResources().getColor(R.color.color_fdad00));
            }
        }

        //电池状态
        if (!TextUtils.isEmpty(mCarUsedReportInfo.getBatScore())) {
            tvBatterStatus.setText(mCarUsedReportInfo.getBatScore());
            if ("优".equals(mCarUsedReportInfo.getBatScore())) {
                tvBatterStatus.setTextColor(getResources().getColor(R.color.white));
            } else {
                tvBatterStatus.setTextColor(getResources().getColor(R.color.color_fdad00));
            }
        }

        //节省钱数
        AppUtil.setTypeface(this, tvSaveMoneyCount);
        String saveMoney = getString(R.string.txt_save_money_num, String.valueOf(mCarUsedReportInfo.getSaveCount()));
        SpannableString spannableString = new SpannableString(saveMoney);
        spannableString.setSpan(new AbsoluteSizeSpan(DensityUtil.sp2px(this, 15)), saveMoney.indexOf(" "), saveMoney.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        tvSaveMoneyCount.setText(spannableString);
    }

    @OnClick(R.id.ic_share_car_used_report)
    void clickCarUsedReport() {
        CarUsedShareUtils.Extra extra = new CarUsedShareUtils.Extra();
        extra.setBeyondUserNum(mCarUsedReportInfo.getRankNoOrZero());
        extra.setDrivenDistance(mCarUsedReportInfo.getMileCountOrZero());
        extra.setMedalUrl("");
        extra.setCheckNum(mCarUsedReportInfo.getCheckCountOrZero());
        extra.setMotorStatus(mCarUsedReportInfo.getEngineScore());
        extra.setBatteryStatus(mCarUsedReportInfo.getBatScore());
        extra.setSaveMoneyNum(mCarUsedReportInfo.getSaveCount());
        extra.setShareUrl(mCarUsedReportInfo.getCodeUrl());
        CarUsedShareUtils.getInstance().startShare(this, extra);
    }

    @Override
    protected void onBack() {
        CarUsedShareUtils.getInstance().destroy();
        super.onBack();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        ShareUtil shareUtil = CarUsedShareUtils.getInstance().getShareUtil();
        if (shareUtil != null) {
            shareUtil.onActivityResult(requestCode, resultCode, data);
        }
    }

}
