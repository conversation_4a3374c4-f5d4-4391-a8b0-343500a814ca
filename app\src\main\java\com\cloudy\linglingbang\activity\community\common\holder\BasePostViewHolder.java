package com.cloudy.linglingbang.activity.community.common.holder;

import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.community.common.BasePostAdapter;
import com.cloudy.linglingbang.activity.community.common.PostFlagsEnum;
import com.cloudy.linglingbang.activity.community.post.CommunityUtils;
import com.cloudy.linglingbang.app.util.ViewHolderUtils;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.postcard.PostCard;

import java.util.ArrayList;
import java.util.List;

/**
 * 相当于填充过程
 * ChildViewHolder 继承 BasePostChildViewHolder
 * 不可继承该类，否则会循环
 * <p>
 * 如果需要修改布局
 * 可以添加新的布局，重写 {@link BasePostAdapter#getDefaultItemLayoutRes()}
 * 如果只修改部分，可以改布局中 include 的内容
 * <p>
 * 如果代码修改可以实现，可以重写 {@link BasePostAdapter#createDefaultViewHolderWithoutSetting(View)}
 * 新的 ViewHolder 继承 {@link BasePostViewHolder}
 * 在 {@link #initChildViewHolder(View)} 中修改子 ViewHolder
 * 在添加子 ViewHolder 时，应继承 {@link BasePostChildViewHolder}
 * 如果布局修后缺少部份 View 要注意判空
 *
 * <AUTHOR>
 * @date 2018/6/22
 */
public class BasePostViewHolder extends BaseRecyclerViewHolder<PostCard> {
    /**
     * 与帖子图标是一样的逻辑，但是区分开
     */
    public int mFlags;
    private int mIconFlags;
    protected List<BasePostChildViewHolder> mChildViewHolderList;
    private CommunityUtils.PostOperateUtils.OperateListener mOperateListener;

    public BasePostViewHolder(View itemView) {
        super(itemView);
        //如果直接创建，就需要设默认值
        mFlags = PostFlagsEnum.addDefaultFlags(mFlags);
        mIconFlags = PostIconEnum.addDefaultShowIconInfo(mFlags);
    }

    /**
     * 由于初始化是在构造函数中完成的，所以不可以通过构造函数传 iconFlags
     */
    public BasePostViewHolder setIconFlags(int iconFlags) {
        mIconFlags = iconFlags;
        return this;
    }

    public BasePostViewHolder setFlags(int flags) {
        mFlags = flags;
        return this;
    }

    public BasePostViewHolder addFlags(PostFlagsEnum flagsEnum) {
        mFlags = flagsEnum.addFlags(mFlags);
        return this;
    }

    public BasePostViewHolder setOperateListener(CommunityUtils.PostOperateUtils.OperateListener operateListener) {
        mOperateListener = operateListener;
        return this;
    }

    /**
     * 要注意该方法从构造函数调用
     * 除非从构造函数传递字段，否则无法使用字段
     * 可在 bindTo 中设置，或者是在构造函数后，手动调用 initChildViewHolder
     * <p>
     * 推迟到绑定的时候再初始化
     */
    protected void initChildViewHolder(View itemView) {
        mChildViewHolderList = new ArrayList<>();
        mChildViewHolderList.add(new PostAuthorViewHolder(itemView));
        mChildViewHolderList.add(new PostExperienceViewHolder(itemView));
        mChildViewHolderList.add(new PostContentViewHolder(itemView));
        mChildViewHolderList.add(new PostImageViewHolder(itemView));
        mChildViewHolderList.add(new PostBottomInfoViewHolder(itemView));
    }

    @Override
    public void bindTo(PostCard postCard, int position) {
        super.bindTo(postCard, position);
        if (mChildViewHolderList == null) {
            //这里应该注意的是，bindTo 是由多个 ViewHolder 调用的，但是初始化只会调用一次
            //所以要生成不同的 childViewHolder 是不可以的
            //必须重写 bindTo 调用 childViewHolder 的不同方法
            initChildViewHolder(itemView);
        }
        for (BasePostChildViewHolder holder : mChildViewHolderList) {
            if (holder instanceof PostContentViewHolder) {
                ((PostContentViewHolder) holder).setIconFlags(mIconFlags);
            }

            if (holder instanceof PostAuthorViewHolder) {
                ((PostAuthorViewHolder) holder).setFlags(mFlags);
                ((PostAuthorViewHolder) holder).setOperateListener(mOperateListener);
            }

            holder.bindTo(postCard);
        }
        ViewHolderUtils.setVisibility(!PostFlagsEnum.GONE_AUTHOR_INFO.checkFlags(mFlags), itemView.findViewById(R.id.rl_author));
    }

    /**
     * 是否需要传 adapter
     */
    public boolean needSetAdapter() {
        return false;
    }
}
