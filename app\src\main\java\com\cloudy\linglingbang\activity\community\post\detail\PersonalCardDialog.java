package com.cloudy.linglingbang.activity.community.post.detail;

import android.content.Context;
import android.content.DialogInterface;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.app.log.LLBTextUtils;
import com.cloudy.linglingbang.app.widget.dialog.alert.AlertController;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog;
import com.cloudy.linglingbang.model.postcard.PersonalCard;

/**
 * 个人名片信息展示对话框
 *
 * <AUTHOR>
 * @date 2019-11-28
 */
public class PersonalCardDialog extends CommonAlertDialog implements View.OnClickListener {
    private PersonalCard mPersonalCard;

    public PersonalCardDialog(Context context, PersonalCard personalCard) {
        super(context);
        this.mPersonalCard = personalCard;
        AlertController alertController = getAlertController();
        alertController.setButton(DialogInterface.BUTTON_POSITIVE, context.getString(R.string.label_driving_rec_close), null, null);
        alertController.setButton(DialogInterface.BUTTON_NEGATIVE, "拨打电话", new OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (mPersonalCard != null && !TextUtils.isEmpty(mPersonalCard.getPhone())) {
                    IntentUtils.startActivityByDialIntent(mContext, mPersonalCard.getPhone());
                }
            }
        }, null);
    }

    @Override
    protected void initView() {
        super.initView();
        TextView tvName = findViewById(R.id.alertTitle);
        TextView tvMobile = findViewById(R.id.tv_mobile);
        TextView tvWX = findViewById(R.id.tv_wx);
        findViewById(R.id.iv_copy_wx).setOnClickListener(this);

        if (mPersonalCard != null) {
            tvMobile.setText(mContext.getString(R.string.text_post_detail_card_mobile, mPersonalCard.getPhone()));
            tvWX.setText(mContext.getString(R.string.text_post_detail_card_wx, mPersonalCard.getWx()));
            tvName.setText(mPersonalCard.getName());
        }
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.iv_copy_wx && mPersonalCard != null) {
            CharSequence str = mPersonalCard.getWx();
            if (!TextUtils.isEmpty(str)) {
                LLBTextUtils.copyText(v.getContext(), str, true);
            }
        }
    }

    @Override
    protected int getDefaultLayoutResId() {
        return R.layout.dialog_personal_card;
    }
}
