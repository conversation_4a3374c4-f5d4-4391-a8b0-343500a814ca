package com.cloudy.linglingbang.activity.club;

import android.app.Activity;
import android.content.Intent;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.model.club.ApplyJoinClubUser;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;

import androidx.appcompat.widget.Toolbar;
import butterknife.BindView;

/**
 * 申请加入车友会页面
 *
 * <AUTHOR>
 * @date 2017/11/17
 */
public class ApplyJoinClubActivity extends BaseActivity {
    /**
     * 申请理由
     */
    @BindView(R.id.et_content)
    TextView mEtContent;
    /**
     * 数量
     */
    @BindView(R.id.tv_text_count)
    TextView mTvTextCount;

    private Long mChannelId;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_apply_join_club);
    }

    @Override
    public boolean onPrepareOptionsMenu(Menu menu) {
        menu.clear();
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(R.menu.menu_done, menu);
        mToolbar.setOnMenuItemClickListener(new Toolbar.OnMenuItemClickListener() {
            @Override
            public boolean onMenuItemClick(MenuItem item) {
                /*if(TextUtils.isEmpty(mEtContent.getText().toString().trim())){
                    ToastUtil.showMessage(ApplyJoinClubActivity.this,"您没有填写任何内容~");
                }else{*/
                ApplyJoinClubUser applyJoinClubUser = new ApplyJoinClubUser();
                applyJoinClubUser.setChannelId(mChannelId);
                applyJoinClubUser.setRemark(mEtContent.getText().toString().trim());
                L00bangRequestManager2.getServiceInstance()
                        .applyJoinChannel(applyJoinClubUser)
                        .compose(L00bangRequestManager2.setSchedulers())
                        .subscribe(new ProgressSubscriber<Object>(ApplyJoinClubActivity.this) {
                            @Override
                            public void onSuccess(Object o) {
                                super.onSuccess(o);
                                ToastUtil.showMessage(ApplyJoinClubActivity.this, getString(R.string.club_apply_success));
                                Intent intent = new Intent();
                                setResult(Activity.RESULT_OK, intent);
                                finish();
                            }
                        });

//                }
                return true;
            }
        });
        return true;
    }

    @Override
    protected void initialize() {
        setMiddleTitle(getString(R.string.club_apply_join_club));
        mChannelId = (Long) IntentUtils.getExtra(getIntent(), 0L);
        if (mChannelId == 0) {
            return;
        }
        mEtContent.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s.toString().trim().length() == 0) {
                    mTvTextCount.setText(20 + "");
                } else {
                    mTvTextCount.setText(20 - s.length() + "");
                }
                if (s.length() == 20) {
                    ToastUtil.showMessage(ApplyJoinClubActivity.this, getString(R.string.has_max_length));
                }

            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

    }
}
