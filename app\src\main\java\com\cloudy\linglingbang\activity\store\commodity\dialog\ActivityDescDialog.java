package com.cloudy.linglingbang.activity.store.commodity.dialog;

import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.method.ScrollingMovementMethod;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog;

/**
 * 活动弹窗说明
 *
 * <AUTHOR>
 * @date 2022/10/16
 */
public class ActivityDescDialog extends CommonAlertDialog {
    public ActivityDescDialog(Context context, String message) {
        super(context, message, context.getString(R.string.dialog_set_charge_success_btn), null, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
            }
        }, null);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        getAlertController().setTitle(mContext.getResources().getString(R.string.commodity_activity_tip));
        super.onCreate(savedInstanceState);
        getAlertController().getMessageView().setMovementMethod(ScrollingMovementMethod.getInstance());
    }

    @Override
    protected int getDefaultLayoutResId() {
        return R.layout.dialog_acitivity_desc;
    }
}
