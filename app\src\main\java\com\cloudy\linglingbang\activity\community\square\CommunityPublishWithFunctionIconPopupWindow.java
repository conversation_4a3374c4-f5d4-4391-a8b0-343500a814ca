package com.cloudy.linglingbang.activity.community.square;

import android.Manifest;
import android.content.Context;
import android.content.res.Resources;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.community.CommunityDoPostImageTextActivity;
import com.cloudy.linglingbang.activity.community.linglab.CommunityDoPostLingLabActivity;
import com.cloudy.linglingbang.activity.community.linglab.LingLabDoPostUtils;
import com.cloudy.linglingbang.activity.newcommunity.PublishGoodsExchangeInfoActivity;
import com.cloudy.linglingbang.activity.welfare.CarInfoEvent;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.DensityUtil;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.PreferenceUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.imagetext.ImageTextAdapter;
import com.cloudy.linglingbang.app.widget.imagetext.ImageTextBean;
import com.cloudy.linglingbang.app.widget.imagetext.ImageTextViewHolder;
import com.cloudy.linglingbang.app.widget.popup.BaseListPopupWindow;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.app.widget.recycler.EmptySupportedRecyclerView;
import com.cloudy.linglingbang.constants.WebUrlConfigConstant;
import com.cloudy.linglingbang.model.community.ContentGroupInfo;
import com.cloudy.linglingbang.model.entrance.FunctionEntrance;
import com.cloudy.linglingbang.model.entrance.FunctionEntranceEnum;
import com.cloudy.linglingbang.model.entrance.HomeFunctionResult;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 社区发言弹窗
 *
 * <AUTHOR>                                                                                                                                                       汝寿
 * @date 2018/6/22
 */
public class CommunityPublishWithFunctionIconPopupWindow extends BaseListPopupWindow<ImageTextBean> implements BaseListPopupWindow.OnChoiceClickListener {

    private int mTopDialogBgResId;
    private int mBottomDialogBgResId;
    private String mChannelId;
    private String mChannelName;
    private ContentGroupInfo mContentGroupInfo;
    private int mOpenFrom;
    private Context mCtx;

    private EmptySupportedRecyclerView mRvFunctionIcon;
    private TextView mTvFunctionTypeName;
    private LinearLayout mLlTop;
    LingLabDoPostUtils mLingLabDoPostUtils;

    public CommunityPublishWithFunctionIconPopupWindow(Context context) {
        super(context, 0);
        mCtx = context;
        refreshData();
        init(mData, this);
    }

    private void refreshFunction() {
        if (mLlTop == null) {
            mLlTop = getContentView().findViewById(R.id.ll_top);
        }
        if (mTvFunctionTypeName == null) {
            mTvFunctionTypeName = getContentView().findViewById(R.id.tv_function_type_name);
        }
        if (mRvFunctionIcon == null) {
            mRvFunctionIcon = getContentView().findViewById(R.id.recycler_view_function);
        }
        mRvFunctionIcon.setLayoutManager(new GridLayoutManager(getContext(), 4));
        RecyclerView.ItemDecoration itemDecoration = createItemDecoration(getContext());
        if (itemDecoration != null) {
            mRvFunctionIcon.addItemDecoration(itemDecoration);
        }
        String key = UserUtils.isRetentiveCustomer() ? HomeFunctionResult.EXTRA_MEMBER : HomeFunctionResult.EXTRA_NORMAL;
        String data = PreferenceUtil.getStringPreference(getContext(), key, "");
        if (!TextUtils.isEmpty(data)) {
            mLlTop.setVisibility(View.VISIBLE);
            HomeFunctionResult homeFunctionResult = new Gson().fromJson(data, HomeFunctionResult.class);
            List<FunctionEntrance> functionalInterfaceList = homeFunctionResult.getFunctionEntranceList().size() > 4 ? homeFunctionResult.getFunctionEntranceList().subList(0, 3) : homeFunctionResult.getFunctionEntranceList();
            if (functionalInterfaceList.size() <= 0) {
                mLlTop.setVisibility(View.GONE);
                if (mRecyclerView != null) {
                    mRecyclerView.setBackgroundResource(R.drawable.bg_home_publish);
                }
            } else {
                if (mRecyclerView != null) {
                    mRecyclerView.setBackgroundResource(R.drawable.bg_home_publish_with_shade);
                }
                mLlTop.setVisibility(View.VISIBLE);
                PublishFunctionAdapter publishFunctionAdapter = new PublishFunctionAdapter(getContext(), functionalInterfaceList);
                mRvFunctionIcon.setLayoutManager(new GridLayoutManager(getContext(), functionalInterfaceList.size()));
                publishFunctionAdapter.setOnItemClickListener(new BaseRecyclerViewHolder.OnItemClickListener() {
                    @Override
                    public void onItemClick(View itemView, int position) {
                        JumpPageUtil.goHomeFunctionEntrance(itemView.getContext(), homeFunctionResult.getFunctionEntranceList().get(position), homeFunctionResult.getAppShowName(), new CarInfoEvent(""));
                    }
                });
                mRvFunctionIcon.setAdapter(publishFunctionAdapter);
                mTvFunctionTypeName.setText(homeFunctionResult.getAppShowName());
            }
        } else {
            mLlTop.setVisibility(View.GONE);
        }

    }

    @Override
    protected void initViews() {
        super.initViews();
    }

    @Override
    protected int getPopAnimationStyle() {
        return R.style.Animation_Bottom_Dialog;
    }

    @Override
    protected void initAttributes() {
        super.initAttributes();
        //setWidth((int) (DeviceUtil.getScreenWidth() - getContext().getResources().getDimension(R.dimen.normal_100)));
        setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
    }

    protected void refreshData() {
        if (mData == null) {
            mData = new ArrayList<>(2);
        } else {
            mData.clear();
        }
        //mData.add(new ImageTextBean(R.string.community_publish_ling_lab, R.drawable.ic_community_publish_ling_lab));
        mData.add(new ImageTextBean(R.string.community_publish_type_image_text, R.drawable.ic_community_publish_type_image_text));
        mData.add(new ImageTextBean(R.string.community_publish_type_video, R.drawable.ic_community_publish_type_apply_author));
//        mData.add(new ImageTextBean(R.string.community_publish_question, R.drawable.ic_community_publish_type_question));
        mData.add(new ImageTextBean(R.string.store_service_mm, R.drawable.ic_community_publish_type_mm));

        refreshFunction();
    }

    public CommunityPublishWithFunctionIconPopupWindow(Context context, List<ImageTextBean> data, OnChoiceClickListener onChoiceClickListener) {
        super(context, data, onChoiceClickListener);
    }

    public CommunityPublishWithFunctionIconPopupWindow setBottomDialogBgResId(int bottomDialogBgResId) {
        mBottomDialogBgResId = bottomDialogBgResId;
        return this;
    }

    public CommunityPublishWithFunctionIconPopupWindow setTopDialogBgResId(int topDialogBgResId) {
        mTopDialogBgResId = topDialogBgResId;
        return this;
    }

    public int getTopDialogBgResId() {
        return mTopDialogBgResId == 0 ? R.drawable.bg_home_publish : mTopDialogBgResId;
    }

    public int getBottomDialogBgResId() {
        return mBottomDialogBgResId == 0 ? R.drawable.bg_pop_community_publish_drop : mBottomDialogBgResId;
    }

    @Override
    protected CharSequence getItemString(ImageTextBean imageTextBean) {
        CharSequence text = imageTextBean.getText();
        if (text != null) {
            //文字不为空
            return text;
        } else {
            int textResId = imageTextBean.getTextResId();
            if (textResId > 0) {
                //显示文字资源
                return getContext().getString(textResId);
            }
        }
        return "";
    }

    @Override
    protected BaseRecyclerViewAdapter<ImageTextBean> createAdapter(Context context, List<ImageTextBean> data) {
        return new PublishTypeAdapter(context, data);
    }

    @Override
    protected RecyclerView.LayoutManager createLayoutManager(Context context) {
        //如果使用 LinearLayoutManager，则 item 的宽度不可以是 match_parent
        /*
        如果要改 size，要注意
        把背景改为 .9
        偏移高度重新计算
         */
        return new GridLayoutManager(context, getData().size());
    }

    @Override
    protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
        return null;
    }

    @Override
    protected int getItemLayoutRes() {
        return R.layout.item_home_publish;
    }

    @Override
    protected int getDefaultLayoutResId() {
        return R.layout.pop_community_publish_with_function_icon;
    }

    public void show(View view, boolean showAsDrop) {
        //可以改为 .9
        //int backgroundResId = showAsDrop ? getTopDialogBgResId() : getBottomDialogBgResId();
        //mRecyclerView.setBackgroundDrawable(getContext().getResources().getDrawable(backgroundResId));
        refreshData();
        if (mRecyclerView.getAdapter() != null) {
            mRecyclerView.getAdapter().notifyDataSetChanged();
        }
        View contentView = getContentView();
        contentView.setPadding(0, 0, DensityUtil.dip2px(getContext(), 4), 0);
        // 测量contentView
        contentView.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED);
        // 计算contentView的高宽
        final int windowHeight = contentView.getMeasuredHeight();
        showAsDropDown(view, 0, -windowHeight - view.getHeight() - DensityUtil.dip2px(getContext(), 30), Gravity.NO_GRAVITY);
    }

    @Override
    public boolean onChoiceClick(int chosenIndex, String chosenText) {
        final Resources resources = getContext().getResources();
        //过滤出提车作业帖和最新
        if (mContentGroupInfo != null && mContentGroupInfo.getContentTypeOrZero() != 1) {
            mContentGroupInfo.setContentGroupName(null);
        }
        //成为作者，不需要弹-消耗金币确认弹窗
//        resources.getString(R.string.community_publish_type_apply_author);
        toPublicPost(chosenText, resources);
        return false;
    }

    private void toPublicPost(String chosenText, Resources resources) {
        if (!AppUtil.checkLogin(getContext())) {
            return;
        }

        SensorsUtils.sensorsClickBtn(chosenText, "快捷发布浮层");
        if (chosenText.equals(resources.getString(R.string.community_publish_type_image_text))) {
//            SensorsUtils.sensorsClickBtn("发布图文", "快捷发布浮层");
            //是否有草稿
//            if (PreferenceUtil.getBooleanPreference(getContext(), LingLabDoPostUtils.KEY_HAVE_DRAFT, false)) {
//                CommunityDoPostLingLabActivity.startActivity(getContext(), PostCard.PostType.LING_SENSE, "", 0, mOpenFrom);
//            } else {
//                LingLabDoPostUtils lingLabDoPostUtils = new LingLabDoPostUtils(getContext());
//                lingLabDoPostUtils.requestPermission(new String[]{
//                        Manifest.permission.READ_EXTERNAL_STORAGE,
//                        Manifest.permission.WRITE_EXTERNAL_STORAGE
//                }, lingLabDoPostUtils.REQUEST_PHOTO_CODE, 1);
//            }
            if (mLingLabDoPostUtils == null) {
                mLingLabDoPostUtils = new LingLabDoPostUtils(getContext());
            }
            mLingLabDoPostUtils.requestPermission(AppUtil.getActivity(getContext()), new String[]{
                    Manifest.permission.READ_EXTERNAL_STORAGE,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE,
//                    Manifest.permission.CAMERA
            }, LingLabDoPostUtils.REQUEST_PHOTO_CODE, 1);

        } else if (chosenText.equals(resources.getString(R.string.community_publish_type_video))) {
//            SensorsUtils.sensorsClickBtn("发布视频", "快捷发布浮层");
            if (UserUtils.isShootVideo()) {
                JumpPageUtil.goShortVideoPostPage(getContext(), PostCard.PostType.SHORT_VIDEO, null, null, new ContentGroupInfo(PostCard.PostSeries.CAR_FORUM), 0, CommunityDoPostImageTextActivity.OPEN_FROM_CAR_TYPE);
            } else {
                JumpPageUtil.goCommonWeb(getContext(), WebUrlConfigConstant.APPLY_AUTHOR);
            }
        } else if (chosenText.equals(resources.getString(R.string.community_publish_type_sign))) {
            CommunityDoPostImageTextActivity.startActivity(getContext(), PostCard.PostType.SIGN_IN, mChannelId, 0, mOpenFrom);
        } else if (chosenText.equals(resources.getString(R.string.community_publish_type_apply_author))) {
//            SensorsUtils.sensorsClickBtn("成为拍客", "快捷发布浮层");
            JumpPageUtil.goCommonWeb(getContext(), WebUrlConfigConstant.APPLY_AUTHOR);
        } else if (chosenText.equals(resources.getString(R.string.community_publish_goods_exchange))) {
//            SensorsUtils.sensorsClickBtn("发布好货", "快捷发布浮层");
            IntentUtils.startActivity(mCtx, PublishGoodsExchangeInfoActivity.class);
        } else if (chosenText.equals(resources.getString(R.string.community_publish_ling_friend))) {
//            SensorsUtils.sensorsClickBtn("菱友+", "快捷发布浮层");
            JumpPageUtil.goCommonWeb(mCtx, WebUrlConfigConstant.CAR_CLUB_POST_PUB);
        } else if (chosenText.equals(resources.getString(R.string.community_publish_ling_lab))) {
//            SensorsUtils.sensorsClickBtn("菱感", "快捷发布浮层");
            CommunityDoPostLingLabActivity.startActivity(getContext(), PostCard.PostType.LING_SENSE, "", 0, mOpenFrom);
        } else if (chosenText.equals(resources.getString(R.string.community_publish_question))) {
//            SensorsUtils.sensorsClickBtn("问答", "快捷发布浮层");
            CommunityDoPostImageTextActivity.startActivity(getContext(), PostCard.PostType.ASK_ENGINEER, null, null, new ContentGroupInfo(PostCard.PostSeries.CAR_FORUM), 0, CommunityDoPostImageTextActivity.OPEN_FROM_CAR_TYPE);
        } else if (chosenText.equals(resources.getString(R.string.store_service_mm))) {
//            SensorsUtils.sensorsClickBtn("客服", "快捷发布浮层");
            JumpPageUtil.goHomeFunctionEntrance(getContext(), FunctionEntrance.create(getContext(), FunctionEntranceEnum.SERVICE_MM), null, null);
        }
    }

    public void setDoPostData(String channelId, int openFrom) {
        this.mChannelId = channelId;
        this.mOpenFrom = openFrom;
    }

    public void setDoPostData(String channelId, String channelName, int openFrom) {
        this.mChannelId = channelId;
        this.mChannelName = channelName;
        this.mOpenFrom = openFrom;
    }

    public void setContentGroupInfo(ContentGroupInfo contentGroupInfo) {
        mContentGroupInfo = contentGroupInfo;
    }

    //权限允许
    public void onPermissionResult(boolean isGranted, int requestCode) {
        if (requestCode == LingLabDoPostUtils.REQUEST_PHOTO_CODE) {
            if (isGranted) {
                mLingLabDoPostUtils.doChoosePhoto(1);
            } else {
                ToastUtil.showMessage(mCtx, mCtx.getString(R.string.ling_lab_need_permission));
            }
        }

    }

    class PublishTypeAdapter extends ImageTextAdapter<ImageTextBean> {
        public PublishTypeAdapter(Context context, List<ImageTextBean> data) {
            super(context, data);
        }

        @Override
        protected BaseRecyclerViewHolder<ImageTextBean> createViewHolder(View itemView) {
            return new PublishTypeViewHolder(itemView);
        }

        @Override
        protected int getItemLayoutRes(int viewType) {
            int layoutRes = CommunityPublishWithFunctionIconPopupWindow.this.getItemLayoutRes();
            if (layoutRes <= 0) {
                return R.layout.item_community_publish_type;
            }
            return layoutRes;
        }

        class PublishTypeViewHolder extends ImageTextViewHolder<ImageTextBean> {
            public PublishTypeViewHolder(View itemView) {
                super(itemView);
            }

            @Override
            protected void initItemView(View itemView) {
                super.initItemView(itemView);
                itemView.setOnClickListener(null);
                itemView.findViewById(R.id.ll_container).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (getOnItemClickListener() != null) {
                            int itemPosition = getPositionOfData();
                            if (itemPosition != -1) {
                                getOnItemClickListener().onItemClick(v, itemPosition);
                            }
                        }
                    }
                });
            }
        }
    }
}
