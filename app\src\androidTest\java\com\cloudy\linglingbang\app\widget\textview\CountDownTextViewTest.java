package com.cloudy.linglingbang.app.widget.textview;

import android.graphics.Color;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import com.cloudy.linglingbang.BaseInstrumentedTest;
import com.cloudy.linglingbang.app.util.timer.CountDownManager;
import com.cloudy.linglingbang.app.util.timer.TimeDelta;

import androidx.annotation.NonNull;

/**
 * 倒计时
 *
 * <AUTHOR>
 * @date 2018/10/17
 */
public class CountDownTextViewTest extends BaseInstrumentedTest {

    @Override
    public void test() {
        super.test();
        final long finishTime = System.currentTimeMillis() + 1000 * 60 * 60 * 24 + 1000 * 10;

        //商品列表，黑底白字
        final CountDownTextView countDownTextView1 = new CountDownTextView(getContext());
        countDownTextView1.setTimeBackgroundColor(Color.BLACK);
        countDownTextView1.setTimeTextColor(Color.WHITE);
        countDownTextView1.setDayTextColor(Color.GRAY);

        //商品详情，红底白字
        final CountDownTextView countDownTextView2 = new CountDownTextView(getContext());
        countDownTextView2.setTextSize(TypedValue.COMPLEX_UNIT_PX, 20);
        countDownTextView2.setHeight(50);
        countDownTextView2.setGravity(Gravity.CENTER_VERTICAL);
        countDownTextView2.setTimeBackgroundColor(Color.RED);
        countDownTextView2.setTimeTextColor(Color.WHITE);
        countDownTextView2.setDayTextColor(Color.RED);

        CountDownManager.getInstance().addOnTickListener(new CountDownManager.OnTickListener() {
            @Override
            public void onTick(long currentTime) {
                countDownTextView1.updateTimeDelta(TimeDelta.create(currentTime, finishTime));
                countDownTextView2.updateTimeDelta(TimeDelta.create(currentTime, finishTime));
            }

            @NonNull
            @Override
            public Object getCountDownTag() {
                return "test";
            }
        });

        LinearLayout linearLayout = new LinearLayout(getContext());
        linearLayout.setOrientation(LinearLayout.VERTICAL);
        getActivity().setContentView(linearLayout, new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        linearLayout.addView(countDownTextView1);
        linearLayout.addView(countDownTextView2);
        getActivity().setContentView(linearLayout);
    }
}