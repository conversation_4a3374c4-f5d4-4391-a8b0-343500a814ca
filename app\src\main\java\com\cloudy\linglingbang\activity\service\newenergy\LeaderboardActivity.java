package com.cloudy.linglingbang.activity.service.newenergy;

import android.app.Activity;
import android.content.Intent;

import com.cloudy.linglingbang.activity.basic.SingleFragmentActivity;

import androidx.fragment.app.Fragment;

/**
 * 排行榜
 *
 * <AUTHOR>
 * @date 2019-07-16
 */
public class LeaderboardActivity extends SingleFragmentActivity {

    public static String vinExtra = "VIN";
    public static String totalMileageExtra = "TOTALMILEAGE";
    public static String carTypeNameExtra = "CARTYPENAME";
    private String vin;
    private int totalMileage;
    private String carTypeName;

    @Override
    protected Fragment createFragment() {
        vin = getIntent().getStringExtra(vinExtra);
        totalMileage = getIntent().getIntExtra(totalMileageExtra, 0);
        carTypeName = getIntent().getStringExtra(carTypeNameExtra);
        return LeaderboardFragment.newInstance(vin, totalMileage, carTypeName);
    }

    @Override
    public void initialize() {
        super.initialize();
        setTitle("里程排行榜");
    }

    public static void startActivity(Activity context, String vin, int totalMileage, String carTypeName) {
        Intent intent = new Intent(context, LeaderboardActivity.class);
        intent.putExtra(vinExtra, vin);
        intent.putExtra(totalMileageExtra, totalMileage);
        intent.putExtra(carTypeNameExtra, carTypeName);
        context.startActivity(intent);
    }
}
