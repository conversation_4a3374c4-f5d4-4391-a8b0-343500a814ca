package com.cloudy.linglingbang.activity.car.edit;

import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.widget.EditText;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.app.receiver.UserInfoChangedHelper;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.dialog.car.ChooseCarColorDialog;
import com.cloudy.linglingbang.constants.FinalSensors;
import com.cloudy.linglingbang.model.car.edit.CarColorBean;
import com.cloudy.linglingbang.model.car.list.BindCarInfo;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;

import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 编辑爱车信息
 *
 * <AUTHOR>
 * @date 2019/3/19
 */
public class EditCarInfoActivity extends BaseActivity {

    /** 昵称 */
    @BindView(R.id.et_nickname)
    EditText mEtNickname;
    /** 车牌号 */
    @BindView(R.id.et_plate_num)
    EditText mEtPlateNum;
    /** 颜色 */
    @BindView(R.id.et_car_color)
    EditText mEtCarColor;

    public static String EXTRA_BIND_CAR_INFO = "bind_car_info";
    private BindCarInfo mBindCarInfo;
    private Integer colorId;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_edit_car_info);
    }

    @Override
    protected void initialize() {
        setMiddleTitle(getString(R.string.title_edit_car_info));
        mBindCarInfo = (BindCarInfo) getIntent().getSerializableExtra(EXTRA_BIND_CAR_INFO);
        if (mBindCarInfo != null) {
            mEtNickname.setText(mBindCarInfo.getCarNickname());
            mEtPlateNum.setText(mBindCarInfo.getLicenseNo());
            mEtCarColor.setText(mBindCarInfo.getColorName());
        }
        //设置颜色编辑框不可编辑
        mEtCarColor.setFocusable(false);
        mEtCarColor.setFocusableInTouchMode(false);
        mEtCarColor.setKeyListener(null);
    }

    /**
     * 保存
     */
    @OnClick(R.id.btn_save)
    void clickSave() {
        saveCarInfo();
    }

    /**
     * 保存修改
     */
    private void saveCarInfo() {
        String carName = mEtNickname.getText().toString();
        String plateNum = mEtPlateNum.getText().toString();
        if (plateNum.length() > 0 && !AppUtil.isVehicleNumber(plateNum)) {
            ToastUtil.showMessage(this, "请输入正确的车牌号");
            return;
        }
        L00bangRequestManager2
                .getServiceInstance()
                .editCar(mBindCarInfo.getVin(), carName, plateNum, colorId)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<Boolean>(this) {
                    @Override
                    public void onSuccess(Boolean aBoolean) {
                        super.onSuccess(aBoolean);
                        ToastUtil.showMessage(EditCarInfoActivity.this, R.string.toast_edit_car_info_save_success);
                        //爱车信息发生变更，发送广播
                        //若是车控首页爱车信息发生变更，发送广播；若不是，可能会--造成车控首页爱车信息多余的刷新操作
                        UserInfoChangedHelper.sendControlCarChangedBroadcast();
                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                finish();
                            }
                        }, 800);
                    }
                });
    }

    /**
     * 获取颜色的点击
     */
    @OnClick(R.id.iv_car_color)
    void clickSelectColor() {
        getCarColor();
    }

    /**
     * 请求获取车辆全部颜色
     */
    private void getCarColor() {
        L00bangRequestManager2
                .getServiceInstance()
                .getColorList(mBindCarInfo.getCarStyleId())
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<List<CarColorBean>>(this) {
                    @Override
                    public void onSuccess(List<CarColorBean> carColorBeans) {
                        super.onSuccess(carColorBeans);
                        ChooseCarColorDialog chooseCarColorDialog = new ChooseCarColorDialog(EditCarInfoActivity.this, null, carColorBeans, mEtCarColor.getText().toString(), new ChooseCarColorDialog.OnChooseColorListener() {
                            @Override
                            public void onChooseColor(CarColorBean carColorBean) {
                                mEtCarColor.setText(carColorBean.getColorName());
                                colorId = carColorBean.getColorId();
                            }
                        });
                        chooseCarColorDialog.show();
                    }
                });
    }

    public static void startActivity(Context context, BindCarInfo bindCarInfo) {
        Intent intent = new Intent(context, EditCarInfoActivity.class);
        intent.putExtra(EXTRA_BIND_CAR_INFO, bindCarInfo);
        context.startActivity(intent);
    }

    @Override
    protected void onStart() {
        super.onStart();
        SensorsUtils.sensorsViewStart(FinalSensors.VEHICLE_INFORMATION_EDITOR_PAGE);
    }

    @Override
    protected void onStop() {
        super.onStop();
        SensorsUtils.sensorsViewEndNew(FinalSensors.VEHICLE_INFORMATION_EDITOR_PAGE_NAME, FinalSensors.VEHICLE_INFORMATION_EDITOR_PAGE, "浏览" + FinalSensors.VEHICLE_INFORMATION_EDITOR_PAGE_NAME + mBindCarInfo == null ? "" : mBindCarInfo.getCarTypeName());
    }
}
