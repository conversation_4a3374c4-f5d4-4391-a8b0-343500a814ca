package com.cloudy.linglingbang.activity.user;

import com.alibaba.verificationsdk.ui.IActivityCallback;
import com.alibaba.verificationsdk.ui.VerifyActivity;
import com.alibaba.verificationsdk.ui.VerifyType;
import com.cloudy.linglingbang.BaseInstrumentedTest;
import com.cloudy.linglingbang.app.util.ToastUtil;

import java.util.Map;

/**
 * 登录验证
 *
 * <AUTHOR>
 * @date 2018/11/27
 */
public class VerifyActivityTest extends BaseInstrumentedTest {
    @Override
    public void test() {
        super.test();
        startVerify();
    }

    private void startVerify() {
        VerifyActivity.startSimpleVerifyUI(getContext(), VerifyType.NOCAPTCHA, "0335", null, new IActivityCallback() {
            @Override
            public void onNotifyBackPressed() {
                ToastUtil.showMessage(getContext(), "验证取消");
            }

            @Override
            public void onResult(int i, Map<String, String> map) {
                verifyDidFinishedWithResult(i, map);
            }
        });
    }

    public void verifyDidFinishedWithResult(int retInt, Map code) {
        switch (retInt) {
            case VerifyActivity.VERIFY_SUCC:
                ToastUtil.showMessage(getContext(), "验证通过");
                break;

            case VerifyActivity.VERIFY_FAILED:
                ToastUtil.showMessage(getContext(), "验证失败");
                break;
        }
    }
}