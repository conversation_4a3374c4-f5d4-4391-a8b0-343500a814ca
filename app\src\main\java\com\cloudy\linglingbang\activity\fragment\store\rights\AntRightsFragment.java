package com.cloudy.linglingbang.activity.fragment.store.rights;

import android.os.Bundle;

import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.constants.FinalSensors;
import com.cloudy.linglingbang.constants.WebUrlConfigConstant;
import com.cloudy.linglingbang.web.BaseX5WebViewFragment;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

/**
 * 蚂蚁权益
 *
 * <AUTHOR>
 * @date 5/25/21
 */
public class AntRightsFragment extends BaseX5WebViewFragment {
    public static Fragment newInstance() {
        return new AntRightsFragment();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setNeedLazyLoad(true);
    }

    @Override
    protected final String setUrl() {
        return WebUrlConfigConstant.STORE_ANT_RIGHT;
    }

    @Override
    protected boolean isShowBar() {
        return false;
    }

    @Override
    public void onStart() {
        super.onStart();
        SensorsUtils.sensorsViewStart(FinalSensors.BROWSE_LIFE_INFORMATION);
    }

    @Override
    public void onStop() {
        super.onStop();
        SensorsUtils.sensorsViewEndNew("跨界权益", FinalSensors.BROWSE_LIFE_INFORMATION, "浏览跨界权益");
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (hidden) {
            SensorsUtils.sensorsViewEndNew("跨界权益", FinalSensors.BROWSE_LIFE_INFORMATION, "浏览跨界权益");
        } else {
            SensorsUtils.sensorsViewStart(FinalSensors.BROWSE_LIFE_INFORMATION);
        }
    }
}
