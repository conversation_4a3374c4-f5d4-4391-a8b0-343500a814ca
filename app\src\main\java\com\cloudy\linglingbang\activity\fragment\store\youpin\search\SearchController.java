package com.cloudy.linglingbang.activity.fragment.store.youpin.search;

import android.content.Context;
import android.view.ViewGroup;

import com.cloudy.linglingbang.activity.search.controller.IBaseSearchController;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 优品搜索控制器
 *
 * <AUTHOR>
 * @date 2020/5/10
 */
public class SearchController {

    private final Context mContext;
    private final ViewGroup mContainer;

    private List<IBaseSearchController> mSearchControllerList;
    /**
     * 当前搜索
     */
    private int mCurrentSearchTypeIndex;
    private final SensorsUtils.StoreHomeAnchor mStoreHomeAnchor;

    public SearchController(Context context, ViewGroup container, SensorsUtils.StoreHomeAnchor storeHomeAnchor) {
        mContext = context;
        mContainer = container;
        mStoreHomeAnchor = storeHomeAnchor;
        init();
    }

    private void init() {
        initViews();
    }

    private void initViews() {
        mSearchControllerList = new ArrayList<>(1);
        mSearchControllerList.add(new CommoditySearchController(mContext, mContainer).setStoreHomeAnchor(mStoreHomeAnchor));
        mCurrentSearchTypeIndex = 0;
        //添加
        for (int i = 0; i < mSearchControllerList.size(); i++) {
            IBaseSearchController iSearchController = mSearchControllerList.get(i);
            iSearchController.initViews();
        }
    }

    public void search(String text, boolean force) {
        //这里显示，是为了比如点历史、搜索等显示出来
        mSearchControllerList.get(mCurrentSearchTypeIndex).search(text, force);
    }

    public void hide() {
        //这里显示，是为了比如点历史、搜索等显示出来
        mSearchControllerList.get(mCurrentSearchTypeIndex).hide();
    }

    public void setStoreHomeAnchor(SensorsUtils.StoreHomeAnchor storeHomeAnchor) {
        CommoditySearchController controller = (CommoditySearchController) mSearchControllerList.get(mCurrentSearchTypeIndex);
        controller.setStoreHomeAnchor(storeHomeAnchor);
    }
}
