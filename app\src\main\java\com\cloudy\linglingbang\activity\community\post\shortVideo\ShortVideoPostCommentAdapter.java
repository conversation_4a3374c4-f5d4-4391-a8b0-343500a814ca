package com.cloudy.linglingbang.activity.community.post.shortVideo;

import android.content.Context;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.Comment;

import java.util.List;

/**
 * 短视频帖评论的adapter
 *
 * <AUTHOR>
 * @date 2018/11/19
 */
public class ShortVideoPostCommentAdapter extends BaseRecyclerViewAdapter<Comment> {
    private ShortVideoCommentReplyAdapter.OnReplyClickListener mReplyClickListener;

    //设置点击回复的监听
    public void setOnReplyClickListener(ShortVideoCommentReplyAdapter.OnReplyClickListener listener) {
        this.mReplyClickListener = listener;
    }


    public ShortVideoPostCommentAdapter(Context context, List data) {
        super(context, data);
    }

    @Override
    protected BaseRecyclerViewHolder createViewHolder(View itemView) {
        ShortVideoCommentViewHolder shortVideoCommentViewHolder = new ShortVideoCommentViewHolder(this, itemView);
        shortVideoCommentViewHolder.setOnReplyClickListener(new ShortVideoCommentReplyAdapter.OnReplyClickListener() {
            @Override
            public void onReplyClick(Comment comment, int position) {
                if (mReplyClickListener != null) {
                    mReplyClickListener.onReplyClick(comment, position);
                }
            }
        });
        return shortVideoCommentViewHolder;
    }

    @Override
    protected int getItemLayoutRes(int viewType) {
        return R.layout.item_short_video_post_comment;
    }

}
