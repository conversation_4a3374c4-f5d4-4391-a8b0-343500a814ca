package com.cloudy.linglingbang.activity.car.list;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.car.edit.EditCarInfoActivity;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertBlackBtnDialog;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog;
import com.cloudy.linglingbang.app.widget.item.CommonItem;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.car.list.BindCarInfo;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 爱车列表的adapter
 * Created by hanfei on 2016/10/15.
 */

public class BindCarListAdapter extends BaseRecyclerViewAdapter<BindCarInfo> {
    private SwitchCarListener mSwitchCarListener;

    public void setSwitchCarListener(SwitchCarListener switchCarListener) {
        mSwitchCarListener = switchCarListener;
    }

    public BindCarListAdapter(Context context, List<BindCarInfo> data) {
        super(context, data);
    }

    @Override
    protected BaseRecyclerViewHolder<BindCarInfo> createViewHolder(View itemView) {
        return new ViewHolder(itemView);
    }

    @Override
    protected int getItemLayoutRes(int viewType) {
        return R.layout.item_my_bind_car;
    }

    class ViewHolder extends BaseRecyclerViewHolder<BindCarInfo> {
        @BindView(R.id.iv_car)
        ImageView mIvCar;
        /** 汽车昵称 */
        @BindView(R.id.tv_car_nickname)
        TextView mTvCarNickname;
        /** 汽车名字 */
        @BindView(R.id.tv_car_type_name)
        TextView mTvCarTypeName;
        @BindView(R.id.tv_car_style_name)
        TextView mTvCarStyleName;
        /** item右边状态 */
        @BindView(R.id.tv_item_right)
        TextView mTvItemRight;
        /** 添加爱车到首页展示 */
        @BindView(R.id.iv_add_index)
        ImageView mIvCurrentCar;

        /**
         * 车牌号
         */
        @BindView(R.id.item_plate_num)
        CommonItem mItemPlateNum;
        /**
         * 发动机号
         */
        @BindView(R.id.item_engine_num)
        CommonItem mItemEngineNum;
        /**
         * vin号
         */
        @BindView(R.id.item_vin_num)
        CommonItem mItemVinNum;

        /**
         * 颜色
         */
        @BindView(R.id.item_car_color)
        CommonItem mItemCarColor;

        @BindView(R.id.ll_bottom_info)
        LinearLayout mLlBottomInfo;

        @BindView(R.id.bt_untied_car)
        Button mBtUntiedCar;

        public ViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            ButterKnife.bind(this, itemView);
        }

        @Override
        public void bindTo(final BindCarInfo bindCarInfo, int position) {
            super.bindTo(bindCarInfo, position);
            //汽车图片
            if (mIvCar != null) {
                new ImageLoad(mIvCar, bindCarInfo.getImage())
                        .setDoNotAnimate()
                        .setPlaceholderAndError(R.drawable.ic_common_place_holder)
                        .load();
            }
            //没有昵称的时候显示车型名称
            mTvCarNickname.setText(bindCarInfo.getCarNickNameOrCarTypeName());

            mTvCarTypeName.setText(bindCarInfo.getCarTypeName());
            mTvCarStyleName.setText(bindCarInfo.getCarStyleName());

            mItemPlateNum.getTvLeft().setText(mContext.getResources().getString(R.string.tv_my_bind_car_plate_num));
            mItemPlateNum.getTvRight().setText(bindCarInfo.getLicenseNo());
            Drawable drawablePlateRight = mContext.getResources().getDrawable(R.drawable.ic_car_item_down);
            drawablePlateRight.setBounds(0, 0, drawablePlateRight.getMinimumWidth(), drawablePlateRight.getMinimumHeight());
            //设置车牌号后边的展开图标
            mItemPlateNum.getTvRight().setCompoundDrawables(null, null, drawablePlateRight, null);

            mItemEngineNum.setVisibility(View.GONE);
            mItemVinNum.setVisibility(View.VISIBLE);
            mItemVinNum.getTvLeft().setText(mContext.getResources().getString(R.string.tv_my_bind_car_vin_num));
            mItemVinNum.getTvRight().setText(bindCarInfo.getVin());
            mItemCarColor.getTvLeft().setText(mContext.getResources().getString(R.string.tv_my_bind_car_car_color));
            mItemCarColor.getTvRight().setText(bindCarInfo.getColorName());
            Drawable drawableRight = mContext.getResources().getDrawable(R.drawable.ic_car_edit);
            drawableRight.setBounds(0, 0, drawableRight.getMinimumWidth(), drawableRight.getMinimumHeight());
            mTvCarNickname.setCompoundDrawables(null, null, drawableRight, null);
            mTvCarNickname.setEnabled(true);
            if (bindCarInfo.getHomeDisplayOrZero() == 1) {
                mIvCurrentCar.setImageResource(R.drawable.ic_bind_car_have_add_index);
                mTvItemRight.setVisibility(View.GONE);
            } else {
                mIvCurrentCar.setImageResource(R.drawable.ic_bind_car_add_index);
                mTvItemRight.setVisibility(View.GONE);
                mTvItemRight.setText(R.string.item_btn_my_bind_car_switch_car);
            }
            mIvCurrentCar.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    //如果是1，就是取消操作
                    if (bindCarInfo.getHomeDisplayOrZero() == 1) {
                        Dialog dialog1 = new CommonAlertBlackBtnDialog(mContext, mContext.getString(R.string.txt_cancel_car_home_display, bindCarInfo.getCarTypeName(), bindCarInfo.getVin()), mContext.getString(R.string.common_ok_queren), mContext.getString(R.string.common_cancel_quxiao),
                                new DialogInterface.OnClickListener() {
                                    @Override
                                    public void onClick(DialogInterface dialogInterface, int i) {
                                        requestAddCarToHome(bindCarInfo.getVin(), bindCarInfo.getHomeDisplayOrZero());
                                    }
                                }, null);
                        dialog1.show();
                    } else {
                        requestAddCarToHome(bindCarInfo.getVin(), bindCarInfo.getHomeDisplayOrZero());
                    }
                }
            });
            //控制汽车信息的展开和收起
            mItemPlateNum.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mLlBottomInfo.getVisibility() == View.VISIBLE) {
                        mLlBottomInfo.setVisibility(View.GONE);
                        Drawable drawablePlateRight = mContext.getResources().getDrawable(R.drawable.ic_car_item_down);
                        drawablePlateRight.setBounds(0, 0, drawablePlateRight.getMinimumWidth(), drawablePlateRight.getMinimumHeight());
                        //设置车牌号后边的展开图标
                        mItemPlateNum.getTvRight().setCompoundDrawables(null, null, drawablePlateRight, null);
                    } else {
                        mLlBottomInfo.setVisibility(View.VISIBLE);
                        Drawable drawablePlateRight = mContext.getResources().getDrawable(R.drawable.ic_car_item_up);
                        drawablePlateRight.setBounds(0, 0, drawablePlateRight.getMinimumWidth(), drawablePlateRight.getMinimumHeight());
                        //设置车牌号后边的展开图标
                        mItemPlateNum.getTvRight().setCompoundDrawables(null, null, drawablePlateRight, null);
                    }
                }
            });

            //解除绑定的弹窗
            mBtUntiedCar.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    CommonAlertDialog dialog = new CommonAlertBlackBtnDialog(mContext, R.string.dialog_message_unbind_car, new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            //解除绑定的请求
                            unbindCar(bindCarInfo);
                        }
                    });
                    dialog.getAlertController().setTitle(mContext.getString(R.string.dialog_title_unbind_car));
                    dialog.show();
                }
            });


            //编辑车辆信息的点击
            mTvCarNickname.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!AppUtil.checkLogin(mContext)) {
                        return;
                    }
                    if (mContext instanceof BindCarListActivity) {

                    }
                    EditCarInfoActivity.startActivity(mContext, bindCarInfo);
                }
            });

        }
    }

    /**
     * 添加爱车到首页
     *
     * @param vin
     * @param homeDisplayOrZero 取反向值
     */
    private void requestAddCarToHome(String vin, Integer homeDisplayOrZero) {
        L00bangRequestManager2
                .getServiceInstance()
                .setCarToHome(vin, homeDisplayOrZero == 0 ? 1 : 0)
                .compose(L00bangRequestManager2.<Boolean>setSchedulers())
                .subscribe(new ProgressSubscriber<Boolean>(mContext) {
                    @Override
                    public void onSuccess(Boolean aBoolean) {
                        super.onSuccess(aBoolean);
                        //刷新数据
                        if (mSwitchCarListener != null) {
                            mSwitchCarListener.switchSuccess();
                        }
                    }
                });
    }

    /**
     * 解绑爱车
     * <p>
     * * @param bindCarInfo 爱车信息
     */
    public void unbindCar(final BindCarInfo bindCarInfo) {
        if (bindCarInfo == null) {
            return;
        }
        if (!AppUtil.checkLogin(mContext)) {
            return;
        }
        L00bangRequestManager2
                .getServiceInstance()
                .unBindInfo(bindCarInfo.getVin())
                .compose(L00bangRequestManager2.<Boolean>setSchedulers())
                .subscribe(new ProgressSubscriber<Boolean>(mContext) {
                    @Override
                    public void onSuccess(Boolean aBoolean) {
                        super.onSuccess(aBoolean);
                        ToastUtil.showMessage(mContext, R.string.txt_my_bind_car_unbind_ok);
                        if (mSwitchCarListener != null) {
                            mSwitchCarListener.switchSuccess();
                        }
                    }
                });
    }

    public interface SwitchCarListener {
        void switchSuccess();
    }

}
