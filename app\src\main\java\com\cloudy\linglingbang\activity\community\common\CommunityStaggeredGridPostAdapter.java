package com.cloudy.linglingbang.activity.community.common;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.community.common.holder.BasePostStaggeredGridViewHolder;
import com.cloudy.linglingbang.activity.community.common.holder.BasePostViewHolder;
import com.cloudy.linglingbang.app.util.DeviceUtil;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.community.EmptyPostCard;
import com.cloudy.linglingbang.model.postcard.PostCard;

import java.util.List;

/**
 * 交错帖子瀑布流的adapter
 *
 * <AUTHOR>
 * @date 2019-11-26
 */
public class CommunityStaggeredGridPostAdapter extends BaseStaggeredGridPostAdapter {

    public static final int VIEW_TYPE_EMPTY = 1;

    public CommunityStaggeredGridPostAdapter(Context context, List<PostCard> data) {
        super(context, data);
    }

    @Override
    protected int getItemLayoutRes(int viewType) {
        switch (viewType) {
            case VIEW_TYPE_EMPTY:
                return R.layout.fragment_empty_white_background;
        }
        return super.getItemLayoutRes(viewType);
    }

    @Override
    protected BaseRecyclerViewHolder<PostCard> createViewHolderWithViewType(View itemView, int viewType) {
        if (viewType == VIEW_TYPE_EMPTY) {
            return new EmptyViewHolder(itemView);
        }
        return super.createViewHolderWithViewType(itemView, viewType);
    }

    @Override
    public int getItemViewType(int position) {
        if (mData.get(position) instanceof EmptyPostCard) {
            return VIEW_TYPE_EMPTY;
        }
        return super.getItemViewType(position);
    }

    @Override
    protected BasePostViewHolder createDefaultViewHolderWithoutSetting(View itemView) {
        return new BasePostStaggeredGridViewHolder(itemView);
    }

    public class EmptyViewHolder extends BaseRecyclerViewHolder<PostCard> {

        TextView tv;
        ImageView mIvEmpty;

        public EmptyViewHolder(View itemView) {
            super(itemView);
            RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(DeviceUtil.getScreenWidth(), ViewGroup.LayoutParams.WRAP_CONTENT);
            layoutParams.topMargin = mContext.getResources().getDimensionPixelSize(R.dimen.normal_300);
            itemView.setLayoutParams(layoutParams);
            tv = itemView.findViewById(R.id.tv_empty_desc);
            mIvEmpty = itemView.findViewById(R.id.iv_empty);
        }

        @Override
        public void bindTo(PostCard o, int position) {
            super.bindTo(o, position);
            if (tv != null) {
                tv.setText(R.string.empty_topic);
            }
            if (mIvEmpty != null) {
                mIvEmpty.setImageResource(R.drawable.ic_message_empty_work_hard);
            }
        }
    }

}

