package com.cloudy.linglingbang.activity.community.post.shortVideo;

import android.content.Context;
import android.graphics.Color;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshFragment;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.community.post.shortVideo2.ShortVideoSlideListActivity2;
import com.cloudy.linglingbang.adapter.newcommunity.PostAuthorInfoHolderWithAttention;
import com.cloudy.linglingbang.app.imageConfig.RoundedCornersTransformation;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.DensityUtil;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.util.span.ForegroundColorAndAbsoluteSizeSpan;
import com.cloudy.linglingbang.app.widget.banner.AdRoundImageView;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.Comment;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.postcard.PostCardItem;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.user.Author;

import java.util.List;

import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import butterknife.ButterKnife;
import io.reactivex.rxjava3.core.Observable;

/**
 * 首页-视频fragment（新）
 *
 * <AUTHOR>
 * @date 2020-4-20
 */
public class ShortVideoPostListFragment2 extends BaseRecyclerViewRefreshFragment<PostCard> {
    //栏目位置，写死为10
    private static final long VIDEO_POSITION = 10;

    /**
     * 记录上次刷新时是否登录。如果上次登录状态不一样，则下次切换到本页面时候刷新
     */
    private boolean mLastIsLogin;

    @Override
    protected int getLayoutRes() {
        return R.layout.activity_short_video_refresh;
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(final List list) {
        RecyclerView.Adapter<? extends RecyclerView.ViewHolder> adapter = new VideoPostAdapter(getContext(), list);
        ((VideoPostAdapter) adapter).setOnItemClickListener(new BaseRecyclerViewHolder.OnItemClickListener() {
            @Override
            public void onItemClick(View itemView, int position) {
                //传 -1 标识新的 首页-视频 页面
//                ShortVideoSlideListActivity.startActivity(getContext(), list, position, -1);
                ShortVideoSlideListActivity2.startActivityByColumnPosition(getContext(), list, position, 10);
            }
        });
        return adapter;
    }

    @Override
    protected void initViews() {
        super.initViews();
        int officeTop = getActivity() == null ? 0 : getActivity().findViewById(R.id.fl_container_app_home).getHeight();
        //设置paddingTop, 避免顶部tab遮挡
        mRootView.setPadding(mRootView.getPaddingLeft(), mRootView.getPaddingTop() + officeTop, mRootView.getPaddingRight(), mRootView.getPaddingBottom());
    }

    @Override
    public boolean isNeedLazyLoad() {
        return true;
    }

    @Override
    public RefreshController createRefreshController() {
        RefreshController refreshController = new RefreshController(this) {
            @Override
            public void initViews(View rootView) {
                super.initViews(rootView);
                rootView.setBackgroundColor(Color.TRANSPARENT);
                swipeToLoadLayout.setBackgroundColor(Color.TRANSPARENT);
                recyclerView.setBackgroundColor(Color.TRANSPARENT);
            }

            @Override
            public void onRefresh() {
                super.onRefresh();
                mLastIsLogin = UserUtils.hasLogin();
            }

            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return null;
            }
        }.setEmptyBackgroundColorId(R.color.white)
                .setEmptyImageResId(R.drawable.ic_message_empty_work_hard);
        refreshController.setPageSize(20);
        return refreshController;
    }

    @Override
    public Observable<BaseResponse<List<PostCard>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return service2.getPostsByColumnPosition(VIDEO_POSITION, pageNo, pageSize);
    }

    class VideoPostAdapter extends BaseRecyclerViewAdapter<PostCard> {

        public VideoPostAdapter(Context context, List<PostCard> data) {
            super(context, data);
        }

        @Override
        protected BaseRecyclerViewHolder<PostCard> createViewHolder(View itemView) {
            return new VideoPostViewHolder(itemView);
        }

        @Override
        protected int getItemLayoutRes(int viewType) {
            return R.layout.item_short_video_post2;
        }
    }

    class VideoPostViewHolder extends BaseRecyclerViewHolder<PostCard> {

        /**
         * 封面图
         */
        @BindView(R.id.iv_video_cover)
        AdRoundImageView mIvVideoCover;

        /**
         * 头像
         *//*
        @BindView(R.id.iv_header)
        AdRoundImageView mIvHeader;

        *//**
         * 昵称
         *//*
        @BindView(R.id.tv_nickname)
        TextView mTvNickname;*/

        /**
         * 阅读数
         */
//        @BindView(R.id.tv_read_count)
//        TextView mTvReadCount;
        /**
         * 点赞数
         */
        @BindView(R.id.tv_praise_count)
        TextView mTvPraiseCount;

        /**
         * 发布时间
         */
        @BindView(R.id.tv_time)
        TextView mTvTime;

        /**
         * 标题
         */
        @BindView(R.id.tv_title)
        TextView mTvTitle;

        /**
         * 置顶评论
         */
        @BindView(R.id.tv_top_comment)
        TextView mTvTopComment;

        /**
         * 浏览标识
         */
        @BindView(R.id.tv_browse_flag)
        TextView mTvBrowseFlag;

        /**
         * 评论数
         */
        @BindView(R.id.tv_comment_count)
        TextView mTvCommentCount;

        /**
         * 蒙层
         */
        @BindView(R.id.iv_mongolia)
        ImageView mIvMongolia;

        private PostAuthorInfoHolderWithAttention mPostAuthorInfoHolderWithAttention;

        public VideoPostViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            ButterKnife.bind(this, itemView);
            mPostAuthorInfoHolderWithAttention = new PostAuthorInfoHolderWithAttention(itemView);
        }

        @Override
        public void bindTo(final PostCard postCard, final int position) {
            super.bindTo(postCard, position);
            mPostAuthorInfoHolderWithAttention.bindTo(postCard);
            //如果有栏目封面
            PostCardItem singlePostCardItem = null;
            if (postCard.getColumnCoverImages() != null && postCard.getColumnCoverImages().size() > 0) {
                singlePostCardItem = postCard.getColumnCoverImages().get(0);
            }
            //如果没有栏目封面，获取帖子封面
            else {
                for (PostCardItem postCardItem : postCard.getImgTexts()) {
                    if (!TextUtils.isEmpty(postCardItem.getImg())) {
                        singlePostCardItem = postCardItem;
                        break;
                    }
                }
            }
            if (singlePostCardItem != null) {
                int width = 0;
                int height = 0;
                //更新大小
                if (!TextUtils.isEmpty(singlePostCardItem.getWidth()) && !TextUtils.isEmpty(singlePostCardItem.getHeight())) {
                    try {
                        width = Integer.parseInt(singlePostCardItem.getWidth());
                        height = Integer.parseInt(singlePostCardItem.getHeight());
                    } catch (NumberFormatException e) {
                        e.printStackTrace();
                    }
                }
                if (width <= 0 || height <= 0) {
                    width = 345;
                    height = 455;
                }
                //加载图片
                mIvVideoCover.createCornerImageLoad(singlePostCardItem.getImg(), RoundedCornersTransformation.CornerType.ALL)
                        .load();

                new ImageLoad(ShortVideoPostListFragment2.this, mIvMongolia, R.drawable.fg_video_post_mongolia)
                        .setDoNotAnimate()
                        .setCircle(true)
                        .setRadius(DensityUtil.dip2px(getContext(), 5))
                        .setScaleType(ImageView.ScaleType.CENTER_CROP)
                        .setCornerType(RoundedCornersTransformation.CornerType.TOP)
                        .load();
            }

//            mTvReadCount.setText(AppUtil.getCommentDesc(postCard.getPostShowCount()));
            if (postCard.getCreationDateOrZero() != 0) {
                String createTime = AppUtil.checkMessTimeNew(postCard.getCreationDateOrZero());
                mTvTime.setText(createTime);
            }
            mTvTitle.setText(postCard.getTitle());
            //作者相关
            /*if (postCard.getAuthor() != null) {
                //加载头像
                String url = AppUtil.getImageUrlBySize(postCard.getAuthor().getPhoto(), AppUtil._120X120);
                new ImageLoad(getContext(), mIvHeader, url, ImageLoad.LoadMode.URL)
                        .setPlaceholder(R.drawable.user_head_default_120x120)
                        .setErrorImageId(R.drawable.user_head_default_120x120)
                        .setCircle(true)
                        .load();
                mTvNickname.setText(postCard.getAuthor().getNickname());
            }*/
            //设置评论
            if (postCard.getTopComment() != null) {
                Comment comment = postCard.getTopComment();
                String commentStr = comment.getUser().getNickname() + "：" + comment.getCommentContent();
                SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder(commentStr);
                ForegroundColorAndAbsoluteSizeSpan span = new ForegroundColorAndAbsoluteSizeSpan(getContext().getResources().getColor(R.color.color_000000), getContext().getResources().getDimension(R.dimen.activity_set_text_26));
                spannableStringBuilder.setSpan(span, 0, comment.getUser().getNickname().length() + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                mTvTopComment.setText(spannableStringBuilder);
                mTvTopComment.setVisibility(View.VISIBLE);
            } else {
                mTvTopComment.setVisibility(View.GONE);
            }
            //浏览数
//            if (postCard.getPostShowCount() > 100) {
//                mTvBrowseFlag.setVisibility(View.VISIBLE);
//            } else {
//                mTvBrowseFlag.setVisibility(View.GONE);
//            }
            mTvBrowseFlag.setVisibility(View.GONE);
            //评论数
            mTvCommentCount.setText(AppUtil.getCommentDesc(postCard.getPostCommentCount()));
            mTvPraiseCount.setText(getString(R.string.short_video_praise_count, AppUtil.getCommentDesc(postCard.getPostPraiseCount())));
            //点赞设置
            if (postCard.getIsPraise() == 1) {
                AppUtil.setDrawableLeft(getContext(), mTvPraiseCount, R.drawable.ic_video_post_praise_s);
            } else {
                AppUtil.setDrawableLeft(getContext(), mTvPraiseCount, R.drawable.ic_video_post_praise_n);
            }

            mTvPraiseCount.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!AppUtil.checkLogin(getContext())) {
                        return;
                    }
                    if (postCard.getIsPraise() == 1) {
                        ToastUtil.showMessage(getContext(), R.string.admire_already);
                    } else {
                        L00bangRequestManager2
                                .getServiceInstance()
                                .praisedPostCard(Long.valueOf(postCard.getPostId()))
                                .compose(L00bangRequestManager2.setSchedulers())
                                .subscribe(new ProgressSubscriber<String>(getContext()) {
                                    @Override
                                    public void onSuccess(String s) {
                                        super.onSuccess(s);
                                        AppUtil.setDrawableLeft(getContext(), mTvPraiseCount, R.drawable.ic_video_post_praise_s);
//                                        mTvPraiseCount.setClickable(false);
                                        postCard.setPostPraiseCount(postCard.getPostPraiseCount() + 1);
                                        postCard.setIsPraise(1);
                                        mTvPraiseCount.setText(getString(R.string.short_video_praise_count, AppUtil.getCommentDesc(postCard.getPostPraiseCount())));
                                    }

                                });
                    }
                }
            });

        }

        private void goPersonPage(Author userIdStr) {
            JumpPageUtil.goToPersonPage(getContext(), userIdStr);
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        //本次登录状态和上次不同，则刷新
        if (isVisibleToUser && mLastIsLogin != UserUtils.hasLogin()) {
            if (getRefreshController() != null) {
                getRefreshController().manualRefresh();
            }
        }
    }
}
