package com.cloudy.linglingbang.activity.car.energy.ble;

import static com.clj.fastble.utils.HexUtil.hexStringToBytes;

import android.annotation.SuppressLint;
import android.util.Base64;

import java.math.BigInteger;
import java.security.MessageDigest;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

public class Utils {
    /**
     * CRC检验
     *
     * @return
     */
    public static String getCRC16(String source) {

        int crc = 0xFFFF;             // 初始值
        int polynomial = 0x1021;             // 校验公式 0001 0000 0010 0001
        byte[] bytes = hexStringToBytes(source);  //把普通字符串转换成十六进制字符串

        for (byte b : bytes) {
            for (int i = 0; i < 8; i++) {
                boolean bit = ((b >> (7 - i) & 1) == 1);
                boolean c15 = ((crc >> 15 & 1) == 1);
                crc <<= 1;
                if (c15 ^ bit)
                    crc ^= polynomial;
            }
        }
        crc &= 0xffff;
        StringBuffer result = new StringBuffer(Integer.toHexString(crc));
        while (result.length() < 4) {         //CRC检验一般为4位，不足4位补0
            result.insert(0, "0");
        }
        return result.toString();
    }

    /**
     * 把字符串转换成十六进制字节数组
     *
     * @return byte[]
     */
    public static byte[] stringToHexByte(String hex) {
        int len = hex.length();
        byte[] output = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            output[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                    + Character.digit(hex.charAt(i + 1), 16));
        }
        return output;
    }


    private static final String UTF_8 = "UTF-8";

    /**
     * 加密
     *
     * @param src 需要加密的内容
     * @param key 加密密码
     * @return
     */
    @SuppressLint("TrulyRandom")
    public static byte[] encrypt(String key, String src) {
        int PWD_SIZE = 16;
        try {
            if (key == null || key.length() <= 0 || src == null || src.length() <= 0) return null;
            StringBuilder sb = new StringBuilder(PWD_SIZE);
            sb.append(key);
            while (sb.length() < PWD_SIZE) {
                sb.append("0");
            }

            SecretKeySpec skey = new SecretKeySpec(hexStringToBytes(sb.toString()), "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/NoPadding");
            byte[] byteContent = hexStringToBytes(src);
            cipher.init(Cipher.ENCRYPT_MODE, skey);// 初始化
            byte[] result = cipher.doFinal(byteContent);
            return result; // 加密
        } catch (Exception ext) {
            ext.printStackTrace();
        }
        return null;
    }

    /**
     * 加密
     *
     * @return
     */
    @SuppressLint("TrulyRandom")
    public static String Decrypt2String(String key, String src) {
        try {
            return byte2Base64(Decrypt(key, src)); // 加密
        } catch (Exception ext) {
            ext.printStackTrace();
        }
        return null;
    }

    /**
     * 解密
     *
     * @param src 需要解密的内容
     * @param key 解密密码
     * @return
     */
    public static byte[] Decrypt(String key, String src) {
        try {
            SecretKeySpec skey = new SecretKeySpec(hexStringToBytes(key), "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/NoPadding");
            byte[] byteContent = hexStringToBytes(src);
            cipher.init(Cipher.DECRYPT_MODE, skey);
            byte[] result = cipher.doFinal(byteContent);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 加密
     *
     * @return
     */
    @SuppressLint("TrulyRandom")
    public static String encrypt2String(String key, String src) {
        try {
            return byte2Base64(encrypt(key, src)); // 加密
        } catch (Exception ext) {
            ext.printStackTrace();
        }
        return null;
    }

    private static String byte2Base64(byte[] encode) {
        try {
            return Base64.encodeToString(encode, Base64.NO_WRAP);

        } catch (Exception ext) {
            ext.printStackTrace();
        }

        return null;
    }


    public static String XOR(String hex1, String hex2) {

        BigInteger i1 = new BigInteger(hex1, 16);
        BigInteger i2 = new BigInteger(hex2, 16);
        BigInteger res = i1.xor(i2);
        StringBuilder result = new StringBuilder(res.toString(16));
        while (result.length() < hex1.length()) {
            result.insert(0, "0");
        }
        return result.toString().toUpperCase();
    }


    /**
     * MD5加密
     */
    public static String encodeMD5(String str) {
        String strDigest = "";
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] data = md5.digest(str.getBytes("utf-8"));
            strDigest = bytesToHexString(data);
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
        return strDigest;
    }


    /**
     * 转base64
     */
    public static String bytesToHexString(byte[] src) {
        StringBuilder stringBuilder = new StringBuilder("");
        if (src == null || src.length <= 0) {
            return null;
        }
        for (int i = 0; i < src.length; i++) {
            int v = src[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }

    public static byte getBits(String s, int startByte, int startBit, int length) {
        startBit = 7 - startBit; //can数据矩阵高到低读数
        byte[] bytes = stringToHexByte(s);
        StringBuilder binaryString = new StringBuilder();
        for (int i = startByte; i < bytes.length && i < startByte + (startBit + length) / 8 + 1; i++) {
            String byteString = String.format("%8s", Integer.toBinaryString(bytes[i] & 0xFF)).replace(' ', '0');
            binaryString.append(byteString);
        }
        int endIndex = startBit + length;
        if (binaryString.length() < endIndex) {
            return (byte) -1;
        }
        return (byte) Integer.parseInt(binaryString.substring(startBit, endIndex), 2);
    }


}
