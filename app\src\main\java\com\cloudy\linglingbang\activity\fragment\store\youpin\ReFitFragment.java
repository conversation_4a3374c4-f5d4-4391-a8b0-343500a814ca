package com.cloudy.linglingbang.activity.fragment.store.youpin;

import android.content.Context;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.animation.TranslateAnimation;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseScrollTabViewPagerFragment;
import com.cloudy.linglingbang.activity.fragment.store.home.DiscussionCommunityFragment;
import com.cloudy.linglingbang.activity.fragment.store.home.model.StoreHomeConstant;
import com.cloudy.linglingbang.app.receiver.UserInfoChangeReceiver;
import com.cloudy.linglingbang.app.receiver.UserInfoChangedHelper;
import com.cloudy.linglingbang.app.util.DeviceUtil;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.PagerSlidingTabStrip;
import com.cloudy.linglingbang.app.widget.textview.CenterDrawableTextView;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;
import butterknife.BindView;
import butterknife.OnClick;

/**
 * 生活-改装
 *
 * <AUTHOR>
 * @date 5/25/21
 */
public class ReFitFragment extends BaseScrollTabViewPagerFragment<Fragment> {
    @BindView(R.id.tv_indicator)
    View mTvIndicator;
    @BindView(R.id.tv_left)
    CenterDrawableTextView mTvLeft;
    @BindView(R.id.tv_right)
    CenterDrawableTextView mTvRight;
    private float mTvIndicatorWidth;

    public static Fragment newInstance() {
        return new ReFitFragment();
    }

    private UserInfoChangedHelper mUserInfoChangedHelper;

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_re_fit;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (mUserInfoChangedHelper == null) {
            mUserInfoChangedHelper = new UserInfoChangedHelper(new UserInfoChangeReceiver() {
                @Override
                protected void onUpdateBaseInfo() {
                    super.onUpdateBaseInfo();
                    userChange();
                }

                @Override
                protected void onUpdateBalanceInfo() {
                    super.onUpdateBalanceInfo();
                    userChange();
                }

                @Override
                protected void onUpdateExpireData() {
                    super.onUpdateCartInfo();
                    userChange();
                }

                private void userChange() {
                    for (Fragment datum : mData) {
                        if (datum instanceof SuperiorProductFragment) {
                            ((SuperiorProductFragment) datum).userChange();
                        }
                    }
                }

            });
        }
        mUserInfoChangedHelper.register(getContext());
    }

    @Override
    protected PagerAdapter createViewPagerAdapter(List<Fragment> data, String[] titles) {
        tabs.setListener(new PagerSlidingTabStrip.SingleListener() {
            @Override
            public TextView createTextTab(Context context, int position) {
                CenterDrawableTextView textView = new CenterDrawableTextView(context);
                textView.setCompoundDrawablePadding(0);
                textView.setGravity(Gravity.NO_GRAVITY);
                textView.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, R.drawable.ic_search_back);

                //textView.setBackgroundResource(R.drawable.select_refit_txt2);
                /*if (position == 0) {
                    textView.setText(titles[0]);
                    //textView.setBackgroundResource(R.drawable.select_refit_txt2);
                } else if (position == 1) {
                    //textView.setBackgroundResource(R.drawable.select_refit_drawable_talk);
                }*/

                textView.setBackgroundResource(R.drawable.select_refit_txt2);
                return textView;
            }
        });
        return new FragmentStatePagerAdapter(getChildFragmentManager()) {

            @Override
            public Fragment getItem(int position) {
                return data.get(position);
            }

            @Override
            public int getCount() {
                return data.size();
            }

            @Nullable
            @Override
            public CharSequence getPageTitle(int position) {
                return titles[position];
            }
        };
    }

    @Override
    protected void initViews() {
        super.initViews();
        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) mTvIndicator.getLayoutParams();
        mTvIndicatorWidth = DeviceUtil.getScreenWidth() / 2;
        layoutParams.width = (int) mTvIndicatorWidth;
        mTvIndicator.setLayoutParams(layoutParams);
        tabs.setShouldExpand(true);
        LinearLayout.LayoutParams leftParam = (LinearLayout.LayoutParams) mTvLeft.getLayoutParams();
        leftParam.width = (int) mTvIndicatorWidth;
        mTvLeft.setLayoutParams(leftParam);
        LinearLayout.LayoutParams rightParam = (LinearLayout.LayoutParams) mTvRight.getLayoutParams();
        leftParam.width = (int) mTvIndicatorWidth;
        mTvRight.setLayoutParams(leftParam);
        mTvLeft.setText(titles[0]);
        mTvRight.setText(titles[1]);
        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                if (position == 0) {
                    mTvLeft.setChecked(true);
                    mTvRight.setChecked(false);
                } else {
                    mTvLeft.setChecked(false);
                    mTvRight.setChecked(true);
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
    }

    @Override
    protected List<Fragment> createAdapterData() {
        List<Fragment> fragmentList = new ArrayList<>();
        fragmentList.add(SuperiorProductFragment.newInstance(StoreHomeConstant.PAGE_CODE_REMOULD_RECOMMEND, "改装-推荐", false));
        fragmentList.add(new DiscussionCommunityFragment());
        return fragmentList;

    }

    @OnClick({R.id.tv_left, R.id.tv_right})
    void clicks(View view) {
        switch (view.getId()) {
            case R.id.tv_left:
                onItemSelected(0);
                mTvLeft.setChecked(true);
                mTvRight.setChecked(false);
                mTvLeft.setEnabled(false);
                mTvRight.setEnabled(true);
                break;
            case R.id.tv_right:
                onItemSelected(1);
                mTvLeft.setChecked(false);
                mTvRight.setChecked(true);
                mTvLeft.setEnabled(true);
                mTvRight.setEnabled(false);
                break;
        }
    }

    @Override
    protected void onItemSelected(int position) {
        super.onItemSelected(position);
        TranslateAnimation animation = new TranslateAnimation(Math.abs(mTvIndicatorWidth * (position - 1)), mTvIndicatorWidth * (position), 0, 0);
        animation.setDuration(200);
        animation.setFillAfter(true);
        mTvIndicator.startAnimation(animation);
        mViewPager.setCurrentItem(position);
        SensorsUtils.sensorsClickBtn("点击改装-" + titles[position], "好物", "好物");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mUserInfoChangedHelper != null) {
            mUserInfoChangedHelper.unregister(getContext());
        }
    }

    @Override
    protected String[] getTitles() {
        return new String[]{"推荐", "讨论社区"};
    }
}
