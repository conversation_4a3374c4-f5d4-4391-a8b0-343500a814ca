package com.cloudy.linglingbang.activity.club.list;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.IntentFilter;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshFragment;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.club.create.CreateCarClubStepOneActivity;
import com.cloudy.linglingbang.activity.search.adapter.SearchCommunityAdapter;
import com.cloudy.linglingbang.app.receiver.UserStatusChangeReceiver;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.widget.dialog.ChooseCarTypeDialog;
import com.cloudy.linglingbang.app.widget.dialog.ChooseCityDialog;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.CarType;
import com.cloudy.linglingbang.model.CityModel;
import com.cloudy.linglingbang.model.ProvinceModel;
import com.cloudy.linglingbang.model.channel.Channel;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BackgroundSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.user.User;
import com.umeng.analytics.MobclickAgent;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.OnClick;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.functions.Function;

/**
 * 首页→社区→车友会
 *
 * <AUTHOR>
 * @date 2017/11/14
 */
public class CarClubListFragment extends BaseRecyclerViewRefreshFragment<CarClubListFragment.CarClubListBean> {
    private CarClubSearchController mCarClubSearchController;
    private CarClubListAdapter mCarClubListAdapter;
    private List<CarClubListBean> mMyJoinedCarClubList;
    private ImageView mIvAddCarClub;

    private final IntentFilter mUserStatusChangeFilter = new IntentFilter(UserStatusChangeReceiver.ACTION);
    private LocalBroadcastManager mLocalBroadcastManager;
    private final BroadcastReceiver mUserStatusChangeReceiver = new UserStatusChangeReceiver() {
        @Override
        protected void onClearUser() {
            checkAddBtnVisibility();
            getMyJoinedCarClub();
        }

        @Override
        protected void onUpdateUser() {
            checkAddBtnVisibility();
            getMyJoinedCarClub();
        }
    };

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<CarClubListBean> list) {
        mCarClubListAdapter = new CarClubListAdapter(getContext(), list);
        mCarClubListAdapter.setOnItemClickListener(new BaseRecyclerViewHolder.OnItemClickListener() {
            @Override
            public void onItemClick(View itemView, int position) {
                onClickItem(position);
            }
        });

        return mCarClubListAdapter;
    }

    /**
     * 点击
     */
    private void onClickItem(int position) {
        List<CarClubListBean> data = getData();
        if (data != null) {
            if (position > -1 && position < data.size()) {
                CarClubListBean carClubListBean = data.get(position);
                if (carClubListBean != null && !carClubListBean.isTitle()) {
                    Channel channel = carClubListBean.getChannel();
                    if (channel != null) {
                        String channelId = channel.getChannelId();
                        if (!TextUtils.isEmpty(channelId)) {
                            JumpPageUtil.goToCarClub(getContext(), channelId);
                            //添加友盟统计
                            MobclickAgent.onEvent(getActivity(), "307");
                        }
                    }
                }
            }
        }
    }

    @Override
    public Observable<BaseResponse<List<CarClubListBean>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return service2.getCarClubList(mCarClubSearchController.getCarTypeId(), mCarClubSearchController.getCityId(), null, pageNo, pageSize)
                .map(new Function<BaseResponse<List<Channel>>, BaseResponse<List<CarClubListBean>>>() {
                    @Override
                    public BaseResponse<List<CarClubListBean>> apply(BaseResponse<List<Channel>> listBaseResponse) {
                        List<Channel> channelList = listBaseResponse.getData();
                        List<CarClubListBean> carClubListBeanList = new ArrayList<>();
                        if (channelList != null) {
                            for (Channel channel : channelList) {
                                carClubListBeanList.add(new CarClubListBean(CarClubListBean.TYPE_CHANNEL, channel));
                            }
                        }
                        return listBaseResponse.cloneWithData(carClubListBeanList);
                    }
                });
    }

    @Override
    public RefreshController<CarClubListBean> createRefreshController() {
        return new RefreshController<CarClubListBean>(this) {
            @Override
            protected void onLoadSuccess(int loadPage, List<CarClubListBean> list, int loadType) {
                boolean needShowEmpty = false;
                if (loadPage <= 1) {
                    //第一页加上标题
                    list.add(0, new CarClubListBean(CarClubListBean.TYPE_CHANNEL_TITLE, getString(R.string.car_club_list_channel_title)));
                    needShowEmpty = updateMyJoinedCarClubList(list);
                }
                super.onLoadSuccess(loadPage, list, loadType);
                checkShowEmpty(needShowEmpty);
            }

            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return null;
            }

            @Override
            public void onRefresh() {
                getMyJoinedCarClub();
                super.onRefresh();
            }
        }
                .setEmptyImageResId(R.drawable.ic_empty_car_club)
                .setEmptyStringResId(R.string.car_club_list_empty);
    }

    /**
     * 检查是否需要显示空布局
     */
    private void checkShowEmpty(boolean needShowEmpty) {
        //计算偏移
        int topMargin;
        if (mMyJoinedCarClubList == null || mMyJoinedCarClubList.isEmpty()) {
            topMargin = getContext().getResources().getDimensionPixelSize(R.dimen.normal_60);
        } else {
            topMargin = getContext().getResources().getDimensionPixelSize(R.dimen.normal_60) * 2;
            topMargin += getContext().getResources().getDimensionPixelSize(R.dimen.normal_130) * mMyJoinedCarClubList.size();
        }
        getRefreshController().forceShowEmptyInfo(needShowEmpty, topMargin);
    }

    /**
     * 获取我加入的社区
     */
    private void getMyJoinedCarClub() {
        if (User.getsUserInstance() != null && !User.getsUserInstance().hasLogin()) {
            //未登录
            onGetMyJoinedCarClubResult(null);
            return;
        }
        //我加入的，传了99
        L00bangRequestManager2.getServiceInstance()
                .getCarClubList(null, null, 1, 1, 99)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new BackgroundSubscriber<List<Channel>>(getContext()) {
                    @Override
                    public void onSuccess(List<Channel> channels) {
                        super.onSuccess(channels);
                        onGetMyJoinedCarClubResult(channels);
                    }
                });
    }

    /**
     * 获取我加入的车友会成功
     * 更新字段，调用更新方法，刷新
     */
    private void onGetMyJoinedCarClubResult(List<Channel> channels) {
        //先清空，保证刷新后的数据用来判断是否显示按钮
        if (mMyJoinedCarClubList == null) {
            mMyJoinedCarClubList = new ArrayList<>();
        } else {
            mMyJoinedCarClubList.clear();
        }
        if (channels != null && channels.size() > 0) {
            mMyJoinedCarClubList.add(new CarClubListBean(CarClubListBean.TYPE_MY_TITLE, getString(R.string.car_club_list_my_channel_title)));
            for (Channel channel : channels) {
                mMyJoinedCarClubList.add(new CarClubListBean(CarClubListBean.TYPE_MY_CHANNEL, channel));
            }
        }
        boolean needShowEmpty = updateMyJoinedCarClubList(getData());
        if (getRefreshController().getAdapter() != null) {
            getRefreshController().getAdapter().notifyDataSetChanged();
            checkShowEmpty(needShowEmpty);
        }
        checkAddBtnVisibility();
    }

    /**
     * 更新数据，独立出该方法用于先加载出我的，或后加载出我的，都可以调用
     *
     * @return 返回除了我加入的，是否为空，用于判断是否显示空布局
     */
    private boolean updateMyJoinedCarClubList(List<CarClubListBean> data) {
        //移除我的
        if (data == null) {
            data = new ArrayList<>();
        } else {
            Iterator<CarClubListBean> iterator = data.iterator();
            while (iterator.hasNext()) {
                CarClubListBean carClubListBean = iterator.next();
                if (carClubListBean != null && carClubListBean.isMyJoined()) {
                    iterator.remove();
                }
            }
        }
        //去除了我加入的为空，或者只有一个标题
        boolean isEmpty = data.isEmpty() || (data.size() == 1 && data.get(0).isTitle());
        //添加我的
        if (mMyJoinedCarClubList != null) {
            data.addAll(0, mMyJoinedCarClubList);
        }
        return isEmpty;
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_car_club_list;
    }

    @Override
    protected void initViews() {
        super.initViews();
        mIvAddCarClub = (ImageView) getRootView().findViewById(R.id.iv_add_car_club);
        mCarClubSearchController = new CarClubSearchController();
        mLocalBroadcastManager = LocalBroadcastManager.getInstance(getContext());
        mLocalBroadcastManager.registerReceiver(mUserStatusChangeReceiver, mUserStatusChangeFilter);
        checkAddBtnVisibility();
    }

    /**
     * 点击车型
     */
    @OnClick(R.id.ll_search_type_car_type)
    void onClickSearchTypeCarType() {
        //添加友盟统计
        MobclickAgent.onEvent(getActivity(), "302");
        mCarClubSearchController.chooseCarType();
    }

    /**
     * 点击城市
     */
    @OnClick(R.id.ll_search_type_location)
    void onClickSearchTypeLocation() {
        //添加友盟统计
        MobclickAgent.onEvent(getActivity(), "303");
        mCarClubSearchController.chooseCity();
    }

    @OnClick(R.id.iv_add_car_club)
    void onClickAddCarClub() {
        //添加友盟统计
        MobclickAgent.onEvent(getActivity(), "301");
        if (AppUtil.checkLogin(getContext())) {
            if (UserUtils.isAuthenticationUser()) {
                checkJoinCarClubStatus();
            } else {
                ToastUtil.showMessage(getContext(), getString(R.string.car_club_list_toast_not_authentication));
            }
        }
    }

    /**
     * 检查加入车友会的状态
     */
    private void checkJoinCarClubStatus() {
        L00bangRequestManager2.getServiceInstance()
                .checkHasJoinCarClub()
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<Object>(getContext()) {
                    @Override
                    public void onSuccess(Object o) {
                        IntentUtils.startActivity(getContext(), CreateCarClubStepOneActivity.class);
                    }
                });
    }

    /**
     * 检查创建车友会按钮的可见性
     */
    private void checkAddBtnVisibility() {
        boolean visibility = false;
        if (User.getsUserInstance() != null && User.getsUserInstance().hasLogin()) {
            //已登录
            if (mMyJoinedCarClubList == null || mMyJoinedCarClubList.isEmpty()) {
                //没有加入车友会
                visibility = true;
            }
        }
        mIvAddCarClub.setVisibility(visibility ? View.VISIBLE : View.GONE);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mLocalBroadcastManager != null) {
            mLocalBroadcastManager.unregisterReceiver(mUserStatusChangeReceiver);
        }
    }

    /**
     * 用于车友会列表展示的bean
     */
    static class CarClubListBean {
        public static final int TYPE_MY_TITLE = 0;
        public static final int TYPE_MY_CHANNEL = 1;
        public static final int TYPE_CHANNEL_TITLE = 2;
        public static final int TYPE_CHANNEL = 3;
        /**
         * 类型
         */
        private final int type;
        /**
         * 用于显示名字
         */
        private String title;
        private Channel channel;

        public CarClubListBean(int type, String title) {
            this.type = type;
            this.title = title;
        }

        public CarClubListBean(int type, Channel channel) {
            this.type = type;
            this.channel = channel;
        }

        public int getType() {
            return type;
        }

        public String getTitle() {
            return title;
        }

        public Channel getChannel() {
            return channel;
        }

        public boolean isTitle() {
            return isTitle(type);
        }

        public static boolean isTitle(int type) {
            return type == TYPE_MY_TITLE || type == TYPE_CHANNEL_TITLE;
        }

        public boolean isMyJoined() {
            return type == TYPE_MY_TITLE || type == TYPE_MY_CHANNEL;
        }
    }

    class CarClubListAdapter extends BaseRecyclerViewAdapter<CarClubListBean> {
        private CarClubListTitleViewHolder mChannelTitleViewHolder;

        public CarClubListAdapter(Context context, List<CarClubListBean> data) {
            super(context, data);
        }

        @Override
        protected BaseRecyclerViewHolder<CarClubListBean> createViewHolder(View itemView) {
            return null;
        }

        @Override
        protected BaseRecyclerViewHolder<CarClubListBean> createViewHolderWithViewType(View itemView, int viewType) {
            if (CarClubListBean.isTitle(viewType)) {
                if (viewType == CarClubListBean.TYPE_CHANNEL_TITLE) {
                    mChannelTitleViewHolder = new CarClubListTitleViewHolder(itemView);
                    return mChannelTitleViewHolder;
                }
                return new CarClubListTitleViewHolder(itemView);
            } else {
                return new CarClubListChannelViewHolder(itemView);
            }
        }

        @Override
        protected int getItemLayoutRes(int viewType) {
            if (CarClubListBean.isTitle(viewType)) {
                return R.layout.item_car_club_title;
            } else {
                return R.layout.item_search_community;
            }
        }

        @Override
        public int getItemViewType(int position) {
            return mData.get(position).getType();
        }

        public void setSearchCondition(String condition) {
            if (mChannelTitleViewHolder != null) {
                mChannelTitleViewHolder.setSearchCondition(condition);
            }
        }
    }

    class CarClubListTitleViewHolder extends BaseRecyclerViewHolder<CarClubListBean> {
        private TextView tvTitle;
        private LinearLayout llSearchCondition;
        private TextView tvSearchCondition;

        public CarClubListTitleViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            tvTitle = (TextView) itemView.findViewById(R.id.tv_title);
            itemView.findViewById(R.id.iv_clear).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mCarClubSearchController != null) {
                        //添加友盟统计
                        MobclickAgent.onEvent(getActivity(), "304");
                        mCarClubSearchController.clearSearch();
                    }
                }
            });
            llSearchCondition = (LinearLayout) itemView.findViewById(R.id.ll_search_condition);
            tvSearchCondition = (TextView) itemView.findViewById(R.id.tv_search_condition);
        }

        /**
         * 设置搜索条件
         */
        private void setSearchCondition(String condition) {
            if (tvSearchCondition != null) {
                tvSearchCondition.setText(condition);
                //第一次设置文字后，再设置文字不支变化宽度
                tvSearchCondition.requestLayout();
            }
            if (llSearchCondition != null) {
                llSearchCondition.setVisibility(TextUtils.isEmpty(condition) ? View.GONE : View.VISIBLE);
            }
        }

        @Override
        public void bindTo(CarClubListBean carClubListBean, int position) {
            super.bindTo(carClubListBean, position);
            tvTitle.setText(carClubListBean.getTitle());
        }
    }

    class CarClubListChannelViewHolder extends BaseRecyclerViewHolder<CarClubListBean> {
        private final SearchCommunityAdapter.ViewHolder mInnerViewHolder;

        public CarClubListChannelViewHolder(View itemView) {
            super(itemView);
            mInnerViewHolder = new SearchCommunityAdapter.ViewHolder(itemView);
            //注意这里，在创建内部 view holder 的时候，会设置点击事件，要重新设置一下
            //而且不可以在initItemView中设置
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (getOnItemClickListener() != null) {
                        getOnItemClickListener().onItemClick(v, getPositionOfData());
                    }
                }
            });
        }

        @Override
        public void bindTo(CarClubListBean carClubListBean, int position) {
            super.bindTo(carClubListBean, position);
            mInnerViewHolder.bindTo(carClubListBean.getChannel(), position);
        }
    }

    /**
     * 搜索相关
     */
    class CarClubSearchController {
        private ChooseCarTypeDialog.ChooseCarTypeUtil mChooseCarTypeUtil;
        private ChooseCityDialog.ChooseCityUtil mChooseCityUtil;
        private Long mCarTypeId;
        private Long mCityId;
        private String mCarTypeName;
        private String mLocationName;

        /**
         * 选择车型
         */
        private void chooseCarType() {
            if (mChooseCarTypeUtil == null) {
                mChooseCarTypeUtil = new ChooseCarTypeDialog.ChooseCarTypeUtil(getContext(), new ChooseCarTypeDialog.OnChooseCarTypeListener() {
                    @Override
                    public boolean onChooseCarType(CarType carType) {
                        CarClubSearchController.this.onChooseCarType(carType);
                        return false;
                    }
                });
            }
            mChooseCarTypeUtil.showDialog();
        }

        /**
         * 选择地区
         */
        private void chooseCity() {
            if (mChooseCityUtil == null) {
                mChooseCityUtil = new ChooseCityDialog.ChooseCityUtil(getContext(), new ChooseCityDialog.OnChooseCityListener() {
                    @Override
                    public boolean onChoseCity(ProvinceModel chosenProvinceModel, CityModel chosenCityModel) {
                        CarClubSearchController.this.onChoseCity(chosenProvinceModel, chosenCityModel);
                        return false;
                    }
                });
            }
            mChooseCityUtil.showDialog();
        }

        /**
         * 选择了车型
         */
        private void onChooseCarType(CarType carType) {
            mCarTypeId = carType.getCarTypeId();
            mCarTypeName = carType.getFullName();
            updateSearch();
        }

        /**
         * 选择了城市
         */
        private void onChoseCity(ProvinceModel chosenProvinceModel, CityModel chosenCityModel) {
            mCityId = chosenCityModel.getCityId();
            mLocationName = AppUtil.getProvinceAndCity(chosenProvinceModel.getProvinceName(), chosenCityModel.getCityName(), " ");
            updateSearch();
        }

        public void clearSearch() {
            mCarTypeId = null;
            mCarTypeName = null;
            mCityId = null;
            mLocationName = null;
            updateSearch();
        }

        private void updateSearch() {
            //不调用刷新，不刷新我的，只刷新筛选的数据
            getRefreshController().getRecyclerView().scrollToPosition(0);//滚到顶部
            getRefreshController().getSwipeToLoadLayout().manualRefresh();//手动刷新
            String condition = "";
            if (!TextUtils.isEmpty(mCarTypeName) && !TextUtils.isEmpty(mLocationName)) {
                condition = mCarTypeName + " " + mLocationName;
            } else if (!TextUtils.isEmpty(mCarTypeName)) {
                condition = mCarTypeName;
            } else if (!TextUtils.isEmpty(mLocationName)) {
                condition = mLocationName;
            }
            if (mCarClubListAdapter != null) {
                mCarClubListAdapter.setSearchCondition(condition);
            }

        }

        public Long getCarTypeId() {
            return mCarTypeId;
        }

        public Long getCityId() {
            return mCityId;
        }
    }

}
