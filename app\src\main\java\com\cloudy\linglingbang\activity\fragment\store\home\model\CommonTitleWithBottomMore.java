package com.cloudy.linglingbang.activity.fragment.store.home.model;

/**
 * 底部查看更多商品
 *
 * <AUTHOR>
 * @date 2020/5/9
 */
public class CommonTitleWithBottomMore extends CommonTitleWithMore {
    private String moreText;

    /**
     * 更多页面跳转类型  16:优品列表   40：二级页面
     */
    private int moreLinkType;
    /**
     * 更多页面跳转的数据   格式：二级类目ID，更多页面样式，更多页面是否显示排序
     * (更多页面样式 0:一行两列    1:一行一列 ; 更多页面是否显示排序  0:否   1:是)
     */
    private String moreLinkUrl;

    @Override
    public int getMoreLinkType() {
        return moreLinkType;
    }

    @Override
    public void setMoreLinkType(int moreLinkType) {
        this.moreLinkType = moreLinkType;
    }

    @Override
    public String getMoreLinkUrl() {
        return moreLinkUrl;
    }

    @Override
    public void setMoreLinkUrl(String moreLinkUrl) {
        this.moreLinkUrl = moreLinkUrl;
    }

    public CommonTitleWithBottomMore(String original) {
        super(original);
    }

    public CommonTitleWithBottomMore(String original, long layoutComponentId) {
        super(original, layoutComponentId);
    }

    public String getMoreText() {
        return moreText;
    }

    public void setMoreText(String moreText) {
        this.moreText = moreText;
    }
}
