package com.cloudy.linglingbang.activity.car.home;

import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.fragment.homePage.today.TodayModel;
import com.cloudy.linglingbang.activity.fragment.homePage.today.viewHolder.TodayColumnHorizontalViewHolder;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;

/**
 * 爱车-栏目水平滚动的viewHolder
 *
 * <AUTHOR>
 * @date 2020-02-24
 */
public class MyCarColumnHorizontalViewHolder extends BaseRecyclerViewHolder<TodayModel.TodayHorizontalColumnPost> {
    private TodayColumnHorizontalViewHolder mTodayColumnHorizontalViewHolder;

    public MyCarColumnHorizontalViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        mTodayColumnHorizontalViewHolder = new TodayColumnHorizontalViewHolder(itemView, R.layout.item_car_column_horizontal_post_item);
    }

    @Override
    public void bindTo(TodayModel.TodayHorizontalColumnPost todayHorizontalColumnPost, int position) {
        super.bindTo(todayHorizontalColumnPost, position);
        itemView.setBackgroundResource(R.color.color_f9f9f9);
        mTodayColumnHorizontalViewHolder.bindTo(todayHorizontalColumnPost, position);
    }
}
