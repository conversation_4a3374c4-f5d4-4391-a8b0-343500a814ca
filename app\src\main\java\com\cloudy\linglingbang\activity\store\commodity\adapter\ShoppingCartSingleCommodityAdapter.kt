package com.cloudy.linglingbang.activity.store.commodity.adapter

import android.content.Context
import android.view.View
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.store.commodity.adapter.holder.OnChangeCommodityListener
import com.cloudy.linglingbang.activity.store.commodity.adapter.holder.ShoppingCartSingleCommodityViewHolder
import com.cloudy.linglingbang.app.widget.SlidingButtonView
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder
import com.cloudy.linglingbang.model.store.commodity.CartCommodity

/**
 * 单个商品的adapter
 *
 * <AUTHOR>
 * @date 2022/10/12
 */
class ShoppingCartSingleCommodityAdapter(context: Context?, data: List<CartCommodity>?) :
    BaseRecyclerViewAdapter<CartCommodity>(context, data) {

    /**
     * 刷新接口的监听
     */
    var needRefreshListener: (Int) -> Unit = {}

    var mOnChangeCommodityListener: OnChangeCommodityListener? = null

    override fun createViewHolder(itemView: View?): BaseRecyclerViewHolder<CartCommodity> {
        var shoppingCarSingle = ShoppingCartSingleCommodityViewHolder(itemView)
        shoppingCarSingle.setOnNeedRefreshListener {
            if (it == 1) {
                needRefreshListener(1)
            }
        }
        shoppingCarSingle.mOnChangeCommodityListener = object : OnChangeCommodityListener {
            override fun onChangeSingleSelect(cartId: Long, isSelect: Int) {
                mOnChangeCommodityListener?.onChangeSingleSelect(cartId, isSelect)
            }

            override fun onChangeSelect(cartIds: MutableList<Long>, isSelect: Int) {
                mOnChangeCommodityListener?.onChangeSelect(cartIds, isSelect)
            }

            override fun onDeleteSingle(cartId: Long) {
                mOnChangeCommodityListener?.onDeleteSingle(cartId)
            }

            override fun onDeleteSingle(cartIds: MutableList<Long>, tips: String) {
                mOnChangeCommodityListener?.onDeleteSingle(cartIds, tips)
            }

            override fun changeNumToCart(cartId: Long, count: Long) {
                mOnChangeCommodityListener?.changeNumToCart(cartId, count)
            }

            override fun onMenuIsOpen(view: View?) {
                mOnChangeCommodityListener?.onMenuIsOpen(view)
            }

            override fun onDownOrMove(slidingButtonView: SlidingButtonView?) {
                mOnChangeCommodityListener?.onDownOrMove(slidingButtonView)
            }
        }
        return shoppingCarSingle;

    }


    override fun getItemLayoutRes(viewType: Int): Int {
        return R.layout.item_shopping_cart_single_activity
    }

    fun setOnNeedRefreshListener(e: (Int) -> Unit) {
        this.needRefreshListener = e
    }

}