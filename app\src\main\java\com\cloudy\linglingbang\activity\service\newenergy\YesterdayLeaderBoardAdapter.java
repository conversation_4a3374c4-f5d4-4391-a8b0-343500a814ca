package com.cloudy.linglingbang.activity.service.newenergy;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.util.span.SpanUtils;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.server.YesterdayLeaderBoard;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 昨日排行的adapter
 *
 * <AUTHOR>
 * @date 2019-07-16
 */
public class YesterdayLeaderBoardAdapter extends BaseRecyclerViewAdapter<YesterdayLeaderBoard> {

    public YesterdayLeaderBoardAdapter(Context context, List<YesterdayLeaderBoard> data) {
        super(context, data);
    }

    @Override
    protected BaseRecyclerViewHolder<YesterdayLeaderBoard> createViewHolder(View itemView) {
        return new ViewHolder(itemView);
    }

    @Override
    protected int getItemLayoutRes(int viewType) {
        return R.layout.item_rank;
    }

    class ViewHolder extends BaseRecyclerViewHolder<YesterdayLeaderBoard> {

        @BindView(R.id.tv_nickName)
        TextView mTvNickName;

        @BindView(R.id.tv_rank_top)
        TextView mTvRankTop;

        @BindView(R.id.tv_mileage)
        TextView mTvMileage;

        @BindView(R.id.iv_rank)
        ImageView mIvRank;

        @BindView(R.id.tv_save)
        TextView mTvSave;

        public ViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            ButterKnife.bind(this, itemView);
        }

        @Override
        public void bindTo(YesterdayLeaderBoard yesterdayLeaderBoard, int position) {
            super.bindTo(yesterdayLeaderBoard, position);
            //昨日形式里程
            String yesterdayMileage = mContext.getString(R.string.txt_remain_mileage, yesterdayLeaderBoard.getMileageOfLastDay());
            SpanUtils.setPartSpanText(mTvMileage, yesterdayMileage, mContext.getResources().getColor(R.color.color_888888),
                    mContext.getResources().getDimension(R.dimen.activity_set_text_20), yesterdayMileage.indexOf("k"), yesterdayMileage.length());

            mTvNickName.setText(yesterdayLeaderBoard.getNickname());
            mTvSave.setText(mContext.getString(R.string.invoice_yuan, yesterdayLeaderBoard.getSaveCount()));
            mTvRankTop.setBackgroundResource(0);
            mIvRank.setVisibility(View.GONE);
            mTvRankTop.setVisibility(View.GONE);
            if (position == 0) {
                mIvRank.setVisibility(View.VISIBLE);
                mIvRank.setImageResource(R.drawable.ic_rank_no1);
            } else if (position == 1) {
                mIvRank.setVisibility(View.VISIBLE);
                mIvRank.setImageResource(R.drawable.ic_rank_no2);
            } else if (position == 2) {
                mIvRank.setVisibility(View.VISIBLE);
                mIvRank.setImageResource(R.drawable.ic_rank_no3);
            } else {
                mTvRankTop.setVisibility(View.VISIBLE);
                mTvRankTop.setBackgroundResource(R.drawable.bg_corner20_solid_3360d8d5);
                String rankStr = mContext.getString(R.string.txt_rank_no, yesterdayLeaderBoard.getRank());
                SpanUtils.setPartSpanText(mTvRankTop, rankStr, mContext.getResources().getColor(R.color.color_60d8d5),
                        mContext.getResources().getDimension(R.dimen.activity_set_text_28), rankStr.indexOf("O") + 1, rankStr.length());

            }
        }
    }
}
