package com.cloudy.linglingbang.activity.community.common.holder.video;

import android.view.View;

import com.cloudy.linglingbang.activity.community.common.holder.BasePostViewHolder;
import com.cloudy.linglingbang.activity.community.common.holder.PostAuthorViewHolder;
import com.cloudy.linglingbang.activity.community.common.holder.PostBottomInfoViewHolder;
import com.cloudy.linglingbang.activity.community.common.holder.PostContentViewHolder;
import com.cloudy.linglingbang.activity.community.common.holder.PostExperienceViewHolder;

import java.util.ArrayList;

/**
 * 视频帖
 *
 * <AUTHOR>
 * @date 2018/8/19
 */
// TODO: [刘汝寿 create at 2018/8/26] 重构删除该类，不能每次视频帖都要特殊处理
public class VideoPostViewHolder extends BasePostViewHolder {
    /**
     * 是否显示视频审核状态
     */
    protected boolean mShowVideoApplyStatus;

    public VideoPostViewHolder(View itemView) {
        this(itemView, false);
    }

    public VideoPostViewHolder(View itemView, boolean showVideoApplyStatus) {
        super(itemView);
        mShowVideoApplyStatus = showVideoApplyStatus;
    }

    @Override
    protected void initChildViewHolder(View itemView) {
        mChildViewHolderList = new ArrayList<>();
        mChildViewHolderList.add(new PostAuthorViewHolder(itemView));
        mChildViewHolderList.add(new PostExperienceViewHolder(itemView));
        mChildViewHolderList.add(new PostContentViewHolder(itemView));
        mChildViewHolderList.add(new VideoPostCoverViewHolder(itemView, mShowVideoApplyStatus));
        mChildViewHolderList.add(new PostBottomInfoViewHolder(itemView));
    }
}
