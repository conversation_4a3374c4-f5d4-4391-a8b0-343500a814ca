package com.cloudy.linglingbang.activity.service.newenergy;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshFragment;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.app.widget.hoverview.HoverGroupLayout;
import com.cloudy.linglingbang.app.widget.hoverview.HoverRecyclerView;
import com.cloudy.linglingbang.app.widget.hoverview.OnChildScrollListener;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.server.YesterdayLeaderBoard;

import java.util.List;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 昨日排行榜
 *
 * <AUTHOR>
 * @date 2019-07-16
 */
public class YesterdayLeaderBoardFragment extends BaseRecyclerViewRefreshFragment<YesterdayLeaderBoard> implements OnChildScrollListener {

    /**
     * 排行查询多少个，服务端要求先写死为100
     */
    private static final int TOP = 100;
    private static HoverGroupLayout mHoverGroupLayout;
    /**
     * 标题导航的第一项，修改为排名
     */
    @BindView(R.id.tv_time)
    TextView mTvTime;

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_base_refresh_rank;
    }

    @Override
    protected void initViews() {
        super.initViews();
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) mTvTime.getLayoutParams();
        layoutParams.weight = 2;
        mTvTime.setLayoutParams(layoutParams);
        mTvTime.setText("排名");
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<YesterdayLeaderBoard> list) {
        return new YesterdayLeaderBoardAdapter(getActivity(), list);
    }

    @Override
    public Observable<BaseResponse<List<YesterdayLeaderBoard>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return service2.rankList(TOP);
    }

    public static YesterdayLeaderBoardFragment newInstance(HoverGroupLayout hoverGroupLayout) {
        mHoverGroupLayout = hoverGroupLayout;
        YesterdayLeaderBoardFragment yesterdayLeaderBoardFragment = new YesterdayLeaderBoardFragment();
        return yesterdayLeaderBoardFragment;
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        HoverRecyclerView recyclerView = (HoverRecyclerView) getRefreshController().getRecyclerView();
        recyclerView.setOnChildScrollListener(this);
    }

    @Override
    public RefreshController<YesterdayLeaderBoard> createRefreshController() {
        final RefreshController<YesterdayLeaderBoard> refreshController = new RefreshController<YesterdayLeaderBoard>(this) {
            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return null;
            }

            //不展示刷新样式
            @Override
            protected boolean showRefreshingWhenLoadDataAfterInitViews() {
                return false;
            }

            @Override
            protected boolean isLoadMoreEnable() {
                //设置不能加载更多
                return false;
            }
        };
        return refreshController;
    }

    /**
     * 供外部调用刷新页面
     */
    public void onRefresh() {
        getRefreshController().onRefresh();
    }

    @Override
    public boolean isChildScroll() {
        return mHoverGroupLayout != null && mHoverGroupLayout.isScrollToEnd();
    }

}
