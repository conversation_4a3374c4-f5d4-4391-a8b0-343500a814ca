package com.cloudy.linglingbang.activity.fragment.store.hotel;

import android.content.Context;
import android.graphics.Paint;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.fragment.mynew.util.SelfUserInfoLoader;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.DensityUtil;
import com.cloudy.linglingbang.app.util.ModelUtils;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.util.span.ForegroundColorAndAbsoluteSizeSpan;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.hotel.HotelRoom;

import java.util.List;

import androidx.annotation.NonNull;

/**
 * 酒店房间价格
 *
 * <AUTHOR>
 * @date 2022/3/17
 */
public class RoomPriceAdapter extends BaseRecyclerViewAdapter<HotelRoom.ProductsBean> {
    public RoomPriceAdapter(Context context, List<HotelRoom.ProductsBean> data, OnMakeOrderListener orderListener) {
        super(context, data);
        mOrderListener = orderListener;
    }

    @Override
    protected BaseRecyclerViewHolder<HotelRoom.ProductsBean> createViewHolder(View itemView) {
        return new HotelRoomViewHolder(itemView);
    }

    @Override
    protected int getItemLayoutRes(int viewType) {
        return R.layout.item_hotel_detail_room_price;
    }

    private class HotelRoomViewHolder extends BaseRecyclerViewHolder<HotelRoom.ProductsBean> {
        TextView mTvPrice;
        TextView mTvOrderPrice;
        TextView mTvOriginalPrice;
        TextView mTvEmptyDesc;
        TextView mTvBtnOk;
        HotelRoom.ProductsBean mProductsBean;

        public HotelRoomViewHolder(View itemView) {
            super(itemView);
            mTvPrice = itemView.findViewById(R.id.tv_price);
            mTvOrderPrice = itemView.findViewById(R.id.tv_order_price);
            mTvOriginalPrice = itemView.findViewById(R.id.tv_original_price);
            mTvEmptyDesc = itemView.findViewById(R.id.tv_empty_desc);
            mTvBtnOk = itemView.findViewById(R.id.btn_ok);
            mTvOriginalPrice.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG);
            mTvBtnOk.setOnClickListener(v -> {
                //下订
                if (mProductsBean != null && AppUtil.checkLogin(v.getContext())) {
                    mOrderListener.makeOrder(mProductsBean);
                }
            });
        }

        @Override
        public void bindTo(HotelRoom.ProductsBean productsBean, int position) {
            super.bindTo(productsBean, position);
            mProductsBean = productsBean;
            mTvEmptyDesc.setText(null);
            //当前房价
            String p = AppUtil.convertBigDecimalPrice(productsBean.getRealHousePrice(), "");
            if (!TextUtils.isEmpty(p)) {
                String str = itemView.getResources().getString(R.string.order_detail_room_c_price) + "¥ " + p;
                SpannableString priceStr = new SpannableString(str);
//                priceStr.setSpan(new StyleSpan(Typeface.BOLD), 0, str.indexOf("¥"), Spanned.SPAN_EXCLUSIVE_INCLUSIVE);
                priceStr.setSpan(new ForegroundColorAndAbsoluteSizeSpan(itemView.getResources().getColor(R.color.color_ea0029), DensityUtil.sp2px(itemView.getContext(), 18)), str.indexOf("¥"), str.length(), Spanned.SPAN_EXCLUSIVE_INCLUSIVE);
                mTvPrice.setText(priceStr);
            } else {
                mTvPrice.setText(null);
            }
            //Ling值价格
            mTvOrderPrice.setText(mTvOrderPrice.getResources().getString(R.string.item_commodity_ling_price, String.valueOf(productsBean.getLingValue())));
            //门市价格
            p = AppUtil.convertBigDecimalPrice(productsBean.getMarketPrice(), "");
            if (!TextUtils.isEmpty(p)) {
                mTvOriginalPrice.setText(ModelUtils.getRmbOrEmptyString(productsBean.getMarketPrice()));
            } else {
                mTvOriginalPrice.setText(null);
            }
            //下单
            if (productsBean.getQuota() > 0) {
                mTvBtnOk.setText(R.string.txt_hotel_room_book);
                if (UserUtils.hasLogin()) {
                    int ling = SelfUserInfoLoader.getInstance().getUserBalanceInfo().getLingCurrency();
                    if (ling < productsBean.getLingValue()) {
                        mTvEmptyDesc.setText(R.string.txt_hotel_ling_insufficient);
                        mTvBtnOk.setEnabled(false);
                        mTvBtnOk.setBackgroundResource(R.drawable.bg_corner4_solid_3d383a40);
                    } else {
                        mTvEmptyDesc.setText(null);
                        mTvBtnOk.setEnabled(true);
                        mTvBtnOk.setBackgroundResource(R.drawable.bg_corner4_solid_ea0029);
                    }
                } else {
                    mTvBtnOk.setEnabled(true);
                    mTvBtnOk.setBackgroundResource(R.drawable.bg_corner4_solid_ea0029);
                }
            } else {
                mTvBtnOk.setText(R.string.txt_hotel_room_full);
                mTvBtnOk.setEnabled(false);
                mTvBtnOk.setBackgroundResource(R.drawable.bg_corner4_solid_3d383a40);
            }

        }
    }

    private final OnMakeOrderListener mOrderListener;

    interface OnMakeOrderListener {
        /**
         * 下订
         */
        void makeOrder(@NonNull HotelRoom.ProductsBean productsBean);
    }
}
