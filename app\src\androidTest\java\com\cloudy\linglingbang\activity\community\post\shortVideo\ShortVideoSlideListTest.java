package com.cloudy.linglingbang.activity.community.post.shortVideo;

import android.content.Intent;

import com.cloudy.linglingbang.BaseInstrumentedTest;
import com.cloudy.linglingbang.activity.basic.IntentUtils;

import static com.cloudy.linglingbang.activity.fragment.homePage.ColumnListActivity.INTENT_EXTRA_COLUMN_NAME;

/**
 * 短视频滑动流帖子
 *
 * <AUTHOR>
 * @date 2018/11/14
 */
public class ShortVideoSlideListTest extends BaseInstrumentedTest {

    @Override
    public void test() {
        super.test();
//        IntentUtils.startActivity(getContext(), ShortVideoSlideListActivity.class);
//        ShortVideoSlideListActivity.startActivity(getContext(), "48226");
        Intent intent = IntentUtils.createStartIntent(getContext(), ShortVideoPostListActivity.class, IntentUtils.INTENT_EXTRA_COMMON, "111");
        intent.putExtra(INTENT_EXTRA_COLUMN_NAME, "dfd");
        IntentUtils.startActivity(getContext(), intent);

//        IntentUtils.startActivity(getContext(), ShortVideoPostListActivity.class);
    }

}
