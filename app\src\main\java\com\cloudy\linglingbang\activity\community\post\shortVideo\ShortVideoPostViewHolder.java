package com.cloudy.linglingbang.activity.community.post.shortVideo;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.View;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.community.post.PostDetailActivity;
import com.cloudy.linglingbang.activity.community.post.PostReportActivity;
import com.cloudy.linglingbang.activity.community.post.detail.GoodsStickyDialog;
import com.cloudy.linglingbang.activity.community.post.detail.PersonalCardDialog;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.widget.ExpressionTextView;
import com.cloudy.linglingbang.app.widget.PraiseCountTextView;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.NormalSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.user.Author;
import com.cloudy.linglingbang.model.user.User;

import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentTransaction;
import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 视频流的viewHolder
 *
 * <AUTHOR>
 * @date 2018/11/20
 */
class ShortVideoPostViewHolder extends BaseRecyclerViewHolder<PostCard> {
    /**
     * 视频封面
     */
    @BindView(R.id.iv_video_cover)
    ImageView mIvVideoCover;
    /**
     * 视频父布局（add视频文件）
     */
    @BindView(R.id.rl_video)
    RelativeLayout mRlVideo;
    /**
     * 点赞数
     */
    @BindView(R.id.tv_praise_count)
    PraiseCountTextView mTvPraiseCount;
    /**
     * 回复数
     */
    @BindView(R.id.tv_reply_count)
    TextView mTvReplyCount;
    /**
     * 头像
     */
    @BindView(R.id.iv_header)
    ImageView mIvHeader;
    /**
     * 昵称
     */
    @BindView(R.id.tv_user_nickname)
    TextView mTvUserNickname;
    /**
     * 加关注
     */
    @BindView(R.id.iv_attention)
    ImageView mIvAttention;
    /**
     * 标题
     */
    @BindView(R.id.tv_title)
    TextView mTvTitle;
    /**
     * 简介
     */
    @BindView(R.id.tv_content)
    ExpressionTextView mTvContent;
    /**
     * 分享
     */
    @BindView(R.id.iv_share)
    ImageView mIvShare;
    /**
     * call名片
     */
    @BindView(R.id.iv_call)
    ImageView mIvCall;
    /**
     * 置顶
     */
    @BindView(R.id.iv_zhi)
    ImageView mIvZhi;
    /**
     * 举报
     */
    @BindView(R.id.tv_report)
    TextView mTvReport;

    /**
     * 4G环境下的提示
     */
    @BindView(R.id.rl_wifi_hint)
    RelativeLayout mRlWifiHint;

    /**
     * 播放按钮
     */
    @BindView(R.id.btn_play_continue)
    Button mBtnPlayContinue;

    private GoodsStickyDialog.GoodsStickyUtils goodsStickyUtils;
    private final Context mContext;
    private PostCard mPostCard;
    /**
     * 是否正在点赞
     */
    private boolean mIsPraising;

    /**
     * 来源
     */
    private final int mOpenFrom;
    private final int mShareVisible;

    public ShortVideoPostViewHolder(View itemView, Context context, int openFrom, int shareVisible) {
        super(itemView);
        mOpenFrom = openFrom;
        mShareVisible = shareVisible;
        mContext = context;
        ButterKnife.bind(this, itemView);
    }

    @Override
    public void bindTo(final PostCard postCard, final int position) {
        super.bindTo(postCard, position);
        mPostCard = postCard;
        mIvShare.setVisibility(mShareVisible == 0 ? View.VISIBLE : View.GONE);
        //先移除视频布局，防止复用干扰
        mRlVideo.removeAllViews();
        mRlWifiHint.setVisibility(View.GONE);
        new ImageLoad(mContext, mIvVideoCover, AppUtil.getImageUrlBySize(postCard.getImgTexts().get(0).getImg(), null), ImageLoad.LoadMode.URL)
                .setPlaceholder(R.drawable.bg_default_car)//设置占位图
                .setDoNotAnimate()
                .load();//执行加载图片
        setPraiseState(postCard);
        if (postCard.getPostCommentCount() == 0) {
            mTvReplyCount.setText(R.string.post_reply);
        } else {
            mTvReplyCount.setText(AppUtil.getCommentDesc(postCard.getPostCommentCount()));
        }
        mTvTitle.setText(postCard.getPostTitle());
        mTvContent.setText(postCard.getImgTexts().get(0).getText());
        if (User.getsUserInstance().hasLogin() && !UserUtils.isSelf(postCard.getAuthor())) {
            mTvReport.setVisibility(View.VISIBLE);
        } else {
            mTvReport.setVisibility(View.GONE);
        }
        /**
         * 如果从推荐登录，则请求接口，告诉已读
         */
//        if (mOpenFrom == ShortVideoSlideListActivity.openFrom.VIDEO_FROM_RECOMMEND) {
//            mTvReport.setVisibility(View.GONE);
//        } else {
//            mTvReport.setVisibility(View.VISIBLE);
//        }
        //用户相关
        if (postCard.getAuthor() != null) {
            mTvUserNickname.setText(postCard.getAuthor().getNickname());
            new ImageLoad(mContext, mIvHeader, AppUtil.getImageUrlBySize(postCard.getAuthor().getPhoto(), null), ImageLoad.LoadMode.URL)
                    .setPlaceholder(R.drawable.user_head_default_120x120)//设置占位图
                    .setCircle(true)
                    .load();//执行加载图片

            if (UserUtils.isSelf(postCard.getAuthor()) || postCard.getAuthor().getAttentionDisplayStatus() == 1) {
                mIvAttention.setVisibility(View.GONE);
            } else {
                mIvAttention.setVisibility(View.VISIBLE);
                if (UserUtils.hasAttentionOther(postCard.getAuthor())) {
                    mIvAttention.setImageResource(R.drawable.ic_post_detail_attentioned);
                } else {
                    mIvAttention.setImageResource(R.drawable.ic_post_detail_add_attentioned);
                }
            }
        }
        mTvReplyCount.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DialogFragment dialogFragment = ShortVideoCommentDialog.newInstance(postCard);
                FragmentTransaction transaction = ((BaseActivity) mContext).getSupportFragmentManager().beginTransaction();
                transaction.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_FADE);
                dialogFragment.show(transaction, "tag");
            }
        });

        //点赞
        mTvPraiseCount.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (AppUtil.checkLogin(mContext)) {
                    onPraiseClick(postCard);
                }
            }
        });

        //分享
        mIvShare.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mContext instanceof ShortVideoSlideListActivity) {
                    ((ShortVideoSlideListActivity) mContext).sharePost(position);
                }
            }
        });

        //点击头像
        mIvHeader.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                goToPersonPage(postCard.getAuthor());
            }
        });

        //点击昵称
        mTvUserNickname.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                goToPersonPage(postCard.getAuthor());
            }
        });

        //举报
        mTvReport.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (AppUtil.checkLogin(v.getContext())) {
                    if (postCard.getProsecutionUncheck() == 1) {
                        ToastUtil.showMessage(v.getContext(), R.string.post_detail_toast_prosecution);
                        return;
                    }
                    //跳转举报详情页
                    IntentUtils.startActivityForResult(AppUtil.getActivity(v.getContext()), PostReportActivity.class, PostDetailActivity.REQUEST_CODE_COMMON, postCard.getPostId());
                }
            }
        });

        /**
         * 加关注
         */
        mIvAttention.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!AppUtil.checkLogin(mContext)) {
                    return;
                }
                if (UserUtils.isSelf(postCard.getAuthor())) {
                    ToastUtil.showMessage(v.getContext(), R.string.user_homepage_can_not_attention_self);
                    return;
                }

                if (!UserUtils.hasAttentionOther(postCard.getAuthor())) {
                    //如果没有关注，则加关注
                    addAttention(v.getContext(), postCard.getAuthor());
                } else {
                    //取消关注
                    deleteAttention(v.getContext(), postCard.getAuthor());
                }

            }
        });
        mIvZhi.setOnClickListener(null);
        mIvCall.setOnClickListener(null);
        mIvZhi.setVisibility(View.GONE);
        mIvCall.setVisibility(View.GONE);
        //如果是好货互通的视频贴
        if (postCard.getSeries() == 3) {
            //是否是自己
            if (UserUtils.isSelf(postCard.getAuthor())) {
                mIvZhi.setVisibility(View.VISIBLE);
                mIvZhi.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        //是否已置顶 是 查看剩余时间，否设置置顶
                        if (postCard.getIsTop() == 1) {
                            Resources resources = v.getResources();
                            if (postCard.getTopExpireDate() > AppUtil.getServerCurrentTime()) {
                                long time = postCard.getTopExpireDate() - AppUtil.getServerCurrentTime();
                                //展示置顶剩余时间
                                CommonAlertDialog dialog = new CommonAlertDialog(v.getContext(), resources.getString(R.string.text_post_set_ding_haved_time, String.valueOf(time / 3600_000), String.valueOf(time / 1000 / 60 % 60)), null, "知道了", null, null);
                                dialog.show();
                            } else {
                                CommonAlertDialog dialog = new CommonAlertDialog(v.getContext(), resources.getString(R.string.text_post_set_ding_haved), null, resources.getString(R.string.label_driving_rec_close), null, null);
                                dialog.show();
                            }
                        } else {
                            //如果未置顶
                            //设置置顶
                            if (goodsStickyUtils == null) {
                                goodsStickyUtils = new GoodsStickyDialog.GoodsStickyUtils(v.getContext(), postCard.getPostId(), false);
                            }
                            goodsStickyUtils.queryStickyNewDateAndShowDialog(new GoodsStickyDialog.StickyAction() {
                                @Override
                                public void onCall(boolean result, long date) {
                                    // 置顶成功,把置顶失效时间置位null,这样方便判断弹窗即 告诉用户您的帖子已置顶 或者显示置顶剩余时间弹窗
                                    mPostCard.setTopExpireDate(result ? null : mPostCard.getTopExpireDate());
                                    mPostCard.setIsTop(result ? 1 : 0);
                                }
                            });
                        }
                    }
                });
            }
            //个人名片信息不为空
            if (postCard.getCallingCard() != null) {
                mIvCall.setVisibility(View.VISIBLE);
                mIvCall.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        //查看个人名片弹窗
                        PersonalCardDialog dialog = new PersonalCardDialog(v.getContext(), postCard.getCallingCard());
                        dialog.show();
                    }
                });
            }
        }
    }

    /**
     * 关注
     */
    private void addAttention(Context context, User user) {
        if (user != null) {
            L00bangRequestManager2.getServiceInstance()
                    .addFriend(mPostCard.getAuthor().getUserIdStr())
                    .compose(L00bangRequestManager2.setSchedulers())
                    .subscribe(new ProgressSubscriber<String>(mContext) {
                        @Override
                        public void onSuccess(String s) {
                            super.onSuccess(s);
                            ToastUtil.showMessage(context, R.string.user_homepage_attention_success);
                            Author author = mPostCard.getAuthor();
                            UserUtils.updateUserAttention(author, true);
                        }
                    });

            L00bangRequestManager2.getServiceInstance()
                    .addFriend(user.getUserIdStr())
                    .compose(L00bangRequestManager2.setSchedulers())
                    .subscribe(new ProgressSubscriber<String>(context) {
                        @Override
                        public void onSuccess(String s) {
                            super.onSuccess(s);
                            ToastUtil.showMessage(context, R.string.user_homepage_attention_success);
                            updateAttention(true);
                        }
                    });
        }
    }

    /**
     * 更新关注
     */
    private void updateAttention(boolean attention) {
        if (mPostCard != null) {
            Author author = mPostCard.getAuthor();
            if (author == null || TextUtils.isEmpty(author.getUserIdStr())) {
                return;
            }

            BaseRecyclerViewAdapter adapter = (BaseRecyclerViewAdapter) mAdapter;
            int index = 0;
            for (Object datum : adapter.getData()) {
                if (datum instanceof PostCard) {
                    PostCard pd = ((PostCard) datum);
                    if (pd.getAuthor() != null && author.getUserIdStr().equals(pd.getAuthor().getUserIdStr())) {
                        UserUtils.updateUserAttention(pd.getAuthor(), attention);
                        if (!pd.getPostId().equals(mPostCard.getPostId())) {
                            mAdapter.notifyItemChanged(index);
                        }
                    }
                }
                index++;
            }

            if (UserUtils.isSelf(mPostCard.getAuthor()) || mPostCard.getAuthor().getAttentionDisplayStatus() == 1) {
                mIvAttention.setVisibility(View.GONE);
            } else {
                mIvAttention.setVisibility(View.VISIBLE);
                if (UserUtils.hasAttentionOther(mPostCard.getAuthor())) {
                    mIvAttention.setImageResource(R.drawable.ic_post_detail_attentioned);
                } else {
                    mIvAttention.setImageResource(R.drawable.ic_post_detail_add_attentioned);
                }
            }
        }

    }

    /**
     * 取消关注
     */
    private void deleteAttention(Context context, User user) {
        if (user != null) {
            L00bangRequestManager2.getServiceInstance()
                    .deleteFriend(user.getUserIdStr())
                    .compose(L00bangRequestManager2.setSchedulers())
                    .subscribe(new ProgressSubscriber<String>(context) {
                        @Override
                        public void onSuccess(String s) {
                            super.onSuccess(s);
                            ToastUtil.showMessage(context, R.string.user_homepage_cancel_attention_success);
                            updateAttention(false);
                        }
                    });
        }
    }

    /**
     * 进入个人详情
     */
    private void goToPersonPage(User user) {
        if (user != null) {
            JumpPageUtil.goToPersonPage(mContext, user);
        }
    }

    /**
     * 点赞操作
     *
     * @param postCard
     */
    public void onPraiseClick(final PostCard postCard) {
        if (postCard.getIsPraise() == 1) {
            ToastUtil.showMessage(mContext, "您已经赞过了");
        } else {
            if (AppUtil.checkLogin(mContext) && !mIsPraising) {
                mIsPraising = true;
                //先成功，失败后复原
                postCard.setIsPraise(1);
                //更新点赞数
                int praiseCount = postCard.getPostPraiseCount() + 1;
                postCard.setPostPraiseCount(praiseCount);
                setPraiseState(postCard);
                //点赞动画
                mTvPraiseCount.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.post_praise_anim));
//                mAdapter.notifyItemChanged(getAdapterPosition());
                L00bangRequestManager2
                        .getServiceInstance()
                        .praisedPostCard(Long.valueOf(postCard.getPostId()))
                        .compose(L00bangRequestManager2.setSchedulers())
                        .subscribe(new NormalSubscriber<String>(mContext) {
                            @Override
                            public void onSuccess(String s) {
                                super.onSuccess(s);
                                mIsPraising = false;
                            }

                            @Override
                            public void onFailure(Throwable e) {
                                super.onFailure(e);
                                mIsPraising = false;
                                postCard.setIsPraise(0);
                                //更新点赞数
                                int praiseCount = postCard.getPostPraiseCount() - 1;
                                postCard.setPostPraiseCount(praiseCount);
                                setPraiseState(postCard);
//                                mAdapter.notifyItemChanged(getAdapterPosition());

                            }
                        });
            }
        }
    }

    /**
     * 设置点赞状态
     */
    public void setPraiseState(PostCard postCard) {
        if (postCard.getPostPraiseCount() == 0) {
            mTvPraiseCount.setText(R.string.post_praise);
        } else {
            mTvPraiseCount.setText(AppUtil.getCommentDesc(postCard.getPostPraiseCount()));
        }
        Drawable drawableTop;
        if (postCard.getIsPraise() == 1) {
            drawableTop = mContext.getResources().getDrawable(R.drawable.ic_video_praise);
//            mTvPraiseCount.setTextColor(mContext.getResources().getColor(R.color.color_ff5252));
        } else {
            drawableTop = mContext.getResources().getDrawable(R.drawable.ic_video_un_praise);
//            mTvPraiseCount.setTextColor(mContext.getResources().getColor(R.color.color_9b9b9b));
        }
        mTvPraiseCount.setCompoundDrawablesWithIntrinsicBounds(null, drawableTop, null, null);
    }
}
