package com.cloudy.linglingbang.activity.community.label;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.community.common.BasePostListActivity;
import com.cloudy.linglingbang.app.widget.CommonSortView;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;

import java.util.List;

import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 标签集合-单个标签下的帖子列表
 *
 * <AUTHOR>
 * @date 2018/6/23
 */
public class LabelPostListActivity extends BasePostListActivity {
    private long mLabelId;
    protected int mOrderType;
    //就是labelName
    private String title;

    @Override
    public Observable<BaseResponse<List<PostCard>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return service2.getPostsByLabelId(mLabelId, mOrderType, pageNo, pageSize);
    }

    @Override
    public RefreshController<PostCard> createRefreshController() {
        final RefreshController<PostCard> refreshController = new RefreshController<PostCard>(this) {
            @Override
            public void initViews(View rootView) {
                super.initViews(rootView);
                recyclerView.setBackgroundResource(R.color.white);
            }

            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return null;
            }
        };
        return refreshController;
    }

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_label_post_list);
    }


    @Override
    protected void initialize() {
        super.initialize();
        mLabelId = getOrParseIntentLongExtra();
        if (mLabelId <= 0) {
            onIntentExtraError();
            return;
        }
        title = (String) IntentUtils.getExtra(getIntent().getExtras(), IntentUtils.INTENT_EXTRA_TITLE, "");
        if (!TextUtils.isEmpty(title)) {
            setMiddleTitle(title);
        }
        CommonSortView commonSortView = findViewById(R.id.common_sort_view);
        commonSortView.setOnSelectSortListener(new CommonSortView.SortSelectListener() {
            @Override
            public void onSortSelect(int selectType) {
                mOrderType = selectType;
                getRefreshController().manualRefresh();
            }
        });
    }


}
