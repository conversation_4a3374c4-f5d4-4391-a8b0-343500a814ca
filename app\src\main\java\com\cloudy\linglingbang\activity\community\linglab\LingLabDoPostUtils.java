package com.cloudy.linglingbang.activity.community.linglab;

import android.app.Activity;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import android.util.Log;

import com.cloudy.linglingbang.ApplicationLLB;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.widget.dialog.alert.NewCommonAlertDialog;
import com.donkingliang.imageselector.utils.ImageSelector;

import java.util.ArrayList;
import java.util.List;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

/**
 * 发菱感贴选择图片的工具类
 *
 * <AUTHOR>
 * @date 2021/12/10
 */
public class LingLabDoPostUtils {
    public int REQUEST_CODE_PERMISSION = 0x00099;
    public final int REQUEST_CODE = 0x00000011;
    public static final int REQUEST_PHOTO_CODE = 3000; //获取权限
    public static final String KEY_HAVE_DRAFT = "isHaveDraft";

    private final Context mContext;

    public LingLabDoPostUtils(Context context) {
        mContext = context;
    }

    NewCommonAlertDialog commonAlertDialog;
    /**
     * 动态获权
     * */
    /**
     * 请求权限
     *
     * @param permissions 请求的权限
     * @param requestCode 请求权限的请求码
     */

    public void requestPermission(Activity activity, String[] permissions, int requestCode, int imgSizes) {
        this.REQUEST_CODE_PERMISSION = requestCode;
        if (checkPermissions(permissions)) {
            if (requestCode == REQUEST_PHOTO_CODE) { //相册
                doChoosePhoto(imgSizes);
            }

            //permissionSuccess(REQUEST_CODE_PERMISSION);
        } else {
            if (commonAlertDialog == null || !commonAlertDialog.isShowing()) {
                commonAlertDialog = new NewCommonAlertDialog(activity,
                        activity.getResources().getString(R.string.permission_picture_pre), "确认", "取消", (dialogInterface, i) -> {
                    try {
                        List<String> needPermissions = getDeniedPermissions(permissions);
                        ActivityCompat.requestPermissions(ApplicationLLB.currentActivity, needPermissions.toArray(new String[needPermissions.size()]), REQUEST_CODE_PERMISSION);
                    } catch (Exception e) {
                        Log.e("5555555", "获取权限try" + e);
                    }
                }, (dialog, which) -> {

                });
                commonAlertDialog.setCanceledOnTouchOutside(false);
                commonAlertDialog.show();
            }

        }
    }

    /**
     * 选择照片
     *
     * @param imgSizes
     */
    public void doChoosePhoto(int imgSizes) {
        //int imgSizes = imgDatas.size();
        //多选(最多9张)
        ImageSelector.builder()
                .useCamera(true) // 设置是否使用拍照
                .setSingle(false) //设置是否单选
                .canPreview(true) //是否点击放大图片查看,，默认为true
                .setMaxSelectCount(9 - (imgSizes - 1)) // 图片的最大选择数量，小于等于0时，不限数量。
                .start(ApplicationLLB.currentActivity, REQUEST_CODE); // 打开相册
    }

    /**
     * 检测所有的权限是否都已授权
     *
     * @param permissions
     * @return
     */
    private boolean checkPermissions(String[] permissions) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            return true;
        }

        for (String permission : permissions) {
            if (ContextCompat.checkSelfPermission(mContext, permission) != PackageManager.PERMISSION_GRANTED) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取权限集中需要申请权限的列表
     *
     * @param permissions
     * @return
     */
    private List<String> getDeniedPermissions(String[] permissions) {
        List<String> needRequestPermissionList = new ArrayList<>();
        for (String permission : permissions) {
            if (ContextCompat.checkSelfPermission(mContext, permission) !=
                    PackageManager.PERMISSION_GRANTED ||
                    ActivityCompat.shouldShowRequestPermissionRationale(ApplicationLLB.currentActivity, permission)) {
                needRequestPermissionList.add(permission);
            }
        }
        return needRequestPermissionList;
    }

}
