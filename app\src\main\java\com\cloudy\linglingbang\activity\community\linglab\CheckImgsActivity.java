package com.cloudy.linglingbang.activity.community.linglab;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.widget.Toast;

import com.cloudy.linglingbang.R;
import com.donkingliang.imageselector.utils.ImageSelector;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 照相或者选择图片
 * 引用ImageSelector框架，
 * 1.需要在Project的build.gradle在添加以下代码：
 * allprojects {
 * repositories {
 * ...
 * maven { url 'https://jitpack.io' }
 * maven { url 'https://maven.google.com' }
 * }
 * }
 * 2.在Module的build.gradle在添加以下代码：
 * implementation 'com.github.donkingliang:ImageSelector:2.1.1'
 * <p>
 * 3.xml文件中声明三个相关Activity
 * 添加相应的权限
 * <!-- 图片选择Activity -->
 * <activity
 * android:name="com.donkingliang.imageselector.ImageSelectorActivity"
 * android:screenOrientation="portrait" />
 * <p>
 * <!-- 图片预览Activity -->
 * <activity
 * android:name="com.donkingliang.imageselector.PreviewActivity"
 * android:screenOrientation="portrait" />
 * <p>
 * <!-- 图片剪切Activity -->
 * <activity
 * android:name="com.donkingliang.imageselector.ClipImageActivity"
 * android:screenOrientation="portrait" />
 */
public class CheckImgsActivity extends BaseCheckImgActivity {
    private Context mContext;
    private RecyclerView img_recycleview;
    private PhotoImagsAdapter photoImagsAdapter;
    private final List<String> imgDatas = new ArrayList<>();
    private final int REQUEST_CODE = 0x00000011;
    private final int REQUEST_PHOTO_CODE = 3000; //获取权限

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_check_imageview);
        mContext = this;
        initView();
        initData();
    }

    public void initView() {
        img_recycleview = findViewById(R.id.img_recycleview);
    }

    public void initData() {
        photoImagsAdapter = new PhotoImagsAdapter(mContext, imgDatas);
        img_recycleview.setAdapter(photoImagsAdapter);

        /**
         * 添加图片
         * 删除图片
         * */
        photoImagsAdapter.setAddImgClickLisitenner(new PhotoImagsAdapter.InfoImgClickListener() {
            @Override
            public void onAddItenClick(int addPosition) {
                //获取操作存储的权限
                requestPermission(new String[]{
                        Manifest.permission.READ_EXTERNAL_STORAGE,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE
                }, REQUEST_PHOTO_CODE);
            }

            @Override
            public void onDeleteItenClick(int deletePosition) {
                imgDatas.remove(imgDatas.get(deletePosition));
                photoImagsAdapter.setmDatas(imgDatas);
                for (int i = 0; i < imgDatas.size(); i++) {
                    Log.e("++CheckImgsActivity++", "图片路径Deiete imgPath = " + imgDatas.get(i));
                }
            }
        });

    }

    @Override
    public void permissionSuccess(int requestCode) {
        super.permissionSuccess(requestCode);
        if (requestCode == REQUEST_PHOTO_CODE) { //相册
            int imgSizes = imgDatas.size();
            //多选(最多9张)
            ImageSelector.builder()
                    .useCamera(true) // 设置是否使用拍照
                    .setSingle(false) //设置是否单选
                    .canPreview(true) //是否点击放大图片查看,，默认为true
//                .setMaxSelectCount(9-(imgSizes-1)) // 图片的最大选择数量，小于等于0时，不限数量。
                    .setMaxSelectCount(0) // 图片的最大选择数量，小于等于0时，不限数量。
                    .start(this, REQUEST_CODE); // 打开相册
        }
    }

    @Override
    public void permissionFail(int requestCode) {
        super.permissionFail(requestCode);
        Toast.makeText(mContext, "您把数据访问禁止了，你将不能享受服务。修改权限可以去设置中修改！", Toast.LENGTH_SHORT).show();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE && resultCode == Activity.RESULT_OK && data != null) {
            List<String> images = data.getStringArrayListExtra(ImageSelector.SELECT_RESULT);
            imgDatas.addAll(images);
            photoImagsAdapter.setmDatas(imgDatas);
            for (int i = 0; i < imgDatas.size(); i++) {
                Log.e("++CheckImgsActivity++", "图片路径Add imgPath = " + imgDatas.get(i));
            }
        }

    }
}
