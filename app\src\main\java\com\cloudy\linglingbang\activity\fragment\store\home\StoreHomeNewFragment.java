package com.cloudy.linglingbang.activity.fragment.store.home;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.fragment.mynew.util.SelfUserInfoLoader;
import com.cloudy.linglingbang.activity.fragment.store.home.model.ElementStaggerdWrapper;
import com.cloudy.linglingbang.activity.fragment.store.home.model.StoreHomeConstant;
import com.cloudy.linglingbang.activity.fragment.store.youpin.search.SearchActivity;
import com.cloudy.linglingbang.activity.store.commodity.ShoppingCartActivity;
import com.cloudy.linglingbang.app.receiver.UserInfoChangeReceiver;
import com.cloudy.linglingbang.app.receiver.UserInfoChangedHelper;
import com.cloudy.linglingbang.app.util.NotchScreenUtils;
import com.cloudy.linglingbang.app.util.StatusBarUtils;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.util.ViewHolderUtils;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.BaseOnClickListener;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.server.Ad.Ad2;
import com.cloudy.linglingbang.model.store.ecology.CartInfo;
import com.cloudy.linglingbang.model.store.home.StoreElementCommodity;
import com.cloudy.linglingbang.model.store.home.StoreElementWaterfallVo;
import com.cloudy.linglingbang.model.store.home.StoreLayoutElement;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import io.reactivex.rxjava3.core.Observable;

/**
 * 好物首页
 *
 * <AUTHOR>
 * @date 2022/12/9
 */
public class StoreHomeNewFragment extends StoreHomeTabFragment {
    /**
     * 购物车数量
     */
    private TextView mTvCartCount;
    /**
     * 用于控制是否展示分享以及购物车数量
     */
    private UserInfoChangedHelper mUserInfoChangedHelper;

    public static Fragment newInstance() {
        return new StoreHomeNewFragment();
    }

    public static Fragment newInstance(int type) {
        return IntentUtils.setFragmentIntArgument(new StoreHomeNewFragment(), type);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (mUserInfoChangedHelper == null) {
            mUserInfoChangedHelper = new UserInfoChangedHelper(new UserInfoChangeReceiver() {
                @Override
                protected void onUpdateCartInfo() {
                    super.onUpdateCartInfo();
                    initCart();
                }

                @Override
                protected void onClearUser() {
                    super.onClearUser();
                    initCart();
                }
            });
        }
        mUserInfoChangedHelper.register(getContext());
    }

    @Override
    protected void initViews() {
        super.initViews();
        int from = IntentUtils.getFragmentIntArgument(this);
        //设置状态栏的高度
        if (from != 1) {
            int statusHeight = Math.max(StatusBarUtils.getStatusBarHeight(mRootView.getContext()), NotchScreenUtils.getNotchSafeWH()[1]);
            mRootView.setPadding(mRootView.getPaddingLeft(),
                    statusHeight,
                    mRootView.getPaddingRight(),
                    mRootView.getPaddingBottom());
        }
        mTvCartCount = mRootView.findViewById(R.id.tv_cart_count);
        //点击购物车
        ViewHolderUtils.setOnClickListener(new BaseOnClickListener(true) {
            @Override
            public void onClick(Context context) {
                super.onClick(context);
                SensorsUtils.sensorsClickBtn("点击购物车（好物）", "好物","购物车（好物）");
                IntentUtils.startActivity(context, ShoppingCartActivity.class);
            }
        }, mRootView.findViewById(R.id.fl_cart));
        ViewHolderUtils.setOnClickListener(new BaseOnClickListener(false) {
            @Override
            public void onClick(Context context) {
                super.onClick(context);
                SensorsUtils.sensorsClickBtn("点击搜索框", "好物","搜索输入框");
                IntentUtils.startActivity(context, SearchActivity.class);
            }
        }, mRootView.findViewById(R.id.search_go_btn));
        //初始化状态
        initCart();
    }

    /**
     * 初始化购物车，在初始化时、用户购物车信息变化时调用
     */
    private void initCart() {
        if (mTvCartCount == null) {
            return;
        }
        if (UserUtils.hasLogin()) {
            //已登录
            CartInfo cartInfo = SelfUserInfoLoader.getInstance().getCartInfo();
            if (cartInfo.getCount() > 0) {
                mTvCartCount.setVisibility(View.VISIBLE);
                mTvCartCount.setText(String.valueOf(cartInfo.getCount()));
            } else {
                mTvCartCount.setVisibility(View.GONE);
            }
        } else {
            //未登录
            mTvCartCount.setVisibility(View.GONE);
        }
    }

    @Override
    public RefreshController<Object> createRefreshController() {
        mPageCode = StoreHomeConstant.PAGE_CODE_HOME_GOODS;
        return new StoreHomeTabRefreshController(this, mPageCode) {
            //是否正在加载数据中
            private boolean isLoading;

            @Override
            public void getListData(int page) {
                if (isLoading) {
                    if (page < 2) {
                        swipeToLoadLayout.setRefreshing(false);
                    } else {
                        swipeToLoadLayout.setLoadingMore(false, true);
                    }
                    return;
                }
                isLoading = true;
                super.getListData(page);
            }

            @Override
            protected boolean isLoadMoreEnable() {
                ElementStaggerdWrapper wrapper = getElementStaggerdWrapper();
                boolean hasStaggerd = wrapper != null && wrapper.getWaterfallVo() != null;
                return hasStaggerd || super.isLoadMoreEnable();
            }

            @Override
            public void onLoadFail(int loadPage, Throwable e) {
                super.onLoadFail(loadPage, e);
                isLoading = false;
            }

            private ElementStaggerdWrapper getElementStaggerdWrapper() {
                if (mData == null) {
                    return null;
                }
                ElementStaggerdWrapper wrapper = null;
                for (int i = 0; i < mData.size(); i++) {
                    Object datum = mData.get(i);
                    if (datum instanceof ElementStaggerdWrapper) {
                        wrapper = (ElementStaggerdWrapper) datum;
                        break;
                    }
                }
                return wrapper;
            }

            @Override
            protected void onLoadSuccess(int loadPage, List<Object> list, int loadType) {
                isLoading = false;
                if (loadPage <= 1) {
                    super.onLoadSuccess(loadPage, list, loadType);
                    setLoadMoreEnable(true);
                    onLoadMore();
                } else {
                    if (list == null || list.isEmpty()) {
                        onLoadMoreComplete();
                        setLoadMoreEnable(false);
                        return;
                    }
                    mPageNo = loadPage;
                    toStaggeredCommodityData(list);
                }
            }

            private void toStaggeredCommodityData(List<Object> list) {
                ElementStaggerdWrapper wrapper = getElementStaggerdWrapper();
                if (wrapper == null) {
                    onLoadMoreComplete();
                    return;
                }
                List<Object> dataList = wrapper.getOriginal();
                if (dataList == null) {
                    dataList = new ArrayList<>();
                }
                dataList.addAll(list);
                wrapper.setOriginal(dataList);
                onLoadMoreComplete();
                int size = list.size();
                setLoadMoreEnable(getPageSize() == size && size > 0);
            }

            @Override
            protected Observable<BaseResponse<List<Object>>> getListDataFromNet(L00bangService2 service2, int pageNo, int pageSize) {
                if (pageNo == 1) {
                    return super.getListDataFromNet(service2, pageNo, pageSize);
                }
                ElementStaggerdWrapper wrapper = getElementStaggerdWrapper();
                StoreElementWaterfallVo waterfallVo = wrapper == null ? null : wrapper.getWaterfallVo();
                if (waterfallVo == null) {
                    setLoadMoreEnable(false);
                    onLoadMoreComplete();
                    isLoading = false;
                    return null;
                }
                Map<String, Object> map = new HashMap<>();
                map.put("pageNo", pageNo - 1);
                map.put("pageSize", getPageSize());
                map.put("waterfallId", waterfallVo.getWaterfallId());
                return service2.getCommodityList(map).map(baseResponse -> {
                    List<Object> list;
                    if (baseResponse.getData() == null) {
                        list = new ArrayList<>();
                    } else {
                        list = new ArrayList<>();
                        for (StoreElementCommodity commodity : baseResponse.getData()) {
                            StoreLayoutElement layoutElement = new StoreLayoutElement();
                            layoutElement.setProductVo(commodity);
                            //layoutElement.setShowModule(StoreHomeElementEnum.COMMODITY_1_2.getType());
                            layoutElement.setLinkType(Ad2.LINK_TYPE.TYPE_NEW_COMMODITY_DETAIL);
                            layoutElement.setImage(commodity.getProductMainImage());
                            layoutElement.setLinkUrl(String.valueOf(commodity.getProductIdOrZero()));
                            list.add(layoutElement);
                        }
                    }
                    return baseResponse.cloneWithData(list);
                });
            }
        };
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_store_home_new;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mUserInfoChangedHelper != null) {
            mUserInfoChangedHelper.unregister(getContext());
        }
    }
}