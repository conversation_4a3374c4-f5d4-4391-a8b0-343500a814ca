package com.cloudy.linglingbang.activity.fragment.store.youpin;

import android.os.Bundle;
import android.view.View;

import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.constants.FinalSensors;
import com.cloudy.linglingbang.constants.WebUrlConfigConstant;
import com.cloudy.linglingbang.web.BaseX5WebViewFragment;

import androidx.annotation.Nullable;

/**
 * 生活缴费
 *
 * <AUTHOR>
 * @date 2021/11/26
 */
public class LifePaymentFragment extends BaseX5WebViewFragment {

    public static LifePaymentFragment newInstance(boolean needLazyLoad) {
        Bundle args = new Bundle();
        args.putBoolean("needLazyLoad", needLazyLoad);
        LifePaymentFragment fragment = new LifePaymentFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected String setUrl() {
        return WebUrlConfigConstant.LIFE_PAYMENT;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle args = getArguments();
        if (args != null && args.getBoolean("needLazyLoad", true)) {
            setNeedLazyLoad(true);
        }
    }

    @Override
    protected void initialize(View view) {
        super.initialize(view);
        if (mToolbar != null) {
            mToolbar.setVisibility(View.GONE);
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        SensorsUtils.sensorsViewStart(FinalSensors.BROWSE_LIFE_INFORMATION);
    }

    @Override
    public void onStop() {
        super.onStop();
        SensorsUtils.sensorsViewEndNew("生活缴费", FinalSensors.BROWSE_LIFE_INFORMATION, "浏览生活缴费");
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (hidden) {
            SensorsUtils.sensorsViewEndNew("生活缴费", FinalSensors.BROWSE_LIFE_INFORMATION, "浏览生活缴费");
        } else {
            SensorsUtils.sensorsViewStart(FinalSensors.BROWSE_LIFE_INFORMATION);
        }
    }
}
