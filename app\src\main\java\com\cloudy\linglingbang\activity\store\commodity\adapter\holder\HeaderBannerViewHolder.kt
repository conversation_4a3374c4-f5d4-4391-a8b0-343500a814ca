package com.cloudy.linglingbang.activity.store.commodity.adapter.holder

import android.content.Context
import android.text.TextUtils
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.store.commodity.CommodityVideoView
import com.cloudy.linglingbang.app.widget.banner.AdImageView
import com.cloudy.linglingbang.app.widget.banner2.Banner2RedDotView
import com.cloudy.linglingbang.app.widget.banner2.Banner2View
import com.cloudy.linglingbang.app.widget.banner2.OnPageChangeListener
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder
import com.cloudy.linglingbang.model.server.Ad.Ad2
import com.cloudy.linglingbang.model.store.commodity.CenterCommodity

/**
 * 商品详情页面顶部轮播
 */
class HeaderBannerViewHolder(itemView: View) : BaseCommodityHolder<Any>(itemView),
    OnPageChangeListener {

    private val banner: Banner2View<CenterCommodity.MainImg> =
        itemView.findViewById(R.id.banner_view)
    private val vwdot:Banner2RedDotView = itemView.findViewById(R.id.vw_dot)
    private val indicator: TextView = itemView.findViewById(R.id.tv_indicator)
    val list = arrayListOf<CenterCommodity.MainImg>()
    var commodityVideoView: CommodityVideoView? = null

    init {
        indicator.visibility = View.GONE
        banner.removeOnPageChangeListener(this)
        banner.addOnPageChangeListener(this)
        banner.setAutoLoop(false)
        banner.setCanLoop(false)
        banner.setOnCreateBannerItemView(object :
            Banner2View.OnCreateBannerItemView<CenterCommodity.MainImg> {
            /**
             * 新建视图
             */
            override fun createView(
                context: Context?,
                container: ViewGroup?,
                position: Int
            ): View? {
                val realIndex = if (list.size > 0) position % list.size else 0
                val t: CenterCommodity.MainImg = list[realIndex]
                if (t.isVideo) {
                    commodityVideoView = context?.let { CommodityVideoView(it) }
                    return commodityVideoView
                }
                val adImageView = AdImageView(context)
                adImageView.scaleType = ImageView.ScaleType.CENTER_CROP
                return adImageView
            }

            /**
             * 绑定数据
             */
            override fun bindView(
                itemView: View?,
                position: Int,
                t: CenterCommodity.MainImg?
            ) {
                if (itemView is CommodityVideoView) {
                    itemView.setData(t)
                } else {
                    if (itemView is AdImageView) {
                        itemView.createImageLoad(t?.mainImg).load()
                    }
                }
            }

        })
        indicator.tag = 0
    }

    override fun bindTo(bean: Any?, position: Int) {
        super.bindTo(bean, position)
        var tempList: ArrayList<CenterCommodity.MainImg>? = null
        mCenterCommodity?.lmCommodityAsDetail?.apply {
            tempList = arrayListOf<CenterCommodity.MainImg>()
            if ((rotationImages?.size ?: 0) > 0) {
                tempList?.addAll(rotationImages)
            }
            if (!TextUtils.isEmpty(videoUrl)) {
                val mainImg = CenterCommodity.MainImg(videoUrl, true)
                mainImg.mainImg = commodityDetailImage
                tempList?.add(0, mainImg)
            }
        }
        if (tempList == null || list == tempList) {
            return
        }
        tempList?.apply {
            list.clear()
            list.addAll(this)
            banner.setNewList(list)
            vwdot.setListCount(list.size)
            indicator.tag = list.size
            indicator.visibility = if (list.size > 1) View.VISIBLE else View.INVISIBLE
            indicator.text = "1/" + list.size
        }
        mCenterCommodity?.promotionActivity?.apply {
            if (activityInfoList.size > 0 && activityInfoList[0].promotionType == 5) {
                indicator.visibility = View.GONE
                vwdot.visibility = View.VISIBLE
            }else{
                indicator.visibility = View.VISIBLE
                vwdot.visibility = View.GONE
            }
        }
    }


    override fun onPageScrolled(
        position: Int,
        positionOffset: Float,
        positionOffsetPixels: Int
    ) {
    }

    override fun onPageSelected(position: Int) {
        val size: Int = if (indicator.tag == null) 1 else indicator.tag as Int
        if (size > 1) {
            indicator.text = ((position) + 1).toString() + "/" + size
        }
        if (position != 0) {
            commodityVideoView?.pause()
        }

        vwdot.moveCount(position)
    }


    override fun onPageScrollStateChanged(state: Int) {

    }

}