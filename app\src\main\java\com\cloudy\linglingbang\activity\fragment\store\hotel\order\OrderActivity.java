package com.cloudy.linglingbang.activity.fragment.store.hotel.order;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.fragment.mynew.util.SelfUserInfoLoader;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ModelUtils;
import com.cloudy.linglingbang.app.util.RegUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.widget.ClearEditText;
import com.cloudy.linglingbang.app.widget.dialog.hotel.HotelRoomDialog;
import com.cloudy.linglingbang.app.widget.item.CommonItem;
import com.cloudy.linglingbang.app.widget.textview.PressEffectiveCompoundButton;
import com.cloudy.linglingbang.model.hotel.HotelRoom;
import com.cloudy.linglingbang.model.hotel.OrderInsertInfo;
import com.cloudy.linglingbang.model.hotel.RoomNumInfo;
import com.cloudy.linglingbang.model.hotel.TimeInfo;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.user.User;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import butterknife.OnClick;

/**
 * 锦江之星下订页面
 *
 * <AUTHOR>
 * @date 2022/3/17
 */
public class OrderActivity extends BaseActivity {

    @BindView(R.id.tv_hotel_name)
    TextView mTvHotelName;
    @BindView(R.id.tv_room_style)
    TextView mTvRoomStyle;
    @BindView(R.id.tv_in_room_data)
    TextView mTvInRoomData;
    @BindView(R.id.tv_pay_price)
    TextView mTvPayPrice;
    @BindView(R.id.tv_room_num)
    TextView mTvRoomNum;
    @BindView(R.id.tv_room_person_num)
    TextView mTvRoomPersonNum;
    @BindView(R.id.tv_in_time)
    TextView mTvInTime;
    @BindView(R.id.item_order_total_price)
    CommonItem mItemOrderTotalPrice;
    @BindView(R.id.item_order_pay_price)
    CommonItem mItemOrderPayPrice;

    @BindView(R.id.et_phone_num)
    ClearEditText mEtPhoneNum;
    @BindView(R.id.et_person_name)
    ClearEditText mEtPersonName;
    @BindView(R.id.rv_room_num)
    RecyclerView mRvRoomNum;
    @BindView(R.id.iv_select_room_num)
    ImageView mIvSelectRoomNum;

    private String innId;
    private String roomTypeCode;
    private String productCode;
    private String roomTypeName;
    private String innName;
    private long dtArrorig;
    private long dtDeporig;
    private int maxCheckIn;

    private int roomCount = 1;
    private HotelRoom mHotelRoom;
    private int dayNum;
    //预计到店的日期
    private long inHotelDate;

    private int quota;

    private final List<RoomNumInfo> roomInfoList = new ArrayList<>();
    private final List<PressEffectiveCompoundButton> mPressEffectiveCompoundButtonList = new ArrayList<>();

    private final List<TimeInfo> timeList = new ArrayList<>();

    private String checkTimeStr;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_order);
    }

    @OnClick({R.id.iv_select_room_num, R.id.ll_hotel_detail, R.id.rl_in_hotel_time})
    void onClicks(View view) {
        switch (view.getId()) {
            case R.id.iv_select_room_num:

                mRvRoomNum.setVisibility(mRvRoomNum.getVisibility() == View.VISIBLE ? View.GONE : View.VISIBLE);
                if (mRvRoomNum.getVisibility() == View.VISIBLE) {
                    mIvSelectRoomNum.setImageResource(R.drawable.ic_order_select_room_num_top);
                } else {
                    mIvSelectRoomNum.setImageResource(R.drawable.ic_order_select_room_num);
                }
                break;
            case R.id.ll_hotel_detail:
                HotelRoomDialog.Utils mUtils = null;
                if (mUtils == null) {
                    mUtils = new HotelRoomDialog.Utils();
                }
                if (mHotelRoom != null) {
                    mUtils.showDialog(this,
                            mHotelRoom.getInnId(),
                            mHotelRoom.getRoomTypeCode(),
                            productCode,
                            dtArrorig, dtDeporig);
                }

                break;

            case R.id.rl_in_hotel_time:
                SelectTimeDialog selectTimeDialog = new SelectTimeDialog(this, timeList);
                selectTimeDialog.show();
                selectTimeDialog.setChooseListener(new SelectTimeDialog.OnChooseListener() {
                    @Override
                    public void chooseTime(int index) {
                        Date date = new Date(dtArrorig);
                        Calendar c = Calendar.getInstance();
                        c.setTime(date);
                        c.add(Calendar.DAY_OF_MONTH, 1);// 今天+1天
                        Date tomorrow = c.getTime();
                        String str;
                        if (index <= 8) {
                            str = AppUtil.formatDate(dtArrorig, "yyyy-MM-dd") + " " + timeList.get(index).getTime();
                        } else {
                            str = AppUtil.formatDate(tomorrow.getTime(), "yyyy-MM-dd") + " " + timeList.get(index).getTime().replace("次日", "");
                        }
                        inHotelDate = AppUtil.parseDate(str + ":00", "yyyy-MM-dd HH:mm:ss");
                        mTvInTime.setText(AppUtil.formatDate(inHotelDate, "MM月dd日 HH:mm"));
                    }
                });
                break;
        }
    }

    public static void startActivity(Context context, String innId, String roomTypeCode, String productCode, long dtArrorig, long dtDeporig, String innName, String roomTypeName, int maxCheckIn) {
        Intent intent = new Intent(context, OrderActivity.class);
        intent.putExtra("innId", innId);
        intent.putExtra("roomTypeCode", roomTypeCode);
        intent.putExtra("productCode", productCode);
        intent.putExtra("dtArrorig", dtArrorig);
        intent.putExtra("dtDeporig", dtDeporig);
        intent.putExtra("innName", innName);
        intent.putExtra("roomTypeName", roomTypeName);
        intent.putExtra("maxCheckIn", maxCheckIn);
        context.startActivity(intent);
    }

    @Override
    protected void initialize() {
        mEtPhoneNum.setText(User.getsUserInstance().getMobile());
        mRvRoomNum.setLayoutManager(new LinearLayoutManager(this, RecyclerView.HORIZONTAL, false));
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(AppUtil.getServerCurrentTime());
        int hour = calendar.get(Calendar.HOUR_OF_DAY);//获取小时

        String[] timeArray = getResources().getStringArray(R.array.select_time_list);
        for (int i = 0; i < timeArray.length; i++) {
            TimeInfo timeInfo = new TimeInfo();
            if (hour < 14) {
                timeInfo.setEnable(1);
            } else {
                if (i <= hour - 14) {
                    timeInfo.setEnable(2);
                } else {
                    timeInfo.setEnable(1);
                }
            }
            if (hour < 19) {
                if (i == 5) {
                    checkTimeStr = timeArray[i].replace("次日", "");
                    timeInfo.setCheckStatus(1);
                } else {
                    timeInfo.setCheckStatus(2);
                }
            } else {
                if (i == hour - 14 + 1) {
                    checkTimeStr = timeArray[i].replace("次日", "");
                    timeInfo.setCheckStatus(1);
                } else {
                    timeInfo.setCheckStatus(2);
                }
            }

            timeInfo.setTime(timeArray[i]);

            timeList.add(timeInfo);
        }
        innId = getIntent().getStringExtra("innId");
        roomTypeCode = getIntent().getStringExtra("roomTypeCode");
        productCode = getIntent().getStringExtra("productCode");
        dtArrorig = getIntent().getLongExtra("dtArrorig", 0);
        dtDeporig = getIntent().getLongExtra("dtDeporig", 0);
        roomTypeName = getIntent().getStringExtra("roomTypeName");
        innName = getIntent().getStringExtra("innName");
        maxCheckIn = getIntent().getIntExtra("maxCheckIn", 1);
        mTvHotelName.setText(innName);
        mTvRoomStyle.setText(roomTypeName);
        dayNum = (int) ((dtDeporig - dtArrorig) / AppUtil.ONEDAY);
        mTvInRoomData.setText(AppUtil.formatDate(dtArrorig, "MM月dd日") + "-" + AppUtil.formatDate(dtDeporig, "MM月dd日") + " 共" + dayNum + "晚");
        String str = AppUtil.formatDate(dtArrorig, "yyyy-MM-dd") + " " + checkTimeStr;
        inHotelDate = AppUtil.parseDate(str + ":00", "yyyy-MM-dd HH:mm:ss");
        mTvInTime.setText(AppUtil.formatDate(inHotelDate, "MM月dd日 HH:mm"));
        //请求房型信息
        requestRoomInfo();
        mTvRoomNum.setText(getResources().getString(R.string.room_num, 1));
        mTvRoomPersonNum.setText(getResources().getString(R.string.room_person_num, maxCheckIn));

    }

    private void requestRoomInfo() {
        Map<String, Object> map = new HashMap<>();
        map.put("innId", innId);
        map.put("roomTypeCode", roomTypeCode);
        map.put("productCode", productCode);
        map.put("dtArrorig", AppUtil.formatDate(dtArrorig));
        map.put("dtDeporig", AppUtil.formatDate(dtDeporig));
        map.put("roomCount", roomCount);
        L00bangRequestManager2.getServiceInstance()
                .hotelRealRoomStatus(map)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<HotelRoom>(this) {
                    @Override
                    public void onSuccess(HotelRoom hotelRoom) {
                        super.onSuccess(hotelRoom);
                        mHotelRoom = hotelRoom;
                        if (hotelRoom != null && hotelRoom.getQuota() > 1) {
                            mIvSelectRoomNum.setVisibility(View.VISIBLE);
                            if (quota != hotelRoom.getQuota()) {
                                quota = hotelRoom.getQuota();
                                roomInfoList.clear();
                                for (int i = 0; i < hotelRoom.getQuota(); i++) {
                                    RoomNumInfo roomNumInfo = new RoomNumInfo();
                                    roomNumInfo.setRoomNum(i + 1);
                                    roomNumInfo.setCheckStatus(i == 0 ? 1 : 2);
                                    roomInfoList.add(roomNumInfo);
                                }
                                updateRoomNum(roomInfoList);
                            }

                        } else {
                            mIvSelectRoomNum.setVisibility(View.GONE);
                        }
                        if (hotelRoom != null) {
                            //实际房价
                            mItemOrderTotalPrice.getTvRight().setText(ModelUtils.getRmbOrEmptyString(hotelRoom.getRealHousePriceStr(), true));

                            mItemOrderPayPrice.getTvRight().setText(ModelUtils.getRmbOrEmptyString(hotelRoom.getReachStorePriceStr(), true));

                            mTvPayPrice.setText(hotelRoom.getLingValueStr());

                        }
                    }
                });
    }

    private RoomNumAdapter roomNumAdapter;
    private void updateRoomNum(List<RoomNumInfo> roomInfoList) {
        roomNumAdapter = new RoomNumAdapter(this, roomInfoList);
        roomNumAdapter.setChooseRoomNumListener(new RoomNumAdapter.ChooseRoomNumListener() {
            @Override
            public void chooseNum(int position) {
                notifyFlowLayout(position);
            }
        });
        mRvRoomNum.setAdapter(roomNumAdapter);
    }

    private void notifyFlowLayout(int position) {
        //房间数量
        roomCount = position + 1;
        requestRoomInfo();

        mTvRoomNum.setText(getResources().getString(R.string.room_num, position + 1));
        mTvRoomPersonNum.setText(getResources().getString(R.string.room_person_num, maxCheckIn * (position + 1)));

        for (int i = 0; i < roomInfoList.size(); i++) {
            roomInfoList.get(i).setCheckStatus(2);
        }
        roomInfoList.get(position).setCheckStatus(1);

        roomNumAdapter.notifyDataSetChanged();

    }

    @OnClick(R.id.tv_submit)
    void clickSubmit() {
        if (mHotelRoom == null) {
            return;
        }
        //校验入住人的准确性
        if (TextUtils.isEmpty(mEtPersonName.getText().toString().trim())) {
            ToastUtil.showMessage(getApplicationContext(), "请填写正确的姓名");
            return;
        }

        //校验手机号的准确性
        if (TextUtils.isEmpty(mEtPhoneNum.getText().toString()) || !RegUtil.checkPhone(mEtPhoneNum.getText().toString())) {
            ToastUtil.showMessage(getApplicationContext(), "请填写正确的联系电话");
            return;
        }

        //校验ling值是否足够
        int ling = SelfUserInfoLoader.getInstance().getUserBalanceInfo().getLingCurrency();

        if (mHotelRoom.getLingValue() > ling) {
            ToastUtil.showMessage(getApplicationContext(), "ling值不足");
            return;
        }

        OrderInsertInfo orderInsertInfo = new OrderInsertInfo();
        orderInsertInfo.setRoomCount(roomCount);
        orderInsertInfo.setRoomTypeCode(mHotelRoom.getRoomTypeCode());
        orderInsertInfo.setDtDeporig(AppUtil.formatDate(dtDeporig));
        orderInsertInfo.setGuestMobile(mEtPhoneNum.getText().toString());
        orderInsertInfo.setDtArrorig(AppUtil.formatDate(dtArrorig));
        orderInsertInfo.setGuestName(mEtPersonName.getText().toString());
        orderInsertInfo.setLingValue(mHotelRoom.getLingValue());
        orderInsertInfo.setProductCode(productCode);
        orderInsertInfo.setPayType(2);
        orderInsertInfo.setOrderPrice(mHotelRoom.getRealHousePrice());
        orderInsertInfo.setEstimateDtArrorig(AppUtil.formatDate(inHotelDate, "yyyy-MM-dd HH:mm:ss"));
        orderInsertInfo.setReachStorePrice(mHotelRoom.getReachStorePrice());
        orderInsertInfo.setInnId(mHotelRoom.getInnId());

        L00bangRequestManager2.getServiceInstance()
                .addHotelOrder(orderInsertInfo)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<Boolean>(this) {
                    @Override
                    public void onSuccess(Boolean b) {
                        super.onSuccess(b);
                        ToastUtil.showMessage(OrderActivity.this, "预订成功");
                        IntentUtils.startActivity(OrderActivity.this, MyOrderActivity.class);
                        finish();
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                        requestRoomInfo();
                    }
                });

    }


}
