package com.cloudy.linglingbang.activity.community.common;

import android.content.Context;

import com.cloudy.linglingbang.app.widget.recycler.header.HeaderAndFooterWrapperAdapter;
import com.cloudy.linglingbang.model.postcard.PostCard;

import java.util.List;

/**
 * 如果需要用到 header 需要特殊处理回调事件
 * 不用 header 或没有操作（没有回调）则不需用
 * <p>
 * 新建 HeaderAndFooterWrapperAdapter
 * 然后 new HeaderWrapperPostAdapter
 * 最后 HeaderAndFooterWrapperAdapter.setInnerAdapter
 * <p>
 * 如
 * <p>
 * mWrapperAdapter = new HeaderAndFooterWrapperAdapter();
 * mWrapperAdapter.setInnerAdapter(new CommunitySquarePostAdapter(getContext(), list, mWrapperAdapter));
 *
 * <AUTHOR>
 * @date 2018/6/26
 */
public class HeaderWrapperPostAdapter extends BasePostAdapter {

    private HeaderAndFooterWrapperAdapter mOuterAdapter;

    public HeaderWrapperPostAdapter(Context context, List<PostCard> data, HeaderAndFooterWrapperAdapter outerAdapter) {
        super(context, data);
        mOuterAdapter = outerAdapter;
    }

    @Override
    protected void onItemRemoved(int position) {
        super.onItemRemoved(position);
        mOuterAdapter.notifyItemRemoved(mOuterAdapter.getHeadersCount() + position);
    }

    @Override
    protected void onItemChanged(int position) {
        super.onItemChanged(position);
        mOuterAdapter.notifyItemChanged(mOuterAdapter.getHeadersCount() + position);
    }

}
