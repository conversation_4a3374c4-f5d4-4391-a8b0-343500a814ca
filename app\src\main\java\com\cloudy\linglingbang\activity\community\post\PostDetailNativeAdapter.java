package com.cloudy.linglingbang.activity.community.post;

import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.format.DateFormat;
import android.text.method.LinkMovementMethod;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ClickableSpan;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.ViewStub;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.aliyun.player.source.VidSts;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.target.Target;
import com.cloudy.aliyunshortvideo.JniLibUtil;
import com.cloudy.aliyunshortvideo.widget.AliyunVodPlayerView;
import com.cloudy.linglingbang.ApplicationLLB;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.ScanImageActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.community.common.holder.BasePostChildViewHolder;
import com.cloudy.linglingbang.activity.community.common.holder.PostIconEnum;
import com.cloudy.linglingbang.activity.community.post.detail.PostDetailBottomInfoViewHolder;
import com.cloudy.linglingbang.activity.community.post.detail.PostDetailLabelsViewHolder;
import com.cloudy.linglingbang.activity.community.post.detail.PostTagCommodityViewHolder;
import com.cloudy.linglingbang.activity.shortvideo.ShortVideoUtil;
import com.cloudy.linglingbang.adapter.newcommunity.PostAuthorInfoHolderWithAttention;
import com.cloudy.linglingbang.app.imageConfig.GlideApp;
import com.cloudy.linglingbang.app.imageConfig.RoundedCornersTransformation;
import com.cloudy.linglingbang.app.log.LLBTextUtils;
import com.cloudy.linglingbang.app.util.DeviceUtil;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.NetworkUtil;
import com.cloudy.linglingbang.app.util.NotchScreenUtils;
import com.cloudy.linglingbang.app.util.ObjectUtils;
import com.cloudy.linglingbang.app.util.StatusBarUtils;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.ViewHolderUtils;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.BaseOnClickListener;
import com.cloudy.linglingbang.app.widget.ExpressionTextView;
import com.cloudy.linglingbang.app.widget.FlowLayout;
import com.cloudy.linglingbang.app.widget.banner.AdImageView;
import com.cloudy.linglingbang.app.widget.banner.AdRoundImageView;
import com.cloudy.linglingbang.app.widget.banner.HomeIndicatorBelowBanner;
import com.cloudy.linglingbang.app.widget.dialog.DialogUtil;
import com.cloudy.linglingbang.app.widget.image.ImageLoadUtils;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.app.widget.tag.RandomDragTagLayout;
import com.cloudy.linglingbang.app.widget.textview.PressEffectiveButton;
import com.cloudy.linglingbang.model.Comment;
import com.cloudy.linglingbang.model.SourceModel;
import com.cloudy.linglingbang.model.postcard.CommentTitle;
import com.cloudy.linglingbang.model.postcard.ExperiencePostCar;
import com.cloudy.linglingbang.model.postcard.ExperiencePostCarType;
import com.cloudy.linglingbang.model.postcard.ExperiencePostLabel;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.postcard.PostCardItem;
import com.cloudy.linglingbang.model.postcard.PostCommodity;
import com.cloudy.linglingbang.model.server.Ad.AdJumpUtil2;
import com.cloudy.linglingbang.model.shortvideo.AliSTSTokenInfo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**
 * 帖子详情原生的adapter
 *
 * <AUTHOR> create at 2016/10/18 17:33
 */
class PostDetailNativeAdapter extends BaseRecyclerViewAdapter<Comment> {

    class VIEW_TYPE {
        /**
         * 评论
         */
        public static final int TYPE_COMMENT = 0;
        /**
         * 帖子作者
         */
        public static final int TYPE_AUTHOR = 1;
        /**
         * 标题和内容
         */
        public static final int TYPE_CONTENT = 2;
        /**
         * 内容
         */
        public static final int TYPE_BASE_CONTENT = 3;
        /**
         * 帖子底部展示的部分信息
         */
        public static final int TYPE_BOTTOM = 4;
        /**
         * 提车作业帖的顶部
         */
        public static final int TYPE_CONTENT_EXPERIENCE = 5;
        /**
         * 置顶回复的标题
         */
        public static final int TYPE_TOP_COMMENT_TITLE = 6;
        /**
         * 全部回复的标题,包括加载部分
         */
        public static final int TYPE_ALL_COMMENT_TITLE = 7;

        /**
         * 短视频帖子
         */
        public static final int TYPE_CONTENT_SHORT_VIDEO = 9;
        /**
         * 封面
         */
        public static final int TYPE_COVER = 10;
        /**
         * 菱感贴
         */
        public static final int TYPE_CONTENT_LING_SENSE = 11;
        /**
         * 相关推荐
         */
        public static final int TYPE_POST_RECOMMEND = 12;
    }

    private PostCard mPostCard;
    private List<PostCard> mRecommendPostList;
    /**
     * 正在加载评论
     */
    private boolean mLoadingComment;
    /**
     * 是否加载所有评论
     */
    private boolean mLoadAllComments = true;

    public void setPostCard(PostCard postCard) {
        mPostCard = postCard;
    }

    public void setRecommendPostList(List<PostCard> recommendPostList) {
        mRecommendPostList = recommendPostList;
    }

    public PostDetailNativeAdapter(Context context, List<Comment> data, PostCard postCard) {
        super(context, data);
        mPostCard = postCard;
    }

    public PostCard getPostCard() {
        return mPostCard;
    }

    public boolean isLoadingComment() {
        return mLoadingComment;
    }

    public void setLoadingComment(boolean loadingComment) {
        mLoadingComment = loadingComment;
    }

    public boolean isLoadAllComments() {
        return mLoadAllComments;
    }

    public void setLoadAllComments(boolean loadAllComments) {
        mLoadAllComments = loadAllComments;
    }

    @Override
    protected BaseRecyclerViewHolder<Comment> createViewHolder(View itemView) {
        return null;
    }

    @Override
    protected BaseRecyclerViewHolder<Comment> createViewHolderWithViewType(View itemView, int viewType) {
        if (viewType == VIEW_TYPE.TYPE_AUTHOR) {
            return new PostDetailAuthorViewHolder(itemView);
        } else if (viewType == VIEW_TYPE.TYPE_CONTENT) {
            return new PostDetailContentViewHolder(itemView);
        } else if (viewType == VIEW_TYPE.TYPE_CONTENT_EXPERIENCE) {
            return new PostDetailContentExperienceViewHolder(itemView);
        } else if (viewType == VIEW_TYPE.TYPE_CONTENT_SHORT_VIDEO) {
            return new PostDetailContentShortVideoViewHolder(itemView);
        } else if (viewType == VIEW_TYPE.TYPE_BASE_CONTENT) {
            return new PostDetailBaseContentViewHolder(itemView);
        } else if (viewType == VIEW_TYPE.TYPE_BOTTOM) {
            return new PostDetailBottomViewHolder(itemView);
        } else if (viewType == VIEW_TYPE.TYPE_TOP_COMMENT_TITLE) {
            return new PostDetailTopCommentTitleViewHolder(itemView);
        } else if (viewType == VIEW_TYPE.TYPE_ALL_COMMENT_TITLE) {
            if (mPostCard != null) {
                /*
                这里只根据 postCard 判断，如果要加上 Activity 中的是否加载所有评论
                则 ViewHolder 可能复用，需要区分类型
                 */
                if (mPostCard.showSeeAuthorOnly()) {
                    return new OfficialCoursePostDetailAllCommentTitleViewHolder(itemView);
                }
            }
            return new PostDetailAllCommentTitleViewHolder(itemView);
        } else if (viewType == VIEW_TYPE.TYPE_COVER) {
            return new TopCoverViewHolder(itemView);
        } else if (viewType == VIEW_TYPE.TYPE_CONTENT_LING_SENSE) {
            return new ContentSenseViewHolder(itemView);
        } else if (viewType == VIEW_TYPE.TYPE_POST_RECOMMEND) {
            return new RecommendViewHolder(itemView);
        } else {
            return new PostDetailCommentViewHolder(this, itemView);
        }
    }

    @Override
    protected int getItemLayoutRes(int viewType) {
        if (viewType == VIEW_TYPE.TYPE_AUTHOR) {
            return R.layout.item_post_detail_author;
        } else if (viewType == VIEW_TYPE.TYPE_CONTENT) {
            return R.layout.item_post_detail_content;
        } else if (viewType == VIEW_TYPE.TYPE_CONTENT_EXPERIENCE) {
            return R.layout.item_post_detail_content_experience;
        } else if (viewType == VIEW_TYPE.TYPE_CONTENT_SHORT_VIDEO) {
            return R.layout.item_post_detail_content_short_video;
        } else if (viewType == VIEW_TYPE.TYPE_BASE_CONTENT) {
            return R.layout.item_post_detail_base_content;
        } else if (viewType == VIEW_TYPE.TYPE_BOTTOM) {
            return R.layout.item_post_detail_bottom;
        } else if (viewType == VIEW_TYPE.TYPE_TOP_COMMENT_TITLE) {
            return R.layout.item_post_detail_comment_title;
        } else if (viewType == VIEW_TYPE.TYPE_ALL_COMMENT_TITLE) {
            return R.layout.item_post_detail_all_comments_title;
        } else if (viewType == VIEW_TYPE.TYPE_COVER) {
            return R.layout.item_post_detail_cover;
        } else if (viewType == VIEW_TYPE.TYPE_CONTENT_LING_SENSE) {
            return R.layout.item_post_detail_content_ling_sense;
        } else if (viewType == VIEW_TYPE.TYPE_POST_RECOMMEND) {
            return R.layout.item_post_detail_recommend;
        } else {
            return R.layout.item_comment;
        }
    }

    @Override
    public int getItemViewType(int position) {
        //data 提前以 position 获取
        Object data = mData.get(position);
        //只要有CoverImage的都展示顶部封图
        if (!TextUtils.isEmpty(mPostCard.getCoverImage())) {
            //如果有封面
            if (position == 0) {
                return VIEW_TYPE.TYPE_COVER;
            } else {
                // position 减 1，后面的逻辑是以 0 开始的判断，但是 data 要从 position 获取
                position -= 1;
            }
        }
        int size = 0;
        List<PostCardItem> imgTexts = mPostCard.getImgTexts();
        if (imgTexts != null) {
            size = imgTexts.size();
        }
        if (size > 0 && mPostCard.getPostTypeIdOrNegative() == PostCard.PostType.LING_SENSE) {
            int imgCount = CommunityUtils.getLingSensePostImageCount(mPostCard, null);
            if (imgCount > 0) {
                size -= imgCount;
                if (size == 0) {
                    size++;
                }
                size++;
                if (position == 1) {
                    return VIEW_TYPE.TYPE_CONTENT_LING_SENSE;
                } else if (position == 2) {
                    return VIEW_TYPE.TYPE_CONTENT;
                }
            }
        }
        if (position == 0) {
            return VIEW_TYPE.TYPE_AUTHOR;
        } else if (position == 1) {
            switch (mPostCard.getPostTypeIdOrNegative()) {
                case PostCard.PostType.CAR_BUYING_EXPERIENCE:
                    return VIEW_TYPE.TYPE_CONTENT_EXPERIENCE;
                case PostCard.PostType.SHORT_VIDEO:
                    return VIEW_TYPE.TYPE_CONTENT_SHORT_VIDEO;
                default:
                    return VIEW_TYPE.TYPE_CONTENT;
            }
        } else if (position <= size) {
            return VIEW_TYPE.TYPE_BASE_CONTENT;
        } else if (position == size + 1) {
            return VIEW_TYPE.TYPE_BOTTOM;
        } else if (position == size + 2 && (mRecommendPostList != null && !mRecommendPostList.isEmpty())) {
            return VIEW_TYPE.TYPE_POST_RECOMMEND;
        } else if (data instanceof CommentTitle) {
            if (((CommentTitle) data).isFromTop()) {
                return VIEW_TYPE.TYPE_TOP_COMMENT_TITLE;
            } else {
                return VIEW_TYPE.TYPE_ALL_COMMENT_TITLE;
            }
        } else {
            return VIEW_TYPE.TYPE_COMMENT;
        }
    }

    public void onClickImage(int position) {
        if (mPostCard.getImgTexts().get(position).getLinkTypeOrZero() > 0) {
            AdJumpUtil2.goToActivityForWeb(mContext, mPostCard.getImgTexts().get(position).getLinkTypeOrZero(), mPostCard.getImgTexts().get(position).getLinkUrl(), new SourceModel(SourceModel.POSITION_TYPE.POST_OPERATION, mPostCard.getPostId()));
        } else {
            JumpPageUtil.goImageDetailForPost(mContext, mPostCard.getImgTexts(), position, null);
        }
        //添加埋点
        SensorsUtils.sensorsClickBtn("点击banner图", "官方帖子详情(" + mPostCard.getPostId() + ")", "图片");
    }

    public void onLongClickText(int position) {
        if (mPostCard.getImgTexts() != null && position > -1 && position < mPostCard.getImgTexts().size()) {
            final String copyText = mPostCard.getImgTexts().get(position).getText();
            if (!TextUtils.isEmpty(copyText)) {
                DialogUtil.showCopyTextDialog(mContext, copyText);
            }
        }
    }

    private void goToImageDetail(String[] urls, int index) {
        // 点击轮播图，查看大图
        Intent in = new Intent(mContext, ScanImageActivity.class);
        in.putExtra("image_urls", urls);
        in.putExtra("image_index", index);
        mContext.startActivity(in);
    }

    /**
     * 帖子作者
     */
    class PostDetailAuthorViewHolder extends BaseRecyclerViewHolder<Comment> {

        //private PostAuthorViewHolder mPostAuthorViewHolder;
        private PostAuthorInfoHolderWithAttention mPostAuthorInfoHolder;

        public PostDetailAuthorViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            //mPostAuthorViewHolder = new com.cloudy.linglingbang.activity.community.post.holder.PostDetailAuthorViewHolder(itemView);
            mPostAuthorInfoHolder = new PostAuthorInfoHolderWithAttention(itemView);
        }

        @Override
        public void bindTo(Comment comment, int position) {
            super.bindTo(comment, position);
            mPostAuthorInfoHolder.bindTo(mPostCard);
        }
    }

    /**
     * 显示第一条，以及精品、提问等
     */
    class PostDetailContentViewHolder extends PostDetailBaseContentViewHolder {

        private TextView mTvPostTitle;
        /**
         * 技师帖问题类型
         */
        private TextView mTvQuestionType;

        /**
         * 好货卡片
         */
        private ExpressionTextView mTvGoodsOwnerName;
        private TextView mTvAddWx;
        private TextView mTvCall;
        private TextView mTvGoodsName;
        private TextView mTvGoodsStock;
        private TextView mTvGoodsArea;
        private AdRoundImageView mIvGoodsImg;
        private View mLlGoodsCard;
        private LinearLayout mLLGoodsName, mLLGoodsNum, mLLGoodsArea;

        public PostDetailContentViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            ButterKnife.bind(this, itemView);
            mTvPostTitle = itemView.findViewById(R.id.tv_post_title);

            mTvGoodsOwnerName = itemView.findViewById(R.id.tv_goods_owner_name);
            mTvCall = itemView.findViewById(R.id.tv_call);
            mTvAddWx = itemView.findViewById(R.id.tv_add_wx);
            mTvGoodsName = itemView.findViewById(R.id.tv_goods_name);
            mTvGoodsStock = itemView.findViewById(R.id.tv_goods_stock);
            mTvGoodsArea = itemView.findViewById(R.id.tv_goods_area);
            mIvGoodsImg = itemView.findViewById(R.id.iv_goods_img);
            mLlGoodsCard = itemView.findViewById(R.id.ll_goods_card);
            mLLGoodsArea = itemView.findViewById(R.id.ll_goods_area);
            mLLGoodsNum = itemView.findViewById(R.id.ll_goods_num);
            mLLGoodsName = itemView.findViewById(R.id.ll_goods_name);
        }

        @Override
        public void bindTo(Comment comment, int position) {
            super.bindTo(comment, position);
            showTitle();
            showProblemTypeDesc();
            if (mPostCard.getSeries() == 3) {
                mLlGoodsCard.setVisibility(View.VISIBLE);
                showGoodsExchangeCard(mPostCard);
            }
        }

        private void showGoodsExchangeCard(final PostCard postCard) {
            String name;
            if (postCard.getCallingCard() != null) {
                name = !TextUtils.isEmpty(postCard.getCallingCard().getName()) ? postCard.getCallingCard().getName() : postCard.getAuthor().getNickname();
                mTvCall.setVisibility(TextUtils.isEmpty(postCard.getCallingCard().getPhone()) ? View.GONE : View.VISIBLE);
                mTvAddWx.setVisibility(TextUtils.isEmpty(postCard.getCallingCard().getWx()) ? View.GONE : View.VISIBLE);
                ViewHolderUtils.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        SensorsUtils.sensorsClickBtn("加货主微信", "帖子详情页");
                        LLBTextUtils.copyText(mTvAddWx.getContext(), postCard.getCallingCard().getWx(), false);
                        ToastUtil.showMessage(mTvAddWx.getContext(), R.string.toast_copy_wx_success);
                    }
                }, mTvAddWx);
                ViewHolderUtils.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        SensorsUtils.sensorsClickBtn("call货主", "帖子详情页");
                        //打电话
                        IntentUtils.startActivityByDialIntent(mTvCall.getContext(), postCard.getCallingCard().getPhone());
                    }
                }, mTvCall);
            } else {
                name = postCard.getAuthor().getNickname();
                mTvCall.setVisibility(View.GONE);
                mTvAddWx.setVisibility(View.GONE);
            }
            mTvGoodsOwnerName.setText(name);

            if (!TextUtils.isEmpty(postCard.getGoodStuffName())) {
                mLLGoodsName.setVisibility(View.VISIBLE);
                mTvGoodsName.setText(postCard.getGoodStuffName());
            } else {
                mLLGoodsName.setVisibility(View.GONE);
            }
            if (!TextUtils.isEmpty(postCard.getGoodNum())) {
                mLLGoodsNum.setVisibility(View.VISIBLE);
                mTvGoodsStock.setText(postCard.getGoodNum());
            } else {
                mLLGoodsNum.setVisibility(View.GONE);
            }
            if (!TextUtils.isEmpty(postCard.getGoodArea())) {
                mLLGoodsArea.setVisibility(View.VISIBLE);
                mTvGoodsArea.setText(postCard.getGoodArea());
            } else {
                mLLGoodsArea.setVisibility(View.GONE);
            }

            if (postCard.getCoverImageText() != null && postCard.getCoverImageText().getImg() != null) {
                new ImageLoad(mIvGoodsImg, postCard.getCoverImageText().getImg())
                        .setPlaceholder(R.drawable.ic_common_place_holder_corner_10)
                        .setTransformations(new CenterCrop(), new RoundedCornersTransformation(4))
                        .setDoNotAnimate()
                        .load();
            }
        }

        public void showTitle() {
            //标题，可能为空
            CharSequence title = mPostCard.getPostTitle();

            //技师帖设置标题
            int postTypeInt = mPostCard.getPostTypeIdOrNegative();
            if (postTypeInt == PostCard.PostType.ASK_TECHNICIAN) {
                //显示问题所属车型，只有我问技师才显示
                if (!TextUtils.isEmpty(mPostCard.getCarTypeStr())) {
                    //不为空显示车型
                    //因为只有一个帖子，没有复用可能
                    title = mContext.getString(R.string.post_detail_question_car_type, mPostCard.getCarTypeStr());
                    //无效
                    //mTvPostTitle.getPaint().setFakeBoldText(false);
                    mTvPostTitle.setTypeface(null, Typeface.NORMAL);
                    mTvPostTitle.setTextColor(mContext.getResources().getColor(R.color.orange_ff7000));
                }
            }
            //拼上话题
            //title = com.cloudy.linglingbang.activity.community.common.holder.PostContentViewHolder.addTopicInfo(mPostCard, title, mTvPostTitle, null);
            //拼上图标
            title = com.cloudy.linglingbang.activity.community.common.holder.PostContentViewHolder.addIconInfo(mPostCard, title, PostIconEnum.addDefaultShowIconInfo(0), mTvPostTitle);
            if (TextUtils.isEmpty(title)) {
                mTvPostTitle.setVisibility(View.GONE);
            } else {
                mTvPostTitle.setVisibility(View.VISIBLE);
                mTvPostTitle.setText(title);
            }
        }

        /**
         * 显示技师帖问题类型
         */
        private void showProblemTypeDesc() {
            if (mPostCard.getPostTypeIdOrNegative() == PostCard.PostType.ASK_TECHNICIAN) {
                initQuestionTypeTextView();
                if (mTvQuestionType == null) {
                    return;
                }
                ViewHolderUtils.setTextAndVisibilityDependsOnText(mTvQuestionType, mPostCard.getProblemTypeDesc());
            }
        }

        /**
         * 初始化问题类型 View
         */
        private void initQuestionTypeTextView() {
            if (mTvQuestionType != null) {
                //已经存在
                return;
            }
            //先查找
            View view = itemView.findViewById(R.id.tv_question_type);
            if (view instanceof TextView) {
                mTvQuestionType = (TextView) view;
                return;
            }
            //未找到，加载
            ViewStub viewStub = itemView.findViewById(R.id.view_stub_question_type);
            if (viewStub != null) {
                view = viewStub.inflate();
                if (view instanceof TextView) {
                    mTvQuestionType = (TextView) view;
                }
            }
        }
    }

    /**
     * 短视频帖的顶部
     */
    class PostDetailContentShortVideoViewHolder extends PostDetailContentViewHolder {
        @Nullable
        @BindView(R.id.rl_wifi_hint)
        RelativeLayout mRlWifiHint;
        @Nullable
        @BindView(R.id.btn_play_continue)
        PressEffectiveButton btn_play_continue;
        //是否自动播放
        private boolean mIsAutoPlay;

        public PostDetailContentShortVideoViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void setWidthHeight() {
            //布局中设置，此处设置为空，不执行任何操作，不能删除该方法
        }

        @Override
        protected void loadImage(PostCardItem postCardItem) {
            if (ivPicture instanceof ImageView) {
                new ImageLoad((ImageView) ivPicture, postCardItem.getImg())
//                        .setPlaceholder(R.drawable.post_pic)
                        .setPlaceholder(R.drawable.ic_common_place_holder)
                        .setDoNotAnimate()
                        .load();
            }
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            setAutoPlay(true);
        }

        @Override
        protected void setFullScreenOrientation() {
            //设置全屏时的方向为竖屏
            playerView.setFullScreenOrientation(AliyunVodPlayerView.FULL_ORIENTATION.SCREEN_PORTRAIT);
        }

        @Override
        protected String getContentText() {
            return mContext.getString(R.string.short_video_post_intro, postCardItem.getText());
        }

        @Override
        public void bindTo(Comment comment, int position) {
            super.bindTo(comment, position);
        }

        @OnClick(R.id.btn_play_continue)
        protected void onPlayClick() {
            initPlayerAndPlay();
        }

        @Override
        protected void setAutoPlay() {
            super.setAutoPlay();
            //设置自动播放
            int visibility;
            if (isAutoPlay() && playerView == null) {
                if (NetworkUtil.getNetWorkStates(mContext) == NetworkUtil.TYPE_WIFI) {
                    //WIFI自动播放
                    visibility = View.GONE;
                    initPlayerAndPlay();
                    //播放之后设置不自动播放，防止下次滑动回来自动播放
                    setAutoPlay(false);
                } else {
                    visibility = View.VISIBLE;
                }
            } else {
                visibility = View.GONE;
            }
            if (mRlWifiHint != null) {
                mRlWifiHint.setVisibility(visibility);
            }
        }

        /**
         * 设置是否自动播放
         */
        public boolean isAutoPlay() {
            return mIsAutoPlay;
        }

        /**
         * 设置是否自动播放
         */
        public void setAutoPlay(boolean autoPlay) {
            mIsAutoPlay = autoPlay;
        }
    }

    /**
     * 提车作业帖的顶部
     */
    class PostDetailContentExperienceViewHolder extends PostDetailContentViewHolder {
        private TextView tv_car_name;
        private TextView tv_buy_car_time;
        private TextView tv_buy_car_shop;
        private FlowLayout flow_layout_purpose;
        private FlowLayout flow_layout_satisfied;
        private FlowLayout flow_layout_improvable;
        private LinearLayout ll_consideration_cars;

        public PostDetailContentExperienceViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            tv_car_name = itemView.findViewById(R.id.tv_car_name);
            tv_buy_car_time = itemView.findViewById(R.id.tv_buy_car_time);
            tv_buy_car_shop = itemView.findViewById(R.id.tv_buy_car_shop);
            flow_layout_purpose = itemView.findViewById(R.id.flow_layout_purpose);
            flow_layout_satisfied = itemView.findViewById(R.id.flow_layout_satisfied);
            flow_layout_improvable = itemView.findViewById(R.id.flow_layout_improvable);
            ll_consideration_cars = itemView.findViewById(R.id.ll_consideration_cars);

        }

        @Override
        public void bindTo(Comment comment, int position) {
            super.bindTo(comment, position);
            if (mPostCard == null) {
                return;
            }
            //车辆信息
            ExperiencePostCar experiencePostCar = mPostCard.getPostCarVo();
            if (experiencePostCar == null) {
                itemView.findViewById(R.id.ll_car_info).setVisibility(View.GONE);
            } else {
                itemView.findViewById(R.id.ll_car_info).setVisibility(View.VISIBLE);
                tv_car_name.setText(mContext.getString(R.string.item_post_my_car, experiencePostCar.getCarTypeName()));
                tv_buy_car_time.setText(DateFormat.format(mContext.getString(R.string.item_post_buy_car_time), experiencePostCar.getPurchaseDateMillis()));
                String buyCarShop = mContext.getString(R.string.item_post_buy_car_shop, experiencePostCar.getShopName());
                SpannableString spannableString = new SpannableString(buyCarShop);
                ClickableSpan span = new ClickableSpan() {
                    @Override
                    public void updateDrawState(TextPaint ds) {
                        //设置颜色
                        ds.setColor(mContext.getResources().getColor(R.color.blue_006cee));
                        ds.setUnderlineText(false);
                    }

                    @Override
                    public void onClick(@NonNull View widget) {
                        ExperiencePostCar experiencePostCar = mPostCard.getPostCarVo();
                        if (experiencePostCar != null) {
                            JumpPageUtil.goDealerDetail(mContext, experiencePostCar.getDealerId());
                        }
                    }
                };
                spannableString.setSpan(span, 6, buyCarShop.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                //必须要加，否则不能点击
                tv_buy_car_shop.setMovementMethod(LinkMovementMethod.getInstance());
                tv_buy_car_shop.setText(spannableString);
            }

            Map<Integer, List<ExperiencePostLabel>> labelMap = mPostCard.getLabelMap();
            if (labelMap != null) {
                LayoutInflater inflater = LayoutInflater.from(mContext);

                //购车目的
                flow_layout_purpose.removeAllViews();
                List<ExperiencePostLabel> purposeLabelList = labelMap.get(ExperiencePostLabel.TYPE_CAR_BUYING_PURPOSE);
                if (purposeLabelList != null && !purposeLabelList.isEmpty()) {
                    for (ExperiencePostLabel postLabel : purposeLabelList) {
                        if (postLabel.getIsSelected() != null && postLabel.getIsSelected() == 1) {
                            Button button = (Button) inflater.inflate(R.layout.item_car_buying_experience_label_blue, flow_layout_purpose, false);
                            button.setText(postLabel.getLabelName());
                            flow_layout_purpose.addView(button);
                        }
                    }
                }

                //满意的地方
                flow_layout_satisfied.removeAllViews();
                List<ExperiencePostLabel> satisfiedLabelList = labelMap.get(ExperiencePostLabel.TYPE_CAR_BUYING_SATISFIED);
                if (satisfiedLabelList != null && !satisfiedLabelList.isEmpty()) {
                    for (ExperiencePostLabel postLabel : satisfiedLabelList) {
                        if (postLabel.getIsSelected() != null && postLabel.getIsSelected() == 1) {
                            Button button = (Button) inflater.inflate(R.layout.item_car_buying_experience_label_orange, flow_layout_satisfied, false);
                            button.setText(postLabel.getLabelName());
                            flow_layout_satisfied.addView(button);
                        }
                    }
                }

                //改进的地方
                flow_layout_improvable.removeAllViews();
                List<ExperiencePostLabel> improvableLabelList = labelMap.get(ExperiencePostLabel.TYPE_CAR_BUYING_IMPROVABLE);
                int improvableCount = 0;
                if (improvableLabelList != null && !improvableLabelList.isEmpty()) {
                    for (ExperiencePostLabel postLabel : improvableLabelList) {
                        if (postLabel.getIsSelected() != null && postLabel.getIsSelected() == 1) {
                            Button button = (Button) inflater.inflate(R.layout.item_car_buying_experience_label_blue, flow_layout_improvable, false);
                            button.setText(postLabel.getLabelName());
                            flow_layout_improvable.addView(button);
                            improvableCount++;
                        }
                    }
                }
                if (improvableCount == 0) {
                    //可选，没有时显示字
                    TextView textView = new TextView(mContext);
                    textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, mContext.getResources().getDimension(R.dimen.activity_set_text_26));
                    textView.setTextColor(mContext.getResources().getColor(R.color.black_c_c2c2c2));
                    textView.setText(mContext.getString(R.string.item_post_experience_need_improve_nothing));
                    flow_layout_improvable.addView(textView);
                }
            }

            //考虑的车型
            ll_consideration_cars.removeAllViews();
            List<ExperiencePostCarType> attentionCarTypeList = mPostCard.getAttentionCarTypeList();
            if (attentionCarTypeList != null && !attentionCarTypeList.isEmpty()) {
                int attentionCarTypeCount = 0;
                for (ExperiencePostCarType carType : attentionCarTypeList) {
                    if (carType != null) {
                        TextView textView = new TextView(mContext);
                        textView.setTextColor(mContext.getResources().getColor(R.color.black_a_2f2f2f));
                        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, mContext.getResources().getDimension(R.dimen.activity_set_text_28));
                        textView.setText(mContext.getString(R.string.item_post_experience_consideration_car_index, ++attentionCarTypeCount, carType.getAttentionCarBrandName(), carType.getAttentionCarTypeName()));
                        ll_consideration_cars.addView(textView);
                    }
                }
            }
        }
    }

    /**
     * 显示内容
     */
    class PostDetailBaseContentViewHolder extends BaseRecyclerViewHolder<Comment> {
        @BindView(R.id.iv_picture)
        View ivPicture;
        @Nullable
        @BindView(R.id.ic_post_detail_ad)
        ImageView mIcPostDetailAd;
        @BindView(R.id.tv_content)
        TextView tvContent;
        @Nullable
        @BindView(R.id.rl_container)
        RelativeLayout rlContainer;
        @Nullable
        @BindView(R.id.iv_play_video)
        ImageView ivPlay;
        @Nullable
        @BindView(R.id.ll_img_desc)
        LinearLayout mLlImgDesc;
        @Nullable
        @BindView(R.id.tv_img_desc)
        TextView mTvImgDesc;
        AliyunVodPlayerView playerView;
        protected PostCardItem postCardItem;

        public PostDetailBaseContentViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            ButterKnife.bind(this, itemView);
            if (ivPicture != null) {
                ivPicture.setOnClickListener(new BaseOnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onClickIvPostImage();
                    }
                });
            }

            if (ivPlay != null) {
                ivPlay.setOnClickListener(new BaseOnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onClickIvPlayVideo();
                    }
                });
            }
        }

        /**
         * 点击帖子图片
         */
        private void onClickIvPostImage() {
            //视频时，点击图片不处理
            if (isVideoItem()) {
                return;
            }
            Object tag = ivPicture.getTag(R.id.tag_position);
            if (tag instanceof Integer) {
                int tagPosition = (int) tag;
                onClickImage(tagPosition);
            }
        }

        /**
         * 点击播放视频
         */
        private void onClickIvPlayVideo() {
            initPlayerAndPlay();
        }

        private boolean isVideoItem() {
            if (postCardItem != null) {
                //url 不为空或 id 不为空
                //noinspection RedundantIfStatement
                if (!TextUtils.isEmpty(postCardItem.getVideoUrl()) || !TextUtils.isEmpty(postCardItem.getVideoId())) {
                    return true;
                }
            }
            return false;
        }

        @Override
        public void bindTo(Comment comment, int position) {
            super.bindTo(comment, position);
            //第一个是 header，偏移 -1
            int imageTextPosition = position - 1;
            if (!TextUtils.isEmpty(mPostCard.getCoverImage())) {
                //封面不为空，再减 1
                imageTextPosition -= 1;
            }
            List<PostCardItem> imageTextList = mPostCard.getImgTexts();
            if (mPostCard.getPostTypeIdOrNegative() == PostCard.PostType.LING_SENSE) {
                int imgCount = CommunityUtils.getLingSensePostImageCount(mPostCard, null);
                if (imgCount > 0) {
                    imageTextPosition += imgCount;
                    //菱感贴轮播图
                    imageTextPosition -= 1;
                }
            }
            if (imageTextList != null && imageTextPosition < imageTextList.size()) {
                postCardItem = imageTextList.get(imageTextPosition);
                if (postCardItem != null) {
                    boolean isVideoItem = isVideoItem();
                    //图片及播放按钮
                    if (TextUtils.isEmpty(postCardItem.getImg()) && !isVideoItem) {
                        //为空，隐藏图片和播放按钮
                        ImageLoadUtils.clearPostCardImage(ivPicture);
                        ViewHolderUtils.goneViews(ivPicture, ivPlay);
                    } else {
                        //设置视频
                        ViewHolderUtils.setVisibility(isVideoItem, ivPlay);
                        if (isVideoItem) {
                            setWidthHeight();
                            setAutoPlay();
                        }
                        //图片不为空
                        //加载图片
                        ivPicture.setVisibility(View.VISIBLE);
                        //判断是否显示广告角标
                        if (postCardItem.getLinkTypeOrZero() > 0) {
                            mIcPostDetailAd.setVisibility(View.VISIBLE);
                        } else {
                            mIcPostDetailAd.setVisibility(View.GONE);
                        }
                        loadImage(postCardItem);
                        ivPicture.setTag(R.id.tag_position, imageTextPosition);

                    }
                    //文字
                    if (TextUtils.isEmpty(postCardItem.getText())) {
                        tvContent.setVisibility(View.GONE);
                        tvContent.setText("");
                    } else {
                        tvContent.setVisibility(View.VISIBLE);
                        //getContentText();
                        showContentWithTopicName();

                        tvContent.setTag(R.id.tag_position, imageTextPosition);
                        tvContent.setOnLongClickListener(new View.OnLongClickListener() {
                            @Override
                            public boolean onLongClick(View v) {
                                Object tag = v.getTag(R.id.tag_position);
                                if (tag instanceof Integer) {
                                    int tagPosition = (int) tag;
                                    onLongClickText(tagPosition);
                                }
                                return false;
                            }
                        });
                    }
                    //图片说明
                    if (mLlImgDesc == null || mTvImgDesc == null) {
                        return;
                    }
                    if (!TextUtils.isEmpty(postCardItem.getImgDesc())) {
                        mLlImgDesc.setVisibility(View.VISIBLE);
                        mTvImgDesc.setText(postCardItem.getImgDesc());
                    } else {
                        mLlImgDesc.setVisibility(View.GONE);
                        mTvImgDesc.setText("");
                    }
                }
            } else {
                ViewHolderUtils.goneViews(ivPicture, mLlImgDesc, tvContent, ivPlay);
            }
        }

        /**
         * 设置内容（子类可以修改）
         */
        protected String getContentText() {
            /*if(TextUtils.isEmpty(mPostCard.getTopicName())){
                return postCardItem.getText();
            }
            return TopicUtils.getNameWithHashtag(mPostCard.getTopicName()) + "\n\n" + postCardItem.getText();*/
            /*CharSequence content = postCardItem.getText();
            content = com.cloudy.linglingbang.activity.community.common.holder.PostContentViewHolder.addTopicInfo(mPostCard, "\n\n" + content, tvContent, new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    onClickContent(view);
                }
            });
            tvContent.setText(content);*/
            return postCardItem.getText();
        }

        /**
         * 设置内容（子类可以修改）
         */
        private void showContentWithTopicName() {

            CharSequence content = postCardItem.getText();
            if (!TextUtils.isEmpty(mPostCard.getTopicName())) {
                content = "\n\n" + content;
            }
            content = com.cloudy.linglingbang.activity.community.common.holder.PostContentViewHolder.addTopicInfo(mPostCard, content, tvContent, new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    onClickContent(view);
                }
            });
            tvContent.setText(content);

        }

        /**
         * 点击内容
         * 即要使话题可点击，内容可占击，使用 CustomLinkMovementMethod
         * 又要使没话题也可以点击（可能因为 ImageSpan 或其他，待测试），所以使用点击事件
         */
        public void onClickContent(View v) {
            ViewParent parent = v.getParent();
            if (parent instanceof ViewGroup) {
                ((ViewGroup) parent).performClick();
            }
        }

        /**
         * 自动播放相关设置
         */
        protected void setAutoPlay() {

        }

        /**
         * 初始化播放器，并播放
         */
        protected void initPlayerAndPlay() {
            if (!isVideoItem()) {
                return;
            }
            if (rlContainer != null) {
                if (!JniLibUtil.loadAliFfmpegLib(mContext)) {
                    return;
                }
                if (mOnVideoClickListener != null) {
                    mOnVideoClickListener.onVideoClick();
                }
                rlContainer.post(new Runnable() {//post确保view初始化完毕
                    @Override
                    public void run() {
                        playerView = new AliyunVodPlayerView(mContext);
                        RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(ivPicture.getWidth(), ivPicture.getHeight());
                        layoutParams.leftMargin = (int) mContext.getResources().getDimension(R.dimen.post_detail_content_horizontal_padding);
                        playerView.setLayoutParams(layoutParams);
                        //设置横竖屏方向
                        setFullScreenOrientation();
                        rlContainer.addView(playerView);
                        if ((!TextUtils.isEmpty(postCardItem.getVideoUrl()))) {
                            //设置视频资源url
                            playerView.setVideoSource(postCardItem.getVideoUrl(), postCardItem.getImg());
                            //准备开始播放
                            playerView.start();
                        } else if (!TextUtils.isEmpty(postCardItem.getVideoId())) {
                            //获取短视频播放凭证
                            ShortVideoUtil.getAliSTSToken(mContext, new ShortVideoUtil.STSTokenCallback() {
                                @Override
                                public void onSuccess(AliSTSTokenInfo tokenInfo) {
                                    //使用vid+STS方式播放
                                    if (playerView != null) {
                                        VidSts vidSts = new VidSts();
                                        vidSts.setVid(postCardItem.getVideoId());//视频vid
                                        vidSts.setAccessKeyId(tokenInfo.getAccessKeyId());//播放凭证id
                                        vidSts.setAccessKeySecret(tokenInfo.getAccessKeySecret());//播放凭证secret
                                        vidSts.setSecurityToken(tokenInfo.getSecurityToken());//播放凭证token
                                        vidSts.setTitle(" ");//隐藏视频标题
                                        playerView.setVideoSource(vidSts, postCardItem.getImg(), "");
                                        //准备开始播放
                                        playerView.start();
                                    }
                                }

                                @Override
                                public void onFailure(Throwable e) {
                                    ToastUtil.showMessage(mContext, mContext.getString(R.string.play_video_failed));
                                    rlContainer.removeView(playerView);
                                }
                            });
                        }
                        //设置小屏播放时视频左上角返回键监听
                        playerView.setOnNormalScreenBackClickListener(new AliyunVodPlayerView.OnNormalScreenBackClickListener() {
                            @Override
                            public void onBackClick() {
                                if (playerView != null) {
                                    playerView.stop();
                                    playerView.release();
                                }
                                rlContainer.removeView(playerView);
                                playerView = null;
                            }
                        });

                    }
                });
            }
        }

        /**
         * 设置视频全屏时屏幕的方向（子类可重写）
         */
        protected void setFullScreenOrientation() {
        }

        /**
         * 加载图片或者封面图等
         */
        protected void loadImage(PostCardItem postCardItem) {
            //因为添加了父布局，所以影响margin左右的计算，要讲父布局的间距考虑进去
            ImageLoadUtils.loadPostCardImage(postCardItem, ivPicture, true);
        }

        /**
         * 设置图片宽高的默认方法，子类可修改
         */
        protected void setWidthHeight() {
            //获取屏幕宽度
            Resources resources = mContext.getResources();
            DisplayMetrics displayMetrics = resources.getDisplayMetrics();
            int screenWidth = displayMetrics.widthPixels;
            //视频比例16:9
            int videoHeight = (int) (screenWidth * 9.0f / 16 + 1);
            postCardItem.setWidth(String.valueOf(screenWidth));
            postCardItem.setHeight(String.valueOf(videoHeight));
        }

    }

    /**
     * 显示底部的时间等
     */
    class PostDetailBottomViewHolder extends BaseRecyclerViewHolder<Comment> {

        private List<BasePostChildViewHolder> mChildViewHolderList;

        public PostDetailBottomViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            mChildViewHolderList = new ArrayList<>();
            mChildViewHolderList.add(new PostTagCommodityViewHolder(itemView));
            mChildViewHolderList.add(new PostDetailBottomInfoViewHolder(itemView));
            mChildViewHolderList.add(new PostDetailLabelsViewHolder(itemView));
        }

        @Override
        public void bindTo(Comment comment, int position) {
            super.bindTo(comment, position);
            for (BasePostChildViewHolder childViewHolder : mChildViewHolderList) {
                childViewHolder.bindTo(mPostCard);
            }
        }
    }

    /**
     * 置顶回复
     */
    public class PostDetailTopCommentTitleViewHolder extends BaseRecyclerViewHolder<Comment> {
        protected TextView mTvCommentTitle;

        public PostDetailTopCommentTitleViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            mTvCommentTitle = itemView.findViewById(R.id.tv_comment_title);
        }

        @Override
        public void bindTo(Comment comment, int position) {
            super.bindTo(comment, position);
            mTvCommentTitle.setText(comment.getCommentContent());
        }
    }

    /**
     * 全部回复
     */
    public class PostDetailAllCommentTitleViewHolder extends PostDetailTopCommentTitleViewHolder {

        //下面的评论加载中和你牛你先说的布局
        protected LinearLayout mLlEmpty;

        protected TextView mTvEmpty;

        /**
         * 加载更多回复
         */
        private TextView mTvLoadAllComments;

        public PostDetailAllCommentTitleViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            mLlEmpty = itemView.findViewById(R.id.ll_empty);
            mTvEmpty = itemView.findViewById(R.id.tv_empty);
            mTvLoadAllComments = itemView.findViewById(R.id.tv_load_all_comments);

            //设计图不可见
            itemView.findViewById(R.id.view_divider).setVisibility(View.GONE);

            mLlEmpty.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    //正在加载中不处理
                    if (!isLoadingComment()) {
                        ((PostDetailActivity) mContext).showReplyPost();
                    }
                }
            });
            //加载更多回复
            mTvLoadAllComments.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    //显示所有回复
                    mLoadAllComments = true;
                    mTvLoadAllComments.setVisibility(View.GONE);
                    mTvEmpty.setText(R.string.post_detail_comment_is_loading);
                    mLlEmpty.setVisibility(View.VISIBLE);
                    ((PostDetailActivity) mContext).onClickLoadAllComments();
                }
            });
        }

        @Override
        public void bindTo(Comment comment, int position) {
            super.bindTo(comment, position);
            mTvCommentTitle.setText(mContext.getString(R.string.post_detail_all_comments, mPostCard.getPostCommentCount()));
            //是否显示加载全部回复按钮
            mTvLoadAllComments.setVisibility(mLoadAllComments ? View.GONE : View.VISIBLE);
            //底部加载中
            if (isLoadingComment()) {
                //这里可能ll_empty是不显示的，只改了字
                mTvEmpty.setText(R.string.post_detail_comment_is_loading);
            } else {
                if (mPostCard.getPostCommentCount() == 0) {
                    if (CommunityUtils.PostUtils.canReply(mPostCard)) {
                        mTvEmpty.setText(R.string.post_detail_no_comment_you_can_reply_first);
                    } else {
                        //暂无评论，不显示你先说
                        mTvEmpty.setText(R.string.post_detail_no_comment);
                    }
                    mLlEmpty.setVisibility(View.VISIBLE);
                } else {
                    mLlEmpty.setVisibility(View.GONE);
                }
            }
        }
    }

    /**
     * 课堂帖的全部评论
     */
    private class OfficialCoursePostDetailAllCommentTitleViewHolder extends PostDetailAllCommentTitleViewHolder {
        private CompoundButton mBtnSeeAuthorOnly;

        public OfficialCoursePostDetailAllCommentTitleViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            mBtnSeeAuthorOnly = itemView.findViewById(R.id.btn_see_author_only);
            ViewHolderUtils.setOnClickListener(new BaseOnClickListener() {
                @Override
                public void onClick(Context context) {
                    super.onClick(context);
                    onClickBtnSeeAuthorOnly();
                }
            }, mBtnSeeAuthorOnly);
        }

        @Override
        public void bindTo(Comment comment, int position) {
            super.bindTo(comment, position);
            updateBtnSeeAuthorOnly();
            //加载中与父类一样，不再处理
            if (!isLoadingComment()) {
                PostDetailActivity postDetailActivity = getPostDetailActivity();
                if (postDetailActivity != null) {
                    if (postDetailActivity.isSeeAuthorOnlyStatus()) {
                        //当前是状态为选中只看楼主
                        if (getAuthorOnlyCommentCount(mPostCard) == 0) {
                            mTvEmpty.setText(R.string.post_detail_no_author_comment);
                            mLlEmpty.setVisibility(View.VISIBLE);
                        } else {
                            mLlEmpty.setVisibility(View.GONE);
                        }
                    }//未选中只看楼主，保留父类逻辑
                }
            }
        }

        /**
         * 只看楼主的回复的数量
         */
        private int getAuthorOnlyCommentCount(PostCard postCard) {
            return postCard != null ? postCard.getPostAuthorCommentCount() : 0;
        }

        private void onClickBtnSeeAuthorOnly() {
            PostDetailActivity postDetailActivity = getPostDetailActivity();
            if (postDetailActivity != null) {
                //切换只看楼主，并加载数据
                postDetailActivity.toggleSeeAuthorOnly();
                //更新按钮展示
                updateBtnSeeAuthorOnly();
            }
        }

        private PostDetailActivity getPostDetailActivity() {
            if (mContext instanceof PostDetailActivity) {
                return (PostDetailActivity) mContext;
            }
            return null;
        }

        /**
         * 更新按钮展示
         */
        private void updateBtnSeeAuthorOnly() {
            PostDetailActivity postDetailActivity = getPostDetailActivity();
            if (postDetailActivity != null) {
                //显示
                ViewHolderUtils.setVisibility(postDetailActivity.isLoadAllComments(), mBtnSeeAuthorOnly);
                //选中
                mBtnSeeAuthorOnly.setChecked(postDetailActivity.isSeeAuthorOnlyStatus());
            }
        }
    }

    /**
     * 顶部封面
     */
    private class TopCoverViewHolder extends BaseRecyclerViewHolder<Comment> {
        private AdImageView mIvImage;

        public TopCoverViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            mIvImage = itemView.findViewById(R.id.iv_image);
        }

        @Override
        public void bindTo(Comment comment, int position) {
            super.bindTo(comment, position);
            if (mPostCard != null) {
                String coverImage = mPostCard.getCoverImage();
                if (!TextUtils.isEmpty(coverImage)) {
                    mIvImage.loadCommonImage(coverImage);
                    mIvImage.setScaleType(ImageView.ScaleType.CENTER_CROP);
                }
            }
        }
    }

    /**
     * 相关推荐
     */
    class RecommendViewHolder extends BaseRecyclerViewHolder<Comment> {
        RecyclerView mRecyclerView;

        public RecommendViewHolder(View itemView) {
            super(itemView);
            mRecyclerView = itemView.findViewById(R.id.recycler_view);
        }

        @Override
        public void bindTo(Comment comment, int position) {
            super.bindTo(comment, position);
            mRecyclerView.setAdapter(new PostDetailRecommendAdapter(mContext, mRecommendPostList));
        }
    }

    /**
     * 菱感贴BannerView图
     */
    class ContentSenseViewHolder extends BaseRecyclerViewHolder<Comment> implements ViewPager.OnPageChangeListener {
        TextView mTvIndicator;
        HomeIndicatorBelowBanner<PostCardItem> mBannerView;
        private List<PostCardItem> bannerData;
        TextView mTvCoverRight, mTvCoverLeft;
        private int coverWidth;
        private int leftMargin;
        private int imgSize;

        public ContentSenseViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            mBannerView = itemView.findViewById(R.id.banner_view);
            mTvIndicator = itemView.findViewById(R.id.tv_indicator);
            mTvCoverRight = itemView.findViewById(R.id.tv_cover_right);
            mTvCoverLeft = itemView.findViewById(R.id.tv_cover_left);
            if (mPostCard != null && mPostCard.getImages() != null) {
                imgSize = mPostCard.getImages().length;
            }
            coverWidth = (int) ((DeviceUtil.getScreenWidth() - mContext.getResources().getDimension(R.dimen.normal_24) * 7) / 2);
            leftMargin = (int) (mContext.getResources().getDimension(R.dimen.normal_16) * (imgSize - 5));
            initAdIndicator();

        }

        void changeAdTurning(boolean isTurning) {
            if (mBannerView != null) {
                mBannerView.getBannerView().changeAdTurning(isTurning);
            }
        }

        private void initAdIndicator() {
            if (imgSize > 5) {
                LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) mBannerView.getBannerView().getAdIndicator().getContainer().getLayoutParams();
                params.setMargins(leftMargin, mContext.getResources().getDimensionPixelOffset(R.dimen.normal_24), 0, 0);
                mBannerView.getBannerView().getAdIndicator().getContainer().setLayoutParams(params);
                RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) mTvCoverRight.getLayoutParams();
                layoutParams.width = coverWidth;
                mTvCoverRight.setLayoutParams(layoutParams);
                RelativeLayout.LayoutParams layoutParamsLeft = (RelativeLayout.LayoutParams) mTvCoverLeft.getLayoutParams();
                layoutParamsLeft.width = coverWidth;
                mTvCoverLeft.setLayoutParams(layoutParamsLeft);
                mTvCoverLeft.setVisibility(View.VISIBLE);
                mTvCoverRight.setVisibility(View.VISIBLE);
            } else {
                mTvCoverLeft.setVisibility(View.GONE);
                mTvCoverRight.setVisibility(View.GONE);
            }
            mBannerView.setScaleType(ImageView.ScaleType.FIT_CENTER);
            mBannerView.setOnCreateBannerItemView(new BannerLabelViewHolder(mBannerView.getScaleType()));

        }

        @Override
        public void bindTo(Comment comment, int position) {
            super.bindTo(comment, position);
            if (mPostCard == null || mPostCard.getImgTexts() == null || mPostCard.getImgTexts().isEmpty()) {
                return;
            }
            final float[] rate = {0.0f};
            List<PostCardItem> postCardItems = mPostCard.getImgTexts();
            if (mBannerView.getTag() != null && ObjectUtils.equals(postCardItems, mBannerView.getTag())) {
                return;
            }
            mBannerView.setTag(postCardItems);
            if (bannerData == null) {
                bannerData = new ArrayList<>();
            } else {
                bannerData.clear();
            }
            CommunityUtils.getLingSensePostImageCount(mPostCard, postCardItem -> {
                bannerData.add(postCardItem);
                float w = Float.parseFloat(postCardItem.getWidth());
                float h = Float.parseFloat(postCardItem.getHeight());
                rate[0] = Math.max(rate[0], h / w);
                return null;
            });
            mBannerView.getBannerView().getViewPager().removeOnPageChangeListener(this);
            if (!bannerData.isEmpty()) {
                mBannerView.getBannerView().getViewPager().addOnPageChangeListener(this);
            }
            int sw = DeviceUtil.getScreenWidth();
            int sh = DeviceUtil.getScreenHeight();
            //减去顶部用户信息
            int maxHeight = sh - itemView.getResources().getDimensionPixelOffset(R.dimen.normal_136);
            //减去标题
            maxHeight = maxHeight - itemView.getResources().getDimensionPixelOffset(R.dimen.normal_80);
            //减去状态栏
            maxHeight = maxHeight - Math.max(NotchScreenUtils.getNotchSafeWH()[1], StatusBarUtils.getStatusBarHeight(ApplicationLLB.ct()));
            maxHeight = maxHeight * 8 / 10;
            float maxRate = maxHeight * 1.0f / sw;
            if (rate[0] > maxRate) {
                rate[0] = maxRate;
            }

            mBannerView.getBannerView().setWhRate(1 / rate[0]);
            mTvIndicator.setTag(bannerData.size());
            mBannerView.setAdList(bannerData);
            mBannerView.getBannerView().setCanLoop(true);
            mBannerView.getBannerView().stopAutoTurning();
            if (bannerData.size() > 1) {
                onPageSelected(mBannerView.getBannerView().getCurrentIndex());
            }

        }

        boolean isRight = false;

        @Override
        public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

        }

        @Override
        public void onPageSelected(int position) {
            int size = (int) mTvIndicator.getTag();
            int currentPosition = position % size;
            String text = (currentPosition + 1) + "/" + size;
            SpannableString string = new SpannableString(text);
            string.setSpan(new AbsoluteSizeSpan(28, true), 0, text.indexOf("/"), SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE);
            mTvIndicator.setText(string);
            mBannerView.getBannerView().getAdIndicator().indicate(currentPosition);
            int translate = (int) (leftMargin + mContext.getResources().getDimension(R.dimen.normal_24) * (imgSize > 6 ? imgSize - 6 : 1));
            if (currentPosition >= 5 && !isRight) {
                TranslateAnimation animation = new TranslateAnimation(0, -translate, 0, 0);
                animation.setDuration(500);

                mBannerView.getBannerView().getAdIndicator().getContainer().startAnimation(animation);
                animation.setAnimationListener(new Animation.AnimationListener() {
                    @Override
                    public void onAnimationStart(Animation animation) {
                        TimerTask timerTask = new TimerTask() {
                            @Override
                            public void run() {
                                ApplicationLLB.currentActivity.runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) mBannerView.getBannerView().getAdIndicator().getContainer().getLayoutParams();
                                        params.setMargins(leftMargin - translate, mContext.getResources().getDimensionPixelOffset(R.dimen.normal_24), 0, 0);
                                        mBannerView.getBannerView().getAdIndicator().getContainer().setLayoutParams(params);

                                    }
                                });

                            }
                        };
                        new Timer().schedule(timerTask, 520);
                    }

                    @Override
                    public void onAnimationEnd(Animation animation) {
                    }

                    @Override
                    public void onAnimationRepeat(Animation animation) {

                    }
                });

                isRight = true;
            } else if (currentPosition < 5 && isRight) {
                TranslateAnimation animation = new TranslateAnimation(-translate, 0, 0, 0);
                animation.setDuration(500);
                mBannerView.getBannerView().getAdIndicator().getContainer().startAnimation(animation);

                animation.setAnimationListener(new Animation.AnimationListener() {
                    @Override
                    public void onAnimationStart(Animation animation) {
                        TimerTask timerTask = new TimerTask() {
                            @Override
                            public void run() {
                                ApplicationLLB.currentActivity.runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) mBannerView.getBannerView().getAdIndicator().getContainer().getLayoutParams();
                                        params.setMargins(leftMargin, mContext.getResources().getDimensionPixelOffset(R.dimen.normal_24), 0, 0);
                                        mBannerView.getBannerView().getAdIndicator().getContainer().setLayoutParams(params);
                                    }
                                });

                            }
                        };
                        new Timer().schedule(timerTask, 100);
                    }

                    @Override
                    public void onAnimationEnd(Animation animation) {

                    }

                    @Override
                    public void onAnimationRepeat(Animation animation) {

                    }
                });

                isRight = false;
            }
        }

        @Override
        public void onPageScrollStateChanged(int state) {

        }
    }

    interface OnVideoClickListener {
        void onVideoClick();
    }

    private OnVideoClickListener mOnVideoClickListener;

    public OnVideoClickListener getOnVideoClickListener() {
        return mOnVideoClickListener;
    }

    public void setOnVideoClickListener(OnVideoClickListener onVideoClickListener) {
        mOnVideoClickListener = onVideoClickListener;
    }

    private class BannerLabelViewHolder implements HomeIndicatorBelowBanner.OnCreateBannerItemView<PostCardItem> {

        private final ImageView.ScaleType mScaleType;

        public BannerLabelViewHolder(ImageView.ScaleType scaleType) {
            mScaleType = scaleType;
        }

        @Override
        public View createView(Context context, ViewGroup container, int position) {
            return View.inflate(context, R.layout.layout_post_detail_banner_itemview, null);
        }

        @Override
        public boolean bindView(View itemView, int position, PostCardItem postCardItem) {
            itemView.setOnClickListener(view -> {
                List<PostCardItem> list = new ArrayList<>();
                CommunityUtils.getLingSensePostImageCount(mPostCard, postCardItem1 -> {
                    list.add(postCardItem1);
                    return null;
                });
                if (list.isEmpty()) {
                    return;
                }
                Intent in = new Intent(mContext, ScanImageActivity.class);
                Bundle bundle = new Bundle();
                bundle.putSerializable(ScanImageActivity.EXTRA_IMAGE_DESC_LIST, (Serializable) list);
                bundle.putInt("image_index", position);
                in.putExtras(bundle);
                mContext.startActivity(in);
                //添加埋点
                SensorsUtils.sensorsClickBtn("点击banner图", "官方帖子详情(" + mPostCard.getPostId() + ")", "图片");
            });
            AdImageView adImageView = itemView.findViewById(R.id.iv_ad);

            adImageView.setScaleType(mScaleType);
            RandomDragTagLayout randomDragTagLayout = itemView.findViewById(R.id.fl_content);
            List<PostCommodity> commodityList = postCardItem.getPostCommodityList();
            if (commodityList == null || commodityList.isEmpty()) {
                adImageView.createImageLoad(postCardItem.getImg())
                        .setDoNotAnimate().setDoNotLoadWebp().load();
                randomDragTagLayout.setVisibility(View.GONE);
                return true;
            }
            GlideApp.with(adImageView)
                    .asBitmap()
                    .listener(new RequestListener<Bitmap>() {
                        @Override
                        public boolean onLoadFailed(@Nullable @org.jetbrains.annotations.Nullable GlideException e, Object model, Target<Bitmap> target, boolean isFirstResource) {
                            addImageTag(adImageView, randomDragTagLayout, postCardItem.getPostCommodityList(), ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
                            return false;
                        }

                        @Override
                        public boolean onResourceReady(Bitmap resource, Object model, Target<Bitmap> target, DataSource dataSource, boolean isFirstResource) {
                            addImageTag(adImageView, randomDragTagLayout, postCardItem.getPostCommodityList(), resource.getWidth(), resource.getHeight());
                            return false;
                        }
                    }).error(adImageView.getPlaceHolderResId())
                    .placeholder(adImageView.getPlaceHolderResId())
                    .load(postCardItem.getImg())
                    .into(adImageView);
            return true;
        }

        private void addImageTag(View ad, RandomDragTagLayout randomDragTagLayout, List<PostCommodity> postCommodityList, int width, int height) {
            ViewGroup.LayoutParams p = ad.getLayoutParams();
            p.width = width;
            p.height = height;
            ad.setLayoutParams(p);
            randomDragTagLayout.removeAllViews();
            if (postCommodityList == null || postCommodityList.isEmpty()) {
                randomDragTagLayout.setVisibility(View.GONE);
                return;
            }
            randomDragTagLayout.setVisibility(View.VISIBLE);
            for (PostCommodity postCommodity : postCommodityList) {
                postCommodity.setCanMove(false);
                randomDragTagLayout.setPostId(mPostCard.getPostId());
                randomDragTagLayout.addTagView(postCommodity);
            }
        }

    }

}
