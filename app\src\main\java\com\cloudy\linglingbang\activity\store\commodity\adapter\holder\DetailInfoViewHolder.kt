package com.cloudy.linglingbang.activity.store.commodity.adapter.holder

import android.graphics.Paint
import android.text.*
import android.view.Gravity
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.store.commodity.CommodityOpenDialog
import com.cloudy.linglingbang.app.util.AppUtil
import com.cloudy.linglingbang.app.util.JumpPageUtil
import com.cloudy.linglingbang.app.util.ViewHolderUtils
import com.cloudy.linglingbang.app.util.span.ForegroundColorAndAbsoluteSizeSpan
import com.cloudy.linglingbang.app.util.span.TagTextView
import com.cloudy.linglingbang.app.util.timer.CountDownManager
import com.cloudy.linglingbang.app.util.timer.TimeDelta
import com.cloudy.linglingbang.app.widget.FlowLayout
import kotlin.math.pow

/**
 * 商品详情名称、销量、价格等信息
 *
 * <AUTHOR>
 * @date 2022/9/26
 */
class DetailInfoViewHolder(itemView: View?) : BaseCommodityHolder<Any>(itemView),
    CountDownManager.OnTickListener {
    private var mTvPrice: TextView? = null
    private var mTvActivityPrice: TextView? = null
    private var mTvOriginalPrice: TextView? = null
    private var mRlPrivacy: View? = null
    private var mRlCoupon: View? = null
    private var mIvActivityName: ImageView? = null
    private var mTvDepositCarPrice: TextView? = null
    private var mTvActivityTime: TextView? = null
    private var mTvCommodityName: TextView? = null
    private var mTvCommoditySecondName: TextView? = null
    private var mTvMonthSales: TextView? = null
    private var mFlowLayout: FlowLayout? = null
    private var pzbLay: View? = null


    init {
        mTvPrice = itemView?.findViewById(R.id.tv_price)
        mTvActivityPrice = itemView?.findViewById(R.id.tv_activity_price)
        mTvOriginalPrice = itemView?.findViewById(R.id.tv_original_price)
        mRlCoupon = itemView?.findViewById(R.id.rl_coupon)
        mFlowLayout = itemView?.findViewById(R.id.flow_layout)
        mRlPrivacy = itemView?.findViewById(R.id.rl_privacy)
        mTvDepositCarPrice = itemView?.findViewById(R.id.tv_deposit_car_price)
        mIvActivityName = itemView?.findViewById(R.id.iv_activity_name)
        mTvActivityTime = itemView?.findViewById(R.id.tv_activity_time)
        mTvCommodityName = itemView?.findViewById(R.id.tv_commodity_name)
        mTvCommoditySecondName = itemView?.findViewById(R.id.tv_second_name)
        mTvMonthSales = itemView?.findViewById(R.id.tv_month_sales)
        pzbLay = itemView?.findViewById(R.id.ll_pzb)
        // 添加点击事件
        pzbLay?.setOnClickListener {
            // 在这里处理点击事件
            handlePzbLayClick()
        }
        ViewHolderUtils.setOnClickListener({
            if (itemView?.context is CommodityOpenDialog) {
                (itemView.context as CommodityOpenDialog).openCouponDialog()
            }
        }, itemView?.findViewById(R.id.rl_coupon))
        mTvOriginalPrice?.paint?.flags = Paint.STRIKE_THRU_TEXT_FLAG

    }

    override fun bindTo(bean: Any?, position: Int) {
        super.bindTo(bean, position)
        CountDownManager.getInstance().removeOnTickListener(this@DetailInfoViewHolder)
        mTvOriginalPrice?.visibility = View.GONE
        mTvPrice?.visibility = View.VISIBLE
        mRlPrivacy?.visibility = View.GONE
        mCenterCommodity?.apply {
            pzbLay?.visibility = if (commodityClassifyId == 0 && !TextUtils.isEmpty(lmCommodityAsDetail.configPageLink)) View.VISIBLE else View.GONE
            mTvDepositCarPrice?.visibility =
                if (TextUtils.isEmpty(depositStr)) View.GONE else View.VISIBLE
            mTvDepositCarPrice?.text =
                itemView.resources.getString(R.string.commodity_deposit, depositStr)

            if (!TextUtils.isEmpty(manageModeStr) && mTvCommodityName is TagTextView) {
                val l = arrayListOf<String>()
                l.add(manageModeStr)
                (mTvCommodityName as TagTextView).setTagTextColor("#ffffff")
                (mTvCommodityName as TagTextView).setTagLayoutRes(R.layout.layout_commodity_tag_new)
                (mTvCommodityName as TagTextView).setTagsBackgroundStyle(R.drawable.bg_corner4_solid_ea0029)
                (mTvCommodityName as TagTextView).setTagStart(l, commodityName)
            } else {
                mTvCommodityName?.text = commodityName
            }
            mTvCommoditySecondName?.text = commoditySecondName
            if (5 == activityStatus || 4 == activityStatus) {
                mTvPrice?.visibility = View.GONE
                mRlPrivacy?.visibility = View.VISIBLE
                CountDownManager.getInstance().addOnTickListener(this@DetailInfoViewHolder)
                var price = if (activityMinPrice == activityMaxPrice) {
                    "¥$activityMinPrice"
                } else {
                    "¥$activityMinPrice-$activityMaxPrice"
                }

                val oPrice = if (minPrice == maxPrice) {
                    "¥$minPrice"
                } else {
                    "¥$minPrice-$maxPrice"
                }

                if (activityStatus == 4) {
                    val sp = SpannableString(oPrice)
                    sp.setSpan(
                        ForegroundColorAndAbsoluteSizeSpan(
                            itemView.resources.getColor(R.color.color_ea0029),
                            itemView.resources.getDimensionPixelSize(R.dimen.common_text_size_36)
                                .toFloat()
                        ),
                        0,
                        1,
                        Spanned.SPAN_INCLUSIVE_INCLUSIVE
                    )
                    mTvPrice?.visibility = View.VISIBLE
                    mTvPrice?.text = sp
                    mTvOriginalPrice?.visibility = View.GONE
                    setActivityPrice(price)
                } else {
                    if (!TextUtils.isEmpty(activityMinPrice) || !TextUtils.isEmpty(activityMaxPrice)) {
                        mTvOriginalPrice?.visibility = View.VISIBLE
                    }
                    mTvOriginalPrice?.text = oPrice
                    setActivityPrice(price)
                }
                mIvActivityName?.visibility = View.VISIBLE
                when (promotionStrategyType) {
                    31 -> {
                        mIvActivityName?.setImageResource(
                            R.drawable.ic_commodity_zhijiang
                        )
                    }

                    32 -> {
                        mIvActivityName?.setImageResource(
                            R.drawable.ic_commodity_zhekou
                        )
                    }

                    33 -> {
                        mIvActivityName?.setImageResource(
                            R.drawable.ic_commodity_tejia
                        )
                    }

                    else -> {
                        mIvActivityName?.visibility = View.INVISIBLE
                    }
                }
                mTvActivityTime?.text = null
            } else {
                mIvActivityName?.visibility = View.INVISIBLE
                mTvActivityTime?.text = null
                mTvPrice?.visibility = View.VISIBLE
                mRlPrivacy?.visibility = View.GONE
                setPrice(minPrice, maxPrice)
            }

            mRlCoupon?.visibility = View.GONE
            promotionActivity?.apply {
                val labels = activityLabelList
                val hasLabels = labels != null && labels.isNotEmpty()
                mRlCoupon?.visibility =
                    if (hasLabels) View.VISIBLE else View.GONE
                if (hasLabels) {
                    mFlowLayout?.apply {
                        if (tag != labels) {
                            removeAllViews()
                            tag = labels
                            val color = resources.getColor(R.color.color_ea0029)
                            val dp4 = resources.getDimensionPixelSize(R.dimen.normal_8)
                            val dp6 = resources.getDimensionPixelSize(R.dimen.normal_12)

                            labels?.forEach { label ->
                                val tv = TextView(context)
                                tv.text = label
                                tv.setPadding(dp6, dp4, dp6, dp4)
                                tv.textSize = 12f
                                tv.setTextColor(color)
                                tv.gravity = Gravity.CENTER
                                tv.setBackgroundResource(R.drawable.shape_bg_shopping_cart_discount)
                                addView(tv)
                            }
                        }
                    }
                }

            }

            lmCommodityAsDetail?.apply {
                mTvMonthSales?.text = itemView.resources.getString(
                    R.string.commodity_monthly_sales,
                    commodityMonthlySales
                )
            }
        }
    }

    private fun setActivityPrice(price: String) {
        val sp = SpannableString(price)
        val index = price.indexOf("-")
        val first = price.indexOf(".")
        val last = price.lastIndexOf(".")

        sp.setSpan(
            ForegroundColorAndAbsoluteSizeSpan(
                itemView.resources.getColor(R.color.color_ffffff),
                itemView.resources.getDimensionPixelSize(R.dimen.activity_set_text_56)
                    .toFloat()
            ),
            1,
            if (first > 0) first else price.length,
            Spanned.SPAN_INCLUSIVE_INCLUSIVE
        )
        if (index > 0) {
            sp.setSpan(
                ForegroundColorAndAbsoluteSizeSpan(
                    itemView.resources.getColor(R.color.color_ffffff),
                    itemView.resources.getDimensionPixelSize(R.dimen.activity_set_text_56)
                        .toFloat()
                ),
                index,
                index + 1,
                Spanned.SPAN_INCLUSIVE_INCLUSIVE
            )
            sp.setSpan(
                ForegroundColorAndAbsoluteSizeSpan(
                    itemView.resources.getColor(R.color.color_ffffff),
                    itemView.resources.getDimensionPixelSize(R.dimen.activity_set_text_56)
                        .toFloat()
                ),
                index + 1,
                if (last > 0) last else price.length,
                Spanned.SPAN_INCLUSIVE_INCLUSIVE
            )
        }
        mTvActivityPrice?.text = sp
    }

    private fun setPrice(minPrice: String, maxPrice: String) {
        val oPrice = if (minPrice == maxPrice) {
            "¥$minPrice"
        } else {
            "¥$minPrice-$maxPrice"
        }
        val sp = SpannableString(oPrice)
        sp.setSpan(
            ForegroundColorAndAbsoluteSizeSpan(
                itemView.resources.getColor(R.color.color_ea0029),
                itemView.resources.getDimensionPixelSize(R.dimen.common_text_size_36)
                    .toFloat()
            ),
            0,
            1,
            Spanned.SPAN_INCLUSIVE_INCLUSIVE
        )
        mTvPrice?.text = sp
    }

    override fun onTick(currentTime: Long) {
        mCenterCommodity?.apply {
            val tsl = currentTime.toString().length - promotionStartTime.toString().length
            val tel = currentTime.toString().length - promotionEndTime.toString().length
            if (currentTime < promotionStartTime * 10.0.pow(tsl.toDouble()).toLong()) {
                activityStatus = 4
                if (mRlPrivacy?.visibility != View.VISIBLE) {
                    bindTo(null, layoutPosition)
                }
            } else if (currentTime >= promotionEndTime * 10.0.pow(tel.toDouble()).toLong()) {
                activityStatus = 6
                bindTo(null, layoutPosition)
                //活动结束，不需要展示了，注销
                CountDownManager.getInstance().removeOnTickListener(this@DetailInfoViewHolder)
            }
            if (activityStatus == 5 || activityStatus == 4) {
                if (activityStatus != 5 && currentTime > promotionStartTime) {
                    activityStatus = 5
                    bindTo(null, layoutPosition)
                }
                val times = StringBuilder()
                val timeDelta = if (activityStatus == 4) {
                    times.append(itemView.resources.getString(R.string.commodity_new_start_time))
                    TimeDelta.create(
                        promotionStartTime * 10.0.pow(tsl.toDouble()).toLong(),
                        currentTime
                    )
                } else {
                    times.append(itemView.resources.getString(R.string.commodity_new_end_time))
                    TimeDelta.create(
                        currentTime,
                        promotionEndTime * 10.0.pow(tel.toDouble()).toLong()
                    )
                }
                if (timeDelta.days > 0) {
                    times.append(timeDelta.days)
                    times.append("天")
                    times.append(" ")
                } else {
                    times.append(" ")
                }

                if (timeDelta.hours > 0) {
                    if (timeDelta.hours < 10) {
                        times.append("0")
                    }
                    times.append(timeDelta.hours)
                    times.append(":")
                } else {
                    times.append("00:")
                }

                if (timeDelta.minutes < 10) {
                    times.append("0")
                }
                times.append(timeDelta.minutes)
                times.append(":")

                if (timeDelta.seconds < 10) {
                    times.append("0")
                }
                times.append(timeDelta.seconds)

                mTvActivityTime?.text = times
            }
        }

    }

    override fun getCountDownTag(): Any {
        return AppUtil.getActivity(itemView.context)
    }

    // 添加点击事件处理方法
    private fun handlePzbLayClick() {
        mCenterCommodity?.let { commodity ->
            // 使用commodity中的数据实现跳转
            val url = commodity.lmCommodityAsDetail.configPageLink // 替换为实际的base URL
            JumpPageUtil.goCommonWeb(itemView.context, url)
        }
    }
}
