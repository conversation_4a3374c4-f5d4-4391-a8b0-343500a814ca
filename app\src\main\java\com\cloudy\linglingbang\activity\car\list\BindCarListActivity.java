package com.cloudy.linglingbang.activity.car.list;

import android.content.Intent;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.basic.SingleFragmentActivity;
import com.cloudy.linglingbang.activity.car.add.AddCarActivity;

import androidx.fragment.app.Fragment;

/**
 * 全部爱车
 *
 * <AUTHOR>
 * @date 2019/3/18
 */
public class BindCarListActivity extends SingleFragmentActivity {

    private BindCarListFragment mBindCarListFragment;

    @Override
    protected Fragment createFragment() {
        mBindCarListFragment = new BindCarListFragment();
        return mBindCarListFragment;
    }

    @Override
    public void initialize() {
        super.initialize();
        setMiddleTitle("我的爱车");
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        menu.clear();
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(R.menu.menu_publish, menu);
        MenuItem menuItem = menu.findItem(R.id.next_step);
        menuItem.setTitle(R.string.title_my_bind_car_right);
        menuItem.setOnMenuItemClickListener(new MenuItem.OnMenuItemClickListener() {
            @Override
            public boolean onMenuItemClick(MenuItem item) {
                clickRight();
                return item.getItemId() == R.id.next_step;
            }
        });
        return true;
    }

    /**
     * 添加爱车
     */
    void clickRight() {
        IntentUtils.startActivity(this, AddCarActivity.class);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (mBindCarListFragment != null) {
            mBindCarListFragment.onRefreshCar();
        }
    }
}
