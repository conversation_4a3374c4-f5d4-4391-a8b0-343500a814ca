package com.cloudy.linglingbang.activity.sign_in;

import android.os.Handler;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.adapter.sign_in.SignDateAdapter;
import com.cloudy.linglingbang.app.util.SignCalendarUtils;
import com.cloudy.linglingbang.app.util.SignDateUtils;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BackgroundSubscriber;
import com.cloudy.linglingbang.model.sign_in.SignInfo;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import butterknife.OnClick;

/**
 * 日历的要求：
 * 日历前后填充完整，改变颜色以区分
 * 日历数据填充的思路：
 * 1、计算选定年月份的第一天是星期几，通过mCalendar.get(Calendar.DAY_OF_WEEK)（注意Calendar中的月份是从0-11）
 * 1：星期日，2：星期一，以此类推，而实际日历中星期日从0开始，古将其作为作为开始的位置计算，所以➖1
 * 2、在1的基础上，计算出上月需要补几天的数据，故需要知道上月有多少天，上个月的日期计算为：lastMonthDays-firstPosition+i+1
 * 3、要求日历前后补齐，故肯定是7的倍数，却不能知道具体是几行
 * 所以将1，2的日期相加对7求余，得出的是最后一行所占的位数
 * 所以最后一行需要补充的就是7-上边的余数
 * 4、由于显示的要求，所以签到model中设置枚举，非本月的，本月未过的，本月已过已签到，本月已过未签到，本月已过补签的，今日已签到，今日未签到
 */
public class SignInActivity extends BaseActivity {

    /**
     * 月日历的recycleview
     */
    @BindView(R.id.gv_sign_date)
    RecyclerView mGvSignDate;

    /**
     * 当前月份的显示
     */
    @BindView(R.id.tv_current_date)
    TextView mTvCurrentDate;

    /**
     * 月日历的布局
     */
    @BindView(R.id.ll_calendar_month)
    RelativeLayout mLlCalendarMonth;

    /**
     * 连续签到天数
     */
    @BindView(R.id.tv_continue_sign_days)
    TextView mTvContinueSignDays;

    SignDateAdapter mSignDateAdapter;

    /**
     * 当前选中的年份
     */
    private int mCurrentYear;
    /**
     * 当前选中的月份
     */
    private int mCurrentMonth;

    /**
     * 当前点击的日期
     */
    private int mClickDay = 0;
    /**
     * 真实的当前月，后台返回的为准
     */
    private int mRealCurrentMonth;
    /**
     * 真实的当前年，后台返回的为准
     */
    private int mRealCurrentYear;
    /**
     * 真实的当前日，后台返回的为准
     */
    private int mRealCurrentDay;
    /**
     * 缓存的增量月份值
     */
    private int cacheIncreaseMonth;
    /**
     * 缓存的减量月份值
     */
    private int cacheReductionMonth;
    /**
     * 缓存的增量年份值
     */
    private int cacheIncreaseYear;
    /**
     * 缓存的减量年份值
     */
    private int cacheReductionYear;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_sign_in);
    }

    @Override
    protected void initialize() {
        //设置toolbar的样式
        setMiddleTitle(getResources().getString(R.string.sign_in_calendar));

        mCurrentYear = SignDateUtils.getCurrentYear();
        mCurrentMonth = SignDateUtils.getCurrentMonth();
        mTvCurrentDate.setText(getString(R.string.sign_calendar_current_day, mCurrentYear, mCurrentMonth));
        getSignInfoAndShow(mCurrentYear, mCurrentMonth);
        //缓存日历数据
        initCacheYearAndMonth();
        //月日历的adapter
        GridLayoutManager gridLayoutManager = new GridLayoutManager(this, 7);
        mGvSignDate.setLayoutManager(gridLayoutManager);
        mSignDateAdapter = new SignDateAdapter(this, SignCalendarUtils.mSignDateModelList);
        mGvSignDate.setAdapter(mSignDateAdapter);

    }

    private void initCacheYearAndMonth() {
        if (mCurrentMonth == 12) {
            cacheIncreaseYear = mCurrentYear + 1;
            cacheIncreaseMonth = 1;
        } else {
            cacheIncreaseYear = mCurrentYear;
            cacheIncreaseMonth = mCurrentMonth + 1;
        }
        if (mCurrentMonth == 1) {
            cacheReductionYear = mCurrentYear - 1;
            cacheReductionMonth = 12;
        } else {
            cacheReductionYear = mCurrentYear;
            cacheReductionMonth = mCurrentMonth - 1;
        }
        //延迟请求，为了是先把当前月的数据请求出来再缓存
        new Handler().postDelayed(new Runnable() {
            public void run() {
                getSignInfoAndShow(cacheIncreaseYear, cacheIncreaseMonth);
                getSignInfoAndShow(cacheReductionYear, cacheReductionMonth);
            }
        }, 500);
    }

    /**
     * 获取指定月份的签到数据
     */
    public void getSignInfoAndShow(final int year, final int month) {
        L00bangRequestManager2
                .getServiceInstance()
                .getSignInfoByMonth(SignDateUtils.formatYearAndMonth(year, month))
                .compose(L00bangRequestManager2.<SignInfo>setSchedulers())
                .subscribe(new BackgroundSubscriber<SignInfo>(this) {
                    @Override
                    public void onSuccess(SignInfo signInfo) {
                        super.onSuccess(signInfo);
                        if (signInfo != null) {
                            //赋值给当前真实的年月
                            mRealCurrentYear = SignDateUtils.getRealCurrentYear(signInfo.getToday());
                            mRealCurrentMonth = SignDateUtils.getRealCurrentMonth(signInfo.getToday());
                            mRealCurrentDay = SignDateUtils.getRealCurrentDay(signInfo.getToday());

                            //setData(signInfo);
                            SignCalendarUtils.setData(signInfo, year, month);
                            //请求本月的时候给adapter填充数据
                            if (month == mCurrentMonth) {
                                mSignDateAdapter.setData(SignCalendarUtils.map.get(mCurrentYear + "-" + mCurrentMonth));
                                mTvContinueSignDays.setText(String.valueOf(signInfo.getDailySignCount()));

                                if (signInfo.getSignMonthMedalCount() == -1) {

                                } else {
                                    if (signInfo.getSignMonthMedalCount() - signInfo.getSignTotal() <= 0) {

                                    } else if (signInfo.getSignMonthMedalCount() - signInfo.getSignTotal() > 0) {

                                    }
                                }

                            }
                        }
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                        SignCalendarUtils.setData(year, month);
                        mSignDateAdapter.setData(SignCalendarUtils.map.get(mCurrentYear + "-" + mCurrentMonth));
                        ToastUtil.showMessage(SignInActivity.this, e.getMessage());
                    }
                });
    }

    @OnClick({R.id.iv_to_left, R.id.iv_to_right})
    void clicks(View view) {
        switch (view.getId()) {
            case R.id.iv_to_left:
                if (mCurrentYear <= SignCalendarUtils.minYear && mCurrentMonth <= SignCalendarUtils.minMonth) {
                    return;
                }
                if (mCurrentMonth == 1) {
                    mCurrentYear -= 1;
                    mCurrentMonth = 12;
                } else {
                    mCurrentMonth -= 1;
                }
                //更新adapter
                setAdapterData();
                if (mCurrentMonth == 1) {
                    cacheReductionYear = mCurrentYear - 1;
                    cacheReductionMonth = 12;
                } else {
                    cacheReductionYear = mCurrentYear;
                    cacheReductionMonth = mCurrentMonth - 1;
                }
                getSignInfoAndShow(cacheReductionYear, cacheReductionMonth);
                break;
            case R.id.iv_to_right:
                if (mCurrentYear > SignCalendarUtils.maxYear) {
                    return;
                }
                if (mCurrentMonth == 12) {
                    mCurrentYear += 1;
                    mCurrentMonth = 1;
                } else {
                    mCurrentMonth += 1;
                }
                //更新adapter
                setAdapterData();
                if (mCurrentMonth == 12) {
                    cacheIncreaseYear = mCurrentYear + 1;
                    cacheIncreaseMonth = 1;
                } else {
                    cacheIncreaseYear = mCurrentYear;
                    cacheIncreaseMonth = mCurrentMonth + 1;
                }
                getSignInfoAndShow(cacheIncreaseYear, cacheIncreaseMonth);
                break;
        }

    }

    private void setAdapterData() {
        if (SignCalendarUtils.map.get(mCurrentYear + "-" + mCurrentMonth) == null || SignCalendarUtils.map.get(mCurrentYear + "-" + mCurrentMonth).size() <= 0) {
            getSignInfoAndShow(mCurrentYear, mCurrentMonth);
        } else {
            mSignDateAdapter.setData(SignCalendarUtils.map.get(mCurrentYear + "-" + mCurrentMonth));
        }
        mTvCurrentDate.setText(getString(R.string.sign_calendar_current_day, mCurrentYear, mCurrentMonth));
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        SignCalendarUtils.mSignDateModelList2.clear();
        SignCalendarUtils.map.clear();
    }
}
