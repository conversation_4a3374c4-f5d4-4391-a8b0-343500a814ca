package com.cloudy.linglingbang.activity.fragment.store.hotel;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Paint;
import android.text.SpannableString;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ModelUtils;
import com.cloudy.linglingbang.app.util.span.ForegroundColorAndAbsoluteSizeSpan;
import com.cloudy.linglingbang.app.widget.dialog.hotel.HotelUtil;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.hotel.HotelList;

import java.util.List;

/**
 * 酒店列表UI
 *
 * <AUTHOR>
 * @date 2022/3/16
 */
public class HotelAdapter extends BaseRecyclerViewAdapter<HotelList> {
    public HotelAdapter(Context context, List<HotelList> data) {
        super(context, data);
    }

    @Override
    protected BaseRecyclerViewHolder<HotelList> createViewHolderWithViewType(View itemView, int viewType) {
        if (viewType == HotelDetailAdapter.ViewType.TYPE_EMPTY) {
            return new EmptyViewHolder(itemView);
        }
        return super.createViewHolderWithViewType(itemView, viewType);
    }

    @Override
    protected BaseRecyclerViewHolder<HotelList> createViewHolder(View itemView) {
        return new HotelViewHolder(itemView);
    }

    @Override
    public int getItemViewType(int position) {
        if (HotelList.EMPTY.equals(mData.get(position))) {
            return HotelDetailAdapter.ViewType.TYPE_EMPTY;
        }
        return super.getItemViewType(position);
    }

    @Override
    protected int getItemLayoutRes(int viewType) {
        if (HotelDetailAdapter.ViewType.TYPE_EMPTY == viewType) {
            return R.layout.fragment_empty_absolute;
        }
        return R.layout.item_hotel;
    }

    private static class HotelViewHolder extends BaseRecyclerViewHolder<HotelList> {
        ImageView mIvHotelImg;
        TextView mTvHotelName;
        TextView mTvDealerAddress;
        TextView mTvAddressName;
        TextView mTvPrice;
        TextView mTvUnit;
        TextView mTvOriginalPrice;

        public HotelViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            mIvHotelImg = itemView.findViewById(R.id.iv_image);
            mTvHotelName = itemView.findViewById(R.id.tv_title);
            mTvDealerAddress = itemView.findViewById(R.id.tv_dealer_address);
            mTvAddressName = itemView.findViewById(R.id.tv_address);
            mTvPrice = itemView.findViewById(R.id.tv_price);
            mTvUnit = itemView.findViewById(R.id.tv_unit);
            mTvOriginalPrice = itemView.findViewById(R.id.tv_original_price);
            mTvOriginalPrice.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG);
        }

        @Override
        public void bindTo(HotelList hotel, int position) {
            super.bindTo(hotel, position);
            Resources resources = itemView.getResources();
            HotelUtil.loadHotelImage(mIvHotelImg, hotel.getImageUrl(), 0);
            //距离
            if (hotel.getDistance() == null || hotel.getDistance() == 0) {
                mTvDealerAddress.setText(null);
            } else {
                if (hotel.getDistance() < 1000) {
                    mTvDealerAddress.setText(resources.getString(R.string.txt_hotel_distance, hotel.getDistance() + "m"));
                } else {
                    int d = (int) (hotel.getDistance() / 100);
                    mTvDealerAddress.setText(resources.getString(R.string.txt_hotel_distance, (d / 10 + "." + (d % 10) + "km")));
                }
            }
            //地址
            mTvAddressName.setText(hotel.getAddress());
            //最低价格
            String p1 = AppUtil.convertBigDecimalPrice(hotel.getMiniRealHousePrice(), "");
            mTvPrice.setText(ModelUtils.getRmbOrEmptyString(p1, true));
            //最低门市价
            String p = AppUtil.convertBigDecimalPrice(hotel.getLowestMarketPrice(), "");
            if (TextUtils.isEmpty(p)) {
                mTvOriginalPrice.setText(null);
                mTvUnit.setVisibility(View.INVISIBLE);
            } else {
                if (p.equalsIgnoreCase(p1)) {
                    mTvPrice.setText(null);
                }
                mTvOriginalPrice.setText(ModelUtils.getRmbOrEmptyString(p, true));
                mTvUnit.setVisibility(View.VISIBLE);
            }

            //酒店名称
            String name = hotel.getInnName() + " " + hotel.getInnTypeStr();
            SpannableString spannableString = new SpannableString(name);
            Object span = new ForegroundColorAndAbsoluteSizeSpan(resources.getColor(R.color.color_868990), resources.getDimensionPixelSize(R.dimen.activity_set_text_24));
            spannableString.setSpan(span, name.lastIndexOf(" "), name.length(), SpannableString.SPAN_INCLUSIVE_INCLUSIVE);
            mTvHotelName.setText(spannableString);
        }
    }

    private static class EmptyViewHolder extends BaseRecyclerViewHolder<HotelList> {
        TextView mTvEmpty;
        ImageView mIvEmpty;

        public EmptyViewHolder(View itemView) {
            super(itemView);
            mIvEmpty = itemView.findViewById(R.id.iv_empty);
            mTvEmpty = itemView.findViewById(R.id.tv_empty_desc);
        }

        @Override
        public void bindTo(HotelList hotelList, int position) {
            super.bindTo(hotelList, position);
            mIvEmpty.setImageResource(R.drawable.ic_hotel_place_no_txt);
            mTvEmpty.setText(R.string.empty_today);
        }
    }
}
