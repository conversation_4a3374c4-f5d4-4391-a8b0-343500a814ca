package com.cloudy.linglingbang.activity.car.home;

import android.content.Context;
import android.content.Intent;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.welfare.CarInfoEvent;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.widget.imagetext.ImageTextAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.app.widget.recycler.TransparentGridItemDecoration;
import com.cloudy.linglingbang.model.entrance.FunctionEntrance;
import com.cloudy.linglingbang.model.entrance.HomeFunctionResult;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;

import java.util.List;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.functions.Function;

/**
 * 更多服务页面
 *
 * <AUTHOR>
 * @date 2020-02-29
 */
public class MyCarMoreServiceActivity extends BaseRecyclerViewRefreshActivity<FunctionEntrance> {

    /**
     * 显示几列
     */
    private static final int SPAN = 3;

    private static final String EXTRA_POSITION = "position";

    private int mPosition;

    @Override
    protected void initialize() {
        super.initialize();
        setTitle("更多服务");
        mPosition = getIntent().getIntExtra(EXTRA_POSITION, -1);
        if (mPosition < 0) {
            return;
        }
    }

    public static void startActivity(Context context, int position) {
        Intent intent = new Intent(context, MyCarMoreServiceActivity.class);
        intent.putExtra(EXTRA_POSITION, position);
        context.startActivity(intent);
    }

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_my_car_more_service);
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(final List<FunctionEntrance> list) {
        MyCarServiceAdapter myCarServiceAdapter = new MyCarServiceAdapter(this, list);
        myCarServiceAdapter.setOnItemClickListener(new BaseRecyclerViewHolder.OnItemClickListener() {
            @Override
            public void onItemClick(View itemView, int position) {
                JumpPageUtil.goHomeFunctionEntrance(itemView.getContext(), list.get(position), null,new CarInfoEvent(""));
            }
        });
        return myCarServiceAdapter;
    }

    @Override
    public Observable<BaseResponse<List<FunctionEntrance>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return L00bangRequestManager2.getServiceInstance().getHomeFunctionList(mPosition)
                .map(new Function<BaseResponse<HomeFunctionResult>, BaseResponse<List<FunctionEntrance>>>() {
                    @Override
                    public BaseResponse<List<FunctionEntrance>> apply(BaseResponse<HomeFunctionResult> listBaseResponse) {
                        if (listBaseResponse.getData() == null) {
                            return listBaseResponse.cloneWithData(null);
                        }
                        List<FunctionEntrance> functionEntrances = listBaseResponse.getData().getFunctionEntranceList();
                        return listBaseResponse.cloneWithData(functionEntrances);
                    }
                });
    }

    @Override
    public RefreshController<FunctionEntrance> createRefreshController() {
        return new RefreshController<FunctionEntrance>(this) {
            @Override
            protected RecyclerView.LayoutManager createLayoutManager(Context context) {
                return new GridLayoutManager(context, SPAN);
            }

            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                int px30 = context.getResources().getDimensionPixelOffset(R.dimen.normal_70);
                return new TransparentGridItemDecoration(SPAN, 0, px30);
            }

            @Override
            public int getEmptyBackgroundColorId() {
                return R.color.white;
            }
        };
    }

    protected int getFunctionItemLayoutResId(int type) {
        return R.layout.item_my_car_function;
    }

    /**
     * 可以提供布局
     */
    public class MyCarServiceAdapter extends ImageTextAdapter<FunctionEntrance> {

        public MyCarServiceAdapter(Context context, List<FunctionEntrance> data) {
            super(context, data);
        }

        @Override
        protected int getItemLayoutRes(int viewType) {
            return getFunctionItemLayoutResId(viewType);
        }
    }
}
