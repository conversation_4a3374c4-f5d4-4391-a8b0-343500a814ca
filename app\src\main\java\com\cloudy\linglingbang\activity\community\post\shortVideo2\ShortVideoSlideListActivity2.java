package com.cloudy.linglingbang.activity.community.post.shortVideo2;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.Window;
import android.view.WindowManager;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.app.util.NetWatchdog;
import com.cloudy.linglingbang.app.util.ShareUtil;
import com.cloudy.linglingbang.app.util.StatusBarUtils;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.constants.ConfigConstant;
import com.cloudy.linglingbang.constants.WebUrlConfigConstant;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.NormalSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.shortvideo.AliSTSTokenInfo;
import com.cloudy.linglingbang.model.user.User;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 短视频列表
 *
 * <AUTHOR>
 * @date 2020/9/16
 */
public class ShortVideoSlideListActivity2 extends BaseActivity {

    public static final int SHORT_VIDEO_DEFAULT_PAGE_SIZE = 20;
    public static final String INTENT_EXTRA_COLUMN_ID = "INTENT_EXTRA_COLUMN_ID";
    public static final String INTENT_EXTRA_COLUMN_POSITION = "INTENT_EXTRA_COLUMN_POSITION";
    public static final String INTENT_EXTRA_POST_ID = "INTENT_EXTRA_POST_ID";
    public static final String INTENT_EXTRA_OPEN_FROM = "INTENT_EXTRA_OPEN_FROM";
    public static final String INTENT_EXTRA_SHARE_GONE = "INTENT_EXTRA_SHARE_GONE";

    /**
     * 当前位置
     */
    public static final String INTENT_EXTRA_POSITION = "INTENT_EXTRA_POSITION";

    /**
     * 帖子列表
     */
    public static final String INTENT_EXTRA_POSTS = "INTENT_EXTRA_POSTS";

    /**
     * 单个帖子
     */
    public static final String INTENT_EXTRA_SINGLE_POST = "INTENT_EXTRA_SINGLE_POST";

    /**
     * 视频列表播放view
     */
    @BindView(R.id.video_play)
    AlivcVideoPlayView videoPlayView;

    /**
     * 判断视频是否正在显示视频列表
     */
    private boolean isHome = true;
    /**
     * 网络状态监听器
     */
    private NetWatchdog netWatchdog;

    /**
     * 帖子列表
     */
    List<PostCard> mPostCards;

    private int mPageNo = -1;

    private int mPosition;

    private long mColumnPosition;

    private long mPostId;

    /**
     * 是否加载更多
     */
    private boolean mIsLoadMore;

    /**
     * 来源
     */
    private int mOpenFrom;

    private ShareUtil mShareUtil;

    /**
     * 是否隐藏分享:0不隐藏；1隐藏
     */
    private int mIsHideShare;

    /**
     * 起始的帖子id
     */
    private long mStartPostId;

    /**
     * 终止的帖子id
     */
    private long mEndPostId;

    public static class OpenFrom {
        public static final int FROM_POST = 0;

        public static final int FROM_COLUMN = 1;

        public static final int FROM_HOME = 2;
    }

    /**
     * 跳转到短视频详情滑动列表
     */
    public static void startActivityByPosition(Context context, long columnId) {
        Intent intent = new Intent(context, ShortVideoSlideListActivity2.class);
        intent.putExtra(INTENT_EXTRA_COLUMN_ID, columnId);
        context.startActivity(intent);
    }

    /**
     * 单独视频帖跳转
     *
     * @param isHideShare 是否隐藏分享。0：不隐藏，1：隐藏
     */
    public static void startActivityByPostId(Context context, String postId, int isHideShare) {
        Intent intent = new Intent(context, ShortVideoSlideListActivity2.class);
        intent.putExtra(INTENT_EXTRA_POST_ID, postId);
        intent.putExtra(INTENT_EXTRA_OPEN_FROM, OpenFrom.FROM_POST);
        intent.putExtra(INTENT_EXTRA_SHARE_GONE, isHideShare);
        context.startActivity(intent);
    }

    /**
     * 单独视频帖跳转
     *
     * @param isHideShare 是否隐藏分享。0：不隐藏，1：隐藏
     */
    public static void startActivityByPostId(Context context, String postId, int isHideShare, boolean isFromNotice) {
        Intent intent = new Intent(context, ShortVideoSlideListActivity2.class);
        intent.putExtra(INTENT_EXTRA_POST_ID, postId);
        intent.putExtra(INTENT_EXTRA_OPEN_FROM, OpenFrom.FROM_POST);
        intent.putExtra(INTENT_EXTRA_SHARE_GONE, isHideShare);
        intent.putExtra(ConfigConstant.IS_FROM_NOTIFY, isFromNotice);
        context.startActivity(intent);
    }

    /**
     * 单独视频帖跳转
     *
     * @param postId 帖子id
     */
    public static void startActivityByPostId(Context context, String postId) {
        startActivityByPostId(context, postId, 0);
    }

    /**
     * 跳转到短视频详情滑动列表
     *
     * @param postCards 帖子列表
     * @param position 当前播放位置
     */
    public static void startActivityByColumnPosition(Context context, List<PostCard> postCards, int position, long columnPosition) {
        String postCardsStr = new Gson().toJson(postCards);
        BigBinder bigBinder = new BigBinder(postCardsStr.getBytes());
        Bundle bundle = new Bundle();
        bundle.putBinder("bytes",bigBinder);
        /*****************************************************************************************/
        Intent intent = new Intent(context, ShortVideoSlideListActivity2.class);
        intent.putExtra(INTENT_EXTRA_COLUMN_POSITION, columnPosition);
        intent.putExtra(INTENT_EXTRA_POSITION, position);
        intent.putExtra(INTENT_EXTRA_POSTS, bundle);
        intent.putExtra(INTENT_EXTRA_OPEN_FROM, OpenFrom.FROM_COLUMN);
        context.startActivity(intent);
    }

    /**
     * 首页跳转到短视频详情滑动列表
     */
    public static void startActivityForHome(Context context, PostCard postCard) {
        String postCardStr = new Gson().toJson(postCard);
        BigBinder bigBinder = new BigBinder(postCardStr.getBytes());
        Bundle bundle = new Bundle();
        bundle.putBinder("bytes",bigBinder);
        /*****************************************************************************************/
        Intent intent = new Intent(context, ShortVideoSlideListActivity2.class);
        intent.putExtra(INTENT_EXTRA_SINGLE_POST, bundle);
        intent.putExtra(INTENT_EXTRA_OPEN_FROM, OpenFrom.FROM_HOME);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        super.onCreate(savedInstanceState);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        StatusBarUtils.setFullScreenTransparentStatusBarAndWhiteText(this);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            WindowManager.LayoutParams localLayoutParams = getWindow().getAttributes();
            localLayoutParams.flags = (WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS | localLayoutParams.flags);
        }
    }

    @Override
    protected void initialize() {
        //先调用一次凭证接口
        StsInfoManager.getInstance().refreshStsToken(ShortVideoSlideListActivity2.this, new MyStsResultListener(ShortVideoSlideListActivity2.this));
        mPostCards = new ArrayList<>();
        mOpenFrom = getIntent().getIntExtra(INTENT_EXTRA_OPEN_FROM, OpenFrom.FROM_POST);
        if (mOpenFrom == OpenFrom.FROM_POST) {
            //从独立的帖子进入
            String postIdStr = getIntent().getStringExtra(INTENT_EXTRA_POST_ID);
            if (!TextUtils.isEmpty(postIdStr)) {
                mPostId = Long.valueOf(postIdStr);
            }
        } else if (mOpenFrom == OpenFrom.FROM_COLUMN) {
            //从首页视频位置进入
            mColumnPosition = getIntent().getLongExtra(INTENT_EXTRA_COLUMN_POSITION, 0);
            mPosition = getIntent().getIntExtra(INTENT_EXTRA_POSITION, 0);
            Bundle bunder = getIntent().getBundleExtra(INTENT_EXTRA_POSTS);
            BigBinder bigBinder = (BigBinder) bunder.getBinder("bytes");
            String postCardsStr = new String(bigBinder.getBytes());
            if (postCardsStr!= null && !postCardsStr.equals("")){
                mPostCards = new Gson().fromJson(postCardsStr,new TypeToken<List<PostCard>>(){}.getType());
            }
            //mPostCards = (ArrayList<PostCard>) getIntent().getSerializableExtra(INTENT_EXTRA_POSTS);
        } else if (mOpenFrom == OpenFrom.FROM_HOME) {
            //如果是从可以下滑的帖子进入
            Bundle bunder = getIntent().getBundleExtra(INTENT_EXTRA_SINGLE_POST);
            BigBinder bigBinder = (BigBinder) bunder.getBinder("bytes");
            String postCardStr = new String(bigBinder.getBytes());
            if (postCardStr!= null && !postCardStr.equals("")){
                PostCard postCard = new Gson().fromJson(postCardStr,PostCard.class);
                //PostCard postCard = (PostCard) getIntent().getSerializableExtra(INTENT_EXTRA_SINGLE_POST);
                if (postCard != null) {
                    mPostCards.add(postCard);
                    mStartPostId = Long.valueOf(postCard.getPostId());
                    mEndPostId = Long.valueOf(postCard.getPostId());
                }
            }
        }
        mIsHideShare = getIntent().getIntExtra(INTENT_EXTRA_SHARE_GONE, 0);
        // 网络监听
        initNetWatchDog();
        //初始化页面
        initView();
//        initLiveView();
        if (mOpenFrom == OpenFrom.FROM_COLUMN) {
            preLoadPlayList(mPageNo);
        } else if (mOpenFrom == OpenFrom.FROM_HOME) {
            preLoadPlayListFoeHome();
        } else {
            loadSinglePost();
        }
    }

    /**
     * 加载单独的帖子
     */
    private void loadSinglePost() {
        L00bangRequestManager2
                .getServiceInstance()
                .getPostDetails(mPostId)
                .compose(L00bangRequestManager2.<PostCard>setSchedulers())
                .subscribe(new ProgressSubscriber<PostCard>(this) {
                    @Override
                    public void onSuccess(PostCard postCard) {
                        super.onSuccess(postCard);
                        if (postCard != null) {
                            mPostCards.add(postCard);
                            mIsLoadMore = false;
                            videoPlayView.refreshVideoList(mPostCards);
                        }
                    }
                });
    }

    @Override
    protected void loadViewLayout() {
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            WindowManager.LayoutParams localLayoutParams = getWindow().getAttributes();
            localLayoutParams.flags = (WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS | localLayoutParams.flags);
        }
        setContentView(R.layout.activity_video_list);
    }

    /**
     * 网络监听
     */
    public void initNetWatchDog() {
        netWatchdog = new NetWatchdog(this);
        netWatchdog.setNetConnectedListener(new MyNetConnectedListener(ShortVideoSlideListActivity2.this));
    }

    /**
     * 初始化View
     */
    protected void initView() {
        videoPlayView.setOpenFrom(mOpenFrom);
        videoPlayView.setIsHideShare(mIsHideShare);
        videoPlayView.setOnRefreshDataListener(new MyRefreshDataListener(this));
        videoPlayView.setOnStsInfoExpiredListener(new OnStsInfoExpiredListener() {
            @Override
            public void onTimeExpired() {
                //刷新获取STS
                StsInfoManager.getInstance().refreshStsToken(ShortVideoSlideListActivity2.this, new MyStsResultListener(ShortVideoSlideListActivity2.this));
            }

            @Override
            public AliSTSTokenInfo refreshSts() {
                //刷新获取STS
                return StsInfoManager.getInstance().refreshStsToken();
            }
        });
    }

    /**
     * 联网加载列表前的准备
     */
    private void preLoadPlayList(int pageNo) {
        //第一次进入，如果传入列表，则将列表的值赋值进来
        if (pageNo == -1) {
            int count = mPostCards.size();
            if (count > 0 && mPosition < count) {
                if (count % 20 == 0) {
                    mIsLoadMore = true;
                    mPageNo = count / 20 + 1;
                    //如果是最后一条，则加载下一页
                    if (mPosition == mPostCards.size() - 1) {
                        loadPlayList(mPageNo);
                    }
                } else {
                    mIsLoadMore = false;
                }
                videoPlayView.refreshVideoList(mPostCards, mPosition);
            }
        }
    }

    /**
     * 联网加载列表前的准备
     */
    private void preLoadPlayListFoeHome() {
        mIsLoadMore = true;
        //请求下一页
        loadPlayListForHome();
        //加载当前视频
        videoPlayView.refreshVideoList(mPostCards);

    }

    /**
     * 获取播放列表数据
     *
     * @param pageNo 请求第pageNo页数据
     */
    private void loadPlayList(final int pageNo) {
        L00bangRequestManager2
                .getServiceInstance()
                .getPostsByColumnPosition(mColumnPosition, pageNo, SHORT_VIDEO_DEFAULT_PAGE_SIZE)
                .compose(L00bangRequestManager2.<List<PostCard>>setSchedulers())
                .subscribe(new NormalSubscriber<List<PostCard>>(this) {
                    @Override
                    public void onSuccess(List<PostCard> postCards) {
                        super.onSuccess(postCards);
                        if (postCards != null && postCards.size() > 0) {
                            mPostCards.addAll(postCards);
                            if (mPageNo <= 1) {
                                videoPlayView.refreshVideoList(postCards);
                            } else {
                                videoPlayView.addMoreData(postCards);
                            }
                            if (postCards.size() < SHORT_VIDEO_DEFAULT_PAGE_SIZE) {
                                mIsLoadMore = false;
                            } else {
                                mIsLoadMore = true;
                                mPageNo++;
                            }

                        } else {
                            mIsLoadMore = false;
                        }
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                        ToastUtil.showMessage(ShortVideoSlideListActivity2.this, "加载失败~");
                        if (videoPlayView != null) {
                            videoPlayView.loadFailure();
                        }
                    }
                });
    }

    /**
     * 获取播放列表数据（推荐页）
     */
    private void loadPlayListForHome() {
        Map<String, Object> m = new HashMap<>();
        m.put("postId", mStartPostId);
        m.put("endPostId", mEndPostId);
        m.put("pageSize", SHORT_VIDEO_DEFAULT_PAGE_SIZE);
        L00bangRequestManager2
                .getServiceInstance()
                .getVideoPostList(m)
                .compose(L00bangRequestManager2.<List<PostCard>>setSchedulers())
                .subscribe(new NormalSubscriber<List<PostCard>>(this) {
                    @Override
                    public void onSuccess(List<PostCard> postCards) {
                        super.onSuccess(postCards);
                        if (postCards != null && postCards.size() > 0) {
                            mPostCards.addAll(postCards);
                            videoPlayView.addMoreData(postCards);
                            if (postCards.size() < SHORT_VIDEO_DEFAULT_PAGE_SIZE) {
                                mIsLoadMore = false;
                            } else {
                                mIsLoadMore = true;
                            }
                            mStartPostId = Long.valueOf(postCards.get(postCards.size() - 1).getPostId());

                        } else {
                            mIsLoadMore = false;
                        }
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                        ToastUtil.showMessage(ShortVideoSlideListActivity2.this, "加载失败~");
                        if (videoPlayView != null) {
                            videoPlayView.loadFailure();
                        }
                    }
                });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE_COMMON && resultCode == Activity.RESULT_OK) {
            //帖子举报成功后返回设置状态
            //若不为空则直接设置举报状态，否则重新获取
            String postId = data.getStringExtra("postId");
//            if (postId != null) {
//                for (PostCard postCard : mPostCards) {
//                    if (postCard.getPostId().equals(postId)) {
//                        postCard.setProsecutionUncheck(1);
//                        mAdapter.notifyDataSetChanged();
//                    }
//                }
//            }
        } else {
            if (mShareUtil != null) {
                mShareUtil.onActivityResult(requestCode, resultCode, data);
            }
        }

    }

    @Override
    protected void onResume() {
        super.onResume();
        if (isHome) {
            videoPlayView.onResume();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (isHome) {
            videoPlayView.onPause();
        }

    }

    @Override
    protected void onStart() {
        super.onStart();
        netWatchdog.startWatch();

    }

    @Override
    protected void onStop() {
        super.onStop();
        netWatchdog.stopWatch();

    }

    @Override
    protected void onDestroy() {

        if (videoPlayView != null) {
            videoPlayView.onDestroy();
        }

        super.onDestroy();
    }



    /**
     * 视频播放列表刷新、加载更多事件监听
     */
    private static class MyRefreshDataListener implements AlivcVideoListView.OnRefreshDataListener {
        WeakReference<ShortVideoSlideListActivity2> weakReference;

        MyRefreshDataListener(ShortVideoSlideListActivity2 activity) {
            weakReference = new WeakReference<>(activity);
        }

        @Override
        public void onRefresh() {

            ShortVideoSlideListActivity2 activity = weakReference.get();
            if (activity != null) {
                activity.mPageNo = 1;
                activity.loadPlayList(activity.mPageNo);
            }
        }

        @Override
        public void onLoadMore() {
            ShortVideoSlideListActivity2 activity = weakReference.get();
            if (activity != null && activity.mIsLoadMore) {
                activity.loadPlayList(activity.mPageNo);
            }
        }
    }

    /**
     * sts刷新监听
     */
    private static class MyStsResultListener implements OnStsResultListener {
        WeakReference<ShortVideoSlideListActivity2> weakReference;

        MyStsResultListener(ShortVideoSlideListActivity2 activity) {
            weakReference = new WeakReference<>(activity);
        }

        @Override
        public void onSuccess(AliSTSTokenInfo tokenInfo) {

            ShortVideoSlideListActivity2 videoListActivity = weakReference.get();
            // videoListActivity.videoPlayView刷新sts信息
            videoListActivity.videoPlayView.refreshStsInfo(tokenInfo);

        }

        @Override
        public void onFail() {

        }
    }

    /**
     * 网络断开重连监听
     */
    private class MyNetConnectedListener implements NetWatchdog.NetConnectedListener {
        private WeakReference<ShortVideoSlideListActivity2> weakReference;

        MyNetConnectedListener(ShortVideoSlideListActivity2 activity) {
            weakReference = new WeakReference<>(activity);
        }

        @Override
        public void onReNetConnected(boolean isReconnect) {
            if (isReconnect) {
                //刷新获取STS
                StsInfoManager.getInstance().refreshStsToken(ShortVideoSlideListActivity2.this, new MyStsResultListener(weakReference.get()));
                //网络重连
                Log.e("Test", "onReNetConnected......");
            }
        }

        @Override
        public void onNetUnConnected() {
            ShortVideoSlideListActivity2 videoListActivity = weakReference.get();
            if (videoListActivity != null) {
                ToastUtil.showMessage(videoListActivity, videoListActivity.getString(R.string.kind_error_network_connect));
            }
            //网络断开
            Log.e("Test", "onNetUnConnected......");
        }
    }

    @OnClick(R.id.iv_back)
    public void onBackPress() {
        onBackPressed();
    }

    /**
     * 分享
     */
    public void sharePost(PostCard postCard) {
        if (postCard != null) {
            if (mShareUtil == null) {
                mShareUtil = ShareUtil.newInstance();
            }
            String postContent = "";
            if (postCard.getImgTexts() != null && postCard.getImgTexts().size() > 0) {
                postContent = postCard.getImgTexts().get(0).getText();
            }
            String shareUrl;
            if (postCard.getPostTypeId() != null && postCard.getPostTypeId().equals(String.valueOf(PostCard.PostType.SHORT_VIDEO))) {
                shareUrl = String.format(Locale.getDefault(), WebUrlConfigConstant.POST_DETAIL_SHORT_VIDEO_SHARE_PATTERN, postCard.getPostId());
            } else {
                shareUrl = String.format(Locale.getDefault(), WebUrlConfigConstant.POST_DETAIL_SHARE_PATTERN, postCard.getPostId());
            }
            String shareTitle = TextUtils.isEmpty(postCard.getPostTitle()) ? getString(R.string.share_post_default_title) : postCard.getPostTitle();
            boolean isChangeTitle = false;
            //如果标题是空，咋替换标题为内容
            if (TextUtils.isEmpty(postCard.getPostTitle())) {
                isChangeTitle = true;
            }
            String[] images = postCard.getImages();
            if (shareUrl == null) {
                shareUrl = WebUrlConfigConstant.SHARE_LLB;
            } else {
                User user = User.getsUserInstance();
                if (UserUtils.hasLogin(user)) {
                    shareUrl = String.format(ShareUtil.POST_SHARE_PARAM, shareUrl, User.getsUserInstance().getUserIdStr());
                }
            }
            if (postContent == null) {
                postContent = "";
            }
            if (postContent.length() > 50) {
                postContent = postContent.substring(0, 50);
            }
            mShareUtil.setShareType(ShareUtil.ShareType.SHARE_TYPE_POST);
            mShareUtil.setShareActivityId(Long.valueOf(postCard.getPostId()));
            mShareUtil.setPostType(PostCard.PostType.SHORT_VIDEO);
            mShareUtil.share(this, shareUrl, postContent, (images != null && images.length > 0) ? Arrays.asList(images) : null, shareTitle, isChangeTitle);
        }
    }

}
