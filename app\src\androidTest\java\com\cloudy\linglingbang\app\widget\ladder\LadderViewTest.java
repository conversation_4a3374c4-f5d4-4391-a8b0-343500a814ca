package com.cloudy.linglingbang.app.widget.ladder;

import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import com.cloudy.linglingbang.BaseInstrumentedTest;
import com.cloudy.linglingbang.R;

/**
 * 阶梯 View 测试
 *
 * <AUTHOR>
 * @date 2019/6/25
 */
public class LadderViewTest extends BaseInstrumentedTest {
    @Override
    public void test() {
        super.test();
        testByInflate();
    }

    private void testByInflate() {
        LinearLayout linearLayout = new LinearLayout(getContext());
        linearLayout.setOrientation(LinearLayout.VERTICAL);
        final LadderView ladderView = (LadderView) LayoutInflater.from(getContext()).inflate(R.layout.layout_ladder_view, linearLayout, false);
        ladderView.updateEditMode();
        linearLayout.addView(ladderView);

        EditText editText = new EditText(getContext());
        editText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                float value = 0F;
                try {
                    value = Float.parseFloat(s.toString());
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                }
                ladderView.setCurrentStep(value);
            }
        });
        linearLayout.addView(editText, new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));

        getActivity().setContentView(linearLayout);
    }

    private void testByNew() {
        FrameLayout frameLayout = new FrameLayout(getContext());

        LadderView ladderView = new LadderView(getContext());
        ladderView.updateEditMode();

        int height = getContext().getResources().getDimensionPixelOffset(R.dimen.normal_180);
        ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, height);
        frameLayout.addView(ladderView, layoutParams);

        getActivity().setContentView(frameLayout);
    }
}