package com.cloudy.linglingbang.activity.car.energy.ble;

import java.util.HashMap;
import java.util.UUID;

/**
 * author: t21800033
 * time  : 2023/2/17
 * desc  :
 */
public interface BleCode {

    //授权时的蓝牙通信服务协议
    UUID UUID_SERVICE_AUTH = UUID.fromString("0000181A-0000-1000-8000-00805F9B34FB");
    UUID UUID_CHARACTERISTIC_WRITE_AUTH = UUID.fromString("00002A6E-0000-1000-8000-00805F9B34FB");
    UUID UUID_CHARACTERISTIC_NOTIFY_AUTH = UUID.fromString("00002A6F-0000-1000-8000-00805F9B34FB");

    //控制时的蓝牙通信服务协议
    UUID UUID_SERVICE_CONTROL = UUID.fromString("0000182A-0000-1000-8000-00805F9B34FB");
    UUID UUID_CHARACTERISTIC_WRITE_CONTROL = UUID.fromString("00002A7E-0000-1000-8000-00805F9B34FB");
    UUID UUID_CHARACTERISTIC_NOTIFY_CONTROL = UUID.fromString("00002A7F-0000-1000-8000-00805F9B34FB");

    //    int MSG_START_CONNECT = 1;
    int MSG_CONNECT_SUCCESS = 2;
    int MSG_ERROR_BLE_DISABLE = 9001;
    int MSG_ERROR_BLE_FAIL = 9002;

    int CAUSE_KEY_BAD = 8001;
    int CAUSE_BLUETOOTH_BAD = 8012;
    int CAUSE_PERMISSIONS_BAD = 8002;
    int CAUSE_GPS_BAD = 8003;
    int CAUSE_AUTH_NOTIFY_FAILURE = 8004;
    int CAUSE_AUTH_CRC16_INCORRECT = 8005;
    int CAUSE_AUTH_RANDOM2_INCORRECT = 8006;
    int CAUSE_AUTH_QUEST_WRITE_FAILURE = 8007;
    int CAUSE_AUTH_WRITE_FAILURE = 8008;
    int CAUSE_AUTH_SET_MTU_FAIL = 8009;
    int CAUSE_AUTH_INFO_UNKNOWN = 8010;
    int CAUSE_SCAN_NOT_FIND = 8101;
    int CAUSE_SCAN_TIMEOUT = 8102;
    int CAUSE_CONTROL_DATA_ERROR = 8201;
    int CAUSE_CONTROL_WRITE_FAIL = 8202;
    int CAUSE_CONTROL_WRITE_BUSY = 8203;
    int CAUSE_CONNECT_FAIL_UNKNOWN = 8301;

    /**
     * 自动连接间隔时间。
     */
    int CONNECT_DELAY = 2 * 1000;
    int CONNECT_TIMEOUT = 8 * 1000;
    int WRITE_TIMEOUT = 5 * 1000;
    /**
     * 连续扫描最小时间间隔。
     */
    int SCAN_MIN_DELAY = 6 * 1000;
    /**
     * 扫描未响应超时时间。
     */
    int SCAN_NOT_CALLBACK_TIMEOUT = SCAN_MIN_DELAY + 1000;
    //最大传输单元
    int MTU = 100;

    /**
     * 出现认证过程中的未知数据，最大容忍次数，超出则尝试更新蓝牙钥匙
     */
    int MAX_AUTH_UNKNOWN_COUNT = 2;

    String ACC_ON = "111";
    String LIGHT_ON = "112";
    String DOOR_OPENED = "113";
    String B_DOOR_OPENED = "114";
    String CAR_RUNNING = "115";
    String GEAR_NOT_N = "116";
    String POWER_OFF_ERROR = "117";
    String KEY_ERROR = "118";
    String LOW_POWER = "119";
    String LR_LIGHT_ERROR = "120";
    String CMD_CONFLICT = "121";
    String WINDOW_ERROR = "122";
    String HORN_ERROR = "123";
    String POWER_ON_ERROR = "124";
    String UN_CHARGE = "125";
    String PARKING_TIME_OUT = "3007";
    String PARKING_OBSTACLE = "3008";
    String PARKING_EXCESSIVE = "3009";
    String PARKING_DOOR_OPENED = "300A";
    String PARKING_ERROR = "300C";
    String PARKING_FUNCTION_ERROR = "300D";
    String PARKING_TYPE_ERROR = "3010";
    String PARKING_UCU_ERROR = "300E";
    String CMD_POWER_OFF = "C0650003";
    String PARKING_POWER_ERROR = "3102";
    String PARKING_REMOTE_POWER_ERROR = "3103";
    String PARKING_NOT_READY = "3104";
    String PARKING_FAILED = "3106";
    String PARKING_MANUAL_ERROR = "3107";
    String PARKING_FUNCTION_ERROR_2 = "3108";
    String PARKING_TIME_OUT_2 = "3107";
    String PARKING_FAILED_2 = "3203";
    String PARKING_MANUAL_ERROR_2 = "3204";
    String PARKING_MODEL_ERROR = "3205";
    String PARKING_FUNCTION_ERROR_3 = "3206";
    String PARKING_TIME_OUT_3 = "3208";
    /**
     * 泊车状态
     */
    String BLE_PARKING_STATE = "30";
    String BLE_PARKING_OUT_STATE = "31";
    String BLE_PARKING_STATE2 = "32";

    HashMap<String, String> bleErrorCodeMap = new HashMap<String, String>() {{
        put(ACC_ON, "车辆已启动，请下电后重试");
        put(LIGHT_ON, "车灯未关闭");
        put(POWER_OFF_ERROR, "设置防盗失败");
        put(UN_CHARGE, "车辆未充电");
        put(POWER_ON_ERROR, "解除防盗失败");
        put(HORN_ERROR, "喇叭异常");
        put(WINDOW_ERROR, "车窗升降异常");
        put(CMD_CONFLICT, "操作过于频繁，请稍后再试");
        put(LR_LIGHT_ERROR, "转向灯异常");
        put(LOW_POWER, "小电瓶电压低");
        put(KEY_ERROR, "智能钥匙异常");
        put(GEAR_NOT_N, "档位不在N或P档");
        put(DOOR_OPENED, "车门未关，请关门后重试");
        put(B_DOOR_OPENED, "后备箱未关，请关闭后重试");
        put(CAR_RUNNING, "车辆行驶中");

        put("00", "操作失败，请稍后重试");
        put("05", "数据长度错误");
        put("07", "指令冲突");
        put("08", "车辆未响应，请稍后重试");
        put("0B", "UCU 无响应");
        put("13", "CRC 校验失败");
        put("16", "车辆未响应，请稍后重试");
        put("1E", "钥匙失效，请重启APP后重试");
        put("7F", "钥匙失效，请重启APP后重试");
    }};

    HashMap<String, String> bleParkingCodeMap = new HashMap<String, String>() {{
        put(PARKING_TIME_OUT, "遥控泊车超时");
        put(PARKING_OBSTACLE, "路径内有障碍物");
        put(PARKING_EXCESSIVE, "规划次数过多");
        put(PARKING_DOOR_OPENED, "请关好车门");
        put(PARKING_ERROR, "指令执行错误");
        put(PARKING_FUNCTION_ERROR, "功能故障");
        put(PARKING_TYPE_ERROR, "泊出方式，泊出类型错误");
        put(PARKING_POWER_ERROR, "未上高压");
        put(PARKING_REMOTE_POWER_ERROR, "未进入遥控上电");
        put(PARKING_NOT_READY, "功能未就绪");
        put(PARKING_FAILED, "出库失败");
        put(PARKING_MANUAL_ERROR, "人工接管，出库失败");
        put(PARKING_FUNCTION_ERROR_2, "功能不可用");
        put(PARKING_TIME_OUT_2, "一键泊出超时");
        put(PARKING_FAILED_2, "泊车失败");
        put(PARKING_MANUAL_ERROR_2, "人工接管");
        put(PARKING_MODEL_ERROR, "车外泊车模式不可用");
        put(PARKING_FUNCTION_ERROR_3, "功能不可用");
        put(PARKING_TIME_OUT_3, "一键泊入超时");
    }};


    byte PARK_IPA_OFF = 0x0;
    byte PARK_IPA_NO_READY = 0x1;
    byte PARK_IPA_READY = 0x2;
    byte PARK_IPA_DETECTING = 0x3;
    byte PARK_IPA_DETECTING_SUSPEND = 0x4;
    byte PARK_IPA_DETECTED = 0x5;
    byte PARK_IPA_BG_DETECTING = 0x6;
    byte PARK_IPA_BG_DETECTED = 0x7;
    byte PARK_IPA_PREPARE = 0x8;
    byte PARK_IPA_PARKING = 0x9;
    byte PARK_IPA_PARKING_SUSPEND = 0xA;
    byte PARK_IPA_FINISH_SUCCESS = 0xB;
    byte PARK_IPA_FINISH_FAILURE = 0xC;
    byte PARK_IPA_FINISH_TERMINATION = 0xD;
    byte PARK_IPA_ERROR_INT_FAULT = 0xE;
    byte PARK_IPA_ERROR_EXT_FAULT = 0xF;

    byte PARK_APO_OFF = 0x0;
    byte PARK_APO_NO_READY = 0x1;
    byte PARK_APO_READY = 0x2;
    byte PARK_APO_PREPARE = 0x3;
    byte PARK_APO_PARKING_OUT = 0x4;
    byte PARK_APO_PARKING_OUT_SUSPEND = 0x5;
    byte PARK_APO_FINISH_SUCCESS = 0x6;
    byte PARK_APO_FINISH_FAILURE = 0x7;
    byte PARK_APO_FINISH_TERMINATION = 0x8;
    byte PARK_APO_ERROR_INT_FAULT = 0x9;
    byte PARK_APO_ERROR_EXT_FAULT = 0xA;
    byte PARK_APO_PREPARE_SUSPEND = 0xB;

    //出库方向选择 0x3EA
    byte PARK_OFF = 0x0; //（无方向）
    byte PARK_STRAIGHT_FORWARD_ONLY = 0x1; //（仅向前直出）
    byte PARK_STRAIGHT_BACK_ONLY = 0x2; //（仅向后直出）
    byte PARK_RESERVERD = 0x3; //（预留）
    byte PARK_EXIT_TO_THE_LEFT_ONLY = 0x4; //（仅向左出库）
    byte PARK_STRAIGHT_FORWARD_OR_TURN_LEFT_OUTBOUND = 0x5; //（可向前直出，向左出库）
    byte PARK_STRAIGHT_BACK_OR_TURN_LEFT_OUTBOUND = 0x6; //（可向后直出，向左出库）
    byte PARK_EXIT_TO_THE_RIGHT_ONLY = 0x8; //（仅向右出库）
    byte PARK_STRAIGHT_FORWARD_OR_TURN_RIGHT_OUTBOUND = 0x9; //（可向前直出，向右出库）
    byte PARK_STRAIGHT_BACK_OR_TURN_RIGHT_OUTBOUND = 0xA; //（可向后直出，向右出库）
    byte PARK_TURN_LEFT_OUTBOUND_OR_TURN_RIGHT_OUTBOUND = 0xC; //（可向左出库，向右出库）
    byte PARK_STRAIGHT_FORWARD_OR_TURN_LEFT_OUTBOUND_OR_TURN_RIGHT_OUTBOUND = 0xD; //（可向前直出，向左出库，向右出库）
    byte PARK_STRAIGHT_BACK_OR_TURN_LEFT_OUTBOUND_OR_TURN_RIGHT_OUTBOUND = 0xE; //（可向后直出，向左出库，向右出库）

}
