package com.cloudy.linglingbang.activity.community.linglab;

import android.Manifest;
import android.app.Activity;
import android.graphics.drawable.GradientDrawable;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.my.service.ItemDragCallback;
import com.cloudy.linglingbang.app.util.ChooseImageController;
import com.cloudy.linglingbang.app.util.DeprecatedUtils;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.app.widget.recycler.EmptySupportedRecyclerView;
import com.cloudy.linglingbang.model.tag.LingLabImageBean;

import org.jetbrains.annotations.NotNull;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 选择图片设配器，带删除图片功能
 */
public class LingLabImageAdapter extends BaseRecyclerViewAdapter<LingLabImageBean> {
    private final ItemTouchHelper mItemTouchHelper;
    private final ItemDragCallback mDragCallback;
    private final RecyclerView.AdapterDataObserver mDataObserver;
    /**
     * itemView宽高
     **/
    private int mItemViewWH;
    /**
     * 选择图片地图弹出框控制器
     **/
    private final ChooseImageController mChooseImageController = null;

    private LingLabDoPostUtils mLingLabDoPostUtils;

    private Activity mActivity;

    public LingLabImageAdapter(EmptySupportedRecyclerView recyclerView, List<LingLabImageBean> data) {
        super(recyclerView.getContext(), data);
        mItemViewWH = mContext.getResources().getDimensionPixelSize(R.dimen.normal_220);
        mItemTouchHelper = new ItemTouchHelper(mDragCallback = new ItemDragCallback());
        mDragCallback.setEnable(true);
        mDragCallback.setOnItemDragListener(() -> {
            setFixedPosition();
            notifyDataSetChanged();
        });

        mItemTouchHelper.attachToRecyclerView(recyclerView);
        registerAdapterDataObserver(mDataObserver = new RecyclerView.AdapterDataObserver() {
            @Override
            public void onChanged() {
                super.onChanged();
                setFixedPosition();
            }

            @Override
            public void onItemRangeChanged(int positionStart, int itemCount) {
                super.onItemRangeChanged(positionStart, itemCount);
                setFixedPosition();
            }

            @Override
            public void onItemRangeChanged(int positionStart, int itemCount, @Nullable @org.jetbrains.annotations.Nullable Object payload) {
                super.onItemRangeChanged(positionStart, itemCount, payload);
                setFixedPosition();
            }

            @Override
            public void onItemRangeInserted(int positionStart, int itemCount) {
                super.onItemRangeInserted(positionStart, itemCount);
                setFixedPosition();
            }

            @Override
            public void onItemRangeRemoved(int positionStart, int itemCount) {
                super.onItemRangeRemoved(positionStart, itemCount);
                setFixedPosition();
            }

            @Override
            public void onItemRangeMoved(int fromPosition, int toPosition, int itemCount) {
                super.onItemRangeMoved(fromPosition, toPosition, itemCount);
                setFixedPosition();

            }
        });
        mDataObserver.onChanged();
    }

    private void setFixedPosition() {
        if (mDragCallback == null) {
            return;
        }
        mDragCallback.clearFixedPosition();
        int size = getItemCount();
        for (int i = 0; i < size; i++) {
            if (getItemViewType(i) != 2) {
                mDragCallback.addFixed(i);

            }
        }
    }


    public void setImageSize(int imageSize) {
        mItemViewWH = imageSize;
    }

    /*public void setChooseImageController(ChooseImageController chooseImageController) {
        mChooseImageController = chooseImageController;
    }*/

    @Override
    public int getItemViewType(int position) {
        return position == mData.size() - 1 ? 1 : 2;
    }

    @Override
    protected BaseRecyclerViewHolder<LingLabImageBean> createViewHolderWithViewType(View itemView, int viewType) {
        return viewType == 2 ? super.createViewHolderWithViewType(itemView, viewType) : new AddImageViewHolder(itemView);
    }

    @Override
    protected BaseRecyclerViewHolder<LingLabImageBean> createViewHolder(View itemView) {
        return new ChooseImageViewHolder(itemView);
    }

    @Override
    protected int getItemLayoutRes(int viewType) {
        return viewType == 2 ? R.layout.item_post_ling_lab_image : R.layout.item_ling_lab_post_add_image;
    }

    public void onPermissionResult(boolean isGranted, int requestCode) {
        if (requestCode == LingLabDoPostUtils.REQUEST_PHOTO_CODE) {
            if (isGranted) {
                mLingLabDoPostUtils.doChoosePhoto(1);
            } else {
                ToastUtil.showMessage(mContext, mContext.getString(R.string.ling_lab_need_permission));
            }
        }

    }

    class AddImageViewHolder extends BaseRecyclerViewHolder<LingLabImageBean> {

        public AddImageViewHolder(View itemView) {
            super(itemView);
            ViewGroup.LayoutParams layoutParams = itemView.getLayoutParams();
            layoutParams.width = mItemViewWH;
            layoutParams.height = mItemViewWH;
            itemView.setLayoutParams(layoutParams);
            ImageView iv = itemView.findViewById(R.id.iv_add_photo);

            int strokeColor = itemView.getContext().getResources().getColor(R.color.color_f8f8f8);
            GradientDrawable gd = new GradientDrawable();
            gd.setColor(strokeColor);
            gd.setCornerRadius(itemView.getContext().getResources().getDimensionPixelSize(R.dimen.normal_4));
            DeprecatedUtils.setBackgroundDrawable(itemView, gd);
            iv.setImageResource(R.drawable.ic_ling_lab_post_add_img);

            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    if (mData.contains(null) ? mData.size() >= 10 : mData.size() >= 9) {
                        ToastUtil.showMessage(mContext, mContext.getResources().getString(R.string.dynamic_choose_img_count_error, 9));
                        return;
                    }
                    mLingLabDoPostUtils = new LingLabDoPostUtils(mContext);
                    mLingLabDoPostUtils.requestPermission(mActivity, new String[]{
                            Manifest.permission.READ_EXTERNAL_STORAGE,
                            Manifest.permission.WRITE_EXTERNAL_STORAGE,
//                            Manifest.permission.CAMERA
                    }, mLingLabDoPostUtils.REQUEST_PHOTO_CODE, mData.size());

                }
            });
        }
    }

    class ChooseImageViewHolder extends BaseRecyclerViewHolder<LingLabImageBean> {
        private ImageView mImageView;
        private TextView mTvCoverLingLab;

        public ChooseImageViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            ViewGroup.LayoutParams layoutParams = itemView.getLayoutParams();
            layoutParams.width = mItemViewWH;
            layoutParams.height = mItemViewWH;
            itemView.setLayoutParams(layoutParams);

            mImageView = itemView.findViewById(R.id.iv_image);
            mTvCoverLingLab = itemView.findViewById(R.id.tv_cover_ling_lab);

            mImageView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    EditImgDialog editImgDialog = new EditImgDialog(mContext, mData, getAdapterPosition());
                    editImgDialog.show();
                    editImgDialog.setDeleteListener(new EditImgDialog.OnImageDeleteListener() {
                        @Override
                        public void removeImage(int index) {
                            if (mDeleteListener != null) {
                                mDeleteListener.removeImage(index);
                            }
                        }
                    });

                }
            });
        }

        @Override
        public void bindTo(LingLabImageBean lingLabImageBean, final int position) {
            super.bindTo(lingLabImageBean, position);

            if (position == 0) {
                mTvCoverLingLab.setVisibility(View.VISIBLE);
            } else {
                mTvCoverLingLab.setVisibility(View.GONE);
            }
            if (!TextUtils.isEmpty(lingLabImageBean.getImgPath())) {
                Glide.with(mContext)
                        .asBitmap()
                        .load(lingLabImageBean.getImgPath())
                        .into(mImageView);

            }
        }
    }

    public void setOnImageDeleteListener(OnImageDeleteListener deleteListener) {
        mDeleteListener = deleteListener;
    }

    OnImageDeleteListener mDeleteListener;

    /**
     * 设置图片删除监听
     */
    public interface OnImageDeleteListener {
        void removeImage(int index);
    }

    @Override
    public void onAttachedToRecyclerView(@NonNull @NotNull RecyclerView recyclerView) {
        super.onAttachedToRecyclerView(recyclerView);
        if (mDataObserver != null) {
            unregisterAdapterDataObserver(mDataObserver);
            registerAdapterDataObserver(mDataObserver);
        }
    }

    @Override
    public void onDetachedFromRecyclerView(@NonNull @NotNull RecyclerView recyclerView) {
        super.onDetachedFromRecyclerView(recyclerView);
        if (mDataObserver != null) {
            unregisterAdapterDataObserver(mDataObserver);
        }
    }

    public void setActivity(Activity activity) {
        mActivity = activity;
    }
}