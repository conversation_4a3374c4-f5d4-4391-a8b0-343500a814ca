package com.cloudy.linglingbang.activity.basic;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.cloudy.linglingbang.app.util.PermissionUtils;
import com.umeng.analytics.MobclickAgent;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import butterknife.ButterKnife;
import pub.devrel.easypermissions.EasyPermissions;

/**
 * fragment基类
 *
 * <AUTHOR> create at 2016/9/30 18:43
 */
public abstract class BaseFragment extends Fragment implements EasyPermissions.RationaleCallbacks {

    protected View mRootView;

    private final String TAG = this.getClass().getSimpleName();

    protected PermissionUtils mPermissionUtils;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if (mRootView == null) {
            int layoutResourceId = getLayoutRes();
            if (layoutResourceId > 0) {
                mRootView = inflater.inflate(layoutResourceId, container, false);
                //buttonKnife注入
                ButterKnife.bind(this, mRootView);
                initViews();
                initViewsWithSavedInstanceState(savedInstanceState);
            }
        }
        ViewGroup parent = (ViewGroup) mRootView.getParent();
        if (parent != null) {
            parent.removeView(mRootView);
        }
        return mRootView;
    }

    public void initViewsWithSavedInstanceState(Bundle savedInstanceState) {
    }

    /**
     * 获取布局资源Id
     */
    protected abstract int getLayoutRes();

    /**
     * 初始化，在{@linkplain #onCreateView(LayoutInflater, ViewGroup, Bundle)}中调用
     */
    protected void initViews() {
    }

    @Override
    public void onResume() {
        super.onResume();
        MobclickAgent.onPageStart(TAG); //友盟统计页面
    }

    @Override
    public void onPause() {
        super.onPause();
        MobclickAgent.onPageEnd(TAG);//友盟统计页面
    }

    @Override
    public void onDetach() {
        super.onDetach();
    }

    /**
     * parent 的可见性变化了，onHiddenChanged 和 setUserVisibleHint 都可以调用
     * 为了不做多余的处理，所以单独提取方法
     */
    public void onParentVisibilityChanged(boolean visible) {

    }

    /**
     * 可刷新接口
     */
    public void refresh() {}

    public View getRootView() {
        return mRootView;
    }

    /**
     * 获取通用方法传递的String参数
     */
    protected String getIntentStringExtra() {
        return IntentUtils.getStringExtra(getArguments());
    }

    /**
     * 获取通用方法传递的对象
     */
    protected Object getIntentExtra(Object defaultObject) {
        return IntentUtils.getExtra(getArguments(), defaultObject);
    }

    /**
     * 登录成功时回调，指某项操作需要登录，登录成功后返回，从onNewIntent中调用此处
     * 转给activity后，可再转给fragment
     */
    public void onLoginSuccess() {

    }

    /**
     * 检查权限的方法
     *
     * @param requestCode 请求码
     * @param explainText 检查权限是弹窗显示的文字
     * @param settingExplainText 如果用户点击不再提醒，弹出去设置弹窗的提示文字（传null表示不弹出去设置窗口）
     * @param permissions 需要检查的权限
     */
    public void checkPermissions(final int requestCode, String explainText, final String settingExplainText, String... permissions) {
        if (mPermissionUtils == null) {
            mPermissionUtils = new PermissionUtils(this);
        }
        mPermissionUtils.setPermissionResultListener(new PermissionUtils.OnPermissionListener() {
            @Override
            public void permissionsGranted(int requestCode, List<String> perms) {
                onPermissionResult(true, requestCode);
            }

            @Override
            public void permissionsDenied(int requestCodeResult, List<String> perms) {
                if (requestCodeResult == requestCode) {
                    onPermissionResult(false, requestCodeResult);
                    PermissionUtils.showSettingDialog(getActivity(), settingExplainText, perms);
                }
            }

            @Override
            public void permissionsDeniedSelfDialog(int requestCodeResult, List<String> perms) {
                if (requestCodeResult == requestCode) {
                    onPermissionResult(false, requestCodeResult);
                }
            }
        });
        mPermissionUtils.checkPermissions(requestCode, explainText, settingExplainText, permissions);
    }

    /**
     * 权限申请结果回调，子类重写该方法，执行某些操作
     *
     * @param isGranted true，有权限，false表示没有
     * @param requestCode 请求code
     */
    protected void onPermissionResult(boolean isGranted, int requestCode) {}

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (mPermissionUtils != null) {
            mPermissionUtils.onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
    }

    @Override
    public void onRationaleAccepted(int requestCode) {
    }

    @Override
    public void onRationaleDenied(int requestCode) {
        onPermissionResult(false, requestCode);
    }
}
