package com.cloudy.linglingbang.activity.club.activity;

import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.RadioGroup;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ChooseImageController;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.UploadImageController;
import com.cloudy.linglingbang.app.util.ValidatorUtils;
import com.cloudy.linglingbang.app.widget.dialog.ChooseCityDialog;
import com.cloudy.linglingbang.app.widget.dialog.VerifyMobileDialog;
import com.cloudy.linglingbang.app.widget.item.ChooseDateTimeItem;
import com.cloudy.linglingbang.app.widget.item.CommonItem;
import com.cloudy.linglingbang.app.widget.textview.PressEffectiveCompoundButton;
import com.cloudy.linglingbang.model.CityModel;
import com.cloudy.linglingbang.model.ProvinceModel;
import com.cloudy.linglingbang.model.SmsTypeEnum;
import com.cloudy.linglingbang.model.club.PublishCarClubActivityBean;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.user.User;
import com.umeng.analytics.MobclickAgent;

import java.io.File;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;

/**
 * 发布车友会活动
 *
 * <AUTHOR>
 * @date 2017/11/17
 */
public class PublishCarClubActivityActivity extends BaseActivity {
    private CommonItem mItemTitle;
    private CommonItem mItemType;
    private RadioGroup mRadioGroupType;
    private Long mChannelId;
    /**
     * 活动类型 123
     */
    private int mChosenTypeId;
    private ChooseDateTimeItem mItemStartTime;
    private ChooseDateTimeItem mItemEndTime;
    private ChooseDateTimeItem mItemApplyEndTime;
    private CommonItem mItemPeopleNumber;
    private CommonItem mItemCity;
    private CommonItem mItemAddress;
    private CommonItem mItemContactMobile;
    private CommonItem mItemVerificationCode;
    private CommonItem mItemDescription;
    private CommonItem mItemCover;
    private ImageView mIvCover;
    private ChooseCityDialog.ChooseCityUtil mChooseCityUtil;
    private Long mChosenCityId;
    private String mCoverPath;
    private List<ValidatorUtils.Validator> mValidatorList;
    private ChooseImageController mChooseImageController;
    private UploadImageController mUploadImageController;
    private VerifyMobileDialog mVerifyMobileDialog;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_publish_car_club_activity);
    }

    @Override
    protected void initialize() {
        //初始化字段
        mChooseImageController = new ChooseImageController(this, new ChooseImageController.OnChooseImageListener() {
            @Override
            public void onAddImage(String path) {
                mCoverPath = path;
                mIvCover.setImageURI(null);
                new ImageLoad(PublishCarClubActivityActivity.this, mIvCover, Uri.fromFile(new File(path)))
                        .setUserMemoryCache(false)//设置不使用缓存
                        .load();
            }
        })
                .setCropWidthAndHeight(690, 275);
        mChannelId = (Long) getIntentExtra(0L);
        //find view
        mItemTitle = (CommonItem) findViewById(R.id.item_title);
        mItemType = (CommonItem) findViewById(R.id.item_type);
        mRadioGroupType = (RadioGroup) mItemType.findViewById(R.id.radio_group);
        mItemStartTime = (ChooseDateTimeItem) findViewById(R.id.item_start_time);
        mItemEndTime = (ChooseDateTimeItem) findViewById(R.id.item_end_time);
        mItemApplyEndTime = (ChooseDateTimeItem) findViewById(R.id.item_apply_end_time);
        mItemPeopleNumber = (CommonItem) findViewById(R.id.item_people_number);
        mItemCity = (CommonItem) findViewById(R.id.item_city);
        mItemAddress = (CommonItem) findViewById(R.id.item_address);
        CommonItem mItemContact = (CommonItem) findViewById(R.id.item_contact);
        mItemContactMobile = (CommonItem) findViewById(R.id.item_contact_mobile);
        mItemVerificationCode = (CommonItem) findViewById(R.id.item_verification_code);
        mItemCover = (CommonItem) findViewById(R.id.item_cover);
        mItemDescription = (CommonItem) findViewById(R.id.item_description);
        mIvCover = (ImageView) findViewById(R.id.iv_cover);

        //init
        mItemTitle.getTvRight().requestFocus();
        mItemCover.getIvRight().setVisibility(View.INVISIBLE);
        mItemContact.getIvRight().setVisibility(View.INVISIBLE);
        mItemContact.getTvRight().setText(User.getsUserInstance().getApproveRealName());
        mItemContactMobile.getTvRight().setText(User.getsUserInstance().getMobile());
        mItemStartTime.getTvRight().setText("");
        //活动类型
        String[] typeArray = getResources().getStringArray(R.array.publish_car_club_activity_type_array);
        LayoutInflater inflater = LayoutInflater.from(this);
        for (int i = 0; i < typeArray.length; i++) {
            String type = typeArray[i];
            PressEffectiveCompoundButton compoundButton = (PressEffectiveCompoundButton) inflater.inflate(R.layout.item_publish_car_club_activity_activity_type, mRadioGroupType, false);
            compoundButton.setText(type);
            //id 直接与类型 id 一致
            compoundButton.setId(i + 1);
            compoundButton.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    //因为 RadioGroup 只处理 RadioButton ,所以这里自己手动处理一下
                    if (isChecked) {
                        int id = buttonView.getId();
                        mChosenTypeId = id;
                        int size = mRadioGroupType.getChildCount();
                        for (int j = 0; j < size; j++) {
                            View child = mRadioGroupType.getChildAt(j);
                            if (child instanceof CompoundButton) {
                                if (child.getId() != id) {
                                    ((CompoundButton) child).setChecked(false);
                                }
                            }
                        }
                        //城市和地址是否可见
                        mItemCity.setVisibility(isOnlineActivity() ? View.GONE : View.VISIBLE);
                        mItemAddress.setVisibility(isOnlineActivity() ? View.GONE : View.VISIBLE);
                    }
                }
            });
            RadioGroup.LayoutParams layoutParams = (RadioGroup.LayoutParams) compoundButton.getLayoutParams();
            //为节省空间,最左边的不设置
            if (i != 0 && layoutParams != null) {
                layoutParams.leftMargin = getResources().getDimensionPixelSize(R.dimen.normal_26);
                mRadioGroupType.addView(compoundButton, layoutParams);
            } else {
                mRadioGroupType.addView(compoundButton);
            }
        }

        //绑定
        mItemCity.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                chooseCity();
            }
        });
        mIvCover.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mChooseImageController.showChooseTypeDialog();
            }
        });
        mItemContactMobile.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showModifyMobileDialog();
            }
        });

        //校验
        mValidatorList = new ArrayList<>();
        mValidatorList.add(new ValidatorUtils.ItemEmptyValidator(mItemTitle));
        mValidatorList.add(new ValidatorUtils.ItemEmptyValidator(mItemType) {
            @Override
            public boolean isValidInner() {
                return mChosenTypeId > 0;
            }
        });

        //开始时间
        mValidatorList.add(new ValidatorUtils.ItemEmptyValidator(mItemStartTime)
                .setToast(getString(R.string.publish_car_club_activity_start_time_prompt)));
        mValidatorList.add(new ValidatorUtils.ToastValidator(this, getString(R.string.publish_car_club_activity_start_time_should_not_before_current_time)) {
            @Override
            public boolean isValidInner() {
                Calendar now = GregorianCalendar.getInstance();
                now.set(Calendar.SECOND, 0);
                now.set(Calendar.MILLISECOND, 0);
                now.add(Calendar.HOUR_OF_DAY, 6);
                //开始时间至少大于当前时间6个小时
                return !mItemStartTime.getChosenCalendar().before(now);
            }
        });

        //结束时间
        mValidatorList.add(new ValidatorUtils.ItemEmptyValidator(mItemEndTime)
                .setToast(getString(R.string.publish_car_club_activity_end_time_prompt)));
        mValidatorList.add(new ValidatorUtils.ToastValidator(this, getString(R.string.publish_car_club_activity_end_time_should_not_before_start_time)) {
            @Override
            public boolean isValidInner() {
                Calendar startTime = (Calendar) mItemStartTime.getChosenCalendar().clone();
                startTime.add(Calendar.HOUR_OF_DAY, 1);
                //结束时间至少大于开始时间1个小时
                return !mItemEndTime.getChosenCalendar().before(startTime);
            }
        });

        //报名截止时间
        mValidatorList.add(new ValidatorUtils.ItemEmptyValidator(mItemApplyEndTime)
                .setToast(getString(R.string.publish_car_club_activity_apply_end_time_prompt)));
        mValidatorList.add(new ValidatorUtils.ToastValidator(this, getString(R.string.publish_car_club_activity_apply_end_time_should_not_before_current_time)) {
            @Override
            public boolean isValidInner() {
                Calendar now = GregorianCalendar.getInstance();
                now.set(Calendar.SECOND, 0);
                now.set(Calendar.MILLISECOND, 0);
                now.add(Calendar.HOUR_OF_DAY, 3);
                //截止时间至少大于当前时间3个小时
                return !mItemApplyEndTime.getChosenCalendar().before(now);
            }
        });
        mValidatorList.add(new ValidatorUtils.ToastValidator(this, getString(R.string.publish_car_club_activity_apply_end_time_should_not_before_start_time)) {
            @Override
            public boolean isValidInner() {
                //截止时间不晚于开始时间
                return !mItemApplyEndTime.getChosenCalendar().after(mItemStartTime.getChosenCalendar());
            }
        });
        mValidatorList.add(new ValidatorUtils.ItemEmptyValidator(mItemPeopleNumber)
                .setToast(getString(R.string.publish_car_club_activity_people_number_hint)));
        mValidatorList.add(new ValidatorUtils.ItemEmptyValidator(mItemCity) {
            @Override
            public boolean isValidInner() {
                //是线上就通过
                return isOnlineActivity() || super.isValidInner();
            }

            @Override
            public void onValidateFail() {
                super.onValidateFail();
                chooseCity();
            }
        });
        mValidatorList.add(new ValidatorUtils.ItemEmptyValidator(mItemAddress) {
            @Override
            public boolean isValidInner() {
                //是线上就通过
                return isOnlineActivity() || super.isValidInner();
            }
        }
                .setToast(getString(R.string.publish_car_club_activity_address_prompt)));
        mValidatorList.add(new ValidatorUtils.ItemEmptyValidator(mItemContactMobile));
        mValidatorList.add(new ValidatorUtils.TextViewEmptyValidator(mItemContactMobile.getTvRight()) {
            @Override
            public boolean isValidInner() {
                return ValidatorUtils.Regex.isPhone(getTrimValidateContent().toString());
            }
        }.setToast(getString(R.string.error_mobile_format)));
        mValidatorList.add(new ValidatorUtils.ItemEmptyValidator(mItemVerificationCode) {
            @Override
            public boolean isValidInner() {
                return mItemVerificationCode.getVisibility() != View.VISIBLE || super.isValidInner();
            }
        });
        mValidatorList.add(new ValidatorUtils.ItemEmptyValidator(mItemDescription)
                .setToast(getString(R.string.publish_car_club_activity_description_hint)));
        mValidatorList.add(new ValidatorUtils.ToastValidator(this, getString(R.string.publish_car_club_activity_cover_prompt)) {
            @Override
            public boolean isValidInner() {
                return !TextUtils.isEmpty(mCoverPath);
            }
        });
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        super.onCreateOptionsMenu(menu);
        if (mToolbar != null && mToolbar.getMenu() != null) {
            MenuItem item = mToolbar.getMenu().findItem(R.id.action_right_text);
            if (item != null) {
                item.setTitle(getString(R.string.title_publish_car_club_activity_publish));
                item.setVisible(true);
            }
        }
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.action_right_text:
                //添加友盟统计
                MobclickAgent.onEvent(this, "317");
                onClickPublish();
                return true;
            default:
                return super.onOptionsItemSelected(item);
        }
    }

    /**
     * 是否选择了线上活动
     */
    private boolean isOnlineActivity() {
        return mChosenTypeId == 3;
    }

    private void chooseCity() {
        if (mChooseCityUtil == null) {
            mChooseCityUtil = new ChooseCityDialog.ChooseCityUtil(this, new ChooseCityDialog.OnChooseCityListener() {
                @Override
                public boolean onChoseCity(ProvinceModel chosenProvinceModel, CityModel chosenCityModel) {
                    mChosenCityId = chosenCityModel.getCityId();
                    String location = AppUtil.getProvinceAndCity(chosenProvinceModel.getProvinceName(), chosenCityModel.getCityName());
                    mItemCity.getTvRight().setText(location);
                    return false;
                }
            });
        }
        mChooseCityUtil.showDialog();
    }

    /**
     * 显示校验手机号的对话框
     */
    private void showModifyMobileDialog() {
        if (mVerifyMobileDialog == null) {
            mVerifyMobileDialog = new VerifyMobileDialog(this, SmsTypeEnum.CREATE_CAR_CLUB_ACTIVITY.getId(), new VerifyMobileDialog.onInputFinishListener() {
                @Override
                public void onInputFinish(String mobile, String verificationCode) {
                    mItemContactMobile.getTvRight().setText(mobile);
                    mItemVerificationCode.getTvRight().setText(verificationCode);
                    mItemVerificationCode.setVisibility(View.VISIBLE);
                }
            });
        }
        mVerifyMobileDialog.show();
    }

    private void onClickPublish() {
        for (ValidatorUtils.Validator validator : mValidatorList) {
            if (!validator.isValid()) {
                return;
            }
        }
        //压缩并上传图片
        if (mUploadImageController == null) {
            mUploadImageController = new UploadImageController(this)
                    .setNeedCompress(true)
                    .setOnUploadSuccessListener(new UploadImageController.OnUploadImageSuccessListener() {
                        @Override
                        public void onUploadSuccess(String result) {
                            publish(result);
                        }
                    });
        }
        mUploadImageController.upload(mCoverPath);
    }

    /**
     * 发布
     */
    private void publish(String imagePath) {
        PublishCarClubActivityBean publishCarClubActivityBean = new PublishCarClubActivityBean();
        publishCarClubActivityBean.setTitle(mItemTitle.getTvRight().getText().toString());
        publishCarClubActivityBean.setActivityType(mChosenTypeId);
        publishCarClubActivityBean.setActivityStartTime(mItemStartTime.getChosenCalendar().getTimeInMillis());
        publishCarClubActivityBean.setActivityEndTime(mItemEndTime.getChosenCalendar().getTimeInMillis());
        publishCarClubActivityBean.setApplyEndTime(mItemApplyEndTime.getChosenCalendar().getTimeInMillis());
        int peopleNumber = 0;
        try {
            peopleNumber = Integer.parseInt(mItemPeopleNumber.getTvRight().getText().toString());
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        publishCarClubActivityBean.setPeopleNum(peopleNumber);
        if (!isOnlineActivity()) {
            //非线上，设置地点
            publishCarClubActivityBean.setMeetingAddress(mItemAddress.getTvRight().getText().toString());
            publishCarClubActivityBean.setMeetingCityId(mChosenCityId);
        }
        //服务器要求需要上传手机号
        publishCarClubActivityBean.setContactMobile(mItemContactMobile.getTvRight().getText().toString());
        if (!User.getsUserInstance().getMobile().equals(mItemContactMobile.getTvRight().getText().toString())) {
            //修改手机号，设置验证码
            publishCarClubActivityBean.setVerificationCode(mItemVerificationCode.getTvRight().getText().toString());
        }
        publishCarClubActivityBean.setDescription(mItemDescription.getTvRight().getText().toString());
        publishCarClubActivityBean.setCoverImg(imagePath);
        publishCarClubActivityBean.setChannelId(mChannelId);

        L00bangRequestManager2.getServiceInstance()
                .publishCarClubActivity(publishCarClubActivityBean)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<Object>(this) {
                    @Override
                    public void onSuccess(Object o) {
                        super.onSuccess(o);
                        onPublishSuccess();
                    }
                });

    }

    private void onPublishSuccess() {
        ToastUtil.showMessage(this, "发布活动成功");
        finish();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (mChooseImageController != null) {
            mChooseImageController.onActivityResult(requestCode, resultCode, data);
        }

    }

    @Override
    protected void onPermissionResult(boolean isGranted, int requestCode) {
        super.onPermissionResult(isGranted, requestCode);
        if (mChooseImageController != null) {
            mChooseImageController.onPermissionResult(isGranted, requestCode);
        }
    }
}
