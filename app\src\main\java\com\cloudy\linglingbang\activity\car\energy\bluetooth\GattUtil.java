package com.cloudy.linglingbang.activity.car.energy.bluetooth;

import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattDescriptor;
import android.bluetooth.BluetoothGattService;
import android.util.Log;

import com.clj.fastble.utils.HexUtil;
import com.cloudy.linglingbang.activity.car.energy.ConnectHelper;
import com.cloudy.linglingbang.activity.car.energy.ble.KeyEntity;
import com.cloudy.linglingbang.activity.travel.park.E300BlueControl;
import com.cloudy.linglingbang.activity.car.energy.ble.Utils;
import java.util.UUID;

/**
 * Gatt特征值读写、鉴权工具类
 * Created by LiYeWen on 2025/05/07
 */
public class GattUtil {

    private static GattUtil instance;

    //蓝牙钥匙masterKey
    private static String masterKey;
    //蓝牙钥匙bleKey
    private static String bleKey;
    //蓝牙钥匙masterKeyRandom
    private static String masterKeyRandom;

    private GattUtil(){}

    /**
     * 获取单例对象
     * @return
     */
    public static GattUtil getInstance() {
        if (instance == null) {
            synchronized (GattUtil.class) {
                instance = new GattUtil();
            }
        }
        return instance;
    }

    /**
     * 开启通知
     * @param gatt
     * @param serviceUuid           服务UUID
     * @param characteristicUuid    特征值UUID
     * @return                      开启成功或者失败
     */
    public synchronized boolean enableBleNotification(BluetoothGatt gatt, UUID serviceUuid, UUID characteristicUuid) {
        if(gatt == null){
            Log.e(ConnectHelper.TAG, "enableBleNotification() gatt == null, serviceUuid=" + serviceUuid +
                    ", characteristicUuid=" + characteristicUuid);
            return false;
        }

        //通过UUID获取服务
        BluetoothGattService service = gatt.getService(serviceUuid);
        if (service == null) {
            Log.e(ConnectHelper.TAG, "enableBleNotification() 未找到服务 service == null, serviceUuid=" + serviceUuid +
                    ", characteristicUuid=" + characteristicUuid);
            return false;
        }

        //通过UUID获取特征值
        BluetoothGattCharacteristic characteristic = service.getCharacteristic(characteristicUuid);
        if (characteristic == null) {
            Log.e(ConnectHelper.TAG, "enableBleNotification() 未找到特征值 characteristic == null, serviceUuid=" + serviceUuid +
                    ", characteristicUuid=" + characteristicUuid);
            return false;
        }

        //检查特征是否支持通知
        int properties = characteristic.getProperties();
        if ((properties & BluetoothGattCharacteristic.PROPERTY_NOTIFY) <= 0) {
            Log.e(ConnectHelper.TAG, "enableBleNotification() 特征不支持通知 properties = " + properties + ", serviceUuid=" + serviceUuid +
                    ", characteristicUuid=" + characteristicUuid);
            return false;
        }

        //开启通知
        boolean success = gatt.setCharacteristicNotification(characteristic, true);
        if (!success) {
            Log.e(ConnectHelper.TAG, "enableBleNotification() 开启通知失败 success == false" + ", serviceUuid=" + serviceUuid +
                    ", characteristicUuid=" + characteristicUuid);
            return false;
        }

        //写入通知描述符
        BluetoothGattDescriptor descriptor = characteristic.getDescriptor(
                UUID.fromString("00002902-0000-1000-8000-00805f9b34fb"));
        if(descriptor == null){
            Log.e(ConnectHelper.TAG, "enableBleNotification() 描述符为空 descriptor == null" + ", serviceUuid=" + serviceUuid +
                    ", characteristicUuid=" + characteristicUuid);
            return false;
        }

        descriptor.setValue(BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE);
        boolean descriptorSuccess = gatt.writeDescriptor(descriptor);
        if(!descriptorSuccess){
            Log.e(ConnectHelper.TAG, "enableBleNotification() 写入描述符失败 descriptorSuccess == false" + ", serviceUuid=" + serviceUuid +
                    ", characteristicUuid=" + characteristicUuid);
            return false;
        }
        return true;

//        // 添加延迟处理
//        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                gatt.writeDescriptor(descriptor);
//            }
//        }, 1000);
    }

    /**
     * 关闭通知
     * @param gatt
     * @param serviceUuid           服务UUID
     * @param characteristicUuid    特征值UUID
     * @return                      关闭成功或者失败
     */
    public synchronized boolean closeBleNotification(BluetoothGatt gatt, UUID serviceUuid, UUID characteristicUuid) {
        if(gatt == null){
            Log.e(ConnectHelper.TAG, "关闭通知 closeBleNotification() gatt == null, serviceUuid=" + serviceUuid +
                    ", characteristicUuid=" + characteristicUuid);
            return false;
        }

        //通过UUID获取服务
        BluetoothGattService service = gatt.getService(serviceUuid);
        if (service == null) {
            Log.e(ConnectHelper.TAG, "关闭通知 closeBleNotification() 未找到服务 service == null, serviceUuid=" + serviceUuid +
                    ", characteristicUuid=" + characteristicUuid);
            return false;
        }

        //通过UUID获取特征值
        BluetoothGattCharacteristic characteristic = service.getCharacteristic(characteristicUuid);
        if (characteristic == null) {
            Log.e(ConnectHelper.TAG, "关闭通知 closeBleNotification() 未找到特征值 characteristic == null, serviceUuid=" + serviceUuid +
                    ", characteristicUuid=" + characteristicUuid);
            return false;
        }

        return gatt.setCharacteristicNotification(characteristic, false);
    }

    /**
     * 发送KeyId进行鉴权（两次鉴权共用方法）
     * @param gatt
     * @param keyIdData
     */
    public synchronized boolean sendKeyId(BluetoothGatt gatt, byte[] keyIdData) {
        if(gatt == null){
            Log.e(ConnectHelper.TAG, "sendKeyId() gatt == null");
            return false;
        }

        //通过UUID获取服务
        BluetoothGattService service = gatt.getService(ConnectUtil.UUID_SERVICE_AUTH);
        if (service == null) {
            Log.e(ConnectHelper.TAG, "sendKeyId() 未找到服务 service == null");
            return false;
        }

        //通过UUID获取特征值
        BluetoothGattCharacteristic characteristic = service.getCharacteristic(ConnectUtil.UUID_CHARACTERISTIC_WRITE_AUTH);
        if (characteristic == null) {
            Log.e(ConnectHelper.TAG, "sendKeyId() 未找到特征值 characteristic == null");
            return false;
        }

        characteristic.setValue(keyIdData);
        boolean writeSuccess = gatt.writeCharacteristic(characteristic);
        if(!writeSuccess){
            Log.e(ConnectHelper.TAG, "sendKeyId() 写入特征值失败 writeSuccess == false");
            return false;
        }
        return true;
    }

    /**
     * 生成第一步鉴权要发送的数据
     * @return
     */
    public byte[] genAuthHex() {
        int currentTme = (int) (System.currentTimeMillis() / 1000);
        byte[] bytes = new byte[4];
        for (int i = bytes.length - 1; i >= 0; i--) {
            bytes[i] = (byte) (currentTme & 0xFF);
            currentTme >>= 8;
        }
        String serviceID = "38C7";
        String subFunction = "0001";
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(serviceID)
                .append(subFunction)
                .append(getReserved(8))
                .append(HexUtil.encodeHexStr(bytes))
                .append(bleKey)
                .append(E300BlueControl.PAYLOAD_LENGTH)
                .append(getReserved(12));
        String Data_Check = Utils.getCRC16(stringBuilder.toString());
        stringBuilder.append(Data_Check);
        stringBuilder.append(getReserved(14));
        byte[] result = HexUtil.hexStringToBytes(stringBuilder.toString());
        return result;
    }

    /**
     * 生成第二步鉴权要发送的数据
     * @return
     */
    public byte[] genHex(String randomdata1) {
        String Service_ID = "38C7";
        String Subfunction = "0002";
        String rollData = "00001122";
        String Random_data = randomdata1;
        String MasterKey = masterKey;
        String MasterKey_random = masterKeyRandom;
        String BleKey = bleKey;
        String Payload_length = "06";
        String Reserved = "000000000000";

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(Service_ID).append(Subfunction).append(rollData).append(Random_data).append(BleKey).append(Payload_length).append(Reserved);
        String Data_Check = Utils.getCRC16(stringBuilder.toString());
        stringBuilder.append(Data_Check);
        stringBuilder.append("00000000000000");
        stringBuilder.length();

        return Utils.encrypt(Utils.XOR(MasterKey, MasterKey_random), stringBuilder.toString());
    }

    /**
     * 解密鉴权返回的数据
     * @return 随机字节
     */
    public String decode(byte[] data) {
        if (data != null && data.length >= 4) {
            String ss;
            try {
                ss = HexUtil.encodeHexStr(data);
            }catch (Exception e){
                Log.e(ConnectHelper.TAG, "decode() 异常了 e = " + e);
                return "000000000000000000000000000000";
            }

            String screct = ss.substring(0, 2);
            String decodeContent = ss.substring(2);
            if ("00".equals(screct)) {
                try {
                    return HexUtil.encodeHexStr(Utils.Decrypt(Utils.XOR(masterKey, masterKeyRandom), decodeContent));
                }catch (Exception e){
                    Log.e(ConnectHelper.TAG, "decode() 异常了 e = " + e);
                }
            } else {
                return decodeContent;
            }
        }
        return "000000000000000000000000000000";
    }

    /**
     * 解密车控返回的数据
     * @param data
     * @param randomKey
     * @return
     */
    public String decodeControl(byte[] data, String randomKey) {

        if (data == null) {
            Log.e(ConnectHelper.TAG, "decodeControl() 解密车控返回的数据失败 data == null");
            return "";
        }

        if (data.length < 4) {
            Log.e(ConnectHelper.TAG, "decodeControl() 解密车控返回的数据失败 data.length = " + data.length);
            return "";
        }

        String hexStr = HexUtil.encodeHexStr(data);
        if(hexStr == null){
            Log.e(ConnectHelper.TAG, "decodeControl() 解密车控返回的数据失败 hexStr == null");
            return "";
        }
        Log.e(ConnectHelper.TAG, "decodeControl() 解密车控返回的数据,  十六进制字符串 hexStr =" + hexStr);

        if (hexStr.length() <= 1) {
            Log.e("testtest", "数据异常");
            Log.e(ConnectHelper.TAG, "decodeControl() 解密车控返回的数据失败 hexStr.length() = " + hexStr.length());
            return "";
        }

        String head = hexStr.substring(0, 2);
        String body = hexStr.substring(2);
        Log.e(ConnectHelper.TAG, "decodeControl() 解密车控返回的数据,  十六进制字符串 head=" + head + ", body=" + body);

        if ("00".equals(head)) {
//            String randomKey = getRandomKey();
            Log.e(ConnectHelper.TAG, "decodeControl() 解密车控返回的数据,  randomKey=" + randomKey);
            return head + HexUtil.encodeHexStr(Utils.Decrypt(randomKey, body));
        } else {
            Log.e(ConnectHelper.TAG, "decodeControl() 无需解密，直接返回原数据 hexStr="+hexStr);
            return hexStr;
        }
    }

    /**
     * 生成固定长度的保留位（全0字符串）
     *
     * @param length 保留位长度
     * @return 保留位
     */
    private String getReserved(int length) {
        return String.format("%0" + length + "d", 0);
    }

    /**
     * 设置蓝牙连接的蓝牙钥匙
     * @param keyEntity 蓝牙钥匙
     */
    public void setParams(KeyEntity keyEntity) {
//        setMac(keyEntity.getMac());
        masterKey = keyEntity.getMasterKey();
        masterKeyRandom = keyEntity.getMasterKeyRandom();
        bleKey = keyEntity.getBleKey();
        Log.e(ConnectHelper.TAG, "设置蓝牙连接的蓝牙钥匙 setParams() masterKey="+masterKey);
    }

}
