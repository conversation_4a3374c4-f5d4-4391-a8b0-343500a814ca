package com.cloudy.linglingbang.activity.basic;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Parcelable;

import java.io.Serializable;

import androidx.fragment.app.Fragment;

/**
 * 打开Activity传递数据，统一的name，可以统一写传递和获取方法。
 * <br/>使用时
 * <br/>1 Activity
 * <br/>1.1传：用{@linkplain #startActivity(Context, Class, Object)}
 * 或 {@linkplain #startActivity(Context, Class, Object, int)}传参数
 * <br/>1.2.1取：用{@linkplain BaseActivity#getIntentStringExtra()}
 * 或{@linkplain BaseActivity#getIntentExtra(Object)}取出参数
 * <br/>1.2.2也可以：{@linkplain #getStringExtra(Bundle)} 和 {@linkplain #getExtra(Bundle, Object)}取出
 * <br/>2 Fragment
 * <br/>2.1传：用{@linkplain #createBundle(Object)}或{@linkplain #createBundle(String, Object)}创建bundle
 * 用{@linkplain android.app.Fragment#setArguments(Bundle)}设置参数
 * <br/>2.2取：用{@linkplain Fragment#getArguments()}取出bundle，再用1.2.2中的取出参数
 * <br/>最多有2个参数，{@linkplain #INTENT_EXTRA_COMMON}和{@linkplain #INTENT_EXTRA_FROM}
 * <br/>数据和来源，再多就可以用对象传递的。
 * <br/>注意传递的数据是有大小限制的
 *
 * <AUTHOR> create at 2016/10/11 13:14
 */
public class IntentUtils {
    /**
     * 这两个name本来是应该以包名开头的
     */
    public static final String INTENT_EXTRA_COMMON = "INTENT_EXTRA_COMMON";
    public static final String INTENT_EXTRA_FROM = "INTENT_EXTRA_FROM";
    /**
     * 标题，为了方便 h5 扩展跳转，以后的 key 设为小写
     */
    public static final String INTENT_EXTRA_TITLE = "title";

    /**
     * Start activity.
     *
     * @param context the context
     * @param targetActivity the target activity
     */
    public static void startActivity(Context context, Class<?> targetActivity) {
        startActivity(context, targetActivity, null);
    }

    /**
     * Start activity.
     * 可以传null，用的是通用的参数名
     *
     * @param context the context
     * @param targetActivity the target activity
     * @param extraValue the extra value
     */
    public static void startActivity(Context context, Class<?> targetActivity, Object extraValue) {
        Intent startIntent = createStartIntent(context, targetActivity, INTENT_EXTRA_COMMON, extraValue);
        startActivity(context, startIntent);
    }

    /**
     * Start activity.
     *
     * @param context the context
     * @param targetActivity the target activity
     * @param extraValue the extra value 参数
     * @param from the from 来源
     */
    public static void startActivity(Context context, Class<?> targetActivity, Object extraValue, int from) {
        Intent startIntent = createStartIntent(context, targetActivity, INTENT_EXTRA_COMMON, extraValue);
        startIntent.putExtra(INTENT_EXTRA_FROM, from);
        startActivity(context, startIntent);
    }

    /**
     * 融云调整部分调整
     */
    public static void startActivityAndFlag(Context context, Class<?> targetActivity, Object extraValue, int from) {
        Intent startIntent = createStartIntent(context, targetActivity, INTENT_EXTRA_COMMON, extraValue);
        startIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        startIntent.putExtra(INTENT_EXTRA_FROM, from);
        startActivity(context, startIntent);
    }

    /**
     * Start activity.
     * 统一在此处处理，如果有动画切换效果，在此处设置
     *
     * @param context the context
     * @param startIntent the start intent
     */
    public static void startActivity(Context context, Intent startIntent) {
        context.startActivity(startIntent);
//		activity.overridePendingTransition(R.anim.my_scale_action, R.anim.my_alpha_action);
//		activity.finish();
    }

    public static void startActivity(Activity activity, Class<?> targetActivity, Bundle bundle) {
        Intent intent = new Intent(activity, targetActivity);
        intent.putExtras(bundle);
        startActivity(activity, intent);
    }

    public static void startActivityForResult(Activity activity, Class<?> targetActivity, int requestCode) {
        startActivityForResult(activity, targetActivity, requestCode, INTENT_EXTRA_COMMON, null);
    }

    public static void startActivityForResult(Activity activity, Class<?> targetActivity, int requestCode, Object extraValue) {
        startActivityForResult(activity, targetActivity, requestCode, INTENT_EXTRA_COMMON, extraValue);
    }

    public static void startActivityForResult(Fragment fragment, Class<?> targetActivity, int requestCode, Object extraValue) {
        startActivityForResult(fragment, targetActivity, requestCode, INTENT_EXTRA_COMMON, extraValue);
    }

    public static void startActivityForResult(Activity activity, Class<?> targetActivity, int requestCode, String extraKey, Object extraValue) {
        Intent startIntent = createStartIntent(activity, targetActivity, extraKey, extraValue);
        startActivityForResult(activity, startIntent, requestCode);
    }

    public static void startActivityForResult(Fragment fragment, Class<?> targetActivity, int requestCode, String extraKey, Object extraValue) {
        Intent startIntent = createStartIntent(fragment.getActivity(), targetActivity, extraKey, extraValue);
        fragment.startActivityForResult(startIntent, requestCode);
    }

    public static void startActivityForResult(Fragment fragment, Intent startIntent, int requestCode) {
        fragment.startActivityForResult(startIntent, requestCode);
    }

    public static void startActivityForResult(Activity activity, Intent startIntent, int requestCode) {
        activity.startActivityForResult(startIntent, requestCode);
    }

    public static Intent createStartIntent(Context context, Class<?> targetActivity, String extraKey, Object extraValue) {
        Intent startIntent = new Intent();
        startIntent.setClass(context, targetActivity);
        startIntent.putExtras(createBundle(extraKey, extraValue));
        return startIntent;
    }

    /**
     * Create bundle bundle.
     *
     * @param extraValue the extra value 将该参数放入bundle，名为通用名，可用此bundle继续设置参数
     * @return the bundle
     */
    public static Bundle createBundle(Object extraValue) {
        return createBundle(INTENT_EXTRA_COMMON, extraValue);
    }

    /**
     * Create bundle bundle.
     *
     * @param extraKey the extra key
     * @param extraValue the extra value
     * @return the bundle
     */
    public static Bundle createBundle(String extraKey, Object extraValue) {
        Bundle bundle = new Bundle();
        if (extraValue != null) {
            if (extraValue instanceof Integer) {
                bundle.putInt(extraKey, (Integer) extraValue);
            } else if (extraValue instanceof Long) {
                bundle.putLong(extraKey, (Long) extraValue);
            } else if (extraValue instanceof Float) {
                bundle.putFloat(extraKey, (Float) extraValue);
            } else if (extraValue instanceof Double) {
                bundle.putDouble(extraKey, (Double) extraValue);
            } else if (extraValue instanceof Boolean) {
                bundle.putBoolean(extraKey, (Boolean) extraValue);
            } else if (extraValue instanceof String) {
                bundle.putString(extraKey, extraValue.toString());
            } else if (extraValue instanceof Parcelable) {
                bundle.putParcelable(extraKey, (Parcelable) extraValue);
            } else {
                try {
                    bundle.putSerializable(extraKey, (Serializable) extraValue);
                } catch (Exception exception) {
                    bundle.putString(extraKey, extraValue.toString());
                }
            }
        }
        return bundle;
    }

    /**
     * Start activity by call intent.
     * 打开拨号界面，并不是直接打电话，不需要call权限
     *
     * @param context the context
     * @param phoneNumber the phone number
     */
    public static void startActivityByDialIntent(Context context, String phoneNumber) {
        Intent intent = new Intent();
        intent.setAction(Intent.ACTION_DIAL);
        intent.setData(Uri.parse("tel:" + phoneNumber));
        if (intent.resolveActivity(context.getPackageManager()) != null) {
            context.startActivity(intent);
        }
    }

    public static void startActivityBySendToIntent(Context context, String phoneNumber, String body) {
        Intent sendIntent = new Intent(Intent.ACTION_SENDTO);
        sendIntent.setData(Uri.parse("smsto:" + phoneNumber));
        sendIntent.putExtra("sms_body", body);
        context.startActivity(sendIntent);
    }

    /**
     * Gets string extra.
     * 以通用名，从bundle中取出参数
     *
     * @param bundle the bundle
     * @return the string extra
     */
    public static String getStringExtra(Bundle bundle) {
        return (String) getExtra(bundle, "");
    }

    /**
     * bundle，如果要Serializable，defaultObject设为null
     *
     * @param defaultObject the default object
     * @return the extra from intent
     */
    public static Object getExtra(Bundle bundle, Object defaultObject) {
        return getExtra(bundle, INTENT_EXTRA_COMMON, defaultObject);
    }

    public static Object getExtra(Intent intent, Object defaultObject) {
        Bundle bundle = null;
        if (intent != null) {
            bundle = intent.getExtras();
        }
        return getExtra(bundle, defaultObject);
    }

    public static Object getExtra(Bundle bundle, String extraName, Object defaultObject) {
        Object r = defaultObject;
        if (bundle != null) {
            if (defaultObject == null) {
                r = bundle.getSerializable(extraName);
                if (r == null) {
                    r = bundle.getParcelable(extraName);
                }
            } else if (defaultObject instanceof String) {
                r = bundle.getString(extraName, (String) defaultObject);
            } else if (defaultObject instanceof Integer) {
                r = bundle.getInt(extraName, (Integer) defaultObject);
            } else if (defaultObject instanceof Boolean) {
                r = bundle.getBoolean(extraName, (Boolean) defaultObject);
            } else if (defaultObject instanceof Float) {
                r = bundle.getFloat(extraName, (Float) defaultObject);
            } else if (defaultObject instanceof Long) {
                r = bundle.getLong(extraName, (Long) defaultObject);
            }
        }
        if (r == null && defaultObject != null) {
            r = defaultObject;
        }
        return r;
    }

    /**
     * 获取 long 参数或解析 string 参数为 long
     * 如果 getLongExtra 为 0，则尝试 getStringExtra
     * <p>
     * 主要用于通过地址打开 Activity，此时参数是通过 String 传过来的
     */
    public static long getOrParseLongExtra(Bundle bundle, String key) {
        return getOrParseLongExtra(bundle, key, 0L);
    }

    public static long getOrParseLongExtra(Bundle bundle, String key, long defaultValue) {
        long r = defaultValue;
        if (bundle != null) {
            r = bundle.getLong(key, defaultValue);
            if (r == defaultValue) {
                //如果 long 没获取到,尝试 int
                r = bundle.getInt(key, (int) defaultValue);
            }
            //即使是 int 也可以用 == 判断相等
            if (r == defaultValue) {
                try {
                    r = Long.parseLong(bundle.getString(key));
                } catch (NumberFormatException e) {
                    //即使返回 null 也会是 NumberFormatException
                    e.printStackTrace();
                }
            }
        }
        return r;
    }

    /**
     * 设置 Fragment 参数
     */
    public static Fragment setFragmentArgument(Fragment fragment, String argument) {
        if (fragment != null) {
            Bundle bundle = new Bundle();
            bundle.putString(IntentUtils.INTENT_EXTRA_COMMON, argument);
            fragment.setArguments(bundle);
        }
        return fragment;
    }

    /**
     * 获取 Fragment 参数
     */
    public static String getFragmentArgument(Fragment fragment) {
        if (fragment != null) {
            Bundle arguments = fragment.getArguments();
            if (arguments != null) {
                return arguments.getString(IntentUtils.INTENT_EXTRA_COMMON);
            }
        }
        return "";
    }

    public static Fragment setFragmentIntArgument(Fragment fragment, int from) {
        if (fragment != null) {
            Bundle bundle = new Bundle();
            bundle.putInt(IntentUtils.INTENT_EXTRA_FROM, from);
            fragment.setArguments(bundle);
        }
        return fragment;
    }

    public static int getFragmentIntArgument(Fragment fragment) {
        if (fragment != null) {
            Bundle arguments = fragment.getArguments();
            if (arguments != null) {
                return arguments.getInt(IntentUtils.INTENT_EXTRA_FROM);
            }
        }
        return -1;
    }
}