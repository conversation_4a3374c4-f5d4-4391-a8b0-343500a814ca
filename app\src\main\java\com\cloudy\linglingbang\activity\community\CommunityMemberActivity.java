package com.cloudy.linglingbang.activity.community;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.widget.UserHeadView;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.app.widget.textview.PressEffectiveRadioButton;
import com.cloudy.linglingbang.model.channel.Channel;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.user.User;
import com.umeng.analytics.MobclickAgent;

import java.util.List;

import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import butterknife.ButterKnife;
import io.reactivex.rxjava3.core.Observable;

/**
 * 社区成员
 *
 * <AUTHOR> create at 2016/10/14 9:33
 */
public class CommunityMemberActivity extends BaseRecyclerViewRefreshActivity<User> {
    private String mChannelId;
    private String mChiefUserId;
    private int mType;

    public final static int TYPE_COMMUNITY = 1;//社区
    public final static int TYPE_CLUB = 2;//车友会

    @Override
    protected void initialize() {
        super.initialize();
        mType = getIntent().getIntExtra("type", 1);
        if (mType == TYPE_CLUB) {
            setMiddleTitle(getString(R.string.club_members));
        } else {
            setMiddleTitle(getString(R.string.community_members));
        }
        mChannelId = getIntent().getStringExtra("channelId");
        if (mChannelId == null) {
            mChannelId = "";
        }
        mChiefUserId = getIntent().getStringExtra("chiefUserId");
        if (mChiefUserId == null) {
            mChiefUserId = "";
        }
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<User> list) {
        return new CommunityMemberAdapter(getContext(), list);
    }

    @Override
    public Observable<BaseResponse<List<User>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return service2.getAttendedUsers(mChannelId, pageNo, pageSize);
    }

    class CommunityMemberAdapter extends BaseRecyclerViewAdapter<User> {
        public CommunityMemberAdapter(Context context, List<User> data) {
            super(context, data);
        }

        @Override
        protected BaseRecyclerViewHolder<User> createViewHolder(View itemView) {
            return new CommunityMemberViewHolder(itemView);
        }

        @Override
        protected int getItemLayoutRes(int viewType) {
            return R.layout.circle_member_item;
        }

        private void attendUser(final User user) {

            L00bangRequestManager2
                    .setSchedulers(L00bangRequestManager2.getInstance().getService().addFriend(user.getUserIdStr()))
                    .subscribe(new ProgressSubscriber<String>(CommunityMemberActivity.this) {
                        @Override
                        public void onSuccess(String s) {
                            super.onSuccess(s);
                            ToastUtil.showMessage(CommunityMemberActivity.this, R.string.send_success);
                            UserUtils.updateUserAttention(user, true);
                            notifyDataSetChanged();
                        }
                    });

//                            DialogUtil.showAddFriendDialog(user.getUserIdStr(), (Activity) mContext);
        }

        /**
         * 取消关注
         */
        private void deleteAttendUser(final User user) {
            Dialog dialog = new CommonAlertDialog(mContext, R.string.dialog_delete_friend_confirm, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    deleteFriends(user);
                }
            });
            dialog.show();
        }

        /**
         * 取消关注具体实现
         */
        private void deleteFriends(final User user) {
            L00bangRequestManager2
                    .getServiceInstance()
                    .deleteFriend(user.getUserIdStr())
                    .compose(L00bangRequestManager2.setSchedulers())
                    .subscribe(new ProgressSubscriber<String>(mContext) {
                        @Override
                        public void onSuccess(String s) {
                            super.onSuccess(s);
                            ToastUtil.showMessage(mContext, "取消关注成功！");
                            UserUtils.updateUserAttention(user, false);
                            notifyDataSetChanged();
                        }
                    });
        }

        class CommunityMemberViewHolder extends BaseRecyclerViewHolder<User> {
            @BindView(R.id.user_head_view)
            UserHeadView user_head_view;
            @BindView(R.id.tv_name)
            TextView tv_name;//姓名和车型
            @BindView(R.id.tv_province)
            TextView tv_province;
            @BindView(R.id.bt_forbid_talk)
            Button bt_forbid_talk;//禁止发帖
            @BindView(R.id.bt_add_attention)
            PressEffectiveRadioButton bt_add_attention;//加关注
            @BindView(R.id.tv_myself)
            TextView tv_myself;//我
            @BindView(R.id.iv_moderator_icon)
            ImageView iv_moderator_icon;//邦主图标
            @BindView(R.id.tv_lv)
            TextView tv_lv;//等级
            @BindView(R.id.tv_carType)
            TextView tv_carType;//车型

            public CommunityMemberViewHolder(View itemView) {
                super(itemView);
            }

            @Override
            protected void initItemView(View itemView) {
                super.initItemView(itemView);
                ButterKnife.bind(this, itemView);

                //点击加关注
                bt_add_attention.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        User user = getData().get(getAdapterPosition());
                        //友盟统计
                        MobclickAgent.onEvent(getContext(), "53");
                        if (UserUtils.hasAttentionOther(user)) {//如果是已经关注
                            deleteAttendUser(user);
                        } else {
                            //加关注
                            attendUser(user);
                        }
                    }
                });

                //点击头像进入个人主页
                user_head_view.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        User user = getData().get(getAdapterPosition());
                        JumpPageUtil.goToPersonPage(mContext, user);
                    }
                });
            }

            @Override
            public void bindTo(User user, int position) {
                super.bindTo(user, position);

                user_head_view.setUser(user);
                if (user.getNickname() != null) {
                    tv_name.setText(user.getNickname());
                }
                tv_province.setText(AppUtil.getProvinceAndCity(user.getProvinceStr(), user.getCityStr()));

                if (!TextUtils.isEmpty(user.getCarTypeStr())) {
                    tv_carType.setText(user.getCarTypeStr());
                }

                if (!TextUtils.isEmpty(user.getRankName())) {
                    tv_lv.setText(user.getRankName());
                }
                //控制加关注
                if (UserUtils.isSelf(user) || user.getAttentionDisplayStatus() == 1) {
                    //如果这条是自己
                    bt_add_attention.setVisibility(View.GONE);
                    tv_myself.setVisibility(View.VISIBLE);//显示我
                } else {
                    tv_myself.setVisibility(View.GONE);//隐藏我
                    bt_add_attention.setVisibility(View.VISIBLE);
                    //判断是否加关注
                    if (UserUtils.hasAttentionOther(user)) {
                        bt_add_attention.setChecked(true);
                        bt_add_attention.setText("已关注");
                    } else {
                        bt_add_attention.setChecked(false);
                        bt_add_attention.setText("关注");
                    }
                }
                //如果是社区详情页，显示邦主图标
                if (mType == TYPE_COMMUNITY) {
                    if (UserUtils.isSelf(mChiefUserId)) {
                        iv_moderator_icon.setVisibility(View.VISIBLE);
                        iv_moderator_icon.setImageResource(R.drawable.community_moderator_icon);

                    } else {
                        iv_moderator_icon.setVisibility(View.GONE);
                    }
                } else {
                    //如果是会长
                    if (user.getChannelRoleId() == Channel.RoleType.PRESIDENT) {
                        iv_moderator_icon.setVisibility(View.VISIBLE);
                        iv_moderator_icon.setImageResource(R.drawable.ic_club_president);
                    } else if (user.getChannelRoleId() == Channel.RoleType.VICE_PRESIDENT) {
                        iv_moderator_icon.setVisibility(View.VISIBLE);
                        iv_moderator_icon.setImageResource(R.drawable.ic_club_vice_president);
                    } else {
                        iv_moderator_icon.setVisibility(View.GONE);
                    }
                }
            }
        }
    }

    /**
     * 进入会员列表（社区或者车友会）
     *
     * @param channel
     * @param type    类型（社区或者车友会）
     */
    public static void goMember(Context context, Channel channel, int type) {
        if (AppUtil.checkLogin(context) && channel != null) {
            Intent intent_member = new Intent(context, CommunityMemberActivity.class);
            intent_member.putExtra("channelId", channel.getChannelId());
            intent_member.putExtra("type", type);
            if (channel.getChief() != null) {
                String chiefIdString = channel.getChief().getUserIdStr();
                if (chiefIdString != null) {
                    intent_member.putExtra("chiefUserId", chiefIdString);
                }
            }
            context.startActivity(intent_member);
        }
    }
}
