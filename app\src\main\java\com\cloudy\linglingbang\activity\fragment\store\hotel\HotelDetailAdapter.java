package com.cloudy.linglingbang.activity.fragment.store.hotel;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.StrikethroughSpan;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.ScanImageActivity;
import com.cloudy.linglingbang.activity.basic.IRefreshContext;
import com.cloudy.linglingbang.activity.fragment.store.hotel.order.OrderActivity;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ModelUtils;
import com.cloudy.linglingbang.app.util.baiduMap.CoordinateTransform;
import com.cloudy.linglingbang.app.util.map.NavigateUtil;
import com.cloudy.linglingbang.app.widget.banner.BannerViewImpl;
import com.cloudy.linglingbang.app.widget.dialog.hotel.HotelRoomDialog;
import com.cloudy.linglingbang.app.widget.dialog.hotel.HotelSelectDateDialog;
import com.cloudy.linglingbang.app.widget.dialog.hotel.HotelUtil;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.app.widget.recycler.holder.CommonEmptyInfoViewHolder;
import com.cloudy.linglingbang.model.hotel.Hotel;
import com.cloudy.linglingbang.model.hotel.HotelRoom;
import com.cloudy.linglingbang.model.wrapper.EmptyInfo;

import java.math.BigDecimal;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

/**
 * <AUTHOR>
 * @date 2022/3/16
 */
public class HotelDetailAdapter extends BaseRecyclerViewAdapter<Object> {
    private Hotel mHotel;
    private long mStartDate;
    private long mEndDate;

    public HotelDetailAdapter(Context context, List<Object> data) {
        super(context, data);
    }

    @Override
    protected BaseRecyclerViewHolder<Object> createViewHolder(View itemView) {
        return new BaseRecyclerViewHolder<>(itemView);
    }

    @Override
    protected BaseRecyclerViewHolder createViewHolderWithViewType(View itemView, int viewType) {
        if (viewType == ViewType.TYPE_TOP) {
            return new HotelInfoViewHolder(itemView, this);
        } else if (viewType == ViewType.TYPE_TITLE) {
            return new HotelDateViewHolder(itemView, this);
        } else if (viewType == ViewType.TYPE_EMPTY) {
            return new CommonEmptyInfoViewHolder(itemView);
        } else {
            return new HotelRoomViewHolder(itemView, this);
        }
    }

    @Override
    public int getItemViewType(int position) {
        if (mHotel != null) {
            if (position == 0) {
                return ViewType.TYPE_TOP;
            } else if (position == 1) {
                return ViewType.TYPE_TITLE;
            }
        }
        if (mData.get(position) instanceof EmptyInfo) {
            return ViewType.TYPE_EMPTY;
        }
        return ViewType.TYPE_ROOM;
    }

    @Override
    protected int getItemLayoutRes(int viewType) {
        if (viewType == ViewType.TYPE_TOP) {
            return R.layout.item_hotel_detail_banner;
        } else if (viewType == ViewType.TYPE_TITLE) {
            return R.layout.item_hotel_detail_title;
        } else if (viewType == ViewType.TYPE_EMPTY) {
            return R.layout.item_empty_info;
        } else {
            return R.layout.item_hotel_detail_room;
        }
    }

    public void setDate(long startDate, long endDate) {
        this.mStartDate = startDate;
        this.mEndDate = endDate;
    }

    public void setHotel(Hotel hotel) {
        mHotel = hotel;
    }

    /**
     * 酒店信息
     */
    private static class HotelInfoViewHolder extends BaseRecyclerViewHolder<Object> {

        BannerViewImpl<String> mBannerView;
        TextView mTvName;
        TextView mTvAddress;
        TextView mTvIndicator;
        private final HotelDetailAdapter mAdapter;

        public HotelInfoViewHolder(View itemView, HotelDetailAdapter adapter) {
            super(itemView);
            mAdapter = adapter;
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            mBannerView = itemView.findViewById(R.id.banner_view);
            mTvIndicator = itemView.findViewById(R.id.tv_indicator);
            mTvName = itemView.findViewById(R.id.tv_name);
            mTvAddress = itemView.findViewById(R.id.tv_address);
            itemView.findViewById(R.id.nav).setOnClickListener(v -> navigate());
            initBannerView();
        }

        /**
         * 初始化轮播控件
         */
        private void initBannerView() {
            mBannerView.setWhRate(375.0f / 235);
            mBannerView.setGetImageUrl(new BannerViewImpl.BindView<String>() {
                @Override
                public boolean bindView(View view, String s) {
                    if (view instanceof ImageView) {
                        HotelUtil.loadHotelImage((ImageView) view, getImgUrl(s), 1);
                        return true;
                    }
                    return false;
                }

                @Override
                public String getImgUrl(String s) {
                    return s;
                }
            });
            //禁止自动滚动
            mBannerView.stopAutoTurning();
            mBannerView.setOnItemClickListener(position -> {
                //点击查看大图
                Intent intent = new Intent(itemView.getContext(), ScanImageActivity.class);
                if (mBannerView.getAdList() != null) {
                    String[] ss = new String[mBannerView.getAdList().size()];
                    String[] paths = mBannerView.getAdList().toArray(ss);
                    intent.putExtra(ScanImageActivity.EXTRA_IMAGE_URLS, paths);
                }
                intent.putExtra(ScanImageActivity.EXTRA_IMAGE_INDEX, position);
                itemView.getContext().startActivity(intent);
            });
            mBannerView.getViewPager().addOnPageChangeListener(new ViewPager.SimpleOnPageChangeListener() {
                @Override
                public void onPageSelected(int position) {
                    super.onPageSelected(position);
                    setIndicator(position);
                }
            });
        }

        private boolean isEmpty(CharSequence charSequence) {
            return TextUtils.isEmpty(charSequence) || "null".equalsIgnoreCase(charSequence.toString());
        }

        /**
         * 地图导航
         */
        private void navigate() {
            if (mAdapter.mHotel == null) {
                return;
            }
            String lag = null;
            String lng = null;
            if (!isEmpty(mAdapter.mHotel.getbLag()) && !isEmpty(mAdapter.mHotel.getbLng())) {
                lag = mAdapter.mHotel.getbLag();
                lng = mAdapter.mHotel.getbLng();
            } else if (!isEmpty(mAdapter.mHotel.getGdLag()) && !isEmpty(mAdapter.mHotel.getGdLng())) {
                lag = mAdapter.mHotel.getGdLag();
                lng = mAdapter.mHotel.getGdLng();
            } else if (!isEmpty(mAdapter.mHotel.gettLag()) && !isEmpty(mAdapter.mHotel.gettLng())) {
                lag = mAdapter.mHotel.gettLag();
                lng = mAdapter.mHotel.gettLng();
            } else if (!isEmpty(mAdapter.mHotel.getgLag()) && !isEmpty(mAdapter.mHotel.getgLng())) {
                lag = mAdapter.mHotel.getgLag();
                lng = mAdapter.mHotel.getgLng();
            }
            if (TextUtils.isEmpty(lag) || TextUtils.isEmpty(lng)) {
                return;
            }
            double[] ls = CoordinateTransform.transformBD09ToGCJ02(Double.parseDouble(lng), Double.parseDouble(lag));
            NavigateUtil.navigate(itemView.getContext(), ls[1] + "", ls[0] + "", mAdapter.mHotel.getInnName());
        }

        /**
         * 轮播图指示器
         */
        private void setIndicator(int position) {
            if (mTvIndicator.getTag() == null) {
                return;
            }
            int size = (int) mTvIndicator.getTag();
            if (size < 2) {
                mTvIndicator.setText(null);
                return;
            }
            int currentPosition = position % size;
            String text = (currentPosition + 1) + "/" + size;
            SpannableString string = new SpannableString(text);
            string.setSpan(new AbsoluteSizeSpan(28, true), 0, text.indexOf("/"), SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE);
            string.setSpan(new AbsoluteSizeSpan(14, true), text.indexOf("/"), text.length(), SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE);
            mTvIndicator.setText(string);
        }

        @Override
        public void bindTo(Object o, int position) {
            super.bindTo(o, position);
            if (mAdapter.mHotel != null) {
                //酒店名称
                mTvName.setText(mAdapter.mHotel.getInnName());
                //酒店地址
                mTvAddress.setText(mAdapter.mHotel.getAddress());
                List<String> list = mAdapter.mHotel.getImageUrl();
                if (list == null || list.isEmpty()) {
                    mTvIndicator.setTag(0);
                } else {
                    mTvIndicator.setTag(list.size());
                }
                //轮播图
                mBannerView.setAdList(list);
                mBannerView.refresh();
                setIndicator(0);
            }
        }
    }

    /**
     * 入住和离店时间
     */
    private static class HotelDateViewHolder extends BaseRecyclerViewHolder<Object> {
        private final TextView mTvDate;
        private final TextView mTvDuration;
        private long mStartDate;
        private long mEndDate;
        private final HotelDetailAdapter mAdapter;

        public HotelDateViewHolder(View itemView, HotelDetailAdapter adapter) {
            super(itemView);
            this.mAdapter = adapter;
            mTvDate = itemView.findViewById(R.id.tv_date);
            mTvDuration = itemView.findViewById(R.id.tv_duration);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            itemView.setOnClickListener(v -> {
                //选择日期弹窗
                Activity activity = AppUtil.getActivity(v.getContext());
                if (AppUtil.checkActivityIsRun(activity)) {
                    HotelSelectDateDialog dateDialog = new HotelSelectDateDialog(v.getContext());
                    dateDialog.setEndDate(mEndDate);
                    dateDialog.setStartDate(mStartDate);
                    dateDialog.setOnDateSelected((startDate, endDate) -> {
                        mAdapter.setDate(startDate, endDate);
                        if (activity instanceof IRefreshContext) {
                            ((IRefreshContext<?>) activity).getRefreshController().getListData(0);
                        }
                        bindTo(null, getAdapterPosition());
                    });
                    dateDialog.show();
                }
            });
        }

        @Override
        public void bindTo(Object o, int position) {
            super.bindTo(o, position);
            mEndDate = mAdapter.mEndDate;
            mStartDate = mAdapter.mStartDate;

            mTvDate.setText(mTvDate.getResources().getString(R.string.txt_hotel_in_date, HotelSelectDateDialog.formatDate(mStartDate, false), HotelSelectDateDialog.formatDate(mEndDate, false)));
            int day = (int) ((mEndDate - mStartDate) / AppUtil.ONEDAY);
            mTvDuration.setText(mTvDuration.getResources().getString(R.string.txt_hotel_in_duration, day));
        }
    }

    private RecyclerView mRecyclerView;

    @Override
    public void onAttachedToRecyclerView(@NonNull RecyclerView recyclerView) {
        super.onAttachedToRecyclerView(recyclerView);
        mRecyclerView = recyclerView;
    }

    /**
     * 酒店房间ViewHolder
     */
    private static class HotelRoomViewHolder extends BaseRecyclerViewHolder<Object> {
        ImageView mIvImage;
        View mShowView;
        TextView mTvImgCount;
        TextView mTvDesc;
        TextView mTvName;
        TextView mTvFast;
        TextView mTvPrice;
        TextView mTvOriginalPrice;
        View mRlChoosePayType;
        RecyclerView mRecyclerView;
        HotelDetailAdapter mAdapter;
        HotelRoom hotelRoom;
        /**
         * 酒店房间详情 弹窗
         */
        HotelRoomDialog.Utils mUtils;

        public HotelRoomViewHolder(View itemView, HotelDetailAdapter adapter) {
            super(itemView);
            mAdapter = adapter;
            mIvImage = itemView.findViewById(R.id.iv_image);
            mTvImgCount = itemView.findViewById(R.id.tv_count);
            mTvDesc = itemView.findViewById(R.id.tv_desc);
            mTvName = itemView.findViewById(R.id.tv_name);
            mTvFast = itemView.findViewById(R.id.tv_break_fast);
            mTvPrice = itemView.findViewById(R.id.tv_price);
            mTvOriginalPrice = itemView.findViewById(R.id.tv_original_price);
            mRlChoosePayType = itemView.findViewById(R.id.rl_choose_pay_type);
            mRecyclerView = itemView.findViewById(R.id.item_recyclerView);
            mShowView = itemView.findViewById(R.id.iv_show);
            itemView.setOnClickListener(v -> {
                if (mShowView.isEnabled()) {
                    mShowView.performClick();
                }
            });
            mShowView.setOnClickListener(v -> {
                //价格列表展示与折叠
                if (mRlChoosePayType.getVisibility() == View.VISIBLE) {
                    mRlChoosePayType.setVisibility(View.GONE);
                    v.setRotation(0);
                } else {
                    v.setRotation(180);
                    if (mAdapter.mRecyclerView != null) {
                        //自动滑动，展示出折叠内容
                        RecyclerView.LayoutManager manager = mAdapter.mRecyclerView.getLayoutManager();
                        if (manager instanceof LinearLayoutManager) {
                            int last = ((LinearLayoutManager) manager).findLastVisibleItemPosition();
                            if (last == getAdapterPosition()) {
                                if (last + 1 < mAdapter.getItemCount()) {
                                    mAdapter.mRecyclerView.smoothScrollToPosition(last + 1);
                                } else {
                                    ((LinearLayoutManager) manager).scrollToPositionWithOffset(getAdapterPosition(), 0);
                                }
                            }
                        }
                    }
                    mRlChoosePayType.setVisibility(View.VISIBLE);
                    if (mRecyclerView.getAdapter() == null) {
                        initRecyclerView();
                    }
                }
            });
        }

        /**
         * 初始化价格RecyclerView视图
         */
        private void initRecyclerView() {
            if (hotelRoom == null || hotelRoom.getProducts() == null || hotelRoom.getProducts().isEmpty()) {
                mRecyclerView.setAdapter(null);
                return;
            }
            RoomPriceAdapter adapter = new RoomPriceAdapter(mRecyclerView.getContext(), hotelRoom.getProducts(), mProductsBean -> {
                //下订
                OrderActivity.startActivity(itemView.getContext(),
                        hotelRoom.getInnId(),
                        hotelRoom.getRoomTypeCode(),
                        mProductsBean.getProductCode(),
                        mAdapter.getStartDate(),
                        mAdapter.getEndDate(),
                        mAdapter.mHotel.getInnName(),
                        hotelRoom.getRoomTypeName(),
                        hotelRoom.getMaxCheckIn());
            });
            adapter.setOnItemClickListener((itemView, position) -> {
                //房间详情弹窗
                if (mUtils == null) {
                    mUtils = new HotelRoomDialog.Utils();
                }
                mUtils.showDialog(itemView.getContext(),
                        hotelRoom.getInnId(),
                        hotelRoom.getRoomTypeCode(),
                        adapter.getData().get(position).getProductCode(),
                        mAdapter.getStartDate(), mAdapter.getEndDate());
            });
            mRecyclerView.setAdapter(adapter);
        }

        @Override
        public void bindTo(Object o, int position) {
            super.bindTo(o, position);
            if (!(o instanceof HotelRoom)) {
                return;
            }
            hotelRoom = (HotelRoom) o;
            mTvImgCount.setVisibility(View.INVISIBLE);
//            mTvImgCount.setText("1张");
            HotelUtil.loadHotelImage(mIvImage, hotelRoom.getImageUrl(), 0);
            //酒店名称
            mTvName.setText(hotelRoom.getRoomTypeName());
            //酒店描述拼接
            String desc = "";
            if (!TextUtils.isEmpty(hotelRoom.getBedTypeName())) {
                desc = desc + " " + hotelRoom.getBedTypeName();
            }
            if (!TextUtils.isEmpty(hotelRoom.getBedWidthName())) {
                desc = desc + " " + hotelRoom.getBedWidthName();
            }
            if (!TextUtils.isEmpty(hotelRoom.getWindowName())) {
                desc = desc + " " + hotelRoom.getWindowName();
            }
            mTvDesc.setText(desc);
            //早餐
            if (hotelRoom.getBreakfastCount() == 1) {
                mTvFast.setText(R.string.txt_hotel_break_fast);
            } else if (hotelRoom.getBreakfastCount() == 2) {
                mTvFast.setText(R.string.txt_hotel_double_break_fast);
            } else {
                mTvFast.setText(R.string.txt_hotel_no_break_fast);
            }
            //当前价格
            mTvPrice.setText(ModelUtils.getRmbOrEmptyString(AppUtil.convertBigDecimalPrice(hotelRoom.getRealHousePrice(), ""), true));
            //门市价
            if (hotelRoom.getMarketPrice() != null && !BigDecimal.ZERO.equals(hotelRoom.getMarketPrice())) {
                StrikethroughSpan span = new StrikethroughSpan();
                String p = "门市价" + ModelUtils.getRmbOrEmptyString(AppUtil.convertBigDecimalPrice(hotelRoom.getMarketPrice(), ""));
                SpannableString string = new SpannableString(p);
                string.setSpan(span, p.lastIndexOf("¥"), p.length(), Spanned.SPAN_EXCLUSIVE_INCLUSIVE);
                mTvOriginalPrice.setText(string);
            } else {
                mTvOriginalPrice.setText("");
            }
            //折叠视图
            mShowView.setEnabled(hotelRoom.getProducts() != null && hotelRoom.getProducts().size() > 0);
            mShowView.setVisibility(mShowView.isEnabled() ? View.VISIBLE : View.INVISIBLE);
            mRlChoosePayType.setVisibility(View.GONE);
            mShowView.setRotation(0);
            mRecyclerView.setAdapter(null);
        }
    }

    static class ViewType {
        /**
         * 顶部
         */
        private static final int TYPE_TOP = 1;
        /**
         * 日期
         */
        private static final int TYPE_TITLE = 2;
        /**
         * 房间item
         */
        private static final int TYPE_ROOM = 3;
        /**
         * 空UI
         */
        static final int TYPE_EMPTY = 4;
    }

    public long getStartDate() {
        return mStartDate;
    }

    public long getEndDate() {
        return mEndDate;
    }
}
