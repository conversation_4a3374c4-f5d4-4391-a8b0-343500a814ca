package com.cloudy.linglingbang.activity.basic;

import android.view.View;
import android.widget.TextView;

import com.cloudy.linglingbang.R;

import androidx.annotation.Nullable;
import butterknife.BindView;

/**
 * fragment中的toolbar
 * 用于在fragment中控制toolbar
 *
 * <AUTHOR>
 * @date 2018/9/11
 */
public class BaseTitleFragment extends BaseFragment {

    /**
     * 公共导航(可能为空)
     */
    @Nullable
    @BindView(R.id.common_toolbar)
    protected androidx.appcompat.widget.Toolbar mToolbar;

    /**
     * 左边title文字(可能为空)
     */
    @Nullable
    @BindView(R.id.toolbar_title_left)
    protected TextView mToolbarTitleLeft;
    /**
     * 左边title文字(可能为空)
     */
    @Nullable
    @BindView(R.id.toolbar_title_right)
    protected TextView mToolbarTitleRight;

    /**
     * 中间title文字(可能为空)
     */
    @Nullable
    @BindView(R.id.toolbar_title)
    protected TextView mToolbarTitle;

    @Override
    protected int getLayoutRes() {
        return 0;
    }

    @Override
    protected void initViews() {
        super.initViews();
        mToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //点击左上角返回时判断是否是从通知栏进入,如果从通知栏进入，则返回时打开首页
                getActivity().finish();
            }
        });
    }

    /**
     * 定义Toolbar背景颜色
     *
     * @param resId 背景颜色资源id
     */
    public void setToolbarBackground(int resId) {
        if (mToolbar != null) {
            mToolbar.setBackgroundColor(getResources().getColor(resId));
        }
    }

}
