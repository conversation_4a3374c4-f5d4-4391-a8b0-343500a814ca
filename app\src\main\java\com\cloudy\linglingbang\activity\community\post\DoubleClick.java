package com.cloudy.linglingbang.activity.community.post;

import android.view.MotionEvent;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.util.DeviceUtil;
import com.cloudy.linglingbang.app.widget.recycler.EmptySupportedRecyclerView;

import java.util.Timer;
import java.util.TimerTask;

/**
 *
 * <AUTHOR>
 * @date 2021/12/14
 */
public class DoubleClick {
    private int clickCount;
    private long firstClickTime;
    private final int CLICK_DELAY = 300;
    private final static int MOVE_OFFSET = 10;
    private float mLastMotionY;
    private float mLastMotionX;
    private final Timer cleanClickTimer = new Timer();
    private final OnDoubleClickListener listener;
    private final EmptySupportedRecyclerView mRecyclerView;

    protected DoubleClick(OnDoubleClickListener l, EmptySupportedRecyclerView recyclerView) {
        listener = l;
        mRecyclerView = recyclerView;
    }

    public interface OnDoubleClickListener {
        void onDoubleClick(MotionEvent event);
    }

    int[] location = new int[2];

    public void dispatchTouchEvent(MotionEvent event) {
        final float y = event.getY();
        final float x = event.getX();
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            mLastMotionY = y;
            mLastMotionX = x;
            clickCount++;
            if (clickCount == 1) {
                firstClickTime = System.currentTimeMillis();
                //超过监听时间50MS还没有再次点击，则将点击次数，点击事件清零。
                cleanClickTimer.schedule(new TimerTask() {
                    @Override
                    public void run() {
                        clickCount = 0;
                        firstClickTime = 0;
                    }
                }, CLICK_DELAY + 50);
            } else if (clickCount == 2) {
                long secondClickTime = System.currentTimeMillis();
                if (secondClickTime - firstClickTime <= CLICK_DELAY) {
                    if (mLastMotionY < DeviceUtil.getScreenHeight() - mRecyclerView.getContext().getResources().getDimensionPixelOffset(R.dimen.normal_96) && mLastMotionY > mRecyclerView.getContext().getResources().getDimensionPixelOffset(R.dimen.normal_128)) {
                        listener.onDoubleClick(event);
                    }
                }
                /*if (mRecyclerView != null) {
                    LinearLayoutManager layoutManager = (LinearLayoutManager) mRecyclerView.getLayoutManager();
                    int lastPosition = layoutManager.findLastVisibleItemPosition();
                    for (int i = 0; i < layoutManager.getChildCount(); i++) {
                        View childAt = layoutManager.getChildAt(i);
                        if (childAt != null) {
                            RecyclerView.ViewHolder childViewHolder = mRecyclerView.getChildViewHolder(childAt);
                            if (childViewHolder instanceof PostDetailNativeAdapter.PostDetailContentViewHolder) {
                                if(lastPosition <= i){
                                    listener.onDoubleClick(event);
                                    return;
                                }
                            }
                            if (childViewHolder instanceof PostDetailNativeAdapter.PostDetailBottomViewHolder) {
                                PostDetailNativeAdapter.PostDetailBottomViewHolder postDetailBottomViewHolder =
                                        (PostDetailNativeAdapter.PostDetailBottomViewHolder) childViewHolder;
                                if(lastPosition < i){
                                    listener.onDoubleClick(event);
                                    return;
                                }
                                postDetailBottomViewHolder.itemView.getLocationInWindow(location);
                                if (mLastMotionY < location[1] && mLastMotionY > mRecyclerView.getContext().getResources().getDimensionPixelOffset(R.dimen.normal_128)) {
                                    listener.onDoubleClick(event);
                                }
                            }
                        }
                    }

                }*/
                clickCount = 0;
                firstClickTime = 0;
            }
        }

        if (event.getAction() == MotionEvent.ACTION_MOVE) {
            final int yDiff = (int) Math.abs(y - mLastMotionY);
            final int xDiff = (int) Math.abs(x - mLastMotionX);
            boolean yMoved = yDiff > MOVE_OFFSET;
            boolean xMoved = xDiff > MOVE_OFFSET;
            // 判断是否是移动
            if (yMoved || xMoved) {
                clickCount = 0;
                firstClickTime = 0;
            }
        }
    }

}
