package com.cloudy.linglingbang.activity.car.home;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.baidu.mapapi.model.LatLng;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseFragment;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.car.connected.CarControlActivity;
import com.cloudy.linglingbang.activity.car.list.BindCarListActivity;
import com.cloudy.linglingbang.activity.car.list.VertifyCarListInfoManager;
import com.cloudy.linglingbang.activity.service.newenergy.CarConditionActivity;
import com.cloudy.linglingbang.activity.service.newenergy.map.BaiduMapViewActivity;
import com.cloudy.linglingbang.activity.service.newenergy.map.EocoderUtils;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.constants.WebUrlConfigConstant;
import com.cloudy.linglingbang.model.car.home.CarOtherInfo;
import com.cloudy.linglingbang.model.car.home.MyCarType;
import com.cloudy.linglingbang.model.car.home.WorkOrderCarHealth;
import com.cloudy.linglingbang.model.car.map.BaiduEocoderResult;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BackgroundSubscriber;
import com.cloudy.linglingbang.model.server.DefaultCarInfo;
import com.cloudy.linglingbang.web.CommonWebActivity;

import java.util.Locale;

import androidx.fragment.app.Fragment;
import butterknife.BindView;
import butterknife.OnClick;

/**
 * 爱车列表上部滑动的每一个fragment
 *
 * <AUTHOR>
 * @date 2020-02-26
 */
public class MyCarListFragment extends BaseFragment {

    public static final String INTENT_KEY = "car";

    /**
     * 保养剩余时间
     */
    @BindView(R.id.tv_maintain_remainder)
    TextView mTvMaintainRemainder;
    /**
     * 车辆健康状态
     */
    @BindView(R.id.tv_car_healthy_status)
    TextView mTvCarHealthyStatus;

    /**
     * 车图
     */
    @BindView(R.id.iv_car_photo)
    ImageView mIvCarPhoto;

    /**
     * 进入车控按钮
     */
    @BindView(R.id.btn_connect_car)
    Button mBtnConnectCar;

    /**
     * 新能源布局
     */
    @BindView(R.id.ll_new_energy)
    LinearLayout mLlNewEnergy;

    /**
     * 续航里程数字
     */
    @BindView(R.id.tv_endurance_mileage)
    TextView mTvEnduranceMileage;

    /**
     * 剩余电量
     */
    @BindView(R.id.tv_surplus_electricity)
    TextView mTvSurplusElectricity;

    /**
     * 爱车位置
     */
    @BindView(R.id.tv_car_position)
    TextView mTvCarPosition;

    /**
     * 爱车位置
     */
    @BindView(R.id.rl_car_position)
    RelativeLayout mLlCarPosition;

    /**
     * 爱车名称
     */
    @BindView(R.id.tv_car_name)
    TextView mTvCarName;

    /**
     * 我的爱车数量
     */
    @BindView(R.id.tv_my_car_count)
    TextView mTvMyCarCount;
    /**
     * 授权按钮
     */
    @BindView(R.id.tv_authorize)
    TextView mTvAuthorize;
    @BindView(R.id.ll_status)
    LinearLayout mLlStatus;
    @BindView(R.id.tv_status)
    TextView mTvStatus;
    @BindView(R.id.iv_status)
    ImageView mIvStatus;

    private MyCarType mMyCarType;

    CarOtherInfo mCarOtherInfo;
    //新能源车辆信息
    private DefaultCarInfo mDefaultCarInfo;

    public static Fragment newInstance(MyCarType carType) {
        Fragment fragment = new MyCarListFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable(INTENT_KEY, carType);
        fragment.setArguments(bundle);
        return fragment;
    }


    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        if (getArguments() != null) {
            mMyCarType = (MyCarType) getArguments().getSerializable(INTENT_KEY);
        }
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_my_car_list;
    }

    @Override
    protected void initViews() {
        super.initViews();
        if (mMyCarType == null) {
            return;
        }
        //获取车辆其他信息（如保养信息）
//        if (mCarOtherInfo == null) {
//            getCarOtherInfo();
//        }
        fillCarInfoData();
    }

    /**
     * 填充车信息
     */
    private void fillCarInfoData() {
        if (mMyCarType.getCarType() == MyCarType.MyListCarType.CAR_TYPE_FUEL_TRUCK) {
            //如果是燃油车
            mBtnConnectCar.setVisibility(View.GONE);
            mLlNewEnergy.setVisibility(View.GONE);
            mLlCarPosition.setVisibility(View.GONE);
            mTvAuthorize.setVisibility(View.GONE);
        } else if (mMyCarType.getCarType() == MyCarType.MyListCarType.CAR_TYPE_CONNECT) {
            //如果是车联网的车
            mBtnConnectCar.setVisibility(View.VISIBLE);
            mLlNewEnergy.setVisibility(View.GONE);
            mLlCarPosition.setVisibility(View.GONE);
            mTvAuthorize.setVisibility(View.GONE);
        } else {
            //如果是新能源的车
            mBtnConnectCar.setVisibility(View.GONE);
            mLlNewEnergy.setVisibility(View.VISIBLE);
            //先查询数据库，如果有数据就显示，没有再请求接口。一分钟内不重复请求
            DefaultCarInfo defaultCarInfo = VertifyCarListInfoManager.getInstance().loadData(mMyCarType.getVin());
            if (defaultCarInfo != null) {
                updateUi(defaultCarInfo);
                //如果大于一分钟，则重新请求数据
                if (System.currentTimeMillis() - defaultCarInfo.getSaveTime() > 60000) {
                    getBasicCarInfo();
                }
            } else {
                getBasicCarInfo();
            }
        }
        //设置车名称
        if (!TextUtils.isEmpty(mMyCarType.getCarNickname())) {
            mTvCarName.setText(mMyCarType.getCarNickname());
        } else if (!TextUtils.isEmpty(mMyCarType.getCarTypeName())) {
            mTvCarName.setText(mMyCarType.getCarTypeName());
        }
        mTvMyCarCount.setText(getContext().getResources().getString(R.string.my_car_count, mMyCarType.getShowCarCount()));
        //设置爱车图片
        new ImageLoad(getContext(), mIvCarPhoto, mMyCarType.getCarImages(), ImageLoad.LoadMode.URL)
                .setPlaceholderAndError(R.drawable.bg_default_car)
                .load();
    }

    /**
     * 获取车辆其他信息（如：保养信息）
     */
    private void getCarOtherInfo() {
        L00bangRequestManager2.getServiceInstance()
                .getCarOtherInfo(mMyCarType.getVin())
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new BackgroundSubscriber<CarOtherInfo>(getActivity()) {
                    @Override
                    public void onSuccess(CarOtherInfo carOtherInfo) {
                        super.onSuccess(carOtherInfo);
                        if (carOtherInfo != null && !TextUtils.isEmpty(carOtherInfo.getMaintainRemindDesc())) {
                            mCarOtherInfo = carOtherInfo;
                            mTvMaintainRemainder.setText(carOtherInfo.getMaintainRemindDesc());
                            mTvMaintainRemainder.setVisibility(View.VISIBLE);
                        } else {
                            mTvMaintainRemainder.setVisibility(View.INVISIBLE);
                        }
                        if (carOtherInfo != null && carOtherInfo.getWorkOrderCarHealthVo() != null) {
                            mCarOtherInfo = carOtherInfo;
                            WorkOrderCarHealth workOrderCarHealth = carOtherInfo.getWorkOrderCarHealthVo();
                            mTvCarHealthyStatus.setVisibility(View.VISIBLE);
                            if (workOrderCarHealth.getReportExpireFlag() == 1) {//已过期
                                mTvCarHealthyStatus.setBackgroundResource(R.drawable.bg_right_corner60_solid_959595);
                            } else {
                                if (workOrderCarHealth.getCarHealthStatus() == 0) {
                                    mTvCarHealthyStatus.setBackgroundResource(R.drawable.bg_right_corner60_solid_5e9d1a);
                                } else if (workOrderCarHealth.getCarHealthStatus() == 1) {
                                    mTvCarHealthyStatus.setBackgroundResource(R.drawable.bg_right_corner60_solid_da5969);
                                } else if (workOrderCarHealth.getCarHealthStatus() == 2) {
                                    mTvCarHealthyStatus.setBackgroundResource(R.drawable.bg_right_corner60_solid_fb833f);
                                }
                            }
                        } else {
                            mTvCarHealthyStatus.setVisibility(View.GONE);
                        }

                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                        mTvMaintainRemainder.setVisibility(View.INVISIBLE);
                        mTvCarHealthyStatus.setVisibility(View.GONE);
                    }
                });

    }

    /**
     * 获取新能源汽车信息
     */
    private void getBasicCarInfo() {
        L00bangRequestManager2.getServiceInstance()
                .getCarBasicInfo(mMyCarType.getVin())
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new BackgroundSubscriber<DefaultCarInfo>(getActivity()) {
                    @Override
                    public void onSuccess(DefaultCarInfo defaultCarInfo) {
                        super.onSuccess(defaultCarInfo);
                        if(getActivity() == null || getActivity().isFinishing()){
                            return;
                        }
                        if (defaultCarInfo != null) {
                            //存储数据库
                            defaultCarInfo.setSaveTime(System.currentTimeMillis());
                            VertifyCarListInfoManager.getInstance().insertOrReplace(defaultCarInfo);
                            updateUi(defaultCarInfo);
                        }
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);

                    }
                });

    }

    private void updateUi(DefaultCarInfo defaultCarInfo) {
        mDefaultCarInfo = defaultCarInfo;
        mTvAuthorize.setVisibility(View.VISIBLE);
        mTvEnduranceMileage.setText(String.valueOf(defaultCarInfo.getRemainMileage()));
        int colorRes = defaultCarInfo.getBatSoc() > 10 ? R.color.common_blue_384967 : R.color.color_ec0000;
        mTvSurplusElectricity.setTextColor(getActivity().getResources().getColor(colorRes));
        mTvSurplusElectricity.setText(String.valueOf(defaultCarInfo.getBatSoc()));
        //车辆状态的显示
        // 1:加热中  2:充电中  3:保温中   4:保温完成  5:补电中
        if (defaultCarInfo.getShowBatteryStatusOrZero() > 0) {
            mLlStatus.setVisibility(View.VISIBLE);
            mTvStatus.setText(defaultCarInfo.getShowBatteryStatusDesc());
            int status = defaultCarInfo.getShowBatteryStatusOrZero();
            if (status == 1) {
                mIvStatus.setImageResource(R.drawable.ic_status_heating);
            } else if (status == 2) {
                mIvStatus.setImageResource(R.drawable.ic_status_charging);
            } else if (status == 3) {
                mIvStatus.setImageResource(R.drawable.ic_status_heat_preservation);
            } else if (status == 4) {
                mIvStatus.setImageResource(R.drawable.ic_status_insulation_completed);
            } else if (status == 5) {
                mIvStatus.setImageResource(R.drawable.ic_status_electricity);
            }
        } else {
            mLlStatus.setVisibility(View.GONE);
        }
        //显示车辆位置
        mLlCarPosition.setVisibility(View.VISIBLE);
        if(!TextUtils.isEmpty(defaultCarInfo.getAddr())){
            mTvCarPosition.setText(defaultCarInfo.getAddr());
        }else{
            getCarLocation(defaultCarInfo.getLatitude(), defaultCarInfo.getLongitude());
        }
    }

    /**
     * 根据经纬度获取位置信息
     */
    private void getCarLocation(double lat, double lng) {
        //根据经纬度获取地址信息
        EocoderUtils.getAddressInfo(getActivity(), lat, lng, new EocoderUtils.OnResultListener() {
            @Override
            public void onSuccess(BaiduEocoderResult.EocodeAddressInfo result) {
                if (!TextUtils.isEmpty(result.getFormatted_address())) {
                    mTvCarPosition.setText(result.getFormatted_address());
                } else {
                    String address = getString(R.string.txt_location_failure);
                    mTvCarPosition.setText(address);
                }
            }

            @Override
            public void onFailure(String message) {
                String address = getString(R.string.txt_location_failure);
                mTvCarPosition.setText(address);
            }
        });

    }

    /**
     * 点击车辆位置
     */
    @OnClick(R.id.rl_car_position)
    void clickCarPosition() {
        if (!AppUtil.checkLogin(getActivity())) {
            return;
        }
        if (mDefaultCarInfo != null) {
            SensorsUtils.sensorsClickBtn("查看爱车位置按钮", "我的");
            LatLng latLng = new LatLng(mDefaultCarInfo.getLatitude(), mDefaultCarInfo.getLongitude());
            BaiduMapViewActivity.IntentExtra intentExtra = new BaiduMapViewActivity.IntentExtra(latLng.longitude, latLng.latitude, System.currentTimeMillis(), mDefaultCarInfo == null ? "" : mDefaultCarInfo.getCarTypeName(), mDefaultCarInfo == null ? "" : mDefaultCarInfo.getCollectTime(), mDefaultCarInfo.getVin());
            BaiduMapViewActivity.startActivity(getActivity(), intentExtra);
        } else {
            ToastUtil.showMessage(getActivity(), "获取车辆信息失败，请稍后再试~");
        }
    }

    @OnClick(R.id.tv_recharge)
    void clickRecharge() {
        SensorsUtils.sensorsClickBtn("一键充电", "我的");
        if (mDefaultCarInfo != null && !TextUtils.isEmpty(mDefaultCarInfo.getChargeUrl())) {
            CommonWebActivity.startActivity(getActivity(), mDefaultCarInfo.getChargeUrl());
        } else {
            ToastUtil.showMessage(getActivity(), "请稍候再试");
        }
    }


    /**
     * 点击上半部分（车图以及右边的数据）
     */
    @OnClick({R.id.iv_car_photo, R.id.ll_new_energy, R.id.btn_connect_car})
    protected void onClickPic() {
        if (mMyCarType.getCarType() == MyCarType.MyListCarType.CAR_TYPE_FUEL_TRUCK) {
            //如果是燃油车，则进入爱车列表
            SensorsUtils.sensorsClickBtn("我的爱车（燃油车）", "我的");
            IntentUtils.startActivity(getActivity(), BindCarListActivity.class);
        } else if (mMyCarType.getCarType() == MyCarType.MyListCarType.CAR_TYPE_CONNECT) {
            //如果是车联网，则进入车控页面
            SensorsUtils.sensorsClickBtn("我的爱车（车联网车）", "我的");
            CarControlActivity.startActivity(getActivity(), mMyCarType.getVin());
        } else {
            //如果是新能源，则进入车控页面
            SensorsUtils.sensorsClickBtn("我的爱车（新能源车）", "我的");
            IntentUtils.startActivity(getContext(), CarConditionActivity.class, mMyCarType.getVin());
        }
    }

    /**
     * 点击车名字进入爱车列表
     */
    @OnClick(R.id.ll_car_name)
    protected void onClickName() {
        SensorsUtils.sensorsClickBtn("点击爱车列表", "我的-保客", mMyCarType.getCarNickname());
        IntentUtils.startActivity(getActivity(), BindCarListActivity.class);
    }

    /**
     * 点击保养时间，跳转到预约服务
     */
    @OnClick(R.id.tv_maintain_remainder)
    protected void onMaintainClick() {
        String url = String.format(Locale.getDefault(), WebUrlConfigConstant.RESERVATION_SERVICE_WITH_VIN, mMyCarType.getVin());
        JumpPageUtil.goCommonWeb(getContext(), url);
    }

    /**
     * 点击授权
     */
    @OnClick(R.id.tv_authorize)
    protected void onAuthorizeClick() {
        SensorsUtils.sensorsClickBtn(mTvAuthorize.getText().toString(), "我的");
        String url = String.format(Locale.getDefault(), WebUrlConfigConstant.CAR_AUTHORIZE, mMyCarType.getVin());
        JumpPageUtil.goCommonWeb(getContext(), url);
    }

    /**
     * 点击车辆健康
     */
    @OnClick(R.id.tv_car_healthy_status)
    protected void onCarHealthyClick() {
        SensorsUtils.sensorsClickBtn(mTvCarHealthyStatus.getText().toString(), "我的", "健康报告（" + mMyCarType.getCarTypeName() + "）");
        String url = String.format(Locale.getDefault(), WebUrlConfigConstant.CAR_HEALTHY, mCarOtherInfo.getWorkOrderCarHealthVo().getOrderSn());
        JumpPageUtil.goCommonWeb(getContext(), url);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
