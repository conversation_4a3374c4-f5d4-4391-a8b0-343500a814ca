package com.cloudy.linglingbang.activity.car.energy.bluetooth;

import com.blankj.utilcode.util.ToastUtils;
import com.cloudy.linglingbang.activity.car.energy.ConnectHelper;
import com.cloudy.linglingbang.activity.travel.park.E300BlueControl;

/**
 * 发送蓝牙车控指令工具类
 * Created by LiYeWen on 2025/05/15
 */
public class SendControlUtil {

    private static SendControlUtil instance;

    /**
     * 获取单例对象
     * @return
     */
    public static SendControlUtil getInstance() {
        if (instance == null) {
            synchronized (SendControlUtil.class) {
                instance = new SendControlUtil();
            }
        }
        return instance;
    }

    /**
     * 开始发送蓝牙指令
     * @param blueControl 对应的车控指令
     */
    public void startSendControl(int blueControl) {
        switch (blueControl) {
            case ConnectHelper.UNLOCK_DOOR:
                sendControl(E300BlueControl.OPEN_DOOR);//开门
                break;
            case ConnectHelper.POWER_OFF:
                sendControl(E300BlueControl.POWER_OFF);//下电
                break;
            case ConnectHelper.OPEN_TAILGATE:
                sendControl(E300BlueControl.OPEN_TAILGATE);//开尾门
                break;
            case ConnectHelper.MODE_PARK_IN:
                sendControl(E300BlueControl.MODE_PARK_IN); //遥控模式
                break;
            case ConnectHelper.START_PARK:
                sendControl(E300BlueControl.START_PARK);//开始泊车
                break;
            case ConnectHelper.PAUSE_PARK:
                sendControl(E300BlueControl.PAUSE_PARK);//暂停泊车
                break;
            case ConnectHelper.STOP_PARK:
                sendControl(E300BlueControl.STOP_PARK);//终止泊车
                break;
            case ConnectHelper.LOCK_DOOR:
                sendControl(E300BlueControl.CLOSE_DOOR);//关门
                break;
            case ConnectHelper.MODE_PARK_OUT:
                sendControl(E300BlueControl.MODE_PARK_OUT);//遥控泊出
                break;
            case ConnectHelper.PARK_MODE_LEFT_V:
                sendControl(E300BlueControl.PARK_MODE_LEFT_V);//泊出方式 左 垂直
                break;
            case ConnectHelper.PARK_MODE_LEFT_H:
                sendControl(E300BlueControl.PARK_MODE_LEFT_H);//泊出方式 左 水平 ***
                break;
            case ConnectHelper.PARK_MODE_RIGHT_V:
                sendControl(E300BlueControl.PARK_MODE_RIGHT_V);//泊出方式 右 垂直
                break;
            case ConnectHelper.PARK_MODE_RIGHT_H:
                sendControl(E300BlueControl.PARK_MODE_RIGHT_H);//泊出方式 右 水平 ***
                break;
            case ConnectHelper.PARK_MODE_FORWARD:
                sendControl(E300BlueControl.PARK_MODE_FORWARD);//泊出模式 向前
                break;
            case ConnectHelper.MODE_PARK_MANUAL:
                sendControl(E300BlueControl.MODE_PARK_MANUAL);//泊出模式 手动泊车
                break;
            case ConnectHelper.PARK_MODE_BACKWARD:
                sendControl(E300BlueControl.PARK_MODE_BACKWARD);//后退
                break;
            case ConnectHelper.PARK_MODE_FORWARD_V:
                sendControl(E300BlueControl.PARK_MODE_FORWARD_V);//泊出方式 前 垂直 ***
                break;
            case ConnectHelper.PARK_MODE_FORWARD_H:
                sendControl(E300BlueControl.PARK_MODE_FORWARD_H);//泊出方式 前 水平
                break;
            case ConnectHelper.PARK_MODE_LEFTWARD:
                sendControl(E300BlueControl.PARK_MODE_LEFTWARD);//向左
                break;
            case ConnectHelper.PARK_MODE_RIGHTWARD:
                sendControl(E300BlueControl.PARK_MODE_RIGHTWARD);//向右
                break;
            case ConnectHelper.PARK_MODE_UP_LEFT:
                sendControl(E300BlueControl.PARK_MODE_UP_LEFT);//左前
                break;
            case ConnectHelper.PARK_MODE_UP_RIGHT:
                sendControl(E300BlueControl.PARK_MODE_UP_RIGHT);//右前
                break;
            case ConnectHelper.PARK_MODE_DOWN_LEFT:
                sendControl(E300BlueControl.PARK_MODE_BACK_LEFT);//左后
                break;
            case ConnectHelper.PARK_MODE_DOWN_RIGHT:
                sendControl(E300BlueControl.PARK_MODE_BACK_RIGHT);//右后
                break;
            case ConnectHelper.PARK_MODE_RETURN:
                sendControl(E300BlueControl.PARK_MODE_RETURN);//回正
                break;
            case ConnectHelper.ONE_KEY_OUT:
                sendControl(E300BlueControl.MODE_PARK_OUT_2);//一键泊出
                break;
            case ConnectHelper.START_PARK_OUT:
                sendControl(E300BlueControl.START_OUT_2);//开始一键泊出
                break;
            case ConnectHelper.PAUSE_PARK_OUT:
                sendControl(E300BlueControl.PAUSE_OUT_2);//暂停一键泊出
                break;
            case ConnectHelper.STOP_PARK_OUT:
                sendControl(E300BlueControl.STOP_OUT_2);//终止一键泊出
                break;
            case ConnectHelper.START_PARK_IN:
                sendControl(E300BlueControl.START_PARK_2);//开始一键泊入
                break;
            case ConnectHelper.PAUSE_PARK_IN:
                sendControl(E300BlueControl.PAUSE_PARK_2);//暂停一键泊入
                break;
            case ConnectHelper.STOP_PARK_IN:
                sendControl(E300BlueControl.STOP_PARK_2);//终止一键泊入
                break;
            case ConnectHelper.SET_OUT_FRONT:
                sendControl(E300BlueControl.SET_FRONT_2);//设置向前泊出
                break;
            case ConnectHelper.SET_OUT_AFTER:
                sendControl(E300BlueControl.SET_AFTER_2);//设置向后泊出
                break;
            case ConnectHelper.SET_OUT_LEFT:
                sendControl(E300BlueControl.SET_LEFT_2);//设置向左泊出
                break;
            case ConnectHelper.SET_OUT_RIGHT:
                sendControl(E300BlueControl.SET_RIGHT_2);//设置向右泊出
                break;
            case ConnectHelper.STRAIGHT_FORWARD:
                sendControl(E300BlueControl.STRAIGHT_FORWARD_2);//直线前进
                break;
            case ConnectHelper.STRAIGHT_BACK_2:
                sendControl(E300BlueControl.STRAIGHT_BACK_2);//直线后退
                break;
            default:
                ToastUtils.showShort("蓝牙已连接");
                break;
        }
    }

    /**
     * 发送蓝牙控制指令
     * @param control
     */
    private void sendControl(E300BlueControl control) {
        //调用蓝牙连接工具类的方法发送蓝牙控制指令
        ConnectUtil.getInstance().sendControl(control);
    }

}
