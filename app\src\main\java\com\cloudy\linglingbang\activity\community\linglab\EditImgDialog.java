package com.cloudy.linglingbang.activity.community.linglab;

import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.view.View;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.dialog.alert.BaseBottomAlertDialog;
import com.cloudy.linglingbang.app.widget.dialog.alert.NewCommonAlertDialog;
import com.cloudy.linglingbang.model.tag.LingLabImageBean;

import java.util.ArrayList;
import java.util.List;

/**
 * 删除图片的弹窗
 *
 * <AUTHOR>
 * @date 2021/9/6
 */
public class EditImgDialog extends BaseBottomAlertDialog {

    private TextView mTvEditImg;

    private final ArrayList<LingLabImageBean> mList = new ArrayList<>();
    private final int currentPosition;

    public EditImgDialog(Context context, List<LingLabImageBean> list, int position) {
        super(context);
        mList.clear();
        mList.addAll(list);
        mList.remove(null);
        this.currentPosition = position;
        getAlertController().setButton(DialogInterface.BUTTON_POSITIVE, context.getString(R.string.dialog_delete_img), null, null);
    }

    @Override
    protected void initView() {
        super.initView();
        mTvEditImg = findViewById(R.id.editImg);
        mTvEditImg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                SensorsUtils.sensorsClickBtn("点击图文发布页-编辑图片按钮", "图文发布页", "编辑图片");
                dismiss();
                Intent in = new Intent(mContext, FullScreenImgActivity.class);
                in.putParcelableArrayListExtra(FullScreenImgActivity.EXTRA_IMAGE_URLS, mList);
                in.putExtra(FullScreenImgActivity.EXTRA_IMAGE_INDEX, currentPosition);
                mContext.startActivity(in);

                /*FullScreenImgDialog fullScreenImgDialog = new FullScreenImgDialog(mContext, mList, currentPosition);
                fullScreenImgDialog.show();*/
            }
        });
        //删除的监听
        getAlertController().getButton(BUTTON_POSITIVE).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dismiss();
                NewCommonAlertDialog commonAlertDialog = new NewCommonAlertDialog(mContext, "确定删除这张照片嘛？", new OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialogInterface, int i) {
                        if (mDeleteListener != null) {
                            mDeleteListener.removeImage(currentPosition);
                        }
                        dismiss();
                    }
                });
                commonAlertDialog.show();
            }
        });
    }

    @Override
    protected int getDefaultLayoutResId() {
        return R.layout.dialog_ling_lab_delete_img;
    }

    public void setDeleteListener(OnImageDeleteListener deleteListener) {
        mDeleteListener = deleteListener;
    }

    OnImageDeleteListener mDeleteListener;

    /**
     * 设置图片删除监听
     */
    public interface OnImageDeleteListener {
        void removeImage(int index);
    }
}
