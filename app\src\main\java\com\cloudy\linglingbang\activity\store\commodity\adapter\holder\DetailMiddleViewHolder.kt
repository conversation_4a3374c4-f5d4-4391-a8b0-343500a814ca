package com.cloudy.linglingbang.activity.store.commodity.adapter.holder

import android.text.TextUtils
import android.view.View
import android.widget.TextView
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.store.commodity.CommodityOpenDialog
import com.cloudy.linglingbang.app.util.ViewHolderUtils
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils

/**
 * 商品详情 参数、规格、服务
 *
 * <AUTHOR>
 * @date 2022/9/26
 */
class DetailMiddleViewHolder(itemView: View?) : BaseCommodityHolder<Any>(itemView) {
    var mTvServiceContent: TextView? = null
    var mTvGuigeContent: TextView? = null
    override fun initItemView(itemView: View?) {
        super.initItemView(itemView)

        itemView?.apply {
            mTvServiceContent = findViewById(R.id.tv_service_content)
            mTvGuigeContent = findViewById(R.id.tv_guide_content)
            findViewById<View>(R.id.rl_select).setOnClickListener {
                var eventName = "点击选择"
                val type =
                    if (mCenterCommodity?.commodityClassifyId == 0) "(整车)" else "(优品)"

                mCenterCommodity?.apply {
                    eventName = " 点击($commodityId)($commodityName)选择"
                }
                SensorsUtils.sensorsClickBtn(eventName, "商品详情页$type", "选择")
                if (context is CommodityOpenDialog) {
                    (context as CommodityOpenDialog).openSkuDialog(CommodityOpenDialog.NOW_BUY)
                }
            }

            ViewHolderUtils.setOnClickListener({
                if (context is CommodityOpenDialog) {
                    (context as CommodityOpenDialog).openParametersDialog()
                }
            }, findViewById(R.id.rl_parameters))
        }
    }

    override fun bindTo(bean: Any?, position: Int) {
        super.bindTo(bean, position)
        itemView.findViewById<View>(R.id.ll_parameters)?.visibility = View.GONE
        mTvServiceContent?.text = mCenterCommodity?.afterSalesReturnStr
        mCenterCommodity?.apply {
            //参数
            itemView.findViewById<View>(R.id.ll_parameters)?.visibility =
                if (argumentsStatus == 1) View.VISIBLE else View.GONE
            //发货地
            itemView.findViewById<View>(R.id.ll_deliver_address)?.visibility =
                if (!TextUtils.isEmpty(logisticsDeliveryAddress)) View.VISIBLE else View.GONE
            itemView.findViewById<TextView>(R.id.tv_deliver_address)?.text =
                logisticsDeliveryAddress

            //服务（车辆商品不显示） -- 2023/4/19 需求：车辆类型不显示此项
            itemView.findViewById<View>(R.id.ll_service)?.visibility =
                if (commodityClassifyId == 0) View.GONE else View.VISIBLE

            //2023/4/19 需求：车辆类型显示为“选择配置”
            itemView.findViewById<TextView>(R.id.tx_rl_select_name)?.let {
                if (commodityClassifyId == 0) {
                    it.text =
                        it.resources.getString(R.string.label_driving_rec_down_load_manage_car_select)
                } else {
                    it.text =
                        it.resources.getString(R.string.label_driving_rec_down_load_manage_select)
                }
            }

            if (TextUtils.isEmpty(localAttributeName)) {
                mTvGuigeContent?.text = null
            } else {
                mTvGuigeContent?.text = mTvGuigeContent?.resources?.getString(
                    R.string.store_choose_commodity_attribute_already,
                    localAttributeName
                )
            }

        }
    }


}