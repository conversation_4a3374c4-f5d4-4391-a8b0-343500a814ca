package com.cloudy.linglingbang.activity.community.post.shortVideo;

import com.cloudy.linglingbang.model.postcard.PostCard;

import java.util.ArrayList;
import java.util.List;

/**
 * 视频列表管理类
 *
 * <AUTHOR>
 * @date 2018/11/22
 */
public class VideoListManager {
    private static VideoListManager instance;

    private List<PostCard> mCardList;

    private int mPosition;

    public static VideoListManager getInstance() {
        if (instance == null) {
            if (instance == null) {
                instance = new VideoListManager();
            }
        }
        return instance;
    }

    public VideoListManager() {
        mCardList = new ArrayList<>();
    }

    /**
     * 设置列表
     */
    public void setList(List<PostCard> cardList, int position) {
        setCardList(cardList);
        mPosition = position;
    }

    public void setCardList(List<PostCard> cardList) {
        mCardList.clear();
        if (cardList != null && cardList.size() > 0) {
            mCardList.addAll(cardList);
        }
    }

    public int getPosition() {
        return mPosition;
    }

    public List<PostCard> getCardList() {
        return mCardList;
    }
}
