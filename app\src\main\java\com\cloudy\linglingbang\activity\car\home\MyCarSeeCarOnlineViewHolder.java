package com.cloudy.linglingbang.activity.car.home;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.welfare.CarInfoEvent;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.widget.banner.AutoResizeHeightImageView;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.entrance.FunctionEntrance;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 线上看车的viewHolder
 *
 * <AUTHOR>
 * @date 2020-02-25
 */
public class MyCarSeeCarOnlineViewHolder extends BaseRecyclerViewHolder<MyCarModel.SeeCarOnlineFunctionModel> {

    @BindView(R.id.tv_subtitle)
    TextView mTvSubtitle;

    @BindView(R.id.tv_title)
    TextView mTvTitle;

    @BindView(R.id.iv_photo)
    AutoResizeHeightImageView mIvPhoto;

    private Context mContext;

    public MyCarSeeCarOnlineViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        ButterKnife.bind(this, itemView);
        mContext = itemView.getContext();
    }

    @Override
    public void bindTo(MyCarModel.SeeCarOnlineFunctionModel seeCarOnlineFunctionModel, int position) {
        super.bindTo(seeCarOnlineFunctionModel, position);
        if (seeCarOnlineFunctionModel.getOriginal() == null || seeCarOnlineFunctionModel.getOriginal().getOriginal() == null) {
            return;
        }
        final List<FunctionEntrance> functionEntrances = seeCarOnlineFunctionModel.getOriginal().getOriginal().getFunctionEntranceList();
        if (functionEntrances != null && functionEntrances.size() > 0) {
            if (!TextUtils.isEmpty(functionEntrances.get(0).getText())) {
                mTvSubtitle.setText(functionEntrances.get(0).getText());
            }
            new ImageLoad(mContext, mIvPhoto, functionEntrances.get(0).getImageUrl(), ImageLoad.LoadMode.URL)
                    .setPlaceholderAndError(R.drawable.ic_common_place_holder)
                    .load();
            mIvPhoto.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    JumpPageUtil.goHomeFunctionEntrance(mContext, functionEntrances.get(0), "",new CarInfoEvent(""));
                }
            });
        }
    }
}
