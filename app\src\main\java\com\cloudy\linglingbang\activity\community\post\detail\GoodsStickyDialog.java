package com.cloudy.linglingbang.activity.community.post.detail;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.view.View;
import android.widget.CheckBox;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.fragment.mynew.util.SelfUserInfoLoader;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.widget.dialog.alert.AlertController;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.postcard.PostConfig;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.error.ApiException;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BackgroundSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.recyclerview.widget.RecyclerView;

/**
 * 好货互通帖子-设置置顶对话框
 *
 * <AUTHOR>
 * @date 2019-11-28
 */
public class GoodsStickyDialog extends CommonAlertDialog implements View.OnClickListener {

    private String mPostId;
    private List<PostConfig.postTopConf> data;
    private Adapter mAdapter;
    private boolean needFinishActivity;

    private GoodsStickyDialog(Context context, List<PostConfig.postTopConf> postTopConfList) {
        super(context);
        data = postTopConfList;
        AlertController alertController = getAlertController();
        alertController.setButton(DialogInterface.BUTTON_POSITIVE, context.getString(R.string.label_driving_rec_close), new OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                finishActivity();
            }
        }, null);
        alertController.setButton(DialogInterface.BUTTON_NEGATIVE, context.getString(R.string.text_post_play_ok), new OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                GoodsStickyDialog.this.onClick(null);
            }
        }, null);
        setCanceledOnTouchOutside(false);
    }

    //type >0 时发布成功后设置置顶
    private int type = 0;

    public void setPostId(String postId) {
        mPostId = postId;
    }

    public void setNeedFinishActivity(boolean needFinishActivity) {
        this.needFinishActivity = needFinishActivity;
    }

    void finishActivity() {
        if (isShowing()) {
            dismiss();
        }
        if (needFinishActivity) {
            Activity activity = AppUtil.getActivity(mContext);
            if (activity != null) {
                activity.finish();
            }
        }
    }

    public void setType(int type) {
        this.type = type;
    }

    private StickyAction mStickyAction;

    public void setStickyAction(StickyAction stickyAction) {
        mStickyAction = stickyAction;
    }

    @Override
    protected void initView() {
        super.initView();
        TextView textView = findViewById(R.id.tv_gold_coins);
        RecyclerView recyclerView = findViewById(R.id.recycler_view);
        int goldCons = SelfUserInfoLoader.getInstance().getUserBalanceInfo().getConsumeGoldCoins();
        textView.setText(mContext.getString(R.string.text_post_detail_set_ding_coin_count, String.valueOf(goldCons)));
        recyclerView.setAdapter(mAdapter = new Adapter(mContext, data));
    }

    @Override
    public void show() {
//        super.show();
//        AlertController alertController = getAlertController();
//        TextView textView = findViewById(R.id.tv_gold_coins);
//        alertController.getButton(DialogInterface.BUTTON_NEGATIVE).setOnClickListener(this);
//        if (type > 0) {
//            ViewHolderUtils.setVisibility(false, textView);
//            ImageView iv_image = findViewById(R.id.iv_image);
//            iv_image.setVisibility(View.VISIBLE);
//            alertController.getButton(DialogInterface.BUTTON_POSITIVE).setText(R.string.text_post_detail_set_ding_give_up);
//        } else {
//            ViewHolderUtils.setVisibility(true, textView);
//            alertController.getButton(DialogInterface.BUTTON_POSITIVE).setText(R.string.label_driving_rec_close);
//        }
    }

    @Override
    public void onClick(View v) {
        final PostConfig.postTopConf postTopConf;
        if (mAdapter == null || (postTopConf = mAdapter.getCheckedObj()) == null) {
            ToastUtil.showMessage(mContext, "请选择置顶时间");
            return;
        }
        Map<String, String> map = new HashMap<>(2);
        map.put("postId", mPostId);
        map.put("topType", String.valueOf(postTopConf.getTopType()));
        L00bangRequestManager2.getServiceInstance()
                .postSeries3Top(map)
                .compose(L00bangRequestManager2.<Boolean>setSchedulers())
                .subscribe(new ProgressSubscriber<Boolean>(mContext) {
                    @Override
                    public void onSuccess(Boolean o) {
                        super.onSuccess(o);
                        if (o != null && o) {
                            if (mStickyAction != null) {
                                mStickyAction.onCall(true, AppUtil.getServerCurrentTime() + postTopConf.getTopTime() * 3600_000);
                            }
                            ToastUtil.showMessage(mContext, "置顶成功");
                            onRequestCompleted();
                            finishActivity();
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        if (e instanceof ApiException) {
                            onFailure(e);
                            dismiss();
                            CommonAlertDialog dialog = new CommonAlertDialog(mContext, e.getMessage(), null, "关闭", null, new OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialog, int which) {
                                    dialog.dismiss();
                                    finishActivity();
                                }
                            });
                            dialog.show();
                        } else {
                            super.onError(e);
                        }
                        if (mStickyAction != null) {
                            mStickyAction.onCall(false, 0);
                        }
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        dismiss();
                    }
                });

    }

    @Override
    protected int getDefaultLayoutResId() {
        return R.layout.dialog_goods_set_ding;
    }

    static class Adapter extends BaseRecyclerViewAdapter<PostConfig.postTopConf> {
        public Adapter(Context context, List<PostConfig.postTopConf> data) {
            super(context, data);
        }

        @Override
        protected BaseRecyclerViewHolder<PostConfig.postTopConf> createViewHolder(View itemView) {
            return new ViewHolder(itemView);
        }

        /** 获取已选中数据 */
        private PostConfig.postTopConf getCheckedObj() {
            if (mData == null || mData.size() == 0) {
                return null;
            }
            for (PostConfig.postTopConf datum : mData) {
                if (datum.getChecked() == 1) {
                    return datum;
                }
            }
            return null;
        }

        @Override
        protected int getItemLayoutRes(int viewType) {
            return R.layout.item_goods_sticky;
        }

        class ViewHolder extends BaseRecyclerViewHolder<PostConfig.postTopConf> {
            TextView mTextView;
            CheckBox mCheckBox;

            public ViewHolder(View itemView) {
                super(itemView);
            }

            @Override
            protected void initItemView(View itemView) {
                super.initItemView(itemView);
                mTextView = itemView.findViewById(R.id.tv_sticky_des);
                mCheckBox = itemView.findViewById(R.id.box_remind);

            }

            @Override
            public void bindTo(PostConfig.postTopConf postConfig, final int position) {
                super.bindTo(postConfig, position);
                mTextView.setText(String.format("置顶%s小时", String.valueOf(postConfig.getTopTime())));
                mCheckBox.setText(String.format("%s金币", String.valueOf(postConfig.getConsumeValue())));
                mCheckBox.setChecked(postConfig.getChecked() != 0);
                mCheckBox.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        for (PostConfig.postTopConf datum : mData) {
                            datum.setChecked(0);
                        }
                        mData.get(position).setChecked(1);
                        notifyDataSetChanged();
                    }
                });
            }
        }

    }

    public interface StickyAction {
        /***
         * 置顶回调
         * @param result 置顶结果
         * @param date 置顶有效时间
         */
        void onCall(boolean result, long date);
    }

    public static class GoodsStickyUtils {
        private Context mContext;
        private GoodsStickyDialog mDialog;
        private StickyAction mStickyAction;
        private String postId;
        private int type;
        private boolean needFinishActivity;

        public GoodsStickyUtils(Context context, String postId, boolean needFinishActivity) {
            mContext = context;
            this.needFinishActivity = needFinishActivity;
            this.postId = postId;
        }

        public void setType(int type) {
            this.type = type;
        }

        public void queryStickyNewDateAndShowDialog(StickyAction stickyAction) {
            mStickyAction = stickyAction;
            Map<String, String> map = new HashMap<>(1);
            map.put("series", "3");
            L00bangRequestManager2.getServiceInstance()
                    .communityPostSeries3Top(map)
                    .compose(L00bangRequestManager2.<Long>setSchedulers())
                    .subscribe(new BackgroundSubscriber<Long>(mContext) {
                        @Override
                        public void onSuccess(Long aLong) {
                            super.onSuccess(aLong);
                            if (aLong != null && aLong > AppUtil.getServerCurrentTime()) {
                                long time = aLong - AppUtil.getServerCurrentTime();
                                int hour = (int) (time / 3600_000);
                                CommonAlertDialog dialog = new CommonAlertDialog(mContext, String.format("置顶位置已满，您可以等%1s%2s分钟后再试试", hour < 1 ? "" : hour + "小时", String.valueOf(time / 1000 / 60 % 60)),
                                        null, "关闭", null, null);
                                dialog.show();
                            } else {
                                showDialog();
                            }
                        }

                        @Override
                        public void onFailure(Throwable e) {
                            super.onFailure(e);
                            showDialog();
                        }
                    });
        }

        public void showDialog() {
            if (mDialog == null) {
                L00bangRequestManager2.getServiceInstance()
                        .postConf()
                        .compose(L00bangRequestManager2.<PostConfig>setSchedulers())
                        .subscribe(new BackgroundSubscriber<PostConfig>(mContext) {
                            @Override
                            public void onSuccess(PostConfig postConfig) {
                                super.onSuccess(postConfig);
                                mDialog = new GoodsStickyDialog(mContext, postConfig.getPostTopConfList());
                                mDialog.setPostId(postId);
                                mDialog.setType(type);
                                mDialog.setStickyAction(mStickyAction);
                                mDialog.setNeedFinishActivity(needFinishActivity);
                                mDialog.show();
                            }
                        });

            } else {
                if (mDialog.isShowing()) {
                    mDialog.dismiss();
                }
                mDialog.setStickyAction(mStickyAction);
                mDialog.show();
            }

        }
    }

}
