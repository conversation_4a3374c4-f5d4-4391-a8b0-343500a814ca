package com.cloudy.linglingbang.activity.car.home;

import android.content.Context;
import android.os.Parcelable;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.util.NotchScreenUtils;
import com.cloudy.linglingbang.app.util.StatusBarUtils;
import com.cloudy.linglingbang.app.widget.AutoVerticalScrollTextView;
import com.cloudy.linglingbang.app.widget.banner.AutoResizeHeightImageView;
import com.cloudy.linglingbang.app.widget.banner.LineAdIndicator;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.car.home.MyCarType;

import java.util.ArrayList;
import java.util.List;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;
import butterknife.BindView;
import butterknife.ButterKnife;

import static android.view.ViewGroup.LayoutParams.MATCH_PARENT;

/**
 * 爱车-保客上部分爱车列表
 *
 * <AUTHOR>
 * @date 2020-02-26
 */
public class MyCarCarListViewHolder extends BaseRecyclerViewHolder<MyCarModel.MyCarTypeList> {

    @BindView(R.id.view_pager_my_car)
    ViewPager mViewPagerMyCar;

    @BindView(R.id.iv_bg)
    AutoResizeHeightImageView mIvBg;

    @BindView(R.id.rl_weather)
    RelativeLayout mRlWeather;

    @BindView(R.id.rl_root)
    RelativeLayout mRlRoot;

    @BindView(R.id.iv_weather)
    ImageView mIvWeather;

    @BindView(R.id.tv_oil_scroll_price)
    AutoVerticalScrollTextView mTvOilScrollPrice;

    private Context mContext;

    private PageAdapter mPagerAdapter;

    final List<Fragment> fragmentList = new ArrayList<>();

    private Fragment mFragment;

    /**
     * 持有以统一天气
     */
    private OilPriceLoader mOilPriceLoader;

    private boolean isFirstRequestWeather = true;

    private OnCityChangeListener mOnCityChangeListener;

    private LineAdIndicator mAdIndicator;

    /**
     * 是否需要刷新adapter
     */
    private boolean needRefresh;



    public MyCarCarListViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        mContext = itemView.getContext();
        ButterKnife.bind(this, itemView);
        //初始化天气信息
        mOilPriceLoader = new OilPriceLoader(itemView) {
            @Override
            protected void onUpdateCity(Context context, long cityId, String cityName) {
                super.onUpdateCity(context, cityId, cityName);
                if (mOnCityChangeListener != null) {
                    mOnCityChangeListener.onCityChange(cityId, cityName);
                }
            }
        };
    }

    @Override
    public void bindTo(final MyCarModel.MyCarTypeList myCarTypeList, final int position) {
        super.bindTo(myCarTypeList, position);
        int top = Math.max(NotchScreenUtils.getNotchSafeWH()[1], StatusBarUtils.getStatusBarHeight(mContext));
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(mRlWeather.getLayoutParams());
        lp.setMargins(itemView.getResources().getDimensionPixelSize(R.dimen.normal_40), top, itemView.getResources().getDimensionPixelSize(R.dimen.normal_40), 0);
        mRlWeather.setLayoutParams(lp);
        //展示天气
        if (mOilPriceLoader != null) {
            //第一次请求下天气
//            if (isFirstRequestWeather) {
//                isFirstRequestWeather = false;
//                mOilPriceLoader.refreshOil(mContext);
//            } else {
//                mOilPriceLoader.onUpdateOilPrice();
//            }
            //重置是需要刷新，其他时候不需要
            if (myCarTypeList.isNeedRefreshOil()) {
                mOilPriceLoader.refreshOil(mContext);
            } else {
                mOilPriceLoader.onUpdateOilPrice();
            }
        }

        mViewPagerMyCar.setPadding(0, mRlWeather.getHeight() + top + itemView.getResources().getDimensionPixelSize(R.dimen.normal_20), 0, 0);

        List<MyCarType> carTypes = myCarTypeList.getCarTypeList();
        //初始化指示器
        initIndicator(carTypes.size());
        //设置viewPager的高度
        RelativeLayout.LayoutParams linearParams = (RelativeLayout.LayoutParams) mViewPagerMyCar.getLayoutParams();
        linearParams.height = mIvBg.getHeight();
        linearParams.width = MATCH_PARENT;
        mViewPagerMyCar.setLayoutParams(linearParams);
        if (carTypes != null && carTypes.size() > 0) {
            fragmentList.clear();
            for (MyCarType myCarType : carTypes) {
                fragmentList.add(MyCarListFragment.newInstance(myCarType));
            }
            if (mPagerAdapter == null) {
                mPagerAdapter = new PageAdapter(mFragment.getChildFragmentManager(), fragmentList);
                mViewPagerMyCar.setAdapter(mPagerAdapter);
                //第一次可能不展示，这里重新在添加一次
                mViewPagerMyCar.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        bindTo(myCarTypeList, position);
                    }
                }, 500);

            }
            if (needRefresh) {
                needRefresh = false;
                mPagerAdapter.updateData(fragmentList);
            } else {
                mPagerAdapter.notifyDataSetChanged();

            }
            mViewPagerMyCar.setCurrentItem(0);
        }
        setIndicatorScroll(mViewPagerMyCar);

    }

    public void updateFragment() {
        if (mPagerAdapter != null && fragmentList != null) {
            mPagerAdapter.updateData(fragmentList);
            needRefresh = true;
        }
    }

    /**
     * 设置指示器滚动
     *
     * @param viewPagerMyCar
     */
    private void setIndicatorScroll(ViewPager viewPagerMyCar) {
        viewPagerMyCar.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                if (mAdIndicator != null) {
                    mAdIndicator.indicate(position, positionOffset, positionOffsetPixels);
                }
            }

            @Override
            public void onPageSelected(int position) {
                if (mAdIndicator != null) {
                    mAdIndicator.indicate(position);
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });

    }

    public void setFragment(Fragment fragment) {
        this.mFragment = fragment;
    }

    public OilPriceLoader getOilLoader() {
        return mOilPriceLoader;
    }

    public void refreshOil() {
        if (mOilPriceLoader != null) {
            mOilPriceLoader.refreshOil(mContext);
        }
    }

    class PageAdapter extends FragmentStatePagerAdapter {
        private FragmentManager mFragmentManager;
        private List<Fragment> mFragments = new ArrayList<>();

        public PageAdapter(FragmentManager fm, List<Fragment> list) {
            super(fm);
            this.mFragmentManager = fm;
            if (list == null) {
                return;
            }
            this.mFragments.addAll(list);
        }

        public void updateData(List<Fragment> mlist) {
            if (mlist == null) {
                return;
            }
            this.mFragments.clear();
            this.mFragments.addAll(mlist);
            notifyDataSetChanged();
        }

        @Override
        public Fragment getItem(int arg0) {
            return mFragments.get(arg0);//
        }

        @Override
        public int getCount() {
            return mFragments.size();//
        }

        @Override
        public Parcelable saveState() {
            return null;
        }

        @Override
        public int getItemPosition(Object object) {
            if (!((Fragment) object).isAdded() || !mFragments.contains(object)) {
                return PagerAdapter.POSITION_NONE;
            }
            return mFragments.indexOf(object);
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position) {

            Fragment instantiateItem = ((Fragment) super.instantiateItem(container, position));
            Fragment item = mFragments.get(position);
            if (instantiateItem == item) {
                return instantiateItem;
            } else {
                //如果集合中对应下标的fragment和fragmentManager中的对应下标的fragment对象不一致，那么就是新添加的，所以自己add进入；这里为什么不直接调用super方法呢，因为fragment的mIndex搞的鬼，以后有机会再补一补。
                mFragmentManager.beginTransaction().add(container.getId(), item).commitNowAllowingStateLoss();
                return item;
            }
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            Fragment fragment = (Fragment) object;
            //如果getItemPosition中的值为PagerAdapter.POSITION_NONE，就执行该方法。
            if (mFragments.contains(fragment)) {
                super.destroyItem(container, position, fragment);
                return;
            }
            //自己执行移除。因为mFragments在删除的时候就把某个fragment对象移除了，所以一般都得自己移除在fragmentManager中的该对象。
            mFragmentManager.beginTransaction().remove(fragment).commitNowAllowingStateLoss();

        }
    }

    public void setOnCityChangeListener(OnCityChangeListener onCityChangeListener) {
        mOnCityChangeListener = onCityChangeListener;
    }

    /**
     * 初始化指示器，
     */
    protected void initIndicator(int count) {
        mAdIndicator = new LineAdIndicator(mContext) {
            @Override
            protected int getNormalBgColor() {
                return mContext.getResources().getColor(R.color.color_e6f5ff);
            }

            @Override
            protected int getSelectedBgColor() {
                return mContext.getResources().getColor(R.color.common_blue_384967);
            }
        };
        mAdIndicator.setSizeAndIndicatorResId(count, null);
        mRlRoot.addView(mAdIndicator.getContainer(), getIndicatorLayoutParams());
    }

    /**
     * 指示器布局参数
     */
    protected ViewGroup.LayoutParams getIndicatorLayoutParams() {
        RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM, RelativeLayout.TRUE);
        layoutParams.addRule(RelativeLayout.CENTER_HORIZONTAL, RelativeLayout.TRUE);
        layoutParams.bottomMargin = itemView.getContext().getResources().getDimensionPixelSize(R.dimen.normal_10);
        return layoutParams;
    }

}
