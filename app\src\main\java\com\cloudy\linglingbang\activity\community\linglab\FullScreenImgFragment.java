package com.cloudy.linglingbang.activity.community.linglab;

import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.target.Target;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseFragment;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.app.log.LogUtils;
import com.cloudy.linglingbang.app.util.PreferenceUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.tag.RandomDragTagLayout;
import com.cloudy.linglingbang.app.widget.tag.RandomDragTagView;
import com.cloudy.linglingbang.model.postcard.PostCardItem;
import com.cloudy.linglingbang.model.postcard.PostCommodity;
import com.cloudy.linglingbang.model.tag.ChooseTagBean;
import com.cloudy.linglingbang.model.user.User;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import static android.app.Activity.RESULT_OK;

/**
 * 大图
 *
 * <AUTHOR>
 * @date 2018/10/30
 */
public class FullScreenImgFragment extends BaseFragment {
    private static final String INTENT_EXTRA_URL = "url";
    private static final String INTENT_EXTRA_PAGE_NUM = "page_number";
    private ImageView mLargeImageView;
    private ImageView mIvAddTag, mIvAddTagComplete;
    public RandomDragTagLayout tagLayout;

    public String mImageUrl;
    //图片对应对象的下标
    String mPageNum;
    private int width, height;

    private final List<PostCommodity> mTagList = new ArrayList<>();

    @Override
    protected int getLayoutRes() {
        return R.layout.layout_large_img;
    }

    public static FullScreenImgFragment newInstance(String imageUrl, String pageNum) {
        FullScreenImgFragment fragment = new FullScreenImgFragment();
        Bundle bundle = new Bundle();
        bundle.putString(INTENT_EXTRA_URL, imageUrl);
        bundle.putString(INTENT_EXTRA_PAGE_NUM, pageNum);
        fragment.setArguments(bundle);
        return fragment;
    }

    public static Fragment newInstance(PostCardItem postCardItem, boolean setWebp, String pageNum) {
        FullScreenImgFragment fragment = new FullScreenImgFragment();
        Bundle bundle = new Bundle();
        bundle.putString(INTENT_EXTRA_PAGE_NUM, pageNum);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected void initViews() {
        super.initViews();
        Bundle arguments = getArguments();
        if (arguments != null) {
            mImageUrl = arguments.getString(INTENT_EXTRA_URL);
            mPageNum = arguments.getString(INTENT_EXTRA_PAGE_NUM);

        }
        mLargeImageView = mRootView.findViewById(R.id.iv_image);
        mIvAddTag = mRootView.findViewById(R.id.iv_add_tag);
        mIvAddTagComplete = mRootView.findViewById(R.id.iv_add_tag_complete);
        tagLayout = mRootView.findViewById(R.id.tag_layout);
        mIvAddTag.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                SensorsUtils.sensorsClickBtn("点击标记", "编辑图片页", "标记");
                if (((FullScreenImgActivity) getActivity()).tagNum >= 5) {
                    ToastUtil.showMessage(getActivity(), "最多同时标记5个商品~");
                    return;
                }
                int px40 = getResources().getDimensionPixelOffset(R.dimen.normal_40);
                int labelMinWidth = px40 + px40 + getResources().getDimensionPixelOffset(R.dimen.normal_16) + getResources().getDimensionPixelOffset(R.dimen.normal_56) + getResources().getDimensionPixelOffset(R.dimen.normal_60);
                if (height < getResources().getDimensionPixelOffset(R.dimen.normal_182) || width / 2 < labelMinWidth) {
                    ToastUtil.showMessage(getActivity(), "图片过窄无法标记哦");
                    return;

                }
                IntentUtils.startActivityForResult(getActivity(), ChooseTagListActivity.class, 100);
            }
        });

        mIvAddTagComplete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                getActivity().onBackPressed();
            }
        });

        loadImage();
    }

    private void showImageTag() {
        tagLayout.removeAllViews();
        List<PostCommodity> postCommodityList = getPostCommodityList(mImageUrl, String.valueOf(mPageNum));
//        if (postCommodityList != null) {
//            ((FullScreenImgActivity) getActivity()).tagNum += postCommodityList.size();
//        }
        tagLayout.setPath(mImageUrl + "_" + User.shareInstance().getUserIdStr() + "_" + mPageNum);
        tagLayout.setPostCommodityList(postCommodityList);

    }

    public List<PostCommodity> getPostCommodityList(String img, String position) {
        List<PostCommodity> tagInfoBeans = null;
        String s = PreferenceUtil.getStringPreference(getActivity(), img + "_" + User.shareInstance().getUserIdStr() + "_" + position, "");
        if (!TextUtils.isEmpty(s)) {
            tagInfoBeans = new Gson().fromJson(s, new TypeToken<ArrayList<PostCommodity>>() {}.getType());
            LogUtils.e("测试标签获取值" + img + ",,,," + s);
        }
        return tagInfoBeans;
    }

    private void loadImage() {

        Glide.with(getActivity())
                .asBitmap()
                .load(mImageUrl)
                .listener(new RequestListener<Bitmap>() {
                    @Override
                    public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Bitmap> target, boolean isFirstResource) {
                        return false;
                    }

                    @Override
                    public boolean onResourceReady(Bitmap resource, Object model, Target<Bitmap> target, DataSource dataSource, boolean isFirstResource) {
                        width = resource.getWidth();
                        height = resource.getHeight();
                        ViewGroup.LayoutParams params = tagLayout.getLayoutParams();
                        params.height = height;
                        params.width = width;
                        tagLayout.setLayoutParams(params);
                        showImageTag();
                        return false;
                    }
                }).into(mLargeImageView);

    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 100 && resultCode == RESULT_OK) {
            ChooseTagBean chooseTagBean = (ChooseTagBean) IntentUtils.getExtra(data, null);
            PostCommodity bean = new PostCommodity();
            if (chooseTagBean != null) {
                bean.setCommodityName(chooseTagBean.getCommodityName());
                bean.setCanMove(true);
                bean.setPicWidth(width);
                bean.setDirection(1);
                //标签在控件上的比例
                bean.setX(0.5f);
                bean.setY(0.5f);
                bean.setCanMove(true);
                bean.setPicHeight(height);
                bean.setBusinessId(chooseTagBean.getBusinessId());
                bean.setCommodityType(chooseTagBean.getCommodityType());
                bean.setCommodityImage(chooseTagBean.getCommodityImage());
            }
            boolean isAddSuccess = tagLayout.addTagView(bean);
            tagLayout.setPath(mImageUrl + "_" + User.shareInstance().getUserIdStr() + "_" + mPageNum);
            if (isAddSuccess) {
                if (getActivity() instanceof FullScreenImgActivity) {
                    ((FullScreenImgActivity) getActivity()).tagNum++;
                }
            }
        }
    }

    public List<PostCommodity> saveTag() {
        mTagList.clear();
        for (int i = 0; i < tagLayout.getChildCount(); i++) {
            View childView = tagLayout.getChildAt(i);
            if (childView instanceof RandomDragTagView) {
                RandomDragTagView tagView = (RandomDragTagView) childView;
                PostCommodity postCommodity = tagView.getPostCommodity();
                postCommodity.setDirection(tagView.isShowLeftView() ? 1 : 2);
                postCommodity.setCommodityName(tagView.getTagText());
                float[] xy = tagView.getPercentTransYX();
                postCommodity.setX(xy[0]);
                postCommodity.setY(xy[1]);
                mTagList.add(postCommodity);
                Log.e("saveTag", "" + postCommodity);
            }
        }
        return mTagList;
    }

}
