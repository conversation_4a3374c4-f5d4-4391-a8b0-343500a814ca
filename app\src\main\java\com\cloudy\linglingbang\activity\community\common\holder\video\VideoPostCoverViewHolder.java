package com.cloudy.linglingbang.activity.community.common.holder.video;

import android.view.View;
import android.widget.ImageView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.community.common.holder.BasePostChildViewHolder;
import com.cloudy.linglingbang.app.util.ViewHolderUtils;
import com.cloudy.linglingbang.app.widget.banner.AdRoundImageView;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.postcard.PostCardItem;

import java.util.List;

/**
 * 视频封面
 *
 * <AUTHOR>
 * @date 2018/8/19
 */
public class VideoPostCoverViewHolder extends BasePostChildViewHolder {
    private boolean mShowVideoApplyStatus;
    private AdRoundImageView mIvCover;
    private ImageView mIvVideoStatus;

    public VideoPostCoverViewHolder(View itemView) {
        this(itemView, false);
    }

    public VideoPostCoverViewHolder(View itemView, boolean showVideoApplyStatus) {
        super(itemView);
        mShowVideoApplyStatus = showVideoApplyStatus;
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        mIvCover = itemView.findViewById(R.id.iv_cover);
        mIvVideoStatus = itemView.findViewById(R.id.iv_video_status);
    }

    @Override
    public void bindTo(PostCard postCard) {
        super.bindTo(postCard);
        if (postCard != null) {
            //封面
            List<PostCardItem> imgTexts = postCard.getImgTexts();
            if (imgTexts != null && imgTexts.size() > 0) {
                PostCardItem postCardItem = imgTexts.get(0);
                if (postCardItem != null) {
                    mIvCover.loadTopCornerImageCenter(postCardItem.getImg());
                }
            }
            //状态
            ViewHolderUtils.setVisibility(mShowVideoApplyStatus, mIvVideoStatus);
            if (mShowVideoApplyStatus) {
                int applyStatus = postCard.getApplyStatus();
                if (applyStatus == PostCard.VIDEO_POST_APPLY_STATUS_PENDING) {
                    mIvVideoStatus.setImageResource(R.drawable.ic_item_post_video_status_pending);
                } else if (applyStatus == PostCard.VIDEO_POST_APPLY_STATUS_REJECTED) {
                    mIvVideoStatus.setImageResource(R.drawable.ic_item_post_video_status_rejected);
                } else {
                    mIvVideoStatus.setImageResource(0);
                }
            }
        }
    }
}
