package com.cloudy.linglingbang.activity.car.add.util;

import android.text.TextUtils;
import android.widget.TextView;

import com.cloudy.linglingbang.app.util.ValidatorUtils;

/**
 * 检查是否为空，如果为空，自动 toast hint
 */
public class TextViewEmptyToastHintValidator extends ValidatorUtils.TextViewEmptyValidator {
    public TextViewEmptyToastHintValidator(TextView textView) {
        super(textView);
        CharSequence hint = textView.getHint();
        if (!TextUtils.isEmpty(hint)) {
            String toast = hint.toString();
            if (!toast.startsWith("请")) {
                toast = "请输入" + toast;
            }
            setToast(toast);
        }
    }
}