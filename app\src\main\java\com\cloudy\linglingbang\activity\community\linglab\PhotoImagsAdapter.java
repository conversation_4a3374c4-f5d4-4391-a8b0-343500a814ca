package com.cloudy.linglingbang.activity.community.linglab;

import android.content.Context;
import android.graphics.Bitmap;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.bumptech.glide.Glide;
import com.cloudy.linglingbang.R;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

class PhotoImagsAdapter extends RecyclerView.Adapter<PhotoImagsAdapter.InfoImageHolder> {

    private final Context context;
    private List<String> mDatas = new ArrayList<>();

    public PhotoImagsAdapter(Context context, List<String> mDatas) {
        this.context = context;
        this.mDatas = mDatas;
    }

    public void setmDatas(List<String> mDatas) {
        this.mDatas = mDatas;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public PhotoImagsAdapter.InfoImageHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View v = LayoutInflater.from(context).inflate(R.layout.item_info_img, parent, false);
        return new InfoImageHolder(v);
    }

    @Override
    public void onBindViewHolder(@NonNull InfoImageHolder holder, final int position) {
        /**
         * 数据操作
         */
        if (mDatas != null && mDatas.size() > 0) {
            if (mDatas.size() == position) {
                holder.img_photo.setImageResource(R.drawable.img_add);
                holder.btn_clearResource.setVisibility(View.GONE);
            } else {
                String dateStr = mDatas.get(position);
                holder.btn_clearResource.setVisibility(View.VISIBLE);
                /**
                 * 适合API 28
                 * */
                Bitmap arrBitmap = ImageCompressUtil.compressBySize(dateStr, 300, 300);
                Glide.with(context).load(arrBitmap).into(holder.img_photo);
            }
        } else {
            holder.img_photo.setImageResource(R.drawable.img_add);
            holder.btn_clearResource.setVisibility(View.GONE);
        }

        //添加图片
        holder.img_photo.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                itemOnClickListener.onAddItenClick(position);
            }
        });
        //删除图片
        holder.btn_clearResource.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                itemOnClickListener.onDeleteItenClick(position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return mDatas != null ? (mDatas.size() + 1) : 1;
    }

    /**
     * 创建ViewHolder
     * 元素声明
     */
    class InfoImageHolder extends RecyclerView.ViewHolder {

        ImageView img_photo;
        ImageView btn_clearResource;
        RelativeLayout re_imgResourxe;

        public InfoImageHolder(@NonNull View itemView) {
            super(itemView);
            img_photo = itemView.findViewById(R.id.img_photo);
            btn_clearResource = itemView.findViewById(R.id.btn_clearResource);
            re_imgResourxe = itemView.findViewById(R.id.re_imgResourxe);
        }
    }

    //添加图片
    private InfoImgClickListener itemOnClickListener;

    interface InfoImgClickListener {
        void onAddItenClick(int addPosition);

        void onDeleteItenClick(int deletePosition);
    }

    public void setAddImgClickLisitenner(InfoImgClickListener itemOnClickListener) {
        this.itemOnClickListener = itemOnClickListener;
    }
}
