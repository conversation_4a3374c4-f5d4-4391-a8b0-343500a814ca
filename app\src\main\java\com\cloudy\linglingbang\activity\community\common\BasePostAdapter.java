package com.cloudy.linglingbang.activity.community.common;

import android.content.Context;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.community.common.holder.BasePostViewHolder;
import com.cloudy.linglingbang.activity.community.common.holder.PostIconEnum;
import com.cloudy.linglingbang.activity.community.common.holder.square.PostWithAttentionViewHolder;
import com.cloudy.linglingbang.activity.community.common.holder.square.VideoPostWithAttentionViewHolder;
import com.cloudy.linglingbang.activity.community.common.holder.video.VideoPostViewHolder;
import com.cloudy.linglingbang.activity.community.common.holder.vote.VotePostViewHolder;
import com.cloudy.linglingbang.activity.community.post.CommunityUtils;
import com.cloudy.linglingbang.activity.user.homepage.UserHomepageFragment;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.postcard.PostCard;

import java.util.List;

/**
 * 帖子基类 Adapter
 *
 * <AUTHOR>
 * @date 2018/6/22
 */
public class BasePostAdapter extends BaseRecyclerViewAdapter<PostCard> implements CommunityUtils.PostOperateUtils.OperateListener {
    /**
     * 与帖子图标是一样的逻辑，但是区分开
     * 由 Adapter 传至 BasePostViewHolder
     * 然后分发给 PostChildViewHolder
     * 但是要注意，为了防止直接创建 ViewHolder，需要在它们构造方法中都调用设置默认值
     */
    public int mFlags;
    private int mIconFlags;

    public BasePostAdapter(Context context, List<PostCard> data) {
        super(context, data);
        mFlags = PostFlagsEnum.addDefaultFlags(mFlags);
        mIconFlags = PostIconEnum.addDefaultShowIconInfo(mIconFlags);
        addIconFlags(PostIconEnum.TOP);
    }

    public BasePostAdapter addIconFlags(PostIconEnum postIconEnum) {
        mIconFlags = postIconEnum.showPostIcon(mIconFlags);
        return this;
    }

    public BasePostAdapter addFlags(PostFlagsEnum flagsEnum) {
        mFlags = flagsEnum.addFlags(mFlags);
        return this;
    }

    @Override
    protected int getItemLayoutRes(int viewType) {
        switch (viewType) {
            case PostCard.PostType.SHORT_VIDEO:
                return getShortVideoItemLayoutRes();
            case PostCard.PostType.VOTE:
                return R.layout.item_vote_post;
            default:
                return getDefaultItemLayoutRes();
        }
    }

    protected int getDefaultItemLayoutRes() {
        return R.layout.item_post;
    }

    protected int getShortVideoItemLayoutRes() {
        return R.layout.item_video_post;
    }

    /**
     * 如果要增加别的类型，请注意 {@link UserHomepageFragment.UserHomepageAdapter#getItemViewType(int)} 中的处理，
     * 因为其根据 Object 区分为不同的类型，所以帖子无法区分
     */
    @Override
    public int getItemViewType(int position) {
        int postTypeId = mData.get(position).getPostTypeIdOrNegative();
        switch (postTypeId) {
            case PostCard.PostType.SHORT_VIDEO:
                return PostCard.PostType.SHORT_VIDEO;
            case PostCard.PostType.VOTE:
            case PostCard.PostType.QUESTIONNAIRE:
                //投票或问卷均处理为投票特殊处理
                return PostCard.PostType.VOTE;
            default:
                return PostCard.PostType.IMAGE_TEXT;
        }
    }

    @Override
    protected BaseRecyclerViewHolder<PostCard> createViewHolderWithViewType(View itemView, int viewType) {
        return createViewHolderWithoutSetting(itemView, viewType)
                .setFlags(mFlags)
                .setIconFlags(mIconFlags)
                .setOperateListener(this);
    }

    @Override
    protected BaseRecyclerViewHolder<PostCard> createViewHolder(View itemView) {
        //由 type 返回
        return null;
    }

    private BasePostViewHolder createViewHolderWithoutSetting(View itemView, int viewType) {
        BasePostViewHolder viewHolder;
        switch (viewType) {
            case PostCard.PostType.SHORT_VIDEO:
                viewHolder = createShortVideoViewHolder(itemView);
                break;
            case PostCard.PostType.VOTE:
                viewHolder = new VotePostViewHolder(itemView);
                break;
            default:
                viewHolder = createDefaultViewHolderWithoutSetting(itemView);
                break;
        }
        if (viewHolder != null && viewHolder.needSetAdapter()) {
            viewHolder.setAdapter(this);
        }
        return viewHolder;
    }

    /**
     * 默认 ViewHolder
     */
    protected BasePostViewHolder createDefaultViewHolderWithoutSetting(View itemView) {
        if (PostFlagsEnum.SHOW_ATTENTION.checkFlags(mFlags)) {
            return new PostWithAttentionViewHolder(itemView);
        } else {
            return new BasePostViewHolder(itemView);
        }
    }

    /**
     * 默认 ViewHolder
     */
    protected BasePostViewHolder createShortVideoViewHolder(View itemView) {
        if (PostFlagsEnum.SHOW_ATTENTION.checkFlags(mFlags)) {
            return new VideoPostWithAttentionViewHolder(itemView);
        } else {
            return new VideoPostViewHolder(itemView);
        }
    }

    @Override
    protected BaseRecyclerViewHolder.OnItemClickListener getDefaultOnItemClickListener() {
        return new BaseRecyclerViewHolder.OnItemClickListener() {
            @Override
            public void onItemClick(View itemView, int position) {
                if (position > -1 && position < mData.size()) {
                    PostCard postCard = mData.get(position);
                    if (postCard != null) {
                        onClickPostCard(itemView.getContext(), postCard);
                    }
                }
            }
        };
    }

    /**
     * 点击帖子
     */
    protected void onClickPostCard(Context context, PostCard postCard) {
        JumpPageUtil.goToPost(context, postCard);
    }

    private void notifyPostCardRemoved(PostCard postCard) {
        int position = mData.indexOf(postCard);
        if (position >= 0) {
            mData.remove(position);
            notifyItemRemoved(position);
            notifyItemRangeChanged(position, mData.size());
            onItemRemoved(position);
        }
    }

    private void notifyPostCardChanged(PostCard postCard) {
        int position = mData.indexOf(postCard);
        if (position >= 0) {
            notifyItemChanged(position);
            onItemChanged(position);
        }
    }

    protected void onItemRemoved(int position) {

    }

    protected void onItemChanged(int position) {
    }

    //以下为帖子操作的回调
    @Override
    public void delete(PostCard postCard) {
        notifyPostCardRemoved(postCard);
    }

    @Override
    public void topOperate(PostCard postCard) {
        notifyPostCardChanged(postCard);
    }

    @Override
    public void cancelTopOperate(PostCard postCard) {
        notifyPostCardChanged(postCard);
    }

    @Override
    public void noticeOperate(PostCard postCard) {

    }

    @Override
    public void cancelNoticeOperate(PostCard postCard) {

    }

    @Override
    public void eliteOperate(PostCard postCard) {
        notifyPostCardChanged(postCard);
    }

    @Override
    public void cancelEliteOperate(PostCard postCard) {
        notifyPostCardChanged(postCard);

    }

    @Override
    public void onPopupShow() {

    }

    @Override
    public void onPopupDismiss() {

    }
}
