package com.cloudy.linglingbang.activity.car.energy.bluetooth;

import static com.cloudy.linglingbang.activity.car.energy.ConnectHelper.ServiceCode;
import android.content.Context;
import android.util.Log;

import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.WidgetControl;
import com.cloudy.linglingbang.constants.AppConstants;
import com.clj.fastble.utils.HexUtil;
import com.cloudy.linglingbang.activity.car.energy.ConnectHelper;
import com.cloudy.linglingbang.activity.car.energy.ble.BleCode;
import com.cloudy.linglingbang.activity.car.energy.ble.Utils;

/**
 * 蓝牙车控结果处理工具类
 * Created by LiYeWen on 2025/05/08
 */
public class ControlDataUtil {

    private static ControlDataUtil instance;
    private ConnectHelper.ParseDataResponseListener parseDataResponseListener;
    private ConnectHelper.BleDataResponseListener bleDataResponseListener;
    private Context mContext;
    private String cmdName;

    private ControlDataUtil(){}

    /**
     * 获取单例对象
     * @return
     */
    public static ControlDataUtil getInstance() {
        if (instance == null) {
            synchronized (ControlDataUtil.class) {
                instance = new ControlDataUtil();
                //初始化蓝牙车控错误码
                ErrorCodeUtil.getInstance().addMapData(ConnectHelper.bleParkingCodeMap, ConnectHelper.bleErrorCodeMap, ConnectHelper.e260SPErrorMsg);
            }
        }
        return instance;
    }

    /**
     * 处理接收到的车控数据
     * @param data
     * @param randomKey
     */
    public void handleControlData(byte[] data, String randomKey) {
        String result = GattUtil.getInstance().decodeControl(data, randomKey);
//        String result = GattUtil.getInstance().decodeControl(data, getRandomKey());
        Log.e(ConnectHelper.TAG, "handleControlData() 解密后的蓝牙控车结果 result=" + result);
        if (result.length() > 28) {
            parseData(result);
//            Log.e(ConnectHelper.TAG, "handleControlData() 解密后的蓝牙控车结果 错误码result.substring(20, 28)=" + result.substring(20, 28));
        } else {
            //显示蓝牙错误默认提示
            showDefaultPrompt();
        }
        //小组件蓝牙车控结束
        WidgetControl.bleControlFinish(cmdName);
    }

    public void setContext(Context context){
        mContext = context;
    }

    /**
     * @param data
     */
    private void parseData(String data) {
        //统一把数据抛给ParkActivity
        if (parseDataResponseListener != null) {
            parseDataResponseListener.responseParseData(data);
        }
        //原来的逻辑和页面操作不管放掉
        if (bleDataResponseListener == null) {
            Log.e(ConnectHelper.TAG, "parseData() 放弃掉bleDataResponseListener后续操作");
            return;
        }

        String data1 = data.substring(2, 6);
        String data2 = data.substring(2, 10);
        // 蓝牙命令失败
        String errorCode = data.substring(20, 28);

        if (data1.equalsIgnoreCase(ConnectHelper.BLE_FAIL)) {
            Log.e(ConnectHelper.TAG, "parseData() 蓝牙控车结果返回，失败，错误码=" + errorCode);
            StringBuilder sb = new StringBuilder();
            String code = getErrorCode(errorCode);
            if (ConnectHelper.bleErrorCodeMap.containsKey(code)) {
                Log.e(ConnectHelper.TAG, "parseData() 蓝牙控车结果返回，bleError bleDataResponseListener=" + bleDataResponseListener);
                sb.append(ConnectHelper.bleErrorCodeMap.get(code));
                if (bleDataResponseListener != null) {
                    bleDataResponseListener.responseControlError(sb.toString());
                }
            } else if (ConnectHelper.bleParkingCodeMap.containsKey(code)) {
                Log.e(ConnectHelper.TAG, "parseData() 蓝牙控车结果返回，bleParkError bleDataResponseListener=" + bleDataResponseListener);
                sb.append(ConnectHelper.bleParkingCodeMap.get(code));
                if (bleDataResponseListener != null) {
                    bleDataResponseListener.responseParkingError(sb.toString());
                }
            } else if (ConnectHelper.BLE_PARKING_STATE.equals(code)) {
                Log.e(ConnectHelper.TAG, "parseData() 蓝牙控车结果返回，蓝牙一键泊车 bleDataResponseListener=" + bleDataResponseListener);
                byte[] codes = HexUtil.hexStringToBytes(errorCode.substring(2));
                switch (codes[0]) {
                    case 0x06://泊车失败
                    case 0x16://未收到ADAS反馈 指令超时
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed();
                        }
                        break;
                    case 0x05://泊车成功结束
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingSuccess();
                        }
                        break;
                    case 0x0F://
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responsePowerOffFailed("下电失败，请回到驾驶座踩刹车接管车辆");
                        }
                        break;
                    default:
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingState(codes[0]);
                        }
                        break;
                }
            } else if (ConnectHelper.BLE_PARKING_OUT_STATE.equals(code)) {
                Log.e(ConnectHelper.TAG, "parseData() 蓝牙控车结果返回，蓝牙一键泊出2 bleDataResponseListener=" + bleDataResponseListener);
                byte[] codes = HexUtil.hexStringToBytes(errorCode.substring(2));
                switch (codes[0]) {
                    case 0x06://出库失败
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                    case 0x07://人工接管，出库失败
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                    case 0x08://功能不可用
//                        ToastUtils.showShort("UCU Rsp ErrorCode: " + errorCode);//for test 只做测试用途，正式版本请删除或注释此行代码
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                        break;
                    case 0x05://泊车成功结束
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingSuccess();
                        }
                        break;
                    default:
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingState(codes[0]);
                        }
                        break;
                }
            } else if (ConnectHelper.BLE_PARKING_STATE2.equals(code)) {
                Log.e(ConnectHelper.TAG, "parseData() 蓝牙控车结果返回，蓝牙一键泊车2 bleDataResponseListener=" + bleDataResponseListener);
                byte[] codes = HexUtil.hexStringToBytes(errorCode.substring(2));
                switch (codes[0]) {
                    case 0x03://泊车失败
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                    case 0x04://人工接管
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                    case 0x06://功能不可用
//                        ToastUtils.showShort("UCU Rsp ErrorCode: " + errorCode);//for test 只做测试用途，正式版本请删除或注释此行代码
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                        break;
                    case 0x02://泊车成功结束
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingSuccess();
                        }
                        break;
                    default:
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingState(codes[0]);
                        }
                        break;
                }
            } else if (ConnectHelper.BLE_PARKING_ERROR_STATE.equals(code)) {
                Log.e(ConnectHelper.TAG, "parseData() 蓝牙控车结果返回，E260SP新增错误码，bleDataResponseListener=" + bleDataResponseListener);
                byte[] codes = HexUtil.hexStringToBytes(errorCode.substring(2));
                if (codes[0] == 0x00) {//无错误
                    if (bleDataResponseListener != null) {
                        bleDataResponseListener.responseParkingSuccess();
                    }
                } else {
                    if (bleDataResponseListener != null) {
                        bleDataResponseListener.responseE260SPParkingFailed(ConnectHelper.e260SPErrorMsg.get(codes[0]));
                    }
                }
            } else {
                //显示蓝牙错误默认提示
                showDefaultPrompt();
            }
        } else if (data1.equalsIgnoreCase(ConnectHelper.BLE_CONTROL_SUCCESS)) {
            Log.e(ConnectHelper.TAG, "parseData() 蓝牙控车结果返回，成功 BLE_CONTROL_SUCCESS");
            if (bleDataResponseListener != null) {
                bleDataResponseListener.responseControlSuccess(cmdName);
            }else {
                Log.e(ConnectHelper.TAG, "parseData() 蓝牙控车结果返回，成功 BLE_CONTROL_SUCCESS bleDataResponseListener == null");
            }

        } else if (data2.equalsIgnoreCase(ErrorCodeUtil.CMD_POWER_OFF)) {
            Log.e(ConnectHelper.TAG, "parseData() 蓝牙控车结果返回，车辆下电成功 CMD_POWER_OFF");
            if (bleDataResponseListener != null) {
                bleDataResponseListener.onPowerOffSuccess();
            }
        } else if (data2.equalsIgnoreCase("C0650001") && ServiceCode.equals(AppConstants.SERVICE_CODE_SMART_PARKING_F510C)
                ||ServiceCode.equals(AppConstants.SERVICE_CODE_SMART_PARKING_F511C) || ServiceCode.equals(AppConstants.SERVICE_CODE_SMART_PARKING_F511S)) {
            String parkingStatus1 = data.substring(28, 44);
            String parkingStatus2 = data.substring(44, 60);
            Log.e(ConnectHelper.TAG, "parseData() 蓝牙控车结果返回，parkingStatus1=" + parkingStatus1);
            Log.e(ConnectHelper.TAG, "parseData() 蓝牙控车结果返回，parkingStatus2=" + parkingStatus2);
            // 0x37d
            byte ipsInfo = Utils.getBits(parkingStatus1, 7, 7, 8);
            byte ipaStatus = Utils.getBits(parkingStatus2, 1, 7, 5);
            byte apoStatus = Utils.getBits(parkingStatus2, 1, 2, 4);
            Log.e(ConnectHelper.TAG, "parseData() 蓝牙控车结果返回，IPS Information智能泊车信息提示:" + Integer.toHexString(ipsInfo));
            Log.e(ConnectHelper.TAG, "parseData() 蓝牙控车结果返回，IPA Operation Status智能泊车辅助运行状态 :" + Integer.toHexString(ipaStatus));
            Log.e(ConnectHelper.TAG, "parseData() 蓝牙控车结果返回，APO Operation Status智能出库运行状态 :" + Integer.toHexString(apoStatus));
            if (bleDataResponseListener != null) {
                if (ConnectHelper.park_type == 1) {
                    switch (ipaStatus) {
                        case BleCode.PARK_IPA_OFF:
                            break;
                        case BleCode.PARK_IPA_NO_READY:
                        case BleCode.PARK_IPA_READY:
                        case BleCode.PARK_IPA_DETECTING:
                        case BleCode.PARK_IPA_DETECTING_SUSPEND:
                        case BleCode.PARK_IPA_DETECTED:
                        case BleCode.PARK_IPA_BG_DETECTING:
                        case BleCode.PARK_IPA_BG_DETECTED:
                            bleDataResponseListener.responseParkingState((byte) 0x08);
                            break;
                        case BleCode.PARK_IPA_PREPARE:
                            break;
                        case BleCode.PARK_IPA_PARKING:
                            bleDataResponseListener.responseParkingState((byte) 0x01);
                            break;
                        case BleCode.PARK_IPA_PARKING_SUSPEND:
                            if (ipsInfo != (byte) 0x56) { //0x56:障碍物过近，是否开启极窄出库模式
                                bleDataResponseListener.responseParkingState((byte) 0x07);
                            } else {
                                bleDataResponseListener.responseParkingState((byte) 0x11);
                            }
                            break;
                        case BleCode.PARK_IPA_FINISH_SUCCESS:
                            bleDataResponseListener.responseParkingSuccess();
                            break;
                        case BleCode.PARK_IPA_FINISH_FAILURE:
                            bleDataResponseListener.responseParkingFailed((byte) 0x03);
                            break;
                        case BleCode.PARK_IPA_FINISH_TERMINATION:
                            bleDataResponseListener.responseParkingFailed((byte) 0x04);
                            break;
                        case BleCode.PARK_IPA_ERROR_INT_FAULT:
                        case BleCode.PARK_IPA_ERROR_EXT_FAULT:
                            bleDataResponseListener.responseParkingFailed((byte) 0x06);
                            break;
                    }
                } else if (ConnectHelper.park_type == 2) {
                    switch (apoStatus) {
                        case BleCode.PARK_APO_OFF:
//                            bleDataResponseListener.responseParkingState((byte) 0x0B);
                            break;
                        case BleCode.PARK_APO_NO_READY:
                            bleDataResponseListener.responseParkingState((byte) 0x0B);
                            break;
                        case BleCode.PARK_APO_READY:
                            bleDataResponseListener.responseParkingState((byte) 0x0B);
                            break;
                        case BleCode.PARK_APO_PREPARE:
                            bleDataResponseListener.responseParkingState((byte) 0x01);
                            break;
                        case BleCode.PARK_APO_PARKING_OUT:
                            bleDataResponseListener.responseParkingState((byte) 0x0A);
                            break;
                        case BleCode.PARK_APO_PARKING_OUT_SUSPEND:
                            if (ipsInfo != (byte) 0x56) { //0x56:障碍物过近，是否开启极窄出库模式
                                bleDataResponseListener.responseParkingState((byte) 0x09);
                            } else {
                                bleDataResponseListener.responseParkingState((byte) 0x11);
                            }
                            break;
                        case BleCode.PARK_APO_FINISH_SUCCESS:
                            bleDataResponseListener.responseParkingSuccess();
                            break;
                        case BleCode.PARK_APO_FINISH_FAILURE:
                            bleDataResponseListener.responseParkingFailed((byte) 0x06);
                            break;
                        case BleCode.PARK_APO_FINISH_TERMINATION:
                            bleDataResponseListener.responseParkingFailed((byte) 0x07);
                            break;
                        case BleCode.PARK_APO_ERROR_INT_FAULT:
                        case BleCode.PARK_APO_ERROR_EXT_FAULT:
                            bleDataResponseListener.responseParkingFailed((byte) 0x08);
                            break;
                        case BleCode.PARK_APO_PREPARE_SUSPEND:
                            bleDataResponseListener.responseParkingState((byte) 0x0B);
                            break;
                    }
                } else {
                    Log.e(ConnectHelper.TAG, "parseData() 蓝牙控车结果返回，park_type=" + ConnectHelper.park_type);
                }
            } else {
                Log.e(ConnectHelper.TAG, "parseData(), bleDataResponseListener is null");
            }


            String code = errorCode.substring(0, 2);
            if (ConnectHelper.BLE_PARKING_OUT_STATE.equals(code)) {
                Log.e(ConnectHelper.TAG, "parseData(), 蓝牙控车结果返回，蓝牙一键泊出2 bleDataResponseListener=" + bleDataResponseListener);
                byte[] codes = HexUtil.hexStringToBytes(errorCode.substring(2));
                switch (codes[0]) {
                    case 0x06://出库失败
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                    case 0x07://人工接管，出库失败
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                    case 0x08://功能不可用
//                        ToastUtils.showShort("UCU Rsp ErrorCode: " + errorCode);//for test 只做测试用途，正式版本请删除或注释此行代码
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                        break;
                    case 0x05://泊车成功结束
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingSuccess();
                        }
                        break;
                    default:
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingState(codes[0]);
                        }
                        break;
                }
            } else if (ConnectHelper.BLE_PARKING_STATE2.equals(code)) {
                Log.e(ConnectHelper.TAG, "parseData() 蓝牙控车结果返回，蓝牙一键泊车2 bleDataResponseListener=" + bleDataResponseListener);
                byte[] codes = HexUtil.hexStringToBytes(errorCode.substring(2));
                switch (codes[0]) {
                    case 0x03://泊车失败
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                    case 0x04://人工接管
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                    case 0x06://功能不可用
//                        ToastUtils.showShort("UCU Rsp ErrorCode: " + errorCode);//for test 只做测试用途，正式版本请删除或注释此行代码
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                        break;
                    case 0x02://泊车成功结束
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingSuccess();
                        }
                        break;
                    default:
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingState(codes[0]);
                        }
                        break;
                }
            }
        }

    }


    private String getErrorCode(String errorCode) {
        String eHead = errorCode.substring(0, 2);
        if ("11".equals(eHead)) {
            byte[] codes = HexUtil.hexStringToBytes(errorCode.substring(2));
            if (codes[0] != 0) {
                if ((codes[0] & 0x80) == 0x80) {//电源未关
                    return ErrorCodeUtil.ACC_ON;
                } else if ((codes[0] & 0x40) == 0x40) {//遥控钥匙 正在使用
                    return ErrorCodeUtil.KEY_ERROR;
                } else if ((codes[0] & 0x20) == 0x20) {//门开着
                    return ErrorCodeUtil.DOOR_OPENED;
                } else if ((codes[0] & 0x10) == 0x10) {//转向灯未 关
                    return ErrorCodeUtil.LIGHT_ON;
                } else if ((codes[0] & 0x8) == 0x8 || (codes[0] & 0x4) == 0x4) {//电动窗故障
                    return ErrorCodeUtil.WINDOW_ERROR;
                } else if ((codes[0] & 0x1) == 0x1) {//设防状态错误
                    return ErrorCodeUtil.POWER_OFF_ERROR;
                }
            }
            if (codes[1] != 0) {
                if ((codes[1] & 0x80) == 0x80) {//喇叭故障
                    return ErrorCodeUtil.HORN_ERROR;
                } else if ((codes[1] & 0x40) == 0x40 || (codes[1] & 0x20) == 0x20 || (codes[1] & 0x10) == 0x10 || (codes[1] & 0x8) == 0x8) {//转向灯 故障
                    return ErrorCodeUtil.LR_LIGHT_ERROR;
                } else if ((codes[1] & 0x4) == 0x4 || (codes[1] & 0x2) == 0x2) {//电源未关z
                    return ErrorCodeUtil.ACC_ON;
                } else if ((codes[1] & 0x1) == 0x1) {//车辆正在 运行
                    return ErrorCodeUtil.CAR_RUNNING;
                }
            }

            if (codes[2] != 0) {
                if ((codes[2] & 0x80) == 0x80) {//车灯未关 闭
                    return ErrorCodeUtil.LIGHT_ON;
                } else if ((codes[2] & 0x40) == 0x40) {//智能钥匙 在车内
                    return ErrorCodeUtil.KEY_ERROR;
                } else if ((codes[1] & 0x20) == 0x20) {//整车防盗 认证失败
                    return ErrorCodeUtil.POWER_OFF_ERROR;
                } else if ((codes[2] & 0x10) == 0x10) {//蓄电池电 压低
                    return ErrorCodeUtil.LOW_POWER;
                } else if ((codes[1] & 0x8) == 0x8) {//变速箱 当前档 位为非 P
                    return ErrorCodeUtil.GEAR_NOT_N;
                }
            }

        } else if ("12".equals(eHead)) {
            byte[] codes = HexUtil.hexStringToBytes(errorCode.substring(2));
            if (codes[0] != 0) {//车门未关紧
                return ErrorCodeUtil.DOOR_OPENED;
            }
            if (codes[1] != 0) {
                if ((codes[1] & 0x80) == 0x80) {//后备箱未 关
                    return ErrorCodeUtil.B_DOOR_OPENED;
                } else if ((codes[1] & 0x40) == 0x40) {//车辆未充 电
                    return ErrorCodeUtil.UN_CHARGE;
                } else if ((codes[1] & 0x20) == 0x20) {//电源未关
                    return ErrorCodeUtil.ACC_ON;
                } else if ((codes[1] & 0x10) == 0x10 || (codes[1] & 0x8) == 0x8) {//设防失败
                    return ErrorCodeUtil.POWER_OFF_ERROR;
                } else if ((codes[1] & 0x4) == 0x4) {//车灯未关 闭
                    return ErrorCodeUtil.LIGHT_ON;
                } else if ((codes[1] & 0x2) == 0x2) {//高压解防 错误
                    return ErrorCodeUtil.POWER_ON_ERROR;
                }
            }

            if (codes[2] != 0) {
                if ((codes[2] & 0x80) == 0x80) {//电源未关
                    return ErrorCodeUtil.ACC_ON;
                } else if ((codes[2] & 0x40) == 0x40) {//车辆正在 运行
                    return ErrorCodeUtil.CAR_RUNNING;
                } else if ((codes[1] & 0x20) == 0x20) {//变速箱当 前档位为 非P
                    return ErrorCodeUtil.GEAR_NOT_N;
                } else if ((codes[2] & 0x10) == 0x10) {//指令冲突
                    return ErrorCodeUtil.CMD_CONFLICT;
                }
            }

        } else if (ConnectHelper.BLE_PARKING_STATE.equals(eHead)) {
            byte[] codes = HexUtil.hexStringToBytes(errorCode.substring(2));
            switch (codes[0]) {
//                case 0x07:
//                    return PARKING_TIME_OUT;
                case 0x08:
                    return ErrorCodeUtil.PARKING_OBSTACLE;
//                case 0x09:
//                    return PARKING_EXCESSIVE;
                case 0x0A:
                    return ErrorCodeUtil.PARKING_DOOR_OPENED;
//                case 0x0C:
//                    return PARKING_ERROR;
                case 0x0D:
                    return ErrorCodeUtil.PARKING_FUNCTION_ERROR;
                case 0x10:
                    return ErrorCodeUtil.PARKING_TYPE_ERROR;
                case 0x0E:
                    return ErrorCodeUtil.PARKING_UCU_ERROR;
                default:
                    return eHead;
            }
        } else if ("20".equals(eHead)) {
            byte[] codes = HexUtil.hexStringToBytes(errorCode.substring(2));
            Log.e(ConnectHelper.TAG, "getErrorCode() codes=" + codes + ",   codes[0]=" + codes[0]);
            switch (codes[0]) {
                case 0x01:
                    return "01";
                case 0x02:
                    return "02";
                case 0x03:
                    return "03";
                case 0x04:
                    return "04";
                case 0x05:
                    return ErrorCodeUtil.IGN1_OUTPUT_ERROR;
                case 0x06:
                    return "06";
                case 0x07:
                    return ErrorCodeUtil.ACC_OUTPUT_ERROR;
                case 0x08:
                    return ErrorCodeUtil.VOLTAGE_EXCEED_ERROR;
                case 0x09:
                    return "09";
                case 0x10:
                    return "10";
                case 0x11:
                    return "11";
                case 0x12:
                    return "12";
                case 0x13:
                    return ErrorCodeUtil.ESCL_UNLOCK_ERROR;
                case 0x14:
                    return "14";
                case 0x15:
                    return "15";
                case 0x16:
                    return ErrorCodeUtil.START_TIMEOUT_ERROR;
                case 0x18:
                    return "18";
                case 0x19:
                    return "19";
                case 0x1A:
                    return "1A";
                case 0x1B:
                    return "1B";
                case 0x1C:
                    return "1C";
                case 0x1D:
                    return "1D";
                case 0x20:
                    return "20";
                case 0x21:
                    return "21";
                case 0x22:
                    return "22";
                case 0x26:
                    return "26";
                case 0x27:
                    return "27";
                case 0x28:
                    return "28";
                case 0x2B:
                    return "2B";
                case 0x2F:
                    return "2F";
                case 0x3C:
                    return "3C";
                case 0x41:
                    return "41";
                case 0x43:
                    return "43";
                case 0x50:
                    return "50";
                case 0x51:
                    return "51";
                case 0x52:
                    return "52";
                case 0x53:
                    return "53";
                case 0x54:
                    return "54";
                case 0x55:
                    return "55";
                case 0x56:
                    return "56";
                case 0x57:
                    return "57";
                case 0x58:
                    return "58";
                case 0x59:
                    return "59";
                case 0x5A:
                    return "5A";
                case 0x5B:
                    return "5B";
                case 0x5C:
                    return "5C";
                case 0x5D:
                    return "5D";
                case 0x5E:
                    return "5E";
                case 0x5F:
                    return "5F";
                case 0x71:
                    return "71";
                case 0x72:
                    return "72";
                case 0x73:
                    return "73";
                case 0x74:
                    return "74";
                case 0x75:
                    return "75";
                case 0x77:
                    return "77";
                case (byte) 0xFF:
                    return "FF";
            }

        }
        //以下错误码是参考ios那边添加的
        else if ("05".equals(eHead)){//数据长度错误
            return "05";
        }
        else if ("07".equals(eHead)){//指令冲突
            return ErrorCodeUtil.CMD_CONFLICT;
        }
        else if ("08".equals(eHead)){//指令超时
            return "08";
        }
        else if ("0B".equals(eHead)){//UCU无响应
            return ErrorCodeUtil.UCU_NO_RESPONSE;
        }
        else if ("13".equals(eHead)){//CRC校验失败
            return ErrorCodeUtil.CRC_VERIFICATION_FAILED;
        }
        else if ("16".equals(eHead)){//BCM超时无响应
            return ErrorCodeUtil.BCM_NO_RESPONSE;
        }
        else if ("1e".equals(eHead)){//用户bleKey校验失败
            return ErrorCodeUtil.BLEKEY_VERIFICATION_FAILED;
        }
        else if ("7F".equals(eHead)){//用户bleKey不存在gaw
            return ErrorCodeUtil.BLEKEY_ABSENT;
        }
        return eHead;
    }

    public void setCmdName(String cmdName){
        this.cmdName = cmdName;
    }

    /**
     * 显示蓝牙错误默认提示
     */
    private void showDefaultPrompt() {
        Log.e(ConnectHelper.TAG, "显示蓝牙错误默认提示 showDefaultPrompt(), mContext = " + mContext);
        if (mContext != null) {
            ToastUtil.showMessage(mContext, "未知错误，请重试");
        }
    }

    public void setParseDataResponseListener(ConnectHelper.ParseDataResponseListener listener) {
        this.parseDataResponseListener = listener;
    }

    public void setBleDataResponseListener(ConnectHelper.BleDataResponseListener listener) {
        this.bleDataResponseListener = listener;
    }

    public void setParamsSuccess() {
        Log.e(ConnectHelper.TAG, "setParamsSuccess() bleDataResponseListener = " + bleDataResponseListener);
        if (bleDataResponseListener != null) {
            bleDataResponseListener.setParamsSuccess();
        }
    }

    public ConnectHelper.BleDataResponseListener getBleDataResponseListener() {
        return bleDataResponseListener;
    }


}