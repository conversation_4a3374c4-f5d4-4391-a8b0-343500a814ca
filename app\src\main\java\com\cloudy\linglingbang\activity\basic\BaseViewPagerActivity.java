package com.cloudy.linglingbang.activity.basic;

import android.content.Context;
import android.view.View;
import android.widget.RadioGroup;

import com.cloudy.linglingbang.R;

import androidx.viewpager.widget.ViewPager;

/**
 * 包含ViewPager的activity
 * Created by han<PERSON><PERSON> on 2016/10/19.
 */

public abstract class BaseViewPagerActivity<T> extends BaseActivity implements IViewPagerTabContext<T> {

    private ViewPagerTabController<T> mViewPagerTabController;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_base_view_pager);
    }

    //初始化创建Controller
    @Override
    protected void initialize() {
        if (mToolbar != null) {
            mToolbar.setVisibility(needShowTitleBar() ? View.VISIBLE : View.GONE);
        }

        mViewPagerTabController = createViewPagerTabController();
        mViewPagerTabController.initViews();
    }

    public ViewPagerTabController<T> createViewPagerTabController() {

        //RadioGroup
        RadioGroup radioGroup = (RadioGroup) findViewById(R.id.radio_group);
        //下面的线
        View ivLine = findViewById(R.id.iv_line);
        //viewpager
        ViewPager viewPager = (ViewPager) findViewById(R.id.viewpager);

        return createViewPagerTabController(radioGroup, ivLine, viewPager);
    }

    public ViewPagerTabController<T> createViewPagerTabController(RadioGroup radioGroup, View viewLine, ViewPager viewPager) {
        return new ViewPagerTabController<T>(this, radioGroup, viewLine, viewPager);
    }

    public ViewPagerTabController<T> getViewPagerTabController() {
        return mViewPagerTabController;
    }

    //下面是实现相关方法
    @Override
    public Context getContext() {
        return this;
    }

    @Override
    public int getRadioButtonResourceId(int index) {
        return 0;
    }

    @Override
    public void onPageSelected(int position) {

    }

    /**
     * 是否显示标题，默认不显示
     *
     * @return 默认为true，子类可重写此方法
     */
    protected boolean needShowTitleBar() {
        return true;
    }

    @Override
    public void onTabClick(int position) {}
}
