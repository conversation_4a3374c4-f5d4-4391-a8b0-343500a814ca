package com.cloudy.linglingbang.activity.community.common.holder.square;

import android.view.View;

import com.cloudy.linglingbang.activity.community.common.holder.BasePostViewHolder;
import com.cloudy.linglingbang.activity.community.common.holder.PostBottomInfoViewHolder;
import com.cloudy.linglingbang.activity.community.common.holder.PostContentViewHolder;
import com.cloudy.linglingbang.activity.community.common.holder.PostExperienceViewHolder;
import com.cloudy.linglingbang.activity.community.common.holder.video.VideoPostCoverViewHolder;

import java.util.ArrayList;

/**
 * 视频帖
 *
 * <AUTHOR>
 * @date 2018/8/19
 */
public class VideoPostWithAttentionViewHolder extends BasePostViewHolder implements PostAuthorWithAttentionViewHolder.OnAttentionListener {
    /**
     * 是否显示视频审核状态
     */
    protected boolean mShowVideoApplyStatus;

    public VideoPostWithAttentionViewHolder(View itemView) {
        this(itemView, false);
    }

    public VideoPostWithAttentionViewHolder(View itemView, boolean showVideoApplyStatus) {
        super(itemView);
        mShowVideoApplyStatus = showVideoApplyStatus;
    }

    @Override
    protected void initChildViewHolder(View itemView) {
        mChildViewHolderList = new ArrayList<>();
        mChildViewHolderList.add(new PostAuthorWithAttentionViewHolder(itemView)
                .setOnAttentionListener(this));
        mChildViewHolderList.add(new PostExperienceViewHolder(itemView));
        mChildViewHolderList.add(new PostContentViewHolder(itemView));
        mChildViewHolderList.add(new VideoPostCoverViewHolder(itemView, mShowVideoApplyStatus));
        mChildViewHolderList.add(new PostBottomInfoViewHolder(itemView));
    }

    @Override
    public boolean needSetAdapter() {
        return true;
    }

    @Override
    public void onAttention(String userIdStr, boolean attention) {
        PostWithAttentionViewHolder.updateAttention(mAdapter, userIdStr, attention);
    }
}
