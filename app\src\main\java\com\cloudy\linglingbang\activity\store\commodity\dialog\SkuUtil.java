package com.cloudy.linglingbang.activity.store.commodity.dialog;

import android.text.TextUtils;

import com.cloudy.linglingbang.model.store.commodity.CommoditySku;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SKU组合算法
 * 参考：https://www.jianshu.com/p/45c7d9dfe5fc
 *
 * <AUTHOR>
 * @date 2018/7/2
 */

public class SkuUtil {
    /**
     * 算法入口
     *
     * @return 拆分所有组合产生的所有情况（生成客户端自己的字典）
     */
    public static Map<String, CommoditySku.SkuValue> skuCollection(CommoditySku commodityTotalSku) {
        List<CommoditySku.SkuValue> skuList = commodityTotalSku.getSkuList();
        sortByAttributeId(commodityTotalSku);

        //用户返回数据
        HashMap<String, CommoditySku.SkuValue> result = new HashMap<>();
        if (skuList == null) {
            return result;
        }
        // 遍历所有库存
        for (CommoditySku.SkuValue commoditySingleSku : skuList) {
            String[] skuKeyAttrs = commoditySingleSku.getSkuAttributeValueIds().split(",");
            //获取所有的组合
            ArrayList<ArrayList<String>> combArr = combInArray(skuKeyAttrs);
            // 对应所有组合添加到结果集里面
            for (int i = 0; i < combArr.size(); i++) {
                add2SKUResult(result, combArr.get(i), commoditySingleSku);
            }

            // 将原始的库存组合也添加进入结果集里面
            String key = TextUtils.join(",", skuKeyAttrs);
            result.put(key, commoditySingleSku);
        }
        return result;
    }

    /**
     * 根据返回的attributeId从小到大排列（防止服务器返回的顺序有误）
     *
     * @param commodityTotalSku
     */
    protected static void sortByAttributeId(CommoditySku commodityTotalSku) {
        List<CommoditySku.SkuValue> skuList = commodityTotalSku.getSkuList();
        if (skuList == null) {
            return;
        }
        //对属性id进行从小到大排序，便于比较相同属性，防止服务端返回的顺序不对
        for (CommoditySku.SkuValue commoditySingleSku : skuList) {
            String attributes = commoditySingleSku.getSkuAttributeValueIds();
            if (attributes.contains(",")) {
                String[] keys = attributes.split(",");
                for (int a = 0; a < keys.length; a++) {
                    for (int b = 0; b < keys.length - 1 - a; b++) {
                        long attributeId_1 = 0;
                        long attributeId_2 = 0;
                        tag:
                        for (CommoditySku.AttributeValueAttr commodityAttribute : commodityTotalSku.getAttrList()) {
                            for (CommoditySku.AttributeValue commodityAttributeValue : commodityAttribute.getLmAttributeValueList()) {
                                if (commodityAttributeValue.getAttributeValueId() == Long.parseLong(keys[b])) {
                                    attributeId_1 = commodityAttributeValue.getAttributeId();
                                    if (attributeId_2 == 0) {
                                        break;
                                    } else {
                                        break tag;
                                    }
                                }
                                if (commodityAttributeValue.getAttributeValueId() == Long.parseLong(keys[b + 1])) {
                                    attributeId_2 = commodityAttributeValue.getAttributeId();
                                    if (attributeId_1 == 0) {
                                        break;
                                    } else {
                                        break tag;
                                    }
                                }
                            }
                        }
                        if (attributeId_1 > attributeId_2) {
                            //交换数据
                            String change = keys[b];
                            keys[b] = keys[b + 1];
                            keys[b + 1] = change;
                        }
                    }
                }
                StringBuffer sb = new StringBuffer();
                for (String key : keys) {
                    sb.append(key + ",");
                }
                String newKey = sb.substring(0, sb.length() - 1);
                commoditySingleSku.setSkuAttributeValueIds(newKey);
            }
        }
    }

    /**
     * 获取所有的组合放到ArrayList里面
     *
     * @param skuKeyAttrs 单个key被； 拆分的数组
     * @return ArrayList
     */
    private static ArrayList<ArrayList<String>> combInArray(String[] skuKeyAttrs) {
        if (skuKeyAttrs == null || skuKeyAttrs.length <= 0) {
            return null;
        }
        int len = skuKeyAttrs.length;
        ArrayList<ArrayList<String>> aResult = new ArrayList<>();
        for (int n = 1; n < len; n++) {
            ArrayList<Integer[]> aaFlags = getCombFlags(len, n);
            for (int i = 0; i < aaFlags.size(); i++) {
                Integer[] aFlag = aaFlags.get(i);
                ArrayList<String> aComb = new ArrayList<>();
                for (int j = 0; j < aFlag.length; j++) {
                    if (aFlag[j] == 1) {
                        aComb.add(skuKeyAttrs[j]);
                    }
                }
                aResult.add(aComb);
            }
        }
        return aResult;
    }

    /**
     * 算法拆分组合 用1和0 的移位去做控制
     * （这块需要你打印才能看的出来）
     *
     * @param len
     * @param n
     * @return
     */
    private static ArrayList<Integer[]> getCombFlags(int len, int n) {
        if (n <= 0) {
            return new ArrayList<>();
        }
        ArrayList<Integer[]> aResult = new ArrayList<>();
        Integer[] aFlag = new Integer[len];
        boolean bNext = true;
        int iCnt1 = 0;
        //初始化
        for (int i = 0; i < len; i++) {
            aFlag[i] = i < n ? 1 : 0;
        }
        aResult.add(aFlag.clone());
        while (bNext) {
            iCnt1 = 0;
            for (int i = 0; i < len - 1; i++) {
                if (aFlag[i] == 1 && aFlag[i + 1] == 0) {
                    for (int j = 0; j < i; j++) {
                        aFlag[j] = j < iCnt1 ? 1 : 0;
                    }
                    aFlag[i] = 0;
                    aFlag[i + 1] = 1;
                    Integer[] aTmp = aFlag.clone();
                    aResult.add(aTmp);
                    if (!TextUtils.join("", aTmp).substring(len - n).contains("0")) {
                        bNext = false;
                    }
                    break;
                }
                if (aFlag[i] == 1) {
                    iCnt1++;
                }
            }
        }
        return aResult;
    }

    /**
     * 添加到数据集合
     *
     * @param result
     * @param newKeyList
     * @param skuModel
     */
    private static void add2SKUResult(HashMap<String, CommoditySku.SkuValue> result, ArrayList<String> newKeyList, CommoditySku.SkuValue skuModel) {
        String key = TextUtils.join(",", newKeyList);
        if (result.containsKey(key)) {
            result.get(key).setSkuStock(result.get(key).getSkuStock() + skuModel.getSkuStock());
            result.get(key).setOriginalPrice(skuModel.getOriginalPrice());
        } else {
            //需要关心所有数据，以便返回数据。这里要new一个新的CommoditySingleSku，否则会计算错误
            result.put(key,
                    new Gson().fromJson(new Gson().toJson(skuModel), CommoditySku.SkuValue.class));
        }
    }
}
