package com.cloudy.linglingbang.activity.store.commodity.adapter;

import android.content.Context;
import android.graphics.Paint;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.store.commodity.CommodityDetailActivity;
import com.cloudy.linglingbang.activity.store.commodity.ReplacementListActivity;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ModelUtils;
import com.cloudy.linglingbang.app.widget.banner.AdRoundImageView;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.SourceModel;
import com.cloudy.linglingbang.model.store.commodity.Compact;
import com.cloudy.linglingbang.model.store.commodity.Replacement;

import java.util.List;
import java.util.Objects;

import androidx.appcompat.widget.AppCompatCheckBox;

/**
 * 换购商品
 *
 * <AUTHOR>
 * @date 2022/10/13
 */
public class ReplacementAdapter extends BaseRecyclerViewAdapter<Object> {
    private final int type;

    public ReplacementAdapter(Context context, List<Object> data, int type) {
        super(context, data);
        this.type = type;
    }

    @Override
    protected BaseRecyclerViewHolder<Object> createViewHolder(View itemView) {
        if (this.type == 1) {
            return new ReplacementCompactViewHolder(itemView);
        }
        return new ReplacementViewHolder(itemView);
    }

    @Override
    protected int getItemLayoutRes(int viewType) {
        if (this.type == 1) {
            return R.layout.item_replacement_compact;
        }
        return R.layout.item_replacement;
    }

    private static class ReplacementCompactViewHolder extends BaseRecyclerViewHolder<Object> {
        AdRoundImageView mIvCommodityImage;
        TextView mTvCommodityName;
        TextView mTvNowPrice;
        TextView tv_expired;
        ImageView iv_checked;
        ImageView iv_add;

        public ReplacementCompactViewHolder(View itemView) {
            super(itemView);
            mIvCommodityImage = itemView.findViewById(R.id.iv_image);
            mTvCommodityName = itemView.findViewById(R.id.tv_name);
            iv_checked = itemView.findViewById(R.id.iv_checked);
            tv_expired = itemView.findViewById(R.id.tv_expired);
            iv_add = itemView.findViewById(R.id.iv_add);
            mTvNowPrice = itemView.findViewById(R.id.tv_now_price);
            iv_add.setOnClickListener(v -> {
                if (commodity != null && AppUtil.checkActivityIsRun(v.getContext())) {
                    ((ReplacementListActivity) v.getContext()).openSkuDialog(commodity.getCommodityId());
                }
            });
            itemView.setOnClickListener(v -> {
                if (commodity != null) {
                    CommodityDetailActivity.Companion.startActivity(v.getContext(), commodity.getCommodityId(), new SourceModel(SourceModel.POSITION_TYPE.COMMODITY_REPLACEMENT_LIST_TYPE, SourceModel.POSITION_TYPE.COMMODITY_REPLACEMENT_LIST_TYPE_VALUE));
                }
            });
        }

        Compact.Commodity commodity;

        @Override
        public void bindTo(Object obj, int position) {
            super.bindTo(obj, position);
            if (!(obj instanceof Compact.Commodity)) {
                return;
            }
            commodity = (Compact.Commodity) obj;
            mTvCommodityName.setText(commodity.getCommodityName());
            mIvCommodityImage.createImageLoad(commodity.getCommodityImage())
                    .load();
            mTvNowPrice.setText(itemView.getResources().getString(R.string.group_buying_old_price,"--"));
            if (!TextUtils.isEmpty(commodity.getSellPriceStr())) {
                mTvNowPrice.setText(itemView.getResources().getString(R.string.group_buying_old_price, commodity.getSellPriceStr()));
            }

            if (0 != commodity.getExpiredReason()) {
                iv_checked.setVisibility(View.GONE);
                if (1 == commodity.getExpiredReason()) {
                    tv_expired.setText(R.string.commodity_sku_size_off);
                } else {
                    tv_expired.setText(R.string.commodity_has_lower_shelf);
                }
                commodity.setIsCheck(0);
                int color_cccccc = mTvCommodityName.getResources().getColor(R.color.color_cccccc);
                Drawable drawable = iv_add.getDrawable().mutate();
                iv_add.setImageDrawable(AppUtil.getTintDrawable(drawable, color_cccccc));
                iv_add.setEnabled(false);
                mIvCommodityImage.setAlpha(0.5f);
                mTvCommodityName.setTextColor(color_cccccc);
                mTvNowPrice.setTextColor(color_cccccc);
            } else {
                iv_add.setEnabled(true);
                tv_expired.setText(null);
                mIvCommodityImage.setAlpha(1.0f);
                iv_add.setImageResource(R.drawable.ic_commodity_list_cart);
                iv_checked.setVisibility(commodity.getIsCheck() == 1 ? View.VISIBLE : View.GONE);
                mTvCommodityName.setTextColor(mTvCommodityName.getResources().getColor(R.color.color_1D1E23));
                mTvNowPrice.setTextColor(mTvCommodityName.getResources().getColor(R.color.color_ea0029));
            }
        }
    }

    private static class ReplacementViewHolder extends BaseRecyclerViewHolder<Object> implements CompoundButton.OnCheckedChangeListener {
        AppCompatCheckBox box;
        AdRoundImageView mIvCommodityImage;
        TextView mTvCommodityName;
        TextView tv_expired;
        TextView mTvNowPrice;
        TextView mTvOriginalPrice;
        Replacement.CommodityInfo mCommodityInfo;

        public ReplacementViewHolder(View itemView) {
            super(itemView);
            tv_expired = itemView.findViewById(R.id.tv_expired);
            box = itemView.findViewById(R.id.box);
            mIvCommodityImage = itemView.findViewById(R.id.iv_image);
            mTvCommodityName = itemView.findViewById(R.id.tv_name);
            mTvNowPrice = itemView.findViewById(R.id.tv_now_price);
            mTvOriginalPrice = itemView.findViewById(R.id.tv_original_price);
            box.setOnCheckedChangeListener(this);
            mTvOriginalPrice.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG);

            itemView.setOnClickListener(v -> {
                if (mCommodityInfo != null) {
                    CommodityDetailActivity.Companion.startActivity(v.getContext(), mCommodityInfo.getCommodityId(), new SourceModel(SourceModel.POSITION_TYPE.COMMODITY_EXCHANGE_LIST_TYPE, SourceModel.POSITION_TYPE.COMMODITY_EXCHANGE_LIST_TYPE_VALUE));
                }
            });
        }

        @Override
        public void bindTo(Object obj, int position) {
            super.bindTo(obj, position);
            if (!(obj instanceof Replacement.CommodityInfo)) {
                return;
            }
            mCommodityInfo = (Replacement.CommodityInfo) obj;
            mTvCommodityName.setText(mCommodityInfo.getCommodityName());
            mIvCommodityImage.createImageLoad(mCommodityInfo.getSkuImage())
                    .load();
            mTvNowPrice.setText(itemView.getResources().getString(R.string.group_buying_old_price,"--"));
            mTvOriginalPrice.setText(itemView.getResources().getString(R.string.group_buying_old_price,"--"));
            mTvNowPrice.setText(ModelUtils.getRmbOrEmptyString(mCommodityInfo.getSellPrice(),true));

            mTvOriginalPrice.setVisibility(View.VISIBLE);
            mTvOriginalPrice.setText(ModelUtils.getRmbOrEmptyString(mCommodityInfo.getOriginPrice(),true));

            if (Objects.equals(mCommodityInfo.getSellPrice(), mCommodityInfo.getOriginPrice())) {
                mTvOriginalPrice.setVisibility(View.GONE);
            }

            if (mCommodityInfo.getExpiredReason() != 0) {
                if (1 == mCommodityInfo.getExpiredReason()) {
                    tv_expired.setText(R.string.commodity_sku_size_off);
                } else {
                    tv_expired.setText(R.string.commodity_has_lower_shelf);
                }
                mIvCommodityImage.setAlpha(0.5f);
                int color_cccccc = mTvCommodityName.getResources().getColor(R.color.color_cccccc);
                mTvCommodityName.setTextColor(color_cccccc);
                mTvNowPrice.setTextColor(color_cccccc);
                box.setEnabled(false);
                box.setOnCheckedChangeListener(null);
                box.setChecked(false);
                box.setOnCheckedChangeListener(this);
                mCommodityInfo.setIsCheck(0);
            } else {
                mIvCommodityImage.setAlpha(1.0f);
                box.setEnabled(true);
                box.setOnCheckedChangeListener(null);
                box.setChecked(mCommodityInfo.getIsCheck() == 1);
                box.setOnCheckedChangeListener(this);
                tv_expired.setText(null);
                mTvCommodityName.setTextColor(mTvCommodityName.getResources().getColor(R.color.color_1D1E23));
                mTvNowPrice.setTextColor(mTvCommodityName.getResources().getColor(R.color.color_ea0029));
            }
        }

        @Override
        public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
            if (mCommodityInfo == null) {
                return;
            }
            if (mCommodityInfo.getIsCheck() == 1) {
                mCommodityInfo.setIsCheck(0);
            } else {
                mCommodityInfo.setIsCheck(1);
            }
            if (AppUtil.checkActivityIsRun(itemView.getContext())) {
                ((ReplacementListActivity) itemView.getContext()).refreshPrice();
            }
        }
    }
}
