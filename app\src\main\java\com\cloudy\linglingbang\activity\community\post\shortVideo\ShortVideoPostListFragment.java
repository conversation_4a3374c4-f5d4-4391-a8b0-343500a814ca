package com.cloudy.linglingbang.activity.community.post.shortVideo;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshFragment;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.app.imageConfig.RoundedCornersTransformation;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.DensityUtil;
import com.cloudy.linglingbang.app.util.DeviceUtil;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.widget.banner.AdRoundImageView;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.postcard.PostCardItem;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;
import butterknife.BindView;
import butterknife.ButterKnife;
import io.reactivex.rxjava3.core.Observable;

/**
 * 首页-视频fragment
 *
 * <AUTHOR>
 * @date 2020-1-8
 */
public class ShortVideoPostListFragment extends BaseRecyclerViewRefreshFragment<PostCard> {
    //栏目位置，写死为10
    private static final long VIDEO_POSITION = 10;

    @Override
    protected int getLayoutRes() {
        return R.layout.activity_short_video_refresh;
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(final List list) {
        RecyclerView.Adapter<? extends RecyclerView.ViewHolder> adapter = new VideoPostAdapter(getContext(), list);
        ((VideoPostAdapter) adapter).setOnItemClickListener(new BaseRecyclerViewHolder.OnItemClickListener() {
            @Override
            public void onItemClick(View itemView, int position) {
                //传 -1 标识新的 首页-视频 页面
                ShortVideoSlideListActivity.startActivity(getContext(), list, position, -1);
            }
        });
        return adapter;
    }

    @Override
    public RefreshController createRefreshController() {
        RefreshController refreshController = new RefreshController(this) {
            @Override
            protected RecyclerView.LayoutManager createLayoutManager(Context context) {
                StaggeredGridLayoutManager layoutManager = new StaggeredGridLayoutManager(2, StaggeredGridLayoutManager.VERTICAL);
                //防止 item 交换位置
                layoutManager.setGapStrategy(StaggeredGridLayoutManager.GAP_HANDLING_NONE);
                return layoutManager;
            }

            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return null;
            }

            @Override
            public void initViews(View rootView) {
                super.initViews(rootView);
                rootView.setBackgroundColor(Color.TRANSPARENT);
                swipeToLoadLayout.setBackgroundColor(Color.TRANSPARENT);
                recyclerView.setBackgroundColor(Color.TRANSPARENT);
                //防止顶部空白
                recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
                    @Override
                    public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                        RecyclerView.LayoutManager layoutManager = recyclerView.getLayoutManager();
                        if (layoutManager instanceof StaggeredGridLayoutManager) {
                            StaggeredGridLayoutManager staggeredGridLayoutManager = (StaggeredGridLayoutManager) layoutManager;
                            int[] first = new int[2];
                            staggeredGridLayoutManager.findFirstCompletelyVisibleItemPositions(first);
                            if (newState == RecyclerView.SCROLL_STATE_IDLE && (first[0] == 1 || first[1] == 1)) {
                                staggeredGridLayoutManager.invalidateSpanAssignments();
                            }
                        }
                    }
                });

            }

        }.setEmptyBackgroundColorId(R.color.white)
                .setEmptyImageResId(R.drawable.ic_message_empty_work_hard);
        refreshController.setPageSize(20);
        return refreshController;
    }

    @Override
    public Observable<BaseResponse<List<PostCard>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return service2.getPostsByColumnPosition(VIDEO_POSITION, pageNo, pageSize);
    }

    class VideoPostAdapter extends BaseRecyclerViewAdapter<PostCard> {

        public VideoPostAdapter(Context context, List<PostCard> data) {
            super(context, data);
        }

        @Override
        protected BaseRecyclerViewHolder<PostCard> createViewHolder(View itemView) {
            return new VideoPostViewHolder(itemView);
        }

        @Override
        protected int getItemLayoutRes(int viewType) {
            return R.layout.item_short_video_post;
        }
    }

    class VideoPostViewHolder extends BaseRecyclerViewHolder<PostCard> {

        /**
         * 封面图
         */
        @BindView(R.id.iv_video_cover)
        AdRoundImageView mIvVideoCover;

        /**
         * 头像
         */
        @BindView(R.id.iv_header)
        ImageView mIvHeader;

        /**
         * 昵称
         */
        @BindView(R.id.tv_nickname)
        TextView mTvNickname;

        /**
         * 阅读数
         */
        @BindView(R.id.tv_read_count)
        TextView mTvReadCount;
        /**
         * 点赞数
         */
//        @BindView(R.id.tv_praise_count)
//        TextView mTvPraiseCount;

        /**
         * 标题
         */
        @BindView(R.id.tv_title)
        TextView mTvTitle;

        public VideoPostViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            ButterKnife.bind(this, itemView);
        }

        @Override
        public void bindTo(PostCard postCard, int position) {
            super.bindTo(postCard, position);
            //如果有栏目封面
            PostCardItem singlePostCardItem = null;
            if (postCard.getColumnCoverImages() != null && postCard.getColumnCoverImages().size() > 0) {
                singlePostCardItem = postCard.getColumnCoverImages().get(0);
            }
            //如果没有栏目封面，获取帖子封面
            else {
                for (PostCardItem postCardItem : postCard.getImgTexts()) {
                    if (!TextUtils.isEmpty(postCardItem.getImg())) {
                        singlePostCardItem = postCardItem;
                        break;
                    }
                }
            }
            if (singlePostCardItem != null) {
                int width = 0;
                int height = 0;
                //更新大小
                if (!TextUtils.isEmpty(singlePostCardItem.getWidth()) && !TextUtils.isEmpty(singlePostCardItem.getHeight())) {
                    try {
                        width = Integer.parseInt(singlePostCardItem.getWidth());
                        height = Integer.parseInt(singlePostCardItem.getHeight());
                    } catch (NumberFormatException e) {
                        e.printStackTrace();
                    }
                }
                if (width <= 0 || height <= 0) {
                    width = 345;
                    height = 455;
                }
                int showWidth = (DeviceUtil.getScreenWidth((Activity) mIvVideoCover.getContext()) - DensityUtil.dip2px(mIvVideoCover.getContext(), 30)) / 2;//获取实际展示的图片宽度
                int showHeight = (int) (showWidth * ((float) height / width));//获取最终图片高度
                ViewGroup.LayoutParams layoutParams = mIvVideoCover.getLayoutParams();
                RelativeLayout.LayoutParams clLayoutParams = (RelativeLayout.LayoutParams) layoutParams;
                clLayoutParams.width = showWidth;//获取实际展示的图片宽度
                clLayoutParams.height = showHeight;//获取最终图片高度
                mIvVideoCover.setLayoutParams(clLayoutParams);
                //加载图片
                mIvVideoCover.createCornerImageLoad(singlePostCardItem.getImg(), RoundedCornersTransformation.CornerType.TOP)
                        .setOverrideSize(showWidth, showHeight)//如果不指定的话，图片会闪动，可能是因为还未正确确定图片大小
                        .load();
            }

            mTvReadCount.setText(AppUtil.getCommentDesc(postCard.getPostShowCount()));
//            mTvPraiseCount.setText(AppUtil.getCommentDesc(postCard.getPostPraiseCount()));
            mTvTitle.setText(postCard.getTitle());
            //作者相关
            if (postCard.getAuthor() != null) {
                //加载头像
                String url = AppUtil.getImageUrlBySize(postCard.getAuthor().getPhoto(), AppUtil._120X120);
                new ImageLoad(getContext(), mIvHeader, url, ImageLoad.LoadMode.URL)
                        .setPlaceholder(R.drawable.user_head_default_120x120)
                        .setErrorImageId(R.drawable.user_head_default_120x120)
                        .setCircle(true)
                        .load();
                mTvNickname.setText(postCard.getAuthor().getNickname());
            }
        }
    }
}
