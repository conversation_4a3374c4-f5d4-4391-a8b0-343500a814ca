package com.cloudy.linglingbang.activity.community;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.text.Layout;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.HomeActivity;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.community.common.HeaderWrapperPostAdapter;
import com.cloudy.linglingbang.activity.community.linglab.CommunityDoPostLingLabActivity;
import com.cloudy.linglingbang.activity.community.linglab.FullScreenImgActivity;
import com.cloudy.linglingbang.activity.community.linglab.LingLabDoPostUtils;
import com.cloudy.linglingbang.activity.community.linglab.viewmodel.ChooseImgViewModel;
import com.cloudy.linglingbang.adapter.newcommunity.CommunityIndexAdapter;
import com.cloudy.linglingbang.app.imageConfig.RoundedCornersTransformation;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.DensityUtil;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.ShareUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.TopicUtils;
import com.cloudy.linglingbang.app.util.ViewHolderUtils;
import com.cloudy.linglingbang.app.widget.CommonSortView;
import com.cloudy.linglingbang.app.widget.banner.AdRoundImageView;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.app.widget.banner.AdRoundImageView;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.app.widget.recycler.header.HeaderAndFooterWrapperAdapter;
import com.cloudy.linglingbang.constants.WebUrlConfigConstant;
import com.cloudy.linglingbang.model.community.Topic;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BackgroundSubscriber;
import com.cloudy.linglingbang.model.server.Ad.Ad2;
import com.cloudy.linglingbang.model.server.Ad.AdJumpUtil2;
import com.cloudy.linglingbang.model.server.Ad.Ad2;
import com.cloudy.linglingbang.model.server.Ad.AdJumpUtil2;
import com.cloudy.linglingbang.model.tag.LingLabImageBean;
import com.donkingliang.imageselector.utils.ImageSelector;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import io.reactivex.rxjava3.core.Observable;

/**
 * 话题详情
 *
 * <AUTHOR>
 * @date 2018/6/22
 */
public class TopicDetailActivity extends BaseRecyclerViewRefreshActivity<PostCard> implements CommonSortView.SortSelectListener {

    private ShareUtil mShareUtil;
    private Long mTopicId;
    private String mTopicName;
    private static final int SHRINK_UP_STATE = 1;// 收起状态
    private static final int SPREAD_STATE = 2;// 展开状态
    private static int mState = SHRINK_UP_STATE;//默认收起状态

    private TextView mTvTopicDetailContent;
    private TextView mIvMoreArrow;
    private RecyclerView mAdListRecyclerView;
    private TextView mTopicListName;
    private View mTopicBottomLine;
    private CommonSortView mCommonSortView;

    /**
     * 话题图标
     */
    private ImageView mIvTopicIcon;

    /**
     * 话题名字
     */
    private TextView mTvTopicName;

    /**
     * 话题简介
     */
    private TextView mTvTopicSummary;

    /**
     * 话题日期
     */
    private TextView mTvTopicDate;

    /**
     * 参与人数
     */
    private TextView mTvPartInNum;

    /**
     * 阅读人数
     */
    private TextView mTvReadNum;

    private Topic mTopic;

    @Override
    protected void initialize() {
        mChooseImgViewModel = new ViewModelProvider(this, new ViewModelProvider.NewInstanceFactory()).get(ChooseImgViewModel.class);
        mTopicId = getOrParseIntentLongExtra();
        super.initialize();
    }

    @Override
    public boolean onPrepareOptionsMenu(Menu menu) {
        menu.clear();
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(R.menu.menu_topic_more, menu);
        mToolbar.setOnMenuItemClickListener(onMenuItemClick);
        return true;
    }

    /**
     * 增加Toolbar监听
     */
    protected Toolbar.OnMenuItemClickListener onMenuItemClick = new Toolbar.OnMenuItemClickListener() {
        @Override
        public boolean onMenuItemClick(MenuItem menuItem) {
            switch (menuItem.getItemId()) {
                case R.id.topic_more:
                    IntentUtils.startActivity(TopicDetailActivity.this, TopicListActivity.class);
                    break;
            }
            return true;
        }
    };

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<PostCard> list) {
        HeaderAndFooterWrapperAdapter headerAndFooterWrapperAdapter = new HeaderAndFooterWrapperAdapter();
        headerAndFooterWrapperAdapter.setInnerAdapter(new HeaderWrapperPostAdapter(this, list, headerAndFooterWrapperAdapter));
        headerAndFooterWrapperAdapter.setInnerAdapter(new CommunityIndexAdapter(this, list));
        LayoutInflater inflater = LayoutInflater.from(this);
        LinearLayout headerView = (LinearLayout) inflater.inflate(R.layout.header_topic_detail, getRefreshController().getRecyclerView(), false);
        initHeaderView(headerView);
        headerAndFooterWrapperAdapter.addHeaderView(headerView);
        return headerAndFooterWrapperAdapter;
    }

    public static void startActivity(Context context, String topicId) {
        IntentUtils.startActivity(context, TopicDetailActivity.class, topicId);
    }

    @Override
    public RefreshController<PostCard> createRefreshController() {
        RefreshController<PostCard> controller = new RefreshController<PostCard>(this) {

            @Override
            public void initViews(View rootView) {
                super.initViews(rootView);
                recyclerView.setBackgroundResource(R.color.white);

            }

            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return null;
            }

            @Override
            public void onRefresh() {
                super.onRefresh();
                requestTopicDetail();
                requestAdList();
            }

            @Override
            protected void onLoadSuccess(int loadPage, List<PostCard> list, int loadType) {
                super.onLoadSuccess(loadPage, list, loadType);
                if (loadPage == 1) {
                    if (list == null || list.size() == 0) {
                        forceShowEmptyInfo(true, DensityUtil.dip2px(TopicDetailActivity.this, 50));
                    }
                }
            }
        };
        controller.setEmptyImageResId(R.drawable.ic_empty_post);
        controller.setEmptyStringResId(R.string.empty_topic);
        return controller;
    }

    /**
     * 设置为我推荐的广告位
     */
    private void setAdListView(List<Ad2> list) {
        TopicDetailAdAdapter adapter;
        if (mAdListRecyclerView.getAdapter() == null) {
            adapter = new TopicDetailAdAdapter(getContext(), list);
            mAdListRecyclerView.setAdapter(adapter);
        } else {
            adapter = (TopicDetailAdAdapter) mAdListRecyclerView.getAdapter();
            adapter.setNewData(list);
        }
        int visibility = adapter.getItemCount() > 0 ? View.VISIBLE : View.GONE;
        if (visibility != mAdListRecyclerView.getVisibility()) {
            mAdListRecyclerView.setVisibility(visibility);
            mTopicListName.setVisibility(visibility);
            mTopicBottomLine.setVisibility(visibility);
        }
    }

    /**
     * 获取为我推荐的广告位数据
     */
    private void requestAdList() {
        if (mTopicId == null) {
            return;
        }
        L00bangRequestManager2.getServiceInstance()
                .getAllTopicAd(mTopicId)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new BackgroundSubscriber<List<Ad2>>(getContext()) {
                    @Override
                    public void onSuccess(List<Ad2> list) {
                        super.onSuccess(list);
                        setAdListView(list);
                    }
                });

    }

    /**
     * 请求话题详情数据
     */
    private void requestTopicDetail() {
        L00bangRequestManager2.getServiceInstance()
                .getTopicDetailById(mTopicId)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new BackgroundSubscriber<Topic>(getContext()) {
                    @Override
                    public void onSuccess(Topic topic) {
                        super.onSuccess(topic);
                        if (topic == null) {
                            return;
                        }
                        mTopic = topic;
                        initHeaderData();
                    }
                });
    }

    private void initHeaderData() {
        mTopicName = mTopic.getTopicName();
        mTvTopicName.setText(TopicUtils.getNameWithHashtag(mTopic.getTopicName()));
        mTvTopicSummary.setMaxLines(2);
        mTvTopicSummary.setText(mTopic.getTopicSummary());
        mTvTopicDate.setText(AppUtil.formatDate(mTopic.getCreateDate(), "yyyy.MM.dd"));
        //显示话题图片
//        Glide.with(getContext()).load(AppUtil.getImageUrlBySize(mTopic.getTopicIcon(), AppUtil._200X200))
//                .dontAnimate()
//                .placeholder(R.drawable.ic_topic_default)
//                .transform(new CenterCrop(getContext()), new RoundedCornersTransformation(getContext(), 8))
//                .into(mIvTopicIcon);
        new ImageLoad(mIvTopicIcon, AppUtil.getImageUrlBySize(mTopic.getTopicIcon(), AppUtil._200X200))
                .setDoNotAnimate()
                .setPlaceholder(R.drawable.ic_common_place_holder)
                .setTransformations(new CenterCrop(), new RoundedCornersTransformation(8))
                .load();
        mTvPartInNum.setText(getContext().getString(R.string.topic_partake, AppUtil.getCommentDesc(mTopic.getPartInNum())));
        mTvReadNum.setText(getContext().getString(R.string.topic_read, AppUtil.getCommentDesc(mTopic.getReadNum())));
        mTvTopicDetailContent.setText(mTopic.getTopicDesc());
        mTvTopicDetailContent.post(new Runnable() {
            @Override
            public void run() {
                Layout l = mTvTopicDetailContent.getLayout();
                if (l != null) {
                    int lines = l.getLineCount();
                    if (lines > 0) {
                        if (l.getEllipsisCount(lines - 1) > 0) {
                            mIvMoreArrow.setVisibility(View.VISIBLE);
                        }
                    }
                }
            }
        });
    }

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_topic_detail);
    }

    private void initHeaderView(LinearLayout headerView) {
        mTvTopicDetailContent = headerView.findViewById(R.id.tv_topic_detail_content);
        mIvTopicIcon = headerView.findViewById(R.id.iv_topic_icon);
        mTvTopicName = headerView.findViewById(R.id.tv_topic_name);
        mTvTopicSummary = headerView.findViewById(R.id.tv_topic_summary);
        mTvTopicDate = headerView.findViewById(R.id.tv_topic_date);
        mTvPartInNum = headerView.findViewById(R.id.tv_part_in_num);
        mTvReadNum = headerView.findViewById(R.id.tv_read_num);
        ViewHolderUtils.setVisibility(false, headerView.findViewById(R.id.line));

        mIvMoreArrow = headerView.findViewById(R.id.iv_more_arrow);
        mCommonSortView = headerView.findViewById(R.id.common_sort);
        mAdListRecyclerView = headerView.findViewById(R.id.recycler_view);
        mTopicListName = headerView.findViewById(R.id.tv_recommend_topic_list);
        mTopicBottomLine = headerView.findViewById(R.id.view_line_hot_topic_list);
        mCommonSortView.setOnSelectSortListener(this);
        //根据TextView是否有省略信息判断是否显示更多
        mIvMoreArrow.setVisibility(View.GONE);
        //获取帖子排序，根据该排序获取列表
        AppUtil.getCardSort(this);
        mIvMoreArrow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //如果是展开状态
                if (mState == SPREAD_STATE) {
                    mTvTopicDetailContent.setMaxLines(4);
                    mTvTopicDetailContent.requestLayout();
                    mState = SHRINK_UP_STATE;
                    mIvMoreArrow.setText(R.string.community_list_my_joined_expand);
                } else {
                    if (mState == SHRINK_UP_STATE) {
                        mTvTopicDetailContent.setMaxLines(Integer.MAX_VALUE);
                        mTvTopicDetailContent.requestLayout();
                        mIvMoreArrow.setText(R.string.community_list_my_joined_collapsed);
                        mState = SPREAD_STATE;
                    }
                }
            }
        });

    }

    @Override
    public Observable<BaseResponse<List<PostCard>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return service2.getPostsByTopicId(pageNo, pageSize, AppUtil.getCardSort(this), mTopicId);
    }

    /**
     * 点击分享
     */
    @OnClick(R.id.ll_share)
    protected void onShareClick(View view) {
        if (mTopic == null) {
            ToastUtil.showMessage(this, "数据请求中，请稍后再试！");
            return;
        }
        String url;
        final String content;
        url = String.format(Locale.getDefault(), WebUrlConfigConstant.TOPIC_SHARE_PATTERN, mTopicId);
        content = mTopic.getTopicSummary();
        List<String> imagePath = new ArrayList<>();
        String shareImg = AppUtil.getImageUrlBySize(mTopic.getTopicIcon(), AppUtil._120X120);
        if (shareImg != null) {
            imagePath.add(shareImg);
        }
        String title = mTopic.getTopicName();
        mShareUtil = ShareUtil.newInstance();
        mShareUtil.setShareType(ShareUtil.ShareType.SHARE_TOPIC);
        mShareUtil.setShareActivityId(mTopicId);
        mShareUtil.share(this, url, content, imagePath, title, false);
    }

    LingLabDoPostUtils mLingLabDoPostUtils;

    /**
     * 点击参与讨论
     */
    @OnClick(R.id.ll_discuss)
    protected void onDiscussClick(View view) {
        if (AppUtil.checkLogin(this)) {
            if (mTopicId == null || mTopicName == null) {
                ToastUtil.showMessage(this, "数据请求中，请稍后再试！");
                return;
            }

            if (mLingLabDoPostUtils == null) {
                mLingLabDoPostUtils = new LingLabDoPostUtils(getContext());
            }
            mLingLabDoPostUtils.requestPermission(AppUtil.getActivity(getContext()), new String[]{
                    Manifest.permission.READ_EXTERNAL_STORAGE,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE,
                    Manifest.permission.CAMERA
            }, LingLabDoPostUtils.REQUEST_PHOTO_CODE, 1);

//            CommunityDoPostImageTextActivity.startActivityToPostTopic(this, PostCard.PostType.TOPIC, mTopicId, mTopicName);
        }
    }

    private ChooseImgViewModel mChooseImgViewModel;

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == HomeActivity.REQUEST_CODE_CHOOSE_IMG_LING_LAB_DO_POST) {
            if (data != null) {
                List<String> images = data.getStringArrayListExtra(ImageSelector.SELECT_RESULT);
                ArrayList<LingLabImageBean> lingLabImageBeanList = new ArrayList<>();
                if (images != null) {
                    for (int i = 0; i < images.size(); i++) {
                        LingLabImageBean lingLabImageBean = new LingLabImageBean(images.get(i), lingLabImageBeanList.size());
                        lingLabImageBeanList.add(i, lingLabImageBean);
                    }
                }
                if (mChooseImgViewModel != null) {
                    mChooseImgViewModel.changeItem(images);
                }
                Intent in = new Intent(this, FullScreenImgActivity.class);
                in.putParcelableArrayListExtra(FullScreenImgActivity.EXTRA_IMAGE_URLS, lingLabImageBeanList);
                in.putExtra(FullScreenImgActivity.EXTRA_IMAGE_INDEX, 0);
                in.putExtra(FullScreenImgActivity.EXTRA_TOPIC, mTopic);
                startActivity(in);
            }
        } else {
            if (mShareUtil != null) {
                mShareUtil.onActivityResult(requestCode, resultCode, data);
            }
        }
    }

    @Override
    protected void onPermissionResult(boolean isGranted, int requestCode) {
        if (requestCode == LingLabDoPostUtils.REQUEST_PHOTO_CODE) {
            if (isGranted) {
                mLingLabDoPostUtils.doChoosePhoto(1);
            } else {
                ToastUtil.showMessage(TopicDetailActivity.this, getString(R.string.ling_lab_need_permission));
            }
        }
    }

    /**
     * 点击排序的回调
     *
     * @param selectType 排序类型
     */
    @Override
    public void onSortSelect(int selectType) {
        getRefreshController().manualRefresh();
    }

    /**
     * 话题广告的adapter
     */
    public static class TopicDetailAdAdapter extends BaseRecyclerViewAdapter<Ad2> {

        public TopicDetailAdAdapter(Context context, List<Ad2> data) {
            super(context, data);
        }

        public void setNewData(List<Ad2> data) {
            mData = data;
            notifyDataSetChanged();
        }

        @Override
        protected BaseRecyclerViewHolder<Ad2> createViewHolder(View itemView) {
            return new TopicDetailAdItemViewHolder(itemView);
        }

        @Override
        protected int getItemLayoutRes(int viewType) {
            return R.layout.item_topic_detail_ad_item_view;
        }
    }

    /**
     * 话题广告viewHolder
     */
    public static class TopicDetailAdItemViewHolder extends BaseRecyclerViewHolder<Ad2> {

        /**
         * 封图
         */
        @BindView(R.id.iv_cover)
        AdRoundImageView mIvCover;

        private Context mContext;

        public TopicDetailAdItemViewHolder(View itemView) {
            super(itemView);
            mContext = itemView.getContext();
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            ButterKnife.bind(this, itemView);
        }

        @Override
        public void bindTo(Ad2 ad2, int position) {
            super.bindTo(ad2, position);
            mIvCover.setShowVideoIcon(false);
            mIvCover.loadCommonImage(ad2.getAdvertiseImage());
            mIvCover.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    AdJumpUtil2.goToActivity(mContext, ad2);
                }
            });

        }
    }

}
