package com.cloudy.linglingbang.app.widget.dialog;

import com.cloudy.linglingbang.BaseInstrumentedTest;
import com.cloudy.linglingbang.model.Version;

/**
 * 升级对话框
 *
 * <AUTHOR>
 * @date 2018/12/12
 */
public class VersionUpdateDialogTest extends BaseInstrumentedTest {
    @Override
    public void test() {
        super.test();
        testShowUpdateDialog();
    }

    private void testShowUpdateDialog() {
        Version version = new Version();
        version.setVersionCode(Integer.MAX_VALUE);
        version.setVersionName("测试版本");
        version.setNote("测试");
        version.setAppPath("https://cdn-res.00bang.cn/apk/2018/11/29/150325015343.apk");
        final VersionUpdateDialog updateDialog = new VersionUpdateDialog(getActivity(), version);
        updateDialog.show();
    }
}