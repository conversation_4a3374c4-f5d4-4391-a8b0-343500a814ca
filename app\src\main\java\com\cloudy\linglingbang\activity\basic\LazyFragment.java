package com.cloudy.linglingbang.activity.basic;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.FrameLayout;

import java.lang.reflect.Field;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

/**
 * 懒加载的 Fragment
 * <p>
 * 子类要使用懒加载，继承该类，{@link #setNeedLazyLoad(boolean)}
 * <p>
 * 在 setUserVisibleHint 和 onHiddenChanged 才加载；
 * 如果是 ViewPager 且可滑动的，可以在滑动时调用 {@link #loadRealView()} 加载实际布局，或者是提供占位图，等展示出来再加载实际布局
 * <p>
 * 原理：
 * <p>
 * 首次只加载一个简单的占位布局，在实际加载时才 inflate 并添加到 ViewGroup
 * 可以减少首次时不必要的加载，可用于优化启动速度
 *
 * <AUTHOR>
 * @date 2018/9/28
 */
public abstract class LazyFragment extends BaseFragment {

    /**
     * 是否需要懒加载
     */
    private boolean mNeedLazyLoad;

    /**
     * 是否可以加载真实视图
     */
    private boolean isCanLoadView;

    /**
     * 懒加载的 View
     */
    private View mLazyView;

    /**
     * 真实加载的 view
     */
    private View mRealView;

    //持有以用来加载真实的 View
    private LayoutInflater mInflater;
    private ViewGroup mContainer;
    private Bundle mSavedInstanceState;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if (!isNeedLazyLoad() || isCanLoadView()) {
            //不需要懒加载，调用父类方法
            mRealView = onCreateRealView(inflater, container, savedInstanceState);
            return removeFromParent(mRealView);
        }

        //需要懒加载，但如果已经加载过了，则不需要再加载
        if (mRealView != null) {
            return removeFromParent(mRealView);
        }

        mInflater = inflater;
        mContainer = container;
        mSavedInstanceState = savedInstanceState;
        if (container == null) {
            return null;
        }

        if (mLazyView == null) {
            mLazyView = onCreateLazyView(inflater, container, savedInstanceState);
        }
        return removeFromParent(mLazyView);
    }

    private View removeFromParent(View view) {
        ViewGroup parent = (ViewGroup) view.getParent();
        if (parent != null) {
            parent.removeView(view);
        }
        return view;
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser) {
            onShow();
        }
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (!hidden) {
            onShow();
        }
    }

    private void onShow() {
        loadRealView();
    }

    public void loadRealView() {
        isCanLoadView = true;
        if (isNeedLazyLoad() && mRealView == null) {
            if (mLazyView != null) {
                //加载真实的 view
                mRealView = onCreateRealView(mInflater, mContainer, mSavedInstanceState);
                if (mRealView != null) {
                    //添加真实的 view
                    ViewParent parent = mLazyView.getParent();
                    if (parent instanceof ViewGroup) {
                        ViewGroup viewGroup = (ViewGroup) parent;
                        //添加到相同的 index
                        int index = viewGroup.indexOfChild(mLazyView);
                        viewGroup.removeViewAt(index);
                        viewGroup.addView(mRealView, index);
                        //设置 mView 否则会出错
                        setView(mRealView);
                        //部分子类在在 onViewCreated 中初始化
                        onViewCreated(mRealView, mSavedInstanceState);
                    }
                }
            }
        }
    }

    /**
     * 创建占位布局
     * 该布局可以用来临时展示，用来加快启动速度
     * 也可以用来展示加载运画，用来加载完成后再展示实际 View （如果有缓存等可以马上展示的的则应使用实际布局）
     */
    public View onCreateLazyView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return new FrameLayout(container.getContext());
    }

    /**
     * 创建实际布局
     */
    public View onCreateRealView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return super.onCreateView(inflater, container, savedInstanceState);
    }

    /**
     * 设置 mView ，因为只会调用一次，也持有 Field，除非设为静态供多个 Fragment 使用
     */
    private void setView(View view) {
        Class<Fragment> fragmentClass = Fragment.class;
        setField(fragmentClass, view, "mView");
        setField(fragmentClass, view, "mInnerView");
    }

    private void setField(Class fragmentClass, View view, String fieldName) {
        try {
            Field field = fragmentClass.getDeclaredField(fieldName);
            if (field != null) {
                field.setAccessible(true);
                try {
                    field.set(this, view);
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        }
    }

    public boolean isNeedLazyLoad() {
        return mNeedLazyLoad;
    }

    private boolean isCanLoadView() {
        return isCanLoadView;
    }

    public void setNeedLazyLoad(boolean needLazyLoad) {
        mNeedLazyLoad = needLazyLoad;
    }

    public boolean isRealViewLoaded() {
        return mRealView != null;
    }
}
