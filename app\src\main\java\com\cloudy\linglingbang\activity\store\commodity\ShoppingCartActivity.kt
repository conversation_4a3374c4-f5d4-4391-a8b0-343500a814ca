package com.cloudy.linglingbang.activity.store.commodity

import android.content.Context
import android.content.DialogInterface
import android.graphics.Rect
import android.text.TextUtils
import android.view.*
import android.widget.*
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import butterknife.BindView
import butterknife.OnClick
import com.blankj.utilcode.util.SizeUtils
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity
import com.cloudy.linglingbang.activity.basic.RefreshController
import com.cloudy.linglingbang.activity.fragment.mynew.util.SelfUserInfoLoader
import com.cloudy.linglingbang.activity.store.commodity.adapter.RecommendCommodityAdapter
import com.cloudy.linglingbang.activity.store.commodity.adapter.ShoppingCartAdapter
import com.cloudy.linglingbang.activity.store.commodity.adapter.holder.OnChangeCommodityListener
import com.cloudy.linglingbang.activity.store.commodity.dialog.CouponDialog
import com.cloudy.linglingbang.activity.store.commodity.dialog.OneClickCleaningDialog
import com.cloudy.linglingbang.app.util.*
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils
import com.cloudy.linglingbang.app.util.timer.CountDownManager
import com.cloudy.linglingbang.app.widget.SlidingButtonView
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog3
import com.cloudy.linglingbang.app.widget.recycler.EmptySupportedRecyclerView
import com.cloudy.linglingbang.app.widget.recycler.header.HeaderAndFooterWrapperAdapter
import com.cloudy.linglingbang.constants.AppConstants
import com.cloudy.linglingbang.constants.FinalSensors
import com.cloudy.linglingbang.constants.WebUrlConfigConstant
import com.cloudy.linglingbang.model.SourceModel
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber
import com.cloudy.linglingbang.model.store.commodity.CartBean
import com.cloudy.linglingbang.model.store.commodity.CartChangeNumber
import com.cloudy.linglingbang.model.store.commodity.CartInfo
import com.cloudy.linglingbang.model.store.commodity.ShoppingCart2
import com.cloudy.linglingbang.model.store.home.StoreElementCommodity
import com.google.gson.Gson
import io.reactivex.rxjava3.core.Observable
import org.json.JSONException
import org.json.JSONObject
import java.math.BigDecimal

/**
 * 购物车
 *
 * <AUTHOR>
 * @date 2022/9/29
 */
open class ShoppingCartActivity : BaseRecyclerViewRefreshActivity<Any>() {

    /**
     * toolbar是否加载较慢(有的机型toolbar加载时机比较晚,容易出现NPE)
     */
    private var isToolbarLoadSlowly = false

    private var amountDetailsPopupWindow: ShoppingCartAmountDetailsPopupWindow? = null

    /**
     * 编辑按钮
     */
    private var mEditItem: MenuItem? = null

    /**
     * 是否正在编辑
     */
    private var isEditStatus = false

    /**
     * 购物车所有数据
     */
    private var shoppingCart: ShoppingCart2? = null

    var mCouponsDialog: CouponDialog? = null

    var deleteDialog: CommonAlertDialog3? = null

    var oneClickCleaningDialog: OneClickCleaningDialog? = null

    @JvmField
    @BindView(R.id.view_line)
    var lineView: View? = null

    @JvmField
    @BindView(R.id.rl_bottom_price)
    var rlBottomPrice: RelativeLayout? = null

    /** 底部价格（正常模式下显示） */
    @JvmField
    @BindView(R.id.ll_price)
    var llPrice: LinearLayout? = null

    /** 去结算按钮（正常模式下显示） */
    @JvmField
    @BindView(R.id.btn_settle_accounts)
    var btnSettleAccounts: Button? = null

    /** 移除按钮（编辑模式下显示） */
    @JvmField
    @BindView(R.id.ll_remove)
    var llRemove: LinearLayout? = null

    /** 清除失效商品按钮（编辑模式下显示） */
    @JvmField
    @BindView(R.id.btn_clear_invalid)
    var btnClearInvalid: Button? = null

    /** 移除商品按钮（编辑模式下显示） */
    @JvmField
    @BindView(R.id.btn_delete)
    var btnDelete: Button? = null

    /** 全选按钮 */
    @JvmField
    @BindView(R.id.cb_check_all)
    var cbCheckAll: CheckBox? = null

    /** 价格 */
    @JvmField
    @BindView(R.id.tv_commodity_price)
    var tvCommodityPrice: TextView? = null

    /** 数量 */
    @JvmField
    @BindView(R.id.tv_count)
    var tvCount: TextView? = null

    /** 数量 */
    @JvmField
    @BindView(R.id.tv_all_discount)
    var tvAllDiscount: TextView? = null

    /** 优惠券 */
    @JvmField
    @BindView(R.id.rl_coupon_entrance)
    var rlCouponEntrance: RelativeLayout? = null

    /** 优惠相关布局 */
    @JvmField
    @BindView(R.id.ll_discount)
    var llDiscount: LinearLayout? = null

    private var mSlidingButtonView: SlidingButtonView? = null

    var shoppingCartAdapter: ShoppingCartAdapter? = null

    var editCheckList = mutableListOf<Long>()

    var shoppingCartRecyclerView: EmptySupportedRecyclerView? = null

    var mIvEmpty: ImageView? = null
    var mTvEmpty: TextView? = null
    var rlTop: RelativeLayout? = null
    var llLoveTitle: LinearLayout? = null
    var llRecommendEmptyView: LinearLayout? = null

    //猜你喜欢上面的横线
    var viewLine: View? = null

    /**
     * 购物车空布局
     */
    var llEmpty: View? = null

    /**
     * 是否是手动选择
     */
    var isManual: Boolean? = true

    var cartInfoList = mutableListOf<CartInfo>()

    /**
     * 购物车和推荐商品是否为空，未请求时置为null
     */
    var isShoppingCartEmpty: Boolean? = null
    var isRecommendEmpty: Boolean? = null
    var isOnRefresh: Boolean? = false


    override fun loadViewLayout() {
        setContentView(R.layout.activity_shopping_cart)
    }

    override fun initialize() {
        super.initialize()
        setMiddleTitle("购物车")
        cbCheckAll?.setOnCheckedChangeListener { _, isChecked ->
            if (isManual == true) {
                //如果是手动点击，则执行相关操作
                onSelectCheckAll(isChecked)
            } else {
                isManual = true
            }

        }
    }

    /**
     * 全选和全不选
     */
    private fun onSelectCheckAll(checked: Boolean) {
        if (!isEditStatus) {
            SensorsUtils.sensorsClickBtn("点击全选", "购物车", "全选按钮")
            var list = mutableListOf<Long>()
            shoppingCart?.cartInfo?.let { t ->
                for (carInfo in t) {
                    carInfo?.commodityList?.let {
                        for (commodity in it) {
                            if (!checked) {
                                list.add(commodity.cartId)
                            } else {
                                if (commodity.isReselect != 1 && commodity.isCheck != 2 && commodity.expiredReason == 0) {
                                    list.add(commodity.cartId)
                                }
                            }
                        }
                    }
                }

                if (list.isNotEmpty()) {
                    <EMAIL>(list, if (checked) 1 else 0)
                }
            }
        } else {
            SensorsUtils.sensorsClickBtn("点击编辑状态下全选", "购物车", "全选按钮")
            //编辑模式
            shoppingCart?.cartInfo?.let { t ->
                for (singleCartInfo in t) {
                    singleCartInfo?.commodityList?.let {
                        for (commodity in it) {
                            commodity.isCheckForEdit = if (checked) 1 else 0
                        }
                    }
                }
                shoppingCartAdapter?.notifyDataSetChanged()
            }

        }


    }


    private fun createAdapterForCart(): RecyclerView.Adapter<out RecyclerView.ViewHolder> {
        shoppingCartAdapter = ShoppingCartAdapter(this, cartInfoList)
        shoppingCartAdapter?.setOnNeedRefreshListener {
            refreshShoppingCartData()
        }
        shoppingCartAdapter?.mOnChangeCommodityListener = object : OnChangeCommodityListener {
            override fun onChangeSingleSelect(cartId: Long, isSelect: Int) {
                if (!isEditStatus) {
                    var cartIds = mutableListOf<Long>()
                    cartIds.add(cartId)
                    <EMAIL>(cartIds, isSelect)
                } else {
//                    if (isSelect == 1) {
//                        editCheckList.add(cartId)
//                    } else {
//                        editCheckList.remove(cartId)
//                    }
                    calculationAllSelect()
                }

            }

            override fun onChangeSelect(cartIds: MutableList<Long>, isSelect: Int) {
                <EMAIL>(cartIds, isSelect)
            }

            override fun onDeleteSingle(cartId: Long) {
                var cartIds = mutableListOf<Long>()
                cartIds.add(cartId)
                <EMAIL>(cartIds, null)
            }

            override fun onDeleteSingle(cartIds: MutableList<Long>, tips: String) {
                <EMAIL>(cartIds, tips)
            }

            override fun changeNumToCart(cartId: Long, count: Long) {
                <EMAIL>(cartId, count)
            }

            override fun onMenuIsOpen(view: View?) {
                <EMAIL>(view)
            }

            override fun onDownOrMove(slidingButtonView: SlidingButtonView?) {
                <EMAIL>(slidingButtonView)
            }

        }
        return shoppingCartAdapter!!
    }


    /**
     * 计算是否全选
     */
    private fun calculationAllSelect() {
        shoppingCart?.cartInfo?.let { t ->
            var all = 0
            var select = 0

            for (singleCartInfo in t) {
                singleCartInfo?.commodityList?.let {
                    for (commodity in it) {
                        all++
                        if (commodity.isCheckForEdit == 1) {
                            select++
                        }
                    }
                }
            }
            if (all == select) {
                if (cbCheckAll?.isChecked == false) {
                    isManual = false
                    cbCheckAll?.isChecked = true
                }
            } else {
                if (cbCheckAll?.isChecked == true) {
                    isManual = false
                    cbCheckAll?.isChecked = false
                }
            }
        }
    }


    /**
     * 刷新购物车
     */
    fun refreshShoppingCartData() {
        getListDataFormCart()
    }


    fun getListDataFormCart() {
        var requestMap = mapOf("channelSourceId" to AppConstants.CHANNEL_SOURCE_ID)
        L00bangRequestManager2.getServiceInstance()
            .getShoppingCart(requestMap)
            .compose(L00bangRequestManager2.setSchedulers())
            .subscribe(object : ProgressSubscriber<ShoppingCart2>(this) {
                override fun onSuccess(shoppingCart2: ShoppingCart2?) {
                    super.onSuccess(shoppingCart2)
                    isShoppingCartEmpty =
                        shoppingCart2 == null || shoppingCart2.cartInfo == null || shoppingCart2.cartInfo.size == 0
                    checkShowEmpty()
                    if (mEditItem != null) {
                        mEditItem?.setTitle(R.string.edit)
//                    mEditItem?.isVisible = true
                        mEditItem?.isEnabled = true
                    } else {
                        isToolbarLoadSlowly = true
                    }
                    shoppingCart = shoppingCart2
                    cartInfoList.clear()
                    cartInfoList.addAll(shoppingCart?.cartInfo as MutableList<CartInfo>)
                    shoppingCartAdapter?.notifyDataSetChanged()
                    //设置猜你喜欢顶部的横线
                    if (shoppingCart2 != null && shoppingCart2.expiredCommodityList.isNotEmpty()) {
                        viewLine?.visibility = View.VISIBLE
                    } else {
                        viewLine?.visibility = View.GONE
                    }
                    initPriceUI()

                }

                override fun onFailure(e: Throwable?) {
                    super.onFailure(e)
                    isShoppingCartEmpty = true
                    checkShowEmpty()
                }

            })

    }


    /**
     * 设置是否是编辑模式
     */
    private fun setEditModel() {
        mEditItem?.setTitle(if (isEditStatus) R.string.commit_done else R.string.edit)
        llPrice?.visibility = if (isEditStatus) View.GONE else View.VISIBLE
        btnSettleAccounts?.visibility = if (isEditStatus) View.GONE else View.VISIBLE
        llRemove?.visibility = if (isEditStatus) View.VISIBLE else View.GONE
        changeToEditModel(isEditStatus)
        refreshController?.apply {
            setRefreshEnable(!isEditStatus)
        }
    }

    /**
     * 价格初始化
     */
    private fun initPriceUI() {
        runOnUiThread {
            shoppingCart?.apply {
                if (cartInfo.isEmpty()) {
                    lineView?.visibility = View.INVISIBLE
                    rlBottomPrice?.visibility = View.INVISIBLE
                    rlCouponEntrance?.visibility = View.GONE
                    mEditItem?.isVisible = false
                    isEditStatus = false
                } else {
                    mEditItem?.isVisible = true
                    lineView?.visibility = View.VISIBLE
                    rlBottomPrice?.visibility = View.VISIBLE
                    rlCouponEntrance?.visibility = View.VISIBLE
                    tvCommodityPrice?.text = "¥$payAmountStr"
                    if (discountAmount?.compareTo(BigDecimal.ZERO)!! > 0) {
                        llDiscount?.visibility = View.VISIBLE
                        tvAllDiscount?.text = "共减 ¥$discountAmountStr"
                    } else {
                        llDiscount?.visibility = View.GONE
                    }
                    if (quantity <= 0) {
                        tvCount?.visibility = View.GONE
                    } else {
                        tvCount?.visibility = View.VISIBLE
                        tvCount?.text = "（${quantity}件）"
                    }
                    if (isAllSelect == 0) {
                        if (cbCheckAll?.isChecked == true) {
                            isManual = false
                            cbCheckAll?.isChecked = false
                        }
                    } else {
                        if (cbCheckAll?.isChecked == false) {
                            isManual = false
                            cbCheckAll?.isChecked = true
                        }
                    }
                    if (isEditStatus) {
                        mEditItem!!.setTitle(R.string.commit_done)
                    } else {
                        mEditItem!!.setTitle(R.string.edit)
                    }

                }
                SelfUserInfoLoader.getInstance().updateCartCountAndDispatch(count)
            }
        }


    }


    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        super.onCreateOptionsMenu(menu)
        mEditItem = mToolbar?.menu?.findItem(R.id.action_right_text)
        mEditItem?.setTitle(R.string.edit)
        mEditItem?.isVisible = isToolbarLoadSlowly
        mEditItem?.isEnabled = isToolbarLoadSlowly
        return true
    }


    /**
     * 点击价格详情
     */
    @OnClick(R.id.tv_see_price_detail)
    fun onPriceDetailClick(view: View) {
        SensorsUtils.sensorsClickBtn("点击查看价格明细", "购物车", "查看价格明细按钮")
        var location = IntArray(2)
        lineView?.getLocationOnScreen(location)
        amountDetailsPopupWindow = ShoppingCartAmountDetailsPopupWindow(this)
        amountDetailsPopupWindow?.contentView!!.measure(0, 0)
        amountDetailsPopupWindow?.showAtLocation(
            lineView,
            Gravity.NO_GRAVITY,
            location[0],
            location[1] - DeviceUtil.getScreenHeight() + DensityUtil.dip2px(context, 71F)
        )
        shoppingCart?.apply {
            amountDetailsPopupWindow?.setShoppingCart(shoppingCart)
        }

    }


    /**
     * 点击清除失效商品
     */
    @OnClick(R.id.btn_clear_invalid)
    fun onClickClearInvalid(view: View) {
        SensorsUtils.sensorsClickBtn("点击编辑状态下清除", "购物车", "清除按钮")
        if (shoppingCart?.expiredCommodityList?.isNotEmpty() == true) {
            oneClickCleaningDialog =
                OneClickCleaningDialog(this, shoppingCart?.expiredCommodityList)
            oneClickCleaningDialog?.setOnClickDelete {
                deleteCartCommodity(it)
            }
            oneClickCleaningDialog?.show()
        } else {
            ToastUtil.showMessage(this, "没有可以清除的商品")
        }
    }

    /**
     * 点击删除
     */
    @OnClick(R.id.btn_delete)
    fun onClickDelete(view: View) {
        SensorsUtils.sensorsClickBtn("点击编辑状态下删除", "购物车", "删除按钮")
        editCheckList.clear()
        shoppingCart?.cartInfo?.let { t ->
            for (singleCartInfo in t) {
                singleCartInfo?.commodityList?.let {
                    for (commodity in it) {
                        if (commodity.isCheckForEdit == 1) {
                            editCheckList.add(commodity.cartId)
                        }
                    }
                }
            }
            deleteCartCommodity(editCheckList)
        }
    }


    /**
     * 删除商品
     */
    private fun deleteCartCommodity(list: MutableList<Long>) {
        if (list.isNotEmpty()) {
            showDeleteCommodityDialog(list, null)
        } else {
            ToastUtil.showMessage(this, "你没有选择任何商品")
        }
    }


    /**
     * 点击进入优惠券按钮
     */
    @OnClick(R.id.tv_coupon)
    protected open fun onCouponClick(view: View?) {
        SensorsUtils.sensorsClickBtn("点击优惠券", "购物车", "优惠券按钮")
        if (mCouponsDialog == null) {
            mCouponsDialog = CouponDialog(this)
            mCouponsDialog?.openFromCart = true
        }
        if (cartInfoList != null && cartInfoList.size > 0) {
            val sb = StringBuilder()
            for (i in cartInfoList.indices) {
                val obj: Any = cartInfoList[i]
                if (obj is CartInfo) {
                    obj.commodityList?.forEach {
                        sb.append(it.commodityId)
                            .append(",")
                    }
                }
            }
            if (sb.isNotEmpty()) {
                sb.delete(sb.length - 1, sb.length)
            }
            val commodityIds = sb.toString()
            mCouponsDialog?.mCommodityIds = commodityIds
        }
        mCouponsDialog?.show()
    }

    /**
     * 点击去结算
     */
    @OnClick(R.id.btn_settle_accounts)
    fun onSettleAccountsClick() {
        SensorsUtils.sensorsClickBtn("点击去结算", "购物车", "结算按钮")
        if (shoppingCart != null && shoppingCart?.quantity!! > 0) {
            val jsonObject = JSONObject()
            try {
                jsonObject.put("orderSource", SourceModel.POSITION_TYPE.SHOPPING_CAR_VALUE)
                jsonObject.put("orderSourceType", SourceModel.POSITION_TYPE.SHOPPING_CAR_TYPE)
            } catch (e: JSONException) {
                e.printStackTrace()
            }
            JumpPageUtil.goCommonWeb(
                this,
                WebUrlConfigConstant.SHOPPING_CART_CONFIRM + "?sensorsInfo=" + jsonObject.toString()
            )
        } else {
            ToastUtil.showMessage(context, "您还没有选中商品～")
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == R.id.action_right_text) {
            if (!isEditStatus) { //点击编辑
                SensorsUtils.sensorsClickBtn("点击编辑", "购物车", "编辑按钮")
                isEditStatus = true
                setEditModel()
                cbCheckAll?.isChecked = false
            } else { //点击完成
                SensorsUtils.sensorsClickBtn("点击编辑状态下完成", "购物车", "完成按钮")
                isEditStatus = false
                setEditModel()
                refreshShoppingCartData()
                initPriceUI()
            }
            return true
        }
        return super.onOptionsItemSelected(item)
    }


    override fun createRefreshController(): RefreshController<Any> {
        return object : RefreshController<Any>(this) {

            override fun onRefresh() {
                isShoppingCartEmpty = null
                isRecommendEmpty = null
                super.onRefresh()
                isOnRefresh = true
                getListDataFormCart()
            }

            override fun onLoadSuccess(loadPage: Int, list: MutableList<Any>?, loadType: Int) {
                super.onLoadSuccess(loadPage, list, loadType)
                isRecommendEmpty = list == null || list.size == 0
                checkShowEmpty()
            }

            override fun onLoadFail(loadPage: Int, e: Throwable?) {
                super.onLoadFail(loadPage, e)
                isRecommendEmpty = true
                checkShowEmpty()
            }

            override fun createLayoutManager(context: Context): RecyclerView.LayoutManager {
                val spanCount = 2
                val manager =
                    StaggeredGridLayoutManager(spanCount, StaggeredGridLayoutManager.VERTICAL)
                manager.spanCount
                //防止 item 交换位置
                manager.gapStrategy = StaggeredGridLayoutManager.GAP_HANDLING_NONE
                return manager
            }


            override fun createItemDecoration(context: Context): RecyclerView.ItemDecoration {
                val px30 = context.resources.getDimensionPixelOffset(R.dimen.normal_30)
                val px16 = context.resources.getDimensionPixelOffset(R.dimen.normal_16)
                return object : RecyclerView.ItemDecoration() {
                    override fun getItemOffsets(
                        outRect: Rect,
                        view: View,
                        parent: RecyclerView,
                        state: RecyclerView.State
                    ) {
                        super.getItemOffsets(outRect, view, parent, state)
                        val data = data ?: return
                        val position = parent.getChildAdapterPosition(view)
                        //第一个为购物车数据，所以此处从第二个开始
                        if (position < 1 || position >= data.size + 1) {
                            return
                        }
                        if (mData[position - 1] is StoreElementCommodity) {
                            var spanIndex = -1
                            val tempSize = mData.size
                            for (i in 0 until tempSize) {
                                if (mData[position - 1] is StoreElementCommodity) {
                                    spanIndex++
                                }
                            }
                            if (spanIndex == -1) {
                                return
                            }
                            if (spanIndex % 2 == 0) {
                                outRect.left = px30
                                outRect.right = px16
                            } else {
                                outRect.left = px16
                                outRect.right = px30
                            }
                        }

                    }
                }
            }
        }
    }


    override fun onPause() {
        super.onPause()
        SensorsUtils.sensorsViewEndNew("购物车（好物）","购物车（好物）")
        CountDownManager.getInstance().onPause(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        CountDownManager.getInstance().onPause(this)
    }

    override fun onResume() {
        super.onResume()
        CountDownManager.getInstance().onResume(this)
        SensorsUtils.sensorsViewStart( "购物车（好物）")
        //从其他页面返回，刷新购物车
        if (isOnRefresh == true) {
            //上一个页面finish之后马上调用，会先调用onresume，然后在调用目标页面的onDestroy，从而订阅会被取消，
            // 会引发retrofit报错IOException: Canceled，暂时修改延时800毫秒，有空在调研更好的解决方案
            mTvEmpty?.postDelayed(Runnable {
                refreshShoppingCartData()
            }, 800)
        }
    }

    /**
     * 切换到编辑模式或者正常模式
     */
    fun changeToEditModel(isEditEventModel: Boolean) {
        shoppingCart?.isEditModel = isEditEventModel
        shoppingCart?.cartInfo?.let { t ->
            for (singleCartInfo in t) {
                singleCartInfo?.commodityList?.let {
                    for (commodity in it) {
                        commodity.isEditModel = isEditEventModel
                        commodity.isCheckForEdit = 0
                    }
                }

            }
            shoppingCartAdapter?.notifyDataSetChanged()
        }

    }

    /**
     * 购物车选择和取消选择
     */
    fun checkOrUnCheckToCart(cartIds: MutableList<Long>, isSelect: Int) {
        var cartBean = CartBean(cartIds, isSelect)
        L00bangRequestManager2.getServiceInstance()
            .checkOrUnCheckToCart(cartBean)
            .compose(L00bangRequestManager2.setSchedulers())
            .subscribe(object : ProgressSubscriber<Boolean>(this) {
                override fun onSuccess(t: Boolean?) {
                    super.onSuccess(t)
                    refreshShoppingCartData()
                }
            })
    }

    /**
     * 删除购物车商品
     */
    private fun deleteFromCart(cartIds: MutableList<Long>) {
        var cartBean = CartBean(cartIds)
        L00bangRequestManager2.getServiceInstance()
            .deleteFromCart(cartBean)
            .compose(L00bangRequestManager2.setSchedulers())
            .subscribe(object : ProgressSubscriber<Boolean>(this) {
                override fun onSuccess(t: Boolean?) {
                    super.onSuccess(t)
                    ToastUtil.showMessage(context, "删除成功！")
                    if (oneClickCleaningDialog?.isShowing == true) {
                        oneClickCleaningDialog?.dismiss()
                    }
                    //关闭编辑模式
                    isEditStatus = false
                    setEditModel()
                    refreshShoppingCartData()
                }
            })

    }

    /**
     * 修改购物车数量
     */
    fun changeNumToCart(cartId: Long, count: Long) {
        var cartBean = CartChangeNumber(cartId, count)
        L00bangRequestManager2.getServiceInstance()
            .changeNumToCart(cartBean)
            .compose(L00bangRequestManager2.setSchedulers())
            .subscribe(object : ProgressSubscriber<Boolean>(this) {
                override fun onSuccess(t: Boolean?) {
                    super.onSuccess(t)
                    refreshShoppingCartData()
                }

                override fun onError(e: Throwable) {
                    super.onError(e)
                    refreshShoppingCartData()
                }
            })

    }

    open fun showDeleteCommodityDialog(cartIds: MutableList<Long>, tips: String?) {
        if (AppUtil.checkLogin(this)) {
            var deleteTips = if (TextUtils.isEmpty(tips)) this.getString(
                R.string.store_shopping_car_delete_tips,
                cartIds.size
            ) else tips
            deleteDialog = CommonAlertDialog3(
                this,
                deleteTips,
                this.getString(R.string.commodity_shopping_cart_delete_conform),
                this.getString(R.string.commodity_shopping_cart_delete_cancel),
                DialogInterface.OnClickListener { dialog, which ->
                    closeMenu() //关闭菜单
                    deleteFromCart(cartIds)

                },
                DialogInterface.OnClickListener { dialog: DialogInterface?, which: Int ->
                    closeMenu() //关闭菜单
                })
            deleteDialog?.show()
        }
    }

    /**
     * 判断是否有菜单打开
     */
    fun menuIsOpen(): Boolean {
        return mSlidingButtonView != null
    }

    /**
     * 关闭菜单
     */
    fun closeMenu() {
        mSlidingButtonView?.closeMenu()
        mSlidingButtonView = null
    }

    fun onMenuIsOpen(view: View?) {
        mSlidingButtonView = view as SlidingButtonView
    }

    fun onDownOrMove(slidingButtonView: SlidingButtonView?) {
        //当有菜单打开，并且不是当前item，则关闭
        if (menuIsOpen()) {
            if (mSlidingButtonView !== slidingButtonView) {
                closeMenu()
            }
        }
    }

    override fun createAdapter(list: MutableList<Any>): RecyclerView.Adapter<out RecyclerView.ViewHolder> {
        var recommendCommodityAdapter = RecommendCommodityAdapter(this, list)
        val headerAndFooterWrapperAdapter: HeaderAndFooterWrapperAdapter<*> =
            HeaderAndFooterWrapperAdapter<Any?>(recommendCommodityAdapter as RecyclerView.Adapter<RecyclerView.ViewHolder>)
        val inflater = LayoutInflater.from(this)
        val headerView = inflater.inflate(
            R.layout.header_shopping_cart,
            refreshController.recyclerView,
            false
        ) as LinearLayout
        initHeaderView(headerView)
        headerAndFooterWrapperAdapter.addHeaderView(headerView)
        return headerAndFooterWrapperAdapter

    }

    private fun initHeaderView(headerView: LinearLayout) {
        shoppingCartRecyclerView = headerView.findViewById(R.id.recycler_shopping_cart)
        llEmpty = headerView.findViewById(R.id.ll_empty)
        mIvEmpty = headerView.findViewById(R.id.iv_empty)
        mTvEmpty = headerView.findViewById(R.id.tv_empty_desc)
        rlTop = headerView.findViewById(R.id.rl_top)
        llLoveTitle = headerView.findViewById(R.id.ll_love_title)
        llRecommendEmptyView = headerView.findViewById(R.id.ll_recommend_empty_view)
        viewLine = headerView.findViewById(R.id.view_line)
        //先置空
        llEmpty?.visibility = View.GONE
        var lifePaymentParam = rlTop?.layoutParams
        lifePaymentParam?.height = SizeUtils.dp2px(340f)
        rlTop?.layoutParams = lifePaymentParam
        //设置购物车空时的文案
        mIvEmpty?.setImageResource(R.drawable.ic_shopping_cart_empty)
        mTvEmpty?.text = "挑点喜欢的装进购物车"
        llEmpty?.setBackgroundResource(R.color.white)
        shoppingCartRecyclerView?.emptyView = llEmpty
        shoppingCartRecyclerView?.layoutManager =
            LinearLayoutManager(shoppingCartRecyclerView?.context)
        shoppingCartRecyclerView?.adapter = createAdapterForCart()


    }

    override fun getListDataFormNet(
        service2: L00bangService2,
        pageNo: Int,
        pageSize: Int
    ): Observable<BaseResponse<List<Any>>> {
        val map = HashMap<String, Any>()
        map["type"] = 1
        map["pageNo"] = if (pageNo > 1) pageNo - 1 else 1
        map["pageSize"] = pageSize
        return service2.getRecommendCommodityList(map)
            .map { listBaseResponse: BaseResponse<List<StoreElementCommodity>> ->
                if (listBaseResponse.data == null || listBaseResponse.data.isEmpty()) {
                    listBaseResponse.cloneWithData(emptyList())
                } else {
                    listBaseResponse.cloneWithData(listBaseResponse.data)
                }
            }
    }


    /**
     * 检查是否需要显示空布局
     */
    open fun checkShowEmpty() {
        if (isRecommendEmpty == null || isShoppingCartEmpty == null) {
            return
        }
        //购物车和推荐同时为空时，推荐整体隐藏
        if (isShoppingCartEmpty!! && isRecommendEmpty!!) {
            llLoveTitle?.visibility = View.GONE
            llRecommendEmptyView?.visibility = View.GONE

        } else {
            if (isRecommendEmpty!!) {
                llRecommendEmptyView?.visibility = View.VISIBLE
            } else {
                llRecommendEmptyView?.visibility = View.GONE
            }
//            refreshController.forceShowEmptyInfo(true, 0)
            llLoveTitle?.visibility = View.VISIBLE
        }

    }

    override fun onBackPressed() {
        super.onBackPressed()
        SensorsUtils.sensorsClickBtn("点击返回", "购物车", "返回按钮")
    }

    override fun onStart() {
        super.onStart()
        SensorsUtils.sensorsViewStart(FinalSensors.BROWSE_SHOPPING_CART)
    }

    override fun onStop() {
        super.onStop()
        SensorsUtils.sensorsViewEndNew(
            "购物车",
            FinalSensors.BROWSE_SHOPPING_CART,
            "浏览购物车"
        )
    }


}