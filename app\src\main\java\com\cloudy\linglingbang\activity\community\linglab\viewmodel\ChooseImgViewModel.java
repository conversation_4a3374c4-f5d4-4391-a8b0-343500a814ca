package com.cloudy.linglingbang.activity.community.linglab.viewmodel;

import com.cloudy.linglingbang.model.tag.LingLabImageBean;

import java.util.ArrayList;
import java.util.List;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

/**
 * 选择图片的viewModel
 *
 * <AUTHOR>
 * @date 2021/12/9
 */
public class ChooseImgViewModel extends ViewModel {
    public int currentPosition = 0;
    public MutableLiveData<List<LingLabImageBean>> mArrayImgLiveData = new MutableLiveData<>();

    public void changeItem(List<String> images) {
        List<LingLabImageBean> imgList = new ArrayList<>();
        for (int i = 0; i < images.size(); i++) {
            LingLabImageBean lingLabImageBean = new LingLabImageBean(images.get(i), i);
            imgList.add(i, lingLabImageBean);
        }
        mArrayImgLiveData.postValue(imgList);
    }
}
