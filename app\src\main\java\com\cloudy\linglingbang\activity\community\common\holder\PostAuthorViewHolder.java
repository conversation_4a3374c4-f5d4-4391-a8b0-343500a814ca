package com.cloudy.linglingbang.activity.community.common.holder;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.community.common.PostFlagsEnum;
import com.cloudy.linglingbang.activity.community.post.CommunityUtils;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.util.ViewHolderUtils;
import com.cloudy.linglingbang.app.util.span.UserIdentitySpanUtils;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.user.Author;

/**
 * 作者
 *
 * <AUTHOR>
 * @date 2018/6/25
 */
public class PostAuthorViewHolder extends BasePostChildViewHolder {
    private ImageView mIvUserHead;
    //private ImageView mIvUserSex;
    private ImageView mIvBigV;
    private TextView mTvUserName;
    private TextView mTvUserIdentity;
    private TextView mTvLocation;
    protected TextView mTvColumn;
    private TextView mTvCarClub;
    protected TextView mTvLabel;
    private ImageView mIvOperation;
    private ImageView mIvGoldCoin;
    protected int mFlags;
    private CommunityUtils.PostOperateUtils.OperateListener mOperateListener;
    private boolean mFinishAfterDelete;

    private Author mAuthor;

    public PostAuthorViewHolder(View itemView) {
        super(itemView);
        //如果直接创建，就需要设默认值
        mFlags = PostFlagsEnum.addDefaultFlags(mFlags);
    }

    public void setFlags(int flags) {
        mFlags = flags;
    }

    public PostAuthorViewHolder addFlags(PostFlagsEnum flagsEnum) {
        mFlags = flagsEnum.addFlags(mFlags);
        return this;
    }

    public void setOperateListener(CommunityUtils.PostOperateUtils.OperateListener operateListener) {
        mOperateListener = operateListener;
    }

    public PostAuthorViewHolder setFinishAfterDelete(boolean finishAfterDelete) {
        mFinishAfterDelete = finishAfterDelete;
        return this;
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        mIvUserHead = itemView.findViewById(R.id.iv_user_head);
        //mIvUserSex = itemView.findViewById(R.id.iv_user_sex);
        mIvBigV = itemView.findViewById(R.id.iv_big_v);
        mTvUserName = itemView.findViewById(R.id.tv_user_name);
        mTvUserIdentity = itemView.findViewById(R.id.tv_user_identity);
        mTvLocation = itemView.findViewById(R.id.tv_location);
        mTvColumn = itemView.findViewById(R.id.tv_column);
        mTvCarClub = itemView.findViewById(R.id.tv_car_club);
        mTvLabel = itemView.findViewById(R.id.tv_label);
        mIvOperation = itemView.findViewById(R.id.iv_operation);
        mIvGoldCoin = itemView.findViewById(R.id.iv_goldCoin);//金币

        //点击用户头像、性别、昵称
        ViewHolderUtils.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onClickUser(v);
            }
        }, mIvUserHead, mIvBigV, mTvUserName, mTvUserIdentity);
        //点击火图标就不跳了

        //点击车友会
        ViewHolderUtils.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onClickCarClub(v);
            }
        }, mTvCarClub);

        //点击标签
        ViewHolderUtils.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onClickTvLabel(v);
            }
        }, mTvLabel);

        //点击栏目
        ViewHolderUtils.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //暂时不作跳转
            }
        }, mTvColumn);

        //暂时将标签、栏目隐藏，如果子类有特殊处理，可以再显示出来
        ViewHolderUtils.hiddenViews(mTvLabel, mTvColumn);

        //点击操作
        ViewHolderUtils.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onClickIvOperation(v);
            }
        }, mIvOperation);
    }

    @Override
    public void bindTo(PostCard postCard) {
        super.bindTo(postCard);
        bindTo(postCard.getAuthor());
//        if (mIvGoldCoin != null) {
//            if (isShowReward(postCard.getPostShareAward())) {
//                mIvGoldCoin.setVisibility(View.VISIBLE);
//            } else {
//                mIvGoldCoin.setVisibility(View.GONE);
//            }
//        }
        //操作
        ViewHolderUtils.setVisibility(showOperation(), mIvOperation);
    }

    /**
     * 是否显示金币
     *
     * @param showReward
     * @return
     */
    private boolean isShowReward(PostCard.PostShareAward showReward) {
        return showReward != null && PostFlagsEnum.SHOW_GOLD_COIN.checkFlags(mFlags) && showReward.getIsAward() == 1;
    }

    /**
     * 如果没有帖子信息，不设置栏目与标签，直接设置作者
     */
    public void bindTo(Author author) {
        //如果没有返回 author 可能会复用

        mAuthor = author;
        if (author != null) {
            //头像，如果没头像显示默认
            if (mIvUserHead != null) {
                String url = AppUtil.getImageUrlBySize(author.getPhoto(), AppUtil._120X120);
                ImageLoad.LoadUtils.loadAvatar(mIvUserHead.getContext(), mIvUserHead, url);
            }
            //大v的展示
            if (mIvBigV != null) {
                mIvBigV.setVisibility(UserUtils.isBigV(author) ? View.VISIBLE : View.GONE);
            }
            //用户昵称与身份
            if (mTvUserIdentity == null) {
                //没有身份，直接设给 tvUserName
                UserIdentitySpanUtils.setUserNameAndIdentity(mTvUserName, author, mFlags, true);
            } else {
                ViewHolderUtils.setText(author.getNickname(), mTvUserName);
                UserIdentitySpanUtils.setUserNameAndIdentity(mTvUserIdentity, author, mFlags, false);
            }
            //地址
            ViewHolderUtils.setText(author.getCityStr(), mTvLocation);

            //需要判断经销商是否为空
            if (mTvCarClub != null) {
                String dealerName = author.getDealerName();
                if (!TextUtils.isEmpty(dealerName)) {
                    //经销商不为空，展示
                    mTvCarClub.setText(dealerName);
                } else {
                    //车友会
                    mTvCarClub.setText(author.getCarClubName());
                }
            }
        } else {
            //头像
            if (mIvUserHead != null) {
                ImageLoad.LoadUtils.loadAvatar(mIvUserHead.getContext(), mIvUserHead, "");
            }
            //性别
            //ViewHolderUtils.hiddenViews(mIvUserSex);
            //用户昵称、地址、车友会
            ViewHolderUtils.setText("", mTvUserName, mTvUserIdentity, mTvLocation, mTvCarClub);
        }
    }

    /**
     * 需要显示操作
     */
    protected boolean showOperation() {
        //设置了回调才显示，默认不显示
        //如果是副邦主，只要加精和置顶不同时满足，则显示
        if (mPostCard.getChiefType() == 1) {
            return true;
        } else {
            return mPostCard.getChiefType() == 3 && (mPostCard.getIsElite() == 0 || mPostCard.getIsTop() == 0);
        }
    }

    /**
     * 点击用户
     */
    private void onClickUser(View v) {
        if (PostFlagsEnum.IGNORE_AVATAR_CLICK.checkFlags(mFlags)) {
            //个人主页不再跳转
            return;
        }
        if (mAuthor != null) {
            JumpPageUtil.goToPersonPage(v.getContext(), mAuthor);
        }
    }

    /**
     * 点击车友会或经销商
     */
    private void onClickCarClub(View v) {
        JumpPageUtil.goClubOrDealer(v.getContext(), mAuthor);
    }

    /**
     * 点击标签
     */
    private void onClickTvLabel(View v) {
        if (mPostCard != null) {
            JumpPageUtil.goLabelPostList(v.getContext(), mPostCard.getLabelName(), mPostCard.getLabelIdOrZero());
        }
    }

    /**
     * 点击操作按钮
     */
    private void onClickIvOperation(View v) {
        //除了默认的操作，额外的操作
        CommunityUtils.PostUtils postUtils = new CommunityUtils.PostUtils(v.getContext(), mPostCard, v, null, mOperateListener)
                .setFinishAfterDelete(mFinishAfterDelete);
        postUtils.showOperatePopup();
    }
}
