package com.cloudy.linglingbang.app.util.image.compress;

import android.os.Environment;

import com.cloudy.linglingbang.BaseInstrumentedTest;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.constants.ConfigConstant;

import org.junit.runner.RunWith;

import java.util.List;

import androidx.test.ext.junit.runners.AndroidJUnit4;

/**
 * Instrumented test, which will execute on an Android device.
 *
 * @see <a href="http://d.android.com/tools/testing">Testing documentation</a>
 */
@RunWith(AndroidJUnit4.class)
public class CompressImageHelperTest extends BaseInstrumentedTest {

    @Override
    public void test() {
        super.test();
        String tmpDir = Environment.getExternalStorageDirectory().getAbsolutePath() + ConfigConstant.SD_TMP_DIRECTORY;
        tmpDir += "原始/";
        CompressImageHelper.with(getContext())
                .load(tmpDir + "1.jpg")
                .load(tmpDir + "0.jpg")
                .listen(new CompressListener() {
                    @Override
                    public void onCompressSuccess(List<CompressResult> compressResultList) {
                        ToastUtil.showMessage(getContext(), "压缩完成，数量" + compressResultList.size());
                    }

                    @Override
                    public void onCompressFail(Throwable e) {
                        ToastUtil.showMessage(getContext(), "压缩失败" + e);
                    }
                })
                .compress();
    }
}
