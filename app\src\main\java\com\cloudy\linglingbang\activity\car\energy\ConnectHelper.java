package com.cloudy.linglingbang.activity.car.energy;

import static android.app.Activity.RESULT_OK;

import android.app.Activity;
import android.app.Application;
import android.content.Intent;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;

import androidx.fragment.app.Fragment;

import com.blankj.utilcode.util.SPUtils;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.car.energy.bluetooth.ConnectFailEnum;
import com.cloudy.linglingbang.activity.car.energy.bluetooth.ScanResultEnum;
import com.cloudy.linglingbang.activity.travel.TravelFragment;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.constants.AppConstants;
import com.cloudy.linglingbang.activity.travel.utils.StringUtils;
import com.cloudy.linglingbang.activity.car.energy.bluetooth.BleScanListener;
import com.cloudy.linglingbang.activity.car.energy.bluetooth.ConnectStatusEnum;
import com.cloudy.linglingbang.activity.car.energy.bluetooth.ConnectUtil;
import com.cloudy.linglingbang.activity.car.energy.bluetooth.ControlDataUtil;
import com.cloudy.linglingbang.activity.car.energy.bluetooth.GattUtil;
import com.cloudy.linglingbang.activity.car.energy.bluetooth.PermissionsUtil;
import com.cloudy.linglingbang.activity.car.energy.bluetooth.ScanUtil;
import com.cloudy.linglingbang.activity.car.energy.bluetooth.SendControlUtil;
import com.cloudy.linglingbang.activity.car.energy.ble.BlueKeyUtil;
import com.cloudy.linglingbang.activity.car.energy.ble.KeyEntity;
import java.util.HashMap;

/**
 * Created by LiYeWen on 2025/04/29
 * 蓝牙连接工具类
 */
public class ConnectHelper {

    public final static String TAG = "蓝牙连接BleTAG";
    private static ConnectHelper instance;
    //是否在界面上显示蓝牙收发日志
    public static boolean SHOW_LOG = false;
    //当前的activity
    private Activity mActivity;
    public static HashMap<String, String> bleErrorCodeMap = new HashMap<>();
    public static HashMap<Byte, String> e260SPErrorMsg = new HashMap<>();
    public static HashMap<String, String> bleParkingCodeMap = new HashMap<>();
    public static final String INTERRUPT = " 蓝牙连接中断";
    /**
     * 是否是用户手动连接蓝牙，true表示手动连接，手动连接就显示那些loading和权限提示
     */
    private boolean isHandleConnect = true;
    //设置当前车辆的mac
    private String mMac;
    //蓝牙连接状态监听
    private ConnectStatusListener mConnectStatusListener;

    public static String ServiceCode = "";
    /**
     * 智慧泊车类型：
     * 0:默认无状态;1:泊入;2:泊出;
     */
    public static int park_type = 0;
    //五菱汽车的
    /**
     * 在HomeActivity 上显示Dialog
     */
    public final static int RESULT_DIALOG = 999;
    public final static int RESULT_FINISH = 1999;
    public final static int RESULT_MAIN_PARK = 2999;
    public final static String RESULT_DIALOG_TITLE = "result_dialog_title";
    public final static String RESULT_DIALOG_MESSAGE = "result_dialog_message";
    //蓝牙扫描结果（用于判断是否有蓝牙信号）
    private int mScanResult = ScanResultEnum.NO_COMPLETE.getCode();

    private ConnectHelper(){}

    /**
     * 获取单例对象
     * @return
     */
    public static ConnectHelper getInstance() {
        if (instance == null) {
            synchronized (ConnectHelper.class) {
                instance = new ConnectHelper();
            }
        }
        return instance;
    }

    public static void instance(Application application) {
        if (instance == null) {
            synchronized (ConnectHelper.class) {
                instance = new ConnectHelper();
            }
        }
    }

    /**
     * 蓝牙连接入口方法，调用此方法默认为手动连接
     */
    public void startConnect() {
        //调用手动连接的方法
        startConnect(true);
    }

    /**
     * 蓝牙连接入口方法
     * @param isHandle   是否是手动连接，true表示是
     */
    public void startConnect(boolean isHandle) {

        if(mActivity == null){
            Log.e(TAG, "进入蓝牙连接入口方法 startConnect() mActivity == null" + INTERRUPT);
            return;
        }

        //将进行自动连接
        if(!isHandle){
            //蓝牙自动连接开关为关闭状态
            if(!canAutoConnectBle()){
                Log.e(TAG, "蓝牙自动连接开关为关闭状态，将不执行蓝牙自动连接 startConnect()" + INTERRUPT);
                return;
            }
        }

        if(ConnectUtil.getInstance().getConnectStatus() == ConnectStatusEnum.NO_CONNECT.getStatus()){
            //未连接直接赋值
            this.isHandleConnect = isHandle;
        }else {
            //连接中、连接中等状态只能切换到手动连接
            if(isHandle){
                this.isHandleConnect = true;
            }
        }

        Log.e(TAG, "进入蓝牙连接入口方法 startConnect() " + getConnectLog() + ",mMac="+mMac);

        //设置蓝牙钥匙，切车后mac会被置空
        if (StringUtils.nullStrOrEmpty(mMac)) {
            setParams(BlueKeyUtil.getInstance().getKeyEntity());
            //切车后如果上一次的蓝牙连接还完完成，将其取消
            if(ConnectUtil.getInstance().getConnectStatus() == ConnectStatusEnum.CONNECTING.getStatus()){
                Log.e(TAG, "进入蓝牙连接入口方法 startConnect() " + getConnectLog() + ",mMac="+mMac + ", CancelConnect车辆发生了变化取消上一次的连接");
                //停止蓝牙扫描
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    ScanUtil.getInstance().stopScan();
                }
                //取消上一次的连接
                ConnectUtil.getInstance().disconnect(false);
            }
        }

        if(ConnectUtil.getInstance().getConnectStatus() != ConnectStatusEnum.NO_CONNECT.getStatus()){
            Log.e(TAG, "进入蓝牙连接入口方法 startConnect() " + getConnectLog() + ",mMac="+mMac + " connecting当前正在连接蓝牙，跳过此次连接");
            return;
        }

        //蓝牙权限检测
        boolean havePermissions = PermissionsUtil.getInstance().checkPermissions(mActivity, isHandleConnect);
        if(!havePermissions){
            //无蓝牙相关权限
            Log.e(TAG, "蓝牙权限阶段 startConnect(), 无蓝牙相关权限(No Permissions)" + getConnectLog() + ",mMac="+mMac + INTERRUPT);
            return;
        }

        if(TextUtils.isEmpty(mMac) || "00:00:00:00:00:00".equals(mMac)){
            Log.e(ConnectHelper.TAG, "蓝牙连接阶段 startConnect() 蓝牙钥匙异常，连接最终失败 mMac=" + mMac + ConnectHelper.INTERRUPT);
            onBleFailCallback(ConnectFailEnum.BLUE_KEY_ERROR.getDesc(), ConnectFailEnum.BLUE_KEY_ERROR.getPointDesc());
            return;
        }

        if (mConnectStatusListener != null) {
            Log.e(ConnectHelper.TAG, "蓝牙权限阶段 startConnect(), mConnectStatusListener.startConnect()" + getConnectLog());
            //蓝牙连接状态分发
            mConnectStatusListener.startConnect();
        }else {
            Log.e(ConnectHelper.TAG, "蓝牙权限阶段 startConnect(), mConnectStatusListener == null");
        }
//            ConnectUtil.getInstance().setConnectStatus(ConnectStatusEnum.CONNECTING.getStatus());

        mScanResult = ScanResultEnum.NO_COMPLETE.getCode();
        //已拥有蓝牙所有权限，开始扫描蓝牙，在mScanListener中监听扫描结果
        ScanUtil.getInstance().startScan(mActivity, mMac, mScanListener);
        //开始连接蓝牙
        ConnectUtil.getInstance().connectGatt(mActivity, mMac);
    }

    /**
     * 蓝牙连接成功回调
     */
    public void onBleSuccessCallback(){
        if (isHandleConnect && mActivity != null && !mActivity.isFinishing() && !mActivity.isDestroyed()) {
            ToastUtil.showMessage(mActivity, "蓝牙连接成功");
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            //停止蓝牙扫描
            ScanUtil.getInstance().stopScan();
        }
        ConnectUtil.getInstance().getHandler().removeCallbacks(mRunnableAutomatic);
        if (mConnectStatusListener != null) {
            //蓝牙连接成功状态分发
            mConnectStatusListener.onConnectSuccess(null, null);
        }else {
            Log.e(ConnectHelper.TAG, "onBleSuccessCallback(), mConnectStatusListener == null");
        }
    }

    /**
     * 蓝牙连接失败回调
     * @param desc      失败原因
     * @param pointDesc 蓝牙连接错误埋点描述
     */
    public void onBleFailCallback(String desc, String pointDesc) {
        //确认是没有蓝牙信号，改一下文案
        if(mScanResult == ScanResultEnum.NO_BLE.getCode()){
            desc = ScanResultEnum.NO_BLE.getDesc();
            pointDesc = ScanResultEnum.NO_BLE.getDesc();
        }

        Log.e(ConnectHelper.TAG, "蓝牙连接最终失败 onBleFailCallback(), 失败原因(" + pointDesc +
                "), desc = " + desc + getConnectLog() + " mMac = " + mMac + ", mScanResult = " + mScanResult);

        if (mConnectStatusListener != null) {
            //蓝牙连接失败状态分发
            mConnectStatusListener.onConnectFail(desc, pointDesc);
        }else {
            Log.e(ConnectHelper.TAG, "蓝牙连接失败 BleHelper onConnectFail(), mConnectStatusListener == null");
        }
//        //蓝牙连接失败后进行蓝牙扫描以便确认是否有蓝牙信号，在mScanListener中监听扫描结果
//        ScanUtil.getInstance().startScan(mActivity, mMac, mScanListener);

        //如果是自动连接，并且连接失败了，2秒后会继续进行自动连接
        if(!isHandleConnect){
            ConnectUtil.getInstance().getHandler().postDelayed(mRunnableAutomatic, 2000);
        }
    }

    /**
     * 自动连接计时器，如果是自动连接，并且连接失败了，2秒后会继续进行自动连接
     */
    private Runnable mRunnableAutomatic = new Runnable() {
        @Override
        public void run() {
            Log.e(ConnectHelper.TAG, "开始进行自动连接 mRunnableAutomatic(), " + getConnectLog() + "mMac = " + mMac);
            if(!isHandleConnect){
                startConnect(false);
            }
        }
    };

    /**
     * 发送蓝牙指令
     * @param blueControl 对应的车控指令
     */
    public void startConnect(int blueControl) {
        //蓝牙连接着可以直接发指令
        if (ConnectUtil.getInstance().isConnectBleWithAuth()) {
            //开始发送蓝牙指令
            SendControlUtil.getInstance().startSendControl(blueControl);
        }else {
            Log.e(ConnectHelper.TAG, "发送蓝牙控制指令失败，准备先连接蓝牙");
            startConnect();
        }
    }

    /**
     * 蓝牙扫描回调监听
     */
    private BleScanListener mScanListener = new BleScanListener() {

        /**
         * 蓝牙扫描成功，即扫描到当前的蓝牙
         * @param mac   要扫描的mac
         */
        public void onSuccess(String mac) {
            Log.e(TAG, "蓝牙扫描阶段，蓝牙扫描成功 mScanListener onSuccess() mac=" + mac);
            if(!TextUtils.isEmpty(mac) && mac.equals(mMac)){
                //扫描到当前蓝牙
                mScanResult = ScanResultEnum.HAVE_BLE.getCode();
            }
//            ConnectUtil.getInstance().connectGatt(mActivity, mMac);
        }

        /**
         * 蓝牙扫描失败，即扫描不到当前的蓝牙
         * @param mac   要扫描的mac
         */
        public void onFailure(String mac) {
            Log.e(TAG, "蓝牙扫描阶段，蓝牙最终扫描失败 mScanListener onFailure() mac=" + mac);
            if(!TextUtils.isEmpty(mac) && mac.equals(mMac)){
                //未扫描到蓝牙
                mScanResult = ScanResultEnum.NO_BLE.getCode();
            }
//            onFailListener(ConnectFailEnum.NO_SIGNAL.getDesc());
//            ConnectUtil.getInstance().setConnectStatus(ConnectStatusEnum.NO_CONNECT.getStatus());
        }

    };

    /**
     * 设置当前的Activity，权限申请相关会用到
     */
    public void setActivity(Activity activity) {
        mActivity = activity;
        ControlDataUtil.getInstance().setContext(activity);
    }

    /**
     * 设置当前车辆的mac
     * @param mac 当前车辆的mac
     */
    public void setMac(String mac) {
        mMac = mac;
        Log.e(TAG, "设置当前车辆的mac setMac() mMac=" + mMac);
    }

    /**
     * 设置蓝牙连接状态监听
     * @param listener 蓝牙连接状态listener
     */
    public void setConnectStatusListener(ConnectStatusListener listener) {
        Log.e(TAG, "setConnectStatusListener() mMac=" + mMac);
        mConnectStatusListener = listener;
        ConnectUtil.getInstance().setConnectStatusListener(listener);
    }

    /**
     * BLE是否已经连接并已鉴权
     * 新增对 Randomdata1 和 Randomdata2 的检查
     *
     * @return
     */
    public boolean isConnectBleWithAuth() {
        return ConnectUtil.getInstance().isConnectBleWithAuth();
    }

    /**
     * 添加车控监听
     * @param listener
     */
    public void addNotifyListener(NotifyListener listener) {
        Log.e(TAG, "addNotifyListener() mMac=" + mMac);
        ConnectUtil.getInstance().addNotifyListener(listener);
    }

    /**
     * 移除车控的监听
     */
    public void removeNotifyListener(NotifyListener listener) {
        ConnectUtil.getInstance().removeNotifyListener(listener);
    }

    /**
     * 获取当前蓝牙的连接状态
     * @return  0:未连接 1:已连接 2：连接中 3：鉴权中(内部直接把鉴权中转换成连接中状态了，所以外层不需要判断鉴权中这个状态了)
     */
    public int getConnectStatus() {
        return ConnectUtil.getInstance().getConnectStatus();
    }

    /**
     * 判断蓝牙是否连接
     * @return  true表示已连接
     */
    public boolean isConnectBle() {
        return ConnectUtil.getInstance().isConnectBle();
    }


    /**
     * 设置蓝牙连接的蓝牙钥匙
     * @param keyEntity 蓝牙钥匙
     */
    public void setParams(KeyEntity keyEntity) {
        setMac(keyEntity.getMac());
        GattUtil.getInstance().setParams(keyEntity);
        ConnectUtil.getInstance().setBleKey(keyEntity.getBleKey());
//        masterKey = keyEntity.getMasterKey();
//        masterKeyRandom = keyEntity.getMasterKeyRandom();
//        bleKey = keyEntity.getBleKey();
//        Log.e(TAG, "设置蓝牙连接的蓝牙钥匙 setParams() mMac=" + mMac + ",masterKey="+masterKey);
    }

    /**
     * 解密
     * @return 随机字节
     */
    public String DecodeControl(byte[] data) {
        return ConnectUtil.getInstance().DecodeControl(data);
    }

    /**
     * 设置开启鉴权通知监听
     * @param listener
     */
    public void setNotifyListener(NotifyListener listener) {
        Log.e(TAG, "setNotifyListener() mMac=" + mMac);
        ConnectUtil.getInstance().setNotifyListener(listener);
    }

    /**
     * 设置开启控制指令通知监听
     * @param listener
     */
    public void setControlNotifyListener(NotifyListener listener) {
        Log.e(TAG, "setControlNotifyListener() mMac=" + mMac);
        ConnectUtil.getInstance().setControlNotifyListener(listener);
    }

    /**
     * 手动断开蓝牙
     */
    public void disconnectHandle() {
        isHandleConnect = true;
        ConnectUtil.getInstance().disconnect(true);
    }

    /**
     * APP主动断开蓝牙
     */
    public void disconnect() {
        ConnectUtil.getInstance().disconnect(false);
    }

    /**
     * 手机系统蓝牙开关
     * @param bluetoothClosed   true表示关闭蓝牙
     */
    public void bluetoothClosed(boolean bluetoothClosed) {
        Log.e(TAG, "外部接听到手机系统蓝牙开关 bluetoothClosed() mMac=" + mMac + ", bluetoothClosed = " + bluetoothClosed);
        if(bluetoothClosed){
            ConnectUtil.getInstance().disconnect(true);
        }else {
            startConnect(false);
        }
    }

    /**
     * 停止蓝牙连接超时
     */
    public void stopConnectTimeOutCount() {
        Log.e(TAG, "外部停止蓝牙连接超时 stopConnectTimeOutCount() mMac=" + mMac);
        disconnect();
    }

    public void disconnectDevices() {
        Log.e(TAG, "外部断开连接 disconnectDevices() mMac=" + mMac);
        disconnect();
    }

    public void disconnectDevice() {
        Log.e(TAG, "外部断开连接 disconnectDevice() mMac=" + mMac);
        disconnect();
    }


    /**
     * 定位权限返回结果
     */
    public void onLocationPermissionPassed(boolean isGranted) {
        Log.e(TAG, "外部调用定位权限返回结果 onLocationPermissionPassed(), isGranted = " + isGranted + ", mMac=" + mMac + getConnectLog()) ;
        //如果定位通过并且手机手动连接的，则重新开始连接蓝牙
        if (isGranted && isHandleConnect) {
            startConnect();
        }
    }

    public void onRequestAndroid12BlePermissionPassed(Fragment fragment, boolean isGranted) {
        Log.e(TAG, "外部调用了 onRequestAndroid12BlePermissionPassed(), isGranted = " + isGranted + ", mMac=" + mMac + getConnectLog()) ;
        PermissionsUtil.getInstance().onRequestAndroid12BlePermissionPassed(fragment, isGranted);
    }

    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        Log.e(TAG, "外部调用 onActivityResult(), requestCode = " + requestCode + ", resultCode = " + resultCode +
                ", mMac=" + mMac + getConnectLog());
        //请求打开蓝牙权限返回
        if (requestCode == TravelFragment.ACTIVITY_REQUEST_BLUETOOTH) {
            if (resultCode == RESULT_OK) {
                Log.e(TAG, "外部调用 onActivityResult(), requestCode = " + requestCode + ", resultCode = " + resultCode +
                        ", mMac=" + mMac + getConnectLog() + ", startConnect()");
                startConnect();
            } else {
                if (mActivity != null){
                    ToastUtil.showMessage(mActivity, R.string.car_control_need_open_bluetooth);
                }
            }
        }
    }

    /**
     * 获取当前手动或自动连接日志文案
     * @return
     */
    public String getConnectLog(){
        return isHandleConnect ? " 当前是手动连接 isHandleConnect = true" : " 当前是自动连接 isHandleConnect = false";
    }

    public void setParseDataResponseListener(ParseDataResponseListener listener) {
        ControlDataUtil.getInstance().setParseDataResponseListener(listener);
    }

    public void setBleDataResponseListener(BleDataResponseListener listener) {
        ControlDataUtil.getInstance().setBleDataResponseListener(listener);
    }

    public BleDataResponseListener getBleDataResponseListener() {
        return ControlDataUtil.getInstance().getBleDataResponseListener();
    }

    public void setControlWriteListener(ServiceWriteListener listener) {
        ConnectUtil.getInstance().setControlWriteListener(listener);
    }

    public void setServiceWriteListener(ServiceWriteListener listener) {
        ConnectUtil.getInstance().setServiceWriteListener(listener);
    }

    /**
     * 这个方法不用了，为了不改动太多外层代码所以留着这个空方法
     * @param scanListener
     */
    public void setScanListener(ScanListener scanListener) {
//        this.scanListener = scanListener;
    }

    public String getRandomKey() {
        return ConnectUtil.getInstance().getRandomKey();
    }

    public void setParamsSuccess() {
        ControlDataUtil.getInstance().setParamsSuccess();
    }

    /**
     * 是否是用户手动连接蓝牙，true表示手动连接，手动连接就显示那些loading和权限提示
     * @return
     */
    public boolean isHandleConnect(){
        return isHandleConnect;
    }

    public void setConnectStatus(int connectStatus) {
        Log.e(TAG, "外部重置了蓝牙状态 setConnectStatus() connectStatus = " + connectStatus + ", mMac=" + mMac);
        if(connectStatus == ConnectStatusEnum.NO_CONNECT.getStatus()){
            disconnect();
        }
    }

    /**
     * 获取蓝牙自动连接开关状态
     * @return  true表示自动连接是开启着的
     */
    public boolean canAutoConnectBle(){
        boolean canAutoConnectBle = SPUtils.getInstance().getBoolean(AppConstants.E300_AUTO_CONNECT_BLE, false);
        Log.e(TAG, "获取蓝牙自动连接开关状态 canAutoConnectBle = " + canAutoConnectBle + ", mMac=" + mMac);
        return canAutoConnectBle;
    }

    public interface ParseDataResponseListener {
        void responseParseData(String data);
    }

    public interface BleDataResponseListener {

        /**
         * 返回蓝牙控制成功
         *
         * @param msg
         */
        void responseControlSuccess(String msg);

        /**
         * 返回蓝牙控制失败
         *
         * @param msg
         */
        void responseControlError(String msg);

        /**
         * 返回自动泊车状态
         *
         * @param state
         */
        void responseParkingState(byte state);

        /**
         * 返回自动泊车错误
         *
         * @param msg
         */
        void responseParkingError(String msg);

        /**
         * 车辆下电成功
         */
        void onPowerOffSuccess();

        void responseParkingFailed();

        void responseParkingFailed(byte state);

        void responseE260SPParkingFailed(String msg);

        void responseParkingSuccess();

        void responsePowerOffFailed(String message);

        void setParamsSuccess();
    }

    public static final int UNLOCK_DOOR = 0;
    public static final int POWER_OFF = 1;
    public static final int OPEN_TAILGATE = 2;
    public static final int MODE_PARK_IN = 4;
    public static final int START_PARK = 5;
    public static final int PAUSE_PARK = 6;
    public static final int STOP_PARK = 7;
    public static final int LOCK_DOOR = 8;
    public static final int MODE_PARK_OUT = 9;
    public static final int PARK_MODE_LEFT_H = 11;
    public static final int PARK_MODE_RIGHT_H = 13;
    public static final int PARK_MODE_FORWARD = 14;
    public static final int MODE_PARK_MANUAL = 15;
    public static final int PARK_MODE_BACKWARD = 16;
    public static final int PARK_MODE_FORWARD_V = 17;
    public static final int PARK_MODE_LEFT_V = 10;
    public static final int PARK_MODE_RIGHT_V = 12;
    public static final int PARK_MODE_FORWARD_H = 18;
    public static final int PARK_MODE_LEFTWARD = 19;
    public static final int PARK_MODE_RIGHTWARD = 20;
    public static final int PARK_MODE_UP_LEFT = 21;
    public static final int PARK_MODE_UP_RIGHT = 22;
    public static final int PARK_MODE_DOWN_LEFT = 23;
    public static final int PARK_MODE_DOWN_RIGHT = 24;
    public static final int PARK_MODE_RETURN = 25;
    public static final int ONE_KEY_OUT = 26;
    public static final int START_PARK_OUT = 27;
    public static final int PAUSE_PARK_OUT = 28;
    public static final int STOP_PARK_OUT = 29;
    public static final int START_PARK_IN = 30;
    public static final int PAUSE_PARK_IN = 31;
    public static final int STOP_PARK_IN = 32;
    public static final int SET_OUT_FRONT = 33;
    public static final int SET_OUT_AFTER = 34;
    public static final int SET_OUT_LEFT = 35;
    public static final int SET_OUT_RIGHT = 36;
    public static final int STRAIGHT_FORWARD = 37;
    public static final int STRAIGHT_BACK_2 = 38;
    /**
     * 泊车状态
     */
    public static String BLE_PARKING_STATE = "30";
    public static String BLE_PARKING_OUT_STATE = "31";
    public static String BLE_PARKING_STATE2 = "32";
    public static String BLE_PARKING_ERROR_STATE = "20";
    // 蓝牙通讯失败
    public static String BLE_FAIL = "FFFF";
    // 蓝牙控制成功
    public static String BLE_CONTROL_SUCCESS = "A956";

}