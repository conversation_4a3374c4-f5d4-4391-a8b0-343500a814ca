package com.cloudy.linglingbang.activity.car.home;

import android.os.Bundle;
import android.util.Log;

import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshFragment;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.fragment.mynew.util.SelfUserInfoLoader;
import com.cloudy.linglingbang.app.receiver.UserInfoChangeReceiver;
import com.cloudy.linglingbang.app.receiver.UserInfoChangedHelper;
import com.cloudy.linglingbang.app.util.LocationHelper;
import com.cloudy.linglingbang.app.util.UserStatusChangeManager;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.banner.BannerView;
import com.cloudy.linglingbang.constants.FinalSensors;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.user.User;

import java.util.List;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 爱车页面
 *
 * <AUTHOR>
 * @date 2020-02-17
 */
public class MyCarFragment extends BaseRecyclerViewRefreshFragment<Object> {

    MyCarAdapter mMyCarAdapter;

    private MyCarLoader mMyCarLoader;

    private LocationHelper mLocationHelper;

    //上一次是否是车主
    private boolean mLastIsCarOwner;

    //用户状态广播接收管理类
    private UserStatusChangeManager mUserStatusChangeManager;
    /**
     * 我的监听
     */
    private UserInfoChangedHelper mUserInfoChangedHelper;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mUserStatusChangeManager = new UserStatusChangeManager(getContext(), new UserStatusChangeManager.SimpleUserStatusChangeReceiver() {
            @Override
            protected void onUpdateUser(User user) {
                if (mMyCarLoader != null) {
                    Log.e("111111", "接收到变化广播");
                    mMyCarLoader.resetCity();
                    //调用强制刷新，防止上一个刷新没完成，用户状态有发生了变化
                    mMyCarLoader.forceRefresh();
                }

                //如果用户身份和上次一样，则不用刷新
//                if (mLastIsCarOwner != UserUtils.isRetentiveCustomer()) {
//                    mLastIsCarOwner = UserUtils.isRetentiveCustomer();
//                    //调用刷新
//                    getRefreshController().manualRefresh();
//                }
            }

        });
        //各个状态都变化
        mUserInfoChangedHelper = new UserInfoChangedHelper(new UserInfoChangeReceiver() {

            @Override
            protected void onUpdateUser() {
                super.onUpdateUser();
                //如果用户身份和上次一样，则不用刷新
                if (mLastIsCarOwner != UserUtils.isRetentiveCustomer()) {
                    mLastIsCarOwner = UserUtils.isRetentiveCustomer();
                    if (mMyCarLoader != null) {
                        mMyCarLoader.resetCity();
                    }
                    //调用刷新
                    getRefreshController().manualRefresh();
                }
            }

            @Override
            protected void onUpdateBaseInfo() {
                super.onUpdateBaseInfo();
                //如果用户身份和上次一样，则不用刷新
                if (mLastIsCarOwner != UserUtils.isRetentiveCustomer()) {
                    mLastIsCarOwner = UserUtils.isRetentiveCustomer();
                    if (mMyCarLoader != null) {
                        mMyCarLoader.resetCity();
                    }
                    //调用刷新
                    getRefreshController().manualRefresh();
                }
            }

            @Override
            protected void onUpdateControlCar() {
                super.onUpdateControlCar();
                //爱车信息发生变化时，请求用户信息，因为用户身份可能改变
                SelfUserInfoLoader.getInstance().getUserBaseInfo(getActivity());
                if (mMyCarLoader != null) {
                    mMyCarLoader.onUpdateShowCar();
                }

            }
        });
        mUserInfoChangedHelper.register(getContext());
        mUserStatusChangeManager.register();
    }



    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<Object> list) {
        if (mMyCarAdapter == null) {
            mMyCarAdapter = new MyCarAdapter(getContext(), list, mMyCarLoader, this);

        }
//        headerAndFooterWrapperAdapter = new HeaderAndFooterWrapperAdapter(adapter);
        return mMyCarAdapter;
    }

    @Override
    public Observable<BaseResponse<List<Object>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return null;
    }

    @Override
    public RefreshController<Object> createRefreshController() {
        if (mMyCarLoader == null) {
            mMyCarLoader = new MyCarLoader(this);
            mMyCarLoader.setFragment(this);
        }
        return mMyCarLoader;
    }

    /**
     * 定位
     */
    public void goLocation() {
        mLocationHelper = LocationHelper.getInstance();
        mLocationHelper.requestLocation(this, new LocationHelper.LocCallBack() {
            @Override
            public void onSuccess(LocationHelper.LocationEntity entity) {
                //定位成功,传递给loader处理
                if (mMyCarLoader != null) {
                    mMyCarLoader.onUpdateLocation();
                }
            }

            @Override
            public void onError(String errMsg) {
                mMyCarLoader.onUpdateLocation();
            }
        });
    }

    @Override
    protected void onPermissionResult(boolean isGranted, int requestCode) {
        super.onPermissionResult(isGranted, requestCode);
        if (requestCode == LocationHelper.PERMISSION_REQUEST_CODE_LOCATION) {
            mLocationHelper.onPermissionResult(isGranted, requestCode);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        BannerView bannerView = getBannerView();
        if (bannerView != null) {
            bannerView.onPause();
        }
        OilPriceLoader oilPriceLoader = getOilPriceLoader();
        if (oilPriceLoader != null) {
            oilPriceLoader.stopScroll();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        BannerView bannerView = getBannerView();
        if (bannerView != null) {
            bannerView.onResume();
        }
        OilPriceLoader oilPriceLoader = getOilPriceLoader();
        if (oilPriceLoader != null) {
            oilPriceLoader.startScroll();
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        SensorsUtils.sensorsViewStart(FinalSensors.MY_CAR_PAGE);
    }

    @Override
    public void onStop() {
        super.onStop();
        if (this.isVisible()) {
            SensorsUtils.sensorsViewEndNew(FinalSensors.MY_CAR_PAGE_NAME, FinalSensors.MY_CAR_PAGE);
        }
    }


    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        //新的页面浏览的埋点
        if (hidden) {
            SensorsUtils.sensorsViewEndNew(FinalSensors.MY_CAR_PAGE_NAME, FinalSensors.MY_CAR_PAGE);
        } else {
            SensorsUtils.sensorsViewStart(FinalSensors.MY_CAR_PAGE);
        }
        BannerView bannerView = getBannerView();
        if (bannerView != null) {
            bannerView.onHiddenChanged(hidden);
        }
        OilPriceLoader oilPriceLoader = getOilPriceLoader();
        if (oilPriceLoader != null) {
            if (hidden) {
                oilPriceLoader.stopScroll();
            } else {
                oilPriceLoader.startScroll();
            }
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        BannerView bannerView = getBannerView();
        if (bannerView != null) {
            bannerView.setUserVisibleHint(isVisibleToUser);
        }
        OilPriceLoader oilPriceLoader = getOilPriceLoader();
        if (oilPriceLoader != null) {
            if (isVisibleToUser) {
                oilPriceLoader.startScroll();
            } else {
                oilPriceLoader.stopScroll();
            }
        }
    }

    public BannerView getBannerView() {
        if (mMyCarAdapter != null) {
            return mMyCarAdapter.getBannerView();
        }
        return null;
    }

    public OilPriceLoader getOilPriceLoader() {
        if (mMyCarAdapter != null) {
            return mMyCarAdapter.getOilPriceLoader();
        }
        return null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mUserInfoChangedHelper != null) {
            mUserInfoChangedHelper.unregister(getContext());
        }
        if (mUserStatusChangeManager != null) {
            mUserStatusChangeManager.unregister();
        }
    }

    //    }
}
