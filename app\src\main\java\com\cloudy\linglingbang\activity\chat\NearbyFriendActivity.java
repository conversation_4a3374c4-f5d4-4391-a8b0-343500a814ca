package com.cloudy.linglingbang.activity.chat;

import android.Manifest;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.LocationHelper;
import com.cloudy.linglingbang.app.util.PermissionUtils;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.widget.UserHeadView;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.user.User;

import java.util.List;

import androidx.recyclerview.widget.RecyclerView;
import butterknife.ButterKnife;
import io.reactivex.rxjava3.core.Observable;

/**
 * 附近的人
 */
public class NearbyFriendActivity extends BaseRecyclerViewRefreshActivity<User> {
    private double longitude;
    private double latitude;
    private LocationHelper mLocationHelper;

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<User> list) {
        return new NearbyFriendAdapter(this, list);
    }

    @Override
    public Observable<BaseResponse<List<User>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {

        if (longitude == 0 || latitude == 0) {
            //如果没有权限，则先将刷新关闭，防止点击第一个对话框取消之后刷新一直存在
            if (!PermissionUtils.checkPermission(this, Manifest.permission.ACCESS_COARSE_LOCATION)) {
                getRefreshController().getSwipeToLoadLayout().setRefreshing(false);
            }
            getLocationRefreshData();//如果是刚进入,则获取经纬度
            return null;
        }
        return service2.getNearByUsers(pageNo, pageSize, String.valueOf(longitude), String.valueOf(latitude));
    }

    /**
     * 获取经纬度,之后再刷新一次数据
     */
    private void getLocationRefreshData() {
        mLocationHelper = LocationHelper.getInstance();
        mLocationHelper.requestLocation(this, new LocationHelper.LocCallBack() {
            @Override
            public void onSuccess(LocationHelper.LocationEntity entity) {
                longitude = entity.longitude;
                latitude = entity.latitude;
                getRefreshController().getListData(1);//获取经纬度之后再次拉取数据
            }

            @Override
            public void onError(String errMsg) {
                ToastUtil.showMessage(NearbyFriendActivity.this, getString(R.string.near_friend_location_fail));
            }
        });
    }

    private class NearbyFriendAdapter extends BaseRecyclerViewAdapter<User> {
        public NearbyFriendAdapter(Context context, List<User> data) {
            super(context, data);
        }

        @Override
        protected BaseRecyclerViewHolder<User> createViewHolder(View itemView) {
            return new NearbyFriendViewHolder(itemView);
        }

        @Override
        protected BaseRecyclerViewHolder.OnItemClickListener getDefaultOnItemClickListener() {
            return new BaseRecyclerViewHolder.OnItemClickListener() {
                @Override
                public void onItemClick(View itemView, int position) {
                    User user = mData.get(position);
                    JumpPageUtil.goToPersonPage(mContext, user);
                }
            };
        }

        @Override
        protected int getItemLayoutRes(int viewType) {
            return R.layout.item_nearby_friend;
        }

        private class NearbyFriendViewHolder extends BaseRecyclerViewHolder<User> {
            UserHeadView user_head_view;
            TextView tv_name;
            Button btn_grade;
            TextView tv_distance;

            public NearbyFriendViewHolder(View itemView) {
                super(itemView);
            }

            @Override
            protected void initItemView(View itemView) {
                super.initItemView(itemView);
                user_head_view = itemView.findViewById(R.id.user_head_view);
                tv_name = (TextView) itemView.findViewById(R.id.tv_name);
                btn_grade = (Button) itemView.findViewById(R.id.btn_grade);
                tv_distance = (TextView) itemView.findViewById(R.id.tv_distance);
                ButterKnife.bind(this, itemView);
            }

            @Override
            public void bindTo(User user, int position) {
                super.bindTo(user, position);
                user_head_view.setUser(user);
                tv_name.setText(user.getNickname());
                tv_distance.setText(getDistanceString(user.getDistance()));
                String rank = user.getRankName();
                if (TextUtils.isEmpty(rank)) {
                    btn_grade.setVisibility(View.GONE);
                } else {
                    btn_grade.setVisibility(View.VISIBLE);
                    btn_grade.setText(rank);
                }
            }

            private String getDistanceString(double distance) {
                if (distance < 100) {
                    return "100米以内";
                } else if (distance < 300) {
                    return "300米以内";
                } else if (distance < 500) {
                    return "500米以内";
                } else if (distance < 1000) {
                    return "1000米以内";
                } else {
                    return AppUtil.convertDistance(distance);
                }
            }
        }
    }

    @Override
    protected void onPermissionResult(boolean isGranted, int requestCode) {
        super.onPermissionResult(isGranted, requestCode);
        if (requestCode == LocationHelper.PERMISSION_REQUEST_CODE_LOCATION) {
            if (mLocationHelper != null) {
                mLocationHelper.onPermissionResult(isGranted, requestCode);
            }
        }
    }
}
