package com.cloudy.linglingbang.activity.store.commodity.adapter.holder

import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.store.CommodityMedalAdapter
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder
import com.cloudy.linglingbang.model.store.ecology.CommodityMedal

/**
 * 帖子详情-勋章
 *
 * <AUTHOR>
 * @date 2022/9/26
 */
class MedalViewHolder(itemView: View?) : BaseCommodityHolder<Any>(itemView) {
    var recyclerView: RecyclerView? = null
    var tv_des: TextView? = null
    override fun initItemView(itemView: View?) {
        super.initItemView(itemView)
        recyclerView = itemView?.findViewById(R.id.recyclerView)
        tv_des = itemView?.findViewById(R.id.tv_des)
    }

    override fun bindTo(bean: Any?, position: Int) {
        super.bindTo(bean, position)
        mCenterCommodity?.apply {
            lmCommodityAsLimit?.apply {
                if (limitMedalRelation == 0) {
                    tv_des?.setText(R.string.tv_medal_condition_commodity_0)
                } else {
                    tv_des?.setText(R.string.tv_medal_condition_commodity_1)
                }
            }
        }
        recyclerView?.adapter =
            object : CommodityMedalAdapter(itemView.context, mCenterCommodity?.commodityMedalList) {
                override fun onItemClick(itemView: View?, position: Int) {
                    super.onItemClick(itemView, position)
                    val eventName =
                        "点击(" + (mCenterCommodity?.commodityId
                            ?: 0L) + ")(" + (mCenterCommodity?.commodityName ?: "") + ")徽章"
                    val type =
                        if (mCenterCommodity?.commodityClassifyId == 0) "(整车)" else "(优品)"
                    SensorsUtils.sensorsClickBtn(eventName, "商品详情页$type", "徽章按钮")
                }

                override fun onBindViewHolder(
                    holder: BaseRecyclerViewHolder<CommodityMedal>,
                    position: Int
                ) {
                    holder.itemView.findViewById<View>(R.id.iv_medal)?.apply {
                        var p = layoutParams
                        if (p == null) {
                            p = ViewGroup.LayoutParams(-1, -1)
                        }
                        if (p is ViewGroup.MarginLayoutParams) {
                            p.rightMargin =
                                holder.itemView.resources.getDimensionPixelOffset(R.dimen.normal_20)
                            p.marginEnd = p.rightMargin
                        }
                        p.height =
                            holder.itemView.resources.getDimensionPixelOffset(R.dimen.normal_56)
                        p.width = p.height
                        layoutParams = p
                    }
                    super.onBindViewHolder(holder, position)
                }
            }
    }

}