package com.cloudy.linglingbang.activity.basic;

import com.cloudy.linglingbang.R;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

/**
 * 单个 Fragment 的 Activity ，可用于复用 Fragment
 *
 * <AUTHOR>
 * @date 2018/8/16
 */
public abstract class SingleFragmentActivity extends BaseActivity {
    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_single_fragment);
    }

    @Override
    public void initialize() {
        FragmentManager fm = getSupportFragmentManager();
        Fragment fragment = fm.findFragmentById(R.id.fl_container);
        if (fragment == null) {
            fragment = createFragment();
            fm.beginTransaction().add(R.id.fl_container, fragment).commit();
        }
    }

    protected abstract Fragment createFragment();
}
