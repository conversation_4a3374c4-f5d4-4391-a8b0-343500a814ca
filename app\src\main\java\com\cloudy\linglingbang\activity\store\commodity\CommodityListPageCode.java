package com.cloudy.linglingbang.activity.store.commodity;

/**
 * 商品列表pageCode
 *
 * <AUTHOR>
 * @date 2022/11/18
 */
public final class CommodityListPageCode {
    /**
     * 官方专区
     */
    public static final String PAGE_CODE_OFFICIAL = "1";
    /**
     * 组件下更多好物
     */
    public static final String PAGE_CODE_MORE_GOODS = "2";
    /**
     * 尊享权益列表
     */
    public static final String PAGE_CODE_VIP_RIGHT = "3";
    /**
     * 选配件
     */
    public static final String PAGE_CODE_SELECT_PARTS = "4";

    /**
     * 首页搜素-整车
     */
    public static final String PAGE_CODE_HOME_SEARCH_CAR = "5";
    /**
     * 首页搜素-商品
     */
    public static final String PAGE_CODE_HOME_SEARCH_COMMODITIES = "6";
    /**
     * 好物商品搜索
     */
    public static final String PAGE_CODE_STORE_SEARCH_COMMODITIES = "7";
    /**
     * 聊天中发送商品
     */
    public static final String PAGE_CODE_IM_SERVER_COMMODITIES = "8";
}
