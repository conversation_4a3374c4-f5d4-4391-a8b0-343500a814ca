package com.cloudy.linglingbang.activity.basic;

import android.content.Intent;
import android.view.View;
import android.widget.RadioGroup;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.task.util.TaskNavigateUtils;

import androidx.viewpager.widget.ViewPager;

/**
 * 包含viewpager的fragment
 * <br/>使用方法，继承后实现要实现的方法
 *
 * <AUTHOR> create at 2016/10/5 16:02
 */
public abstract class BaseViewPagerFragment<T> extends BaseFragment implements IViewPagerTabContext<T>, TaskNavigateUtils.TaskNavigable {
    private ViewPagerTabController<T> mViewPagerTabController;

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_base_view_pager;
    }

    //初始化创建Controller
    @Override
    protected void initViews() {
        super.initViews();
        mViewPagerTabController = createViewPagerTabController();
        mViewPagerTabController.initViews();
    }

    public ViewPagerTabController<T> createViewPagerTabController() {

        //RadioGroup
        RadioGroup radioGroup = (RadioGroup) mRootView.findViewById(R.id.radio_group);
        //下面的线
        View ivLine = mRootView.findViewById(R.id.iv_line);
        //viewpager
        ViewPager viewPager = (ViewPager) mRootView.findViewById(R.id.viewpager);

        return createViewPagerTabController(radioGroup, ivLine, viewPager);
    }

    public ViewPagerTabController<T> createViewPagerTabController(RadioGroup radioGroup, View viewLine, ViewPager viewPager) {
        return new ViewPagerTabController<T>(this, radioGroup, viewLine, viewPager);
    }

    public ViewPagerTabController<T> getViewPagerTabController() {
        return mViewPagerTabController;
    }

    //下面是实现相关方法
    @Override
    public int getRadioButtonResourceId(int index) {
        return 0;
    }

    @Override
    public void onPageSelected(int position) {

    }

    @Override
    public void onTabClick(int position) {

    }

    @Override
    public void checkTaskNavigate(Intent intent) {
        if (mViewPagerTabController != null) {
            TaskNavigateUtils.checkFragmentTaskSecondLevelNavigate(this, intent, mViewPagerTabController.getViewPager(), mViewPagerTabController.getData());
        }
    }
}
