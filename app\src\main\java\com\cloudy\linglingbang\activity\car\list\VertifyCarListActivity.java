package com.cloudy.linglingbang.activity.car.list;

import android.content.Context;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.model.car.home.MyCarType;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;

import java.util.List;

import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 授权车辆的列表页面
 *
 * <AUTHOR>
 * @date 2020-02-27
 */
public class VertifyCarListActivity extends BaseRecyclerViewRefreshActivity<MyCarType> {

    @Override
    protected void initialize() {
        setMiddleTitle("被授权车辆");
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<MyCarType> list) {
        return new VertifyCarListAdapter(this, list);
    }

    @Override
    public Observable<BaseResponse<List<MyCarType>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return service2.getCarList();
    }

    @Override
    public RefreshController<MyCarType> createRefreshController() {
        final RefreshController<MyCarType> refreshController = new RefreshController<MyCarType>(VertifyCarListActivity.this) {
            @Override
            public void initViews(View rootView) {
                super.initViews(rootView);
                recyclerView.setBackgroundColor(getResources().getColor(R.color.app_bg));
            }

            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return null;
            }

            //不展示刷新样式
            @Override
            protected boolean showRefreshingWhenLoadDataAfterInitViews() {
                return false;
            }

            @Override
            protected boolean isRefreshEnable() {
                return false;
            }
        };

        return refreshController;
    }

}
