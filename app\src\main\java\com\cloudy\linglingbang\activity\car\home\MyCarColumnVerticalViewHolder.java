package com.cloudy.linglingbang.activity.car.home;

import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.fragment.homePage.today.TodayModel;
import com.cloudy.linglingbang.activity.fragment.homePage.today.adapter.viewHolder.FindBasePostViewHolder;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;

/**
 * 竖直栏目详情的viewHolder
 *
 * <AUTHOR>
 * @date 2020-02-24
 */
public class MyCarColumnVerticalViewHolder extends BaseRecyclerViewHolder<TodayModel.TodayColumnPost> {

    private FindBasePostViewHolder mTodayColumnViewHolder;

    public MyCarColumnVerticalViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        mTodayColumnViewHolder = new FindBasePostViewHolder(itemView);
    }

    @Override
    public void bindTo(TodayModel.TodayColumnPost todayColumnPost, int position) {
        super.bindTo(todayColumnPost, position);
        itemView.setBackgroundResource(R.color.color_f9f9f9);
        mTodayColumnViewHolder.bindTo(todayColumnPost, position);
    }
}
