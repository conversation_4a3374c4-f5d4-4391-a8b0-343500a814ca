package com.cloudy.linglingbang.activity.community.post.shortVideo2;

import android.app.Activity;
import android.content.Context;
import android.graphics.Rect;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.live.dialog.LiveCommodityListDialog;
import com.cloudy.linglingbang.activity.vhall.vhallModel.VideoCommodityAndAd;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ViewHolderUtils;
import com.cloudy.linglingbang.model.SourceModel;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.server.Ad.Ad2;
import com.cloudy.linglingbang.model.server.Ad.AdJumpUtil2;

import java.util.Locale;

import androidx.annotation.NonNull;

/**
 * 视频列表的adapter
 *
 * <AUTHOR>
 * @date 2020/9/16
 */
public class LittleVideoListAdapter extends BaseVideoListAdapter<LittleVideoListAdapter.MyHolder, PostCard> {
    public static final String TAG = LittleVideoListAdapter.class.getSimpleName();
    private OnItemBtnClick mItemBtnClick;
    //来源
    private int mOpenFrom;

    //是否隐藏分享：0：不隐藏；1：隐藏
    private int mIsHideShare;

    public LittleVideoListAdapter(Context context) {
        super(context);
    }

    @NonNull
    @Override
    public LittleVideoListAdapter.MyHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        View inflate = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.item_view_pager, viewGroup, false);
        return new MyHolder(inflate, this);
    }

    @Override
    public void onBindViewHolder(@NonNull MyHolder myHolder, final int position) {
        super.onBindViewHolder(myHolder, position);

        myHolder.mVideoInfoView.setVideoInfo(list.get(position));
        myHolder.setProgress(0);
        myHolder.bindVideoAdAndCommodity(null, list.get(position).getPostId());

    }

    public final class MyHolder extends BaseVideoListAdapter.BaseHolder {
        private final ImageView thumb;
        public FrameLayout playerView;
        private final ViewGroup mRootView;
        private final VideoInfoView mVideoInfoView;
        private final ImageView mPlayIconImageView;
        //        private ProgressBar mProgressBottomVod;
        private final SeekBar mSeekBar;
        private final TextView mTotalDuration;
        private final TextView mPlayDuration;

        MyHolder(@NonNull View itemView, BaseVideoListAdapter adapter) {
            super(itemView);
            Log.d(TAG, "new PlayerManager");
            thumb = itemView.findViewById(R.id.img_thumb);
            playerView = itemView.findViewById(R.id.player_view);
            mRootView = itemView.findViewById(R.id.root_view);
            mVideoInfoView = itemView.findViewById(R.id.content_view);
            mPlayIconImageView = itemView.findViewById(R.id.iv_play_icon);
//            mProgressBottomVod = itemView.findViewById(R.id.progress_bottom_vod);
            mSeekBar = itemView.findViewById(R.id.seek_fm);
            mTotalDuration = itemView.findViewById(R.id.tv_totalDuration);
            mPlayDuration = itemView.findViewById(R.id.tv_playDuration);
            itemView.findViewById(R.id.ll_seek)
                    .setOnTouchListener(new View.OnTouchListener() {
                        @Override
                        public boolean onTouch(View v, MotionEvent event) {
                            Rect seekRect = new Rect();
                            mSeekBar.getHitRect(seekRect);
                            if ((event.getY() >= (seekRect.top - 500)) && (event.getY() <= (seekRect.bottom + 500))) {
                                float y = seekRect.top + seekRect.height() / 2;
                                //seekBar only accept relative x
                                float x = event.getX() - seekRect.left;
                                if (x < 0) {
                                    x = 0;
                                } else if (x > seekRect.width()) {
                                    x = seekRect.width();
                                }
                                MotionEvent me = MotionEvent.obtain(event.getDownTime(), event.getEventTime(),
                                        event.getAction(), x, y, event.getMetaState());
                                return mSeekBar.onTouchEvent(me);
                            }
                            return false;
                        }
                    });
            mVideoInfoView.setAdapter(adapter);
            mVideoInfoView.setOpenFrom(mOpenFrom);
            mVideoInfoView.setIsHideShare(mIsHideShare);
        }

        @Override
        public ImageView getCoverView() {
            return thumb;
        }

        @Override
        public ViewGroup getContainerView() {
            return mRootView;
        }

        @Override
        public ImageView getPlayIcon() {
            return mPlayIconImageView;
        }

        @Override
        public SeekBar getSeekBar() {
            return mSeekBar;
        }

        @Override
        public void setProgress(int p) {
            if (mSeekBar != null) {
                mSeekBar.setProgress(p);
            }
            mPlayDuration.setText(getTimeStr(p));
        }

        @Override
        public void setMax(int max) {
            if (mSeekBar != null) {
                mSeekBar.setMax(max);
            }
            mTotalDuration.setText(getTimeStr(max));
        }

        private String getTimeStr(long time) {
            if (time <= 0) {
                return "00:00";
            }
            long seconds = time / 1000;
            return String.format(Locale.getDefault(), "%02d:%02d", seconds / 60, seconds % 60);
        }

        @Override
        public void setSecondaryProgress(int secondaryProgress) {
            if (mSeekBar != null) {
                mSeekBar.setSecondaryProgress(secondaryProgress);
            }
        }

        @Override
        public RelativeLayout getWifiHint() {
            return mVideoInfoView.mRlWifiHint;
        }

        @Override
        public Button getBtnPlayContinue() {
            return mVideoInfoView.mBtnPlayContinue;
        }

        public void bindVideoAdAndCommodity(VideoCommodityAndAd videoCommodityAndAd, String postId) {
            ViewHolderUtils.setVisibility(false, mVideoInfoView.getIvCommodityActivity(), mVideoInfoView.getTvAdName());
            ViewHolderUtils.setOnClickListener(null, mVideoInfoView.getIvCommodityActivity(), mVideoInfoView.getTvAdName());
            if (TextUtils.isEmpty(postId) || videoCommodityAndAd == null) {
                return;
            }
            int position = getAdapterPosition();
            if (list == null || list.isEmpty() || list.size() <= position || !postId.equals(list.get(position).getPostId())) {
                return;
            }
            if (videoCommodityAndAd.getPositionInfo() != null) {
                final Ad2 ad2 = videoCommodityAndAd.getPositionInfo();
                ViewHolderUtils.setVisibility(true, mVideoInfoView.getTvAdName());
                TextView textView = mVideoInfoView.getTvAdName();
                textView.setText(ad2.getAdvertiseName());
                ViewHolderUtils.setOnClickListener(v -> {
                    AdJumpUtil2.goToActivity(itemView.getContext(), ad2, new SourceModel(SourceModel.POSITION_TYPE.POST_VIDEO, postId));
                }, textView);
            }
            if (videoCommodityAndAd.getCommoditys() != null && videoCommodityAndAd.getCommoditys().size() > 0) {
                ViewHolderUtils.setVisibility(true, mVideoInfoView.getIvCommodityActivity());
                ViewHolderUtils.setOnClickListener(v -> {
                    Activity activity = AppUtil.getActivity(itemView.getContext());
                    if (activity != null) {
                        LiveCommodityListDialog dialog = new LiveCommodityListDialog(activity, postId);
                        dialog.setOpenFromType(LiveCommodityListDialog.OPEN_FROM_VIDEO);
                        dialog.addData(videoCommodityAndAd.getCommoditys());
                        dialog.show();
                    }
                }, mVideoInfoView.getIvCommodityActivity());
            }
        }
    }

    public interface OnItemBtnClick {
        void onDownloadClick(int position);
    }

    public void setItemBtnClick(
            OnItemBtnClick mItemBtnClick) {
        this.mItemBtnClick = mItemBtnClick;
    }

    public void setOpenFrom(int openFrom) {
        mOpenFrom = openFrom;
    }

    public void setIsHideShare(int isHideShare) {
        mIsHideShare = isHideShare;
    }
}
