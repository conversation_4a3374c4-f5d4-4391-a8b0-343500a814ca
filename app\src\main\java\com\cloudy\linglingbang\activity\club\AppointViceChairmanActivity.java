package com.cloudy.linglingbang.activity.club;

import android.app.Dialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.view.View;
import android.widget.RelativeLayout;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.adapter.club.AppointMemberListAdapter;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog;
import com.cloudy.linglingbang.app.widget.recycler.NestedLinearLayoutManager;
import com.cloudy.linglingbang.model.club.ChannelMember;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.user.User;

import java.util.ArrayList;
import java.util.List;

import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import butterknife.OnClick;

import static com.cloudy.linglingbang.R.id.rl_add_vice_chairman;
import static com.cloudy.linglingbang.activity.club.AppointMemberListActivity.REFRESH_LIST;

/**
 * 任命副会长页面
 *
 * <AUTHOR>
 * @date 2017/11/22
 */
public class AppointViceChairmanActivity extends BaseActivity implements AppointMemberListAdapter.OnListener {
    /**
     * 列表
     */
    @BindView(R.id.rv_member_list)
    RecyclerView mRecyclerView;

    /**
     * 任命副会长按钮
     */
    @BindView(rl_add_vice_chairman)
    RelativeLayout mRlAddViceChairman;

    private Long mChannelId;
    private List<User> mData = new ArrayList<>();
    private AppointMemberListAdapter mAdapter;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_appoint_vice_chairman);
    }

    @Override
    protected void initialize() {
        setMiddleTitle(getResources().getString(R.string.club_appoint_vice_chairman));
        mChannelId = (Long) getIntentExtra(0L);
        if (mChannelId == null || mChannelId == 0L) {
            return;
        }
        mAdapter = new AppointMemberListAdapter(this, mData, 1);
        mAdapter.setCallBackListener(this);
        mRecyclerView.setAdapter(mAdapter);
        mRecyclerView.setLayoutManager(new NestedLinearLayoutManager(this));
        requestData();

    }

    /**
     * 请求服务器数据，刷新列表
     */
    private void requestData() {
        ChannelMember channelMember = new ChannelMember();
        channelMember.setChannelId(mChannelId);
        channelMember.setMemberLevel(1);//查询所有副会长
        L00bangRequestManager2.getServiceInstance()
                .getClubMembers(channelMember)
                .compose(L00bangRequestManager2.<List<User>>setSchedulers())
                .subscribe(new ProgressSubscriber<List<User>>(AppointViceChairmanActivity.this) {
                    @Override
                    public void onSuccess(List<User> list) {
                        super.onSuccess(list);
                        mData.clear();
                        mData.addAll(list);
                        mAdapter.notifyDataSetChanged();
                    }
                });
    }

    /**
     * 点击取消任命
     *
     * @param position
     */
    @Override
    public void cancelAppoint(final int position) {
        Dialog dialog = new CommonAlertDialog(this, getString(R.string.club_make_sure_cancel_vice), new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                ChannelMember channelMember = new ChannelMember();
                channelMember.setAssignUserIdStr(mData.get(position).getUserIdStr());
                channelMember.setAssginFlag(1);//1是取消任命
                L00bangRequestManager2.getServiceInstance()
                        .assignVice(channelMember)
                        .compose(L00bangRequestManager2.setSchedulers())
                        .subscribe(new ProgressSubscriber<Object>(AppointViceChairmanActivity.this) {
                            @Override
                            public void onSuccess(Object o) {
                                super.onSuccess(o);
                                ToastUtil.showMessage(AppointViceChairmanActivity.this, getString(R.string.club_appoint_cancel));
                                mAdapter.removeListItem(mData.get(position));
                            }
                        });
            }
        });
        dialog.show();
    }

    /**
     * 点击添加副会长
     *
     * @param view
     */
    @OnClick(rl_add_vice_chairman)
    void addViceClick(View view) {
        //如果超过3个，提示最多只能任命3个
        if (mData != null && mData.size() >= 3) {
            ToastUtil.showMessage(this, getString(R.string.club_chairman_max_count));
        } else {
            IntentUtils.startActivityForResult(this, AppointMemberListActivity.class, 0, mChannelId);
        }

    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == REFRESH_LIST) {
            requestData();
        }
    }
}
