package com.cloudy.linglingbang.activity.store.commodity

/**
 * 详情页打开弹窗
 * <AUTHOR>
 * @date 2022/10/12
 */
interface CommodityOpenDialog {

    companion object {
        /**
         * 选择规格
         */
        const val SELECT = 0

        /**
         * 加入购物车
         */
        const val SHOPPING_CAR = 1

        /**
         * 立即购买
         */
        const val NOW_BUY = 2

        /**
         * 更新购物车中商品sku
         */
        const val NOW_UPDATE_CART = 3
    }

    /**
     * 打开sku弹窗
     * openType 0：选择规格，1：加入购物车，2：立即购买
     */
    fun openSkuDialog(openType: Int)

    /**
     * 打开参数弹窗
     */
    fun openParametersDialog()

    /**
     * 打开优惠卷弹窗
     */
    fun openCouponDialog()


    interface WayType {
        companion object {
            /**
             * 送货到家
             */
            const val TYPE_HOME = 0

            /**
             * 选择门店
             */
            const val TYPE_SELECT_SHOP = 1

            /**
             * 虚拟收货
             */
            const val TYPE_VIRTUALLY = 2
        }
    }
}