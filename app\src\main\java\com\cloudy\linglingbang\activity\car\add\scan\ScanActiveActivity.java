package com.cloudy.linglingbang.activity.car.add.scan;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.text.TextUtils;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.car.add.AddCarController;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertBlackBtnDialog;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog;
import com.cloudy.linglingbang.model.car.add.AuthResult;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.error.ApiException;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;

import java.util.HashMap;
import java.util.Map;

/**
 * 扫码激活
 *
 * <AUTHOR>
 * @date 2019/3/27
 */
public class ScanActiveActivity extends ScanCodeActivity {
    @Override
    protected void onGetScanResult(String scanResult) {
        //super.onGetScanResult(scanResult);
        addCarInfo(scanResult);
    }

    /**
     * 增加绑定信息
     */
    private void addCarInfo(String scanResult) {
        Map<String, String> map = new HashMap<>();
        map.put("vin", mVin);
        map.put("qrcode", scanResult);
        L00bangRequestManager2.getServiceInstance()
                .addBindInfoFinish(map)
                .compose(L00bangRequestManager2.<String>setSchedulers())
                .subscribe(new ProgressSubscriber<String>(this) {
                    @Override
                    public void onSuccess(String s) {
                        super.onSuccess(s);
                        //直接进入下一步
                        goNextStep();
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                        if (e instanceof ApiException) {
                            if (((ApiException) e).getCode() > 0) {
                                //大于 0
                                showErrorDialog(e.getMessage());
                                return;
                            }
                        }
                        showErrorDialog(null);
                    }

                    @Override
                    protected boolean showToastOnError() {
                        return false;
                    }
                });
    }

    /**
     * 绑定成功
     */
    private void onBindSuccess(final AuthResult authResult) {
        Context context = this;
        Dialog dialog = new CommonAlertDialog(context, R.string.dialog_add_car_auth_message, R.string.dialog_add_car_auth_ok, R.string.common_cancel_quxiao,
                new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.dismiss();
                        goAuth(authResult);
                    }
                },
                new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.cancel();
                        ScanActiveActivity.this.finish();
                    }
                });
        dialog.show();
    }

    /**
     * 去认证
     */
    private void goAuth(AuthResult authResult) {
        AddCarController.goRealPersonAuth(this, mVin, authResult);
        finish();
    }

    /**
     * 显示失败对话框
     */
    private void showErrorDialog(String message) {
        Context context = this;
        if (TextUtils.isEmpty(message)) {
            message = context.getString(R.string.add_car_scan_active_scan_error);
        }
        Dialog dialog = new CommonAlertBlackBtnDialog(context, message, context.getString(R.string.add_car_scan_active_retry_scan), null, null, null);
        dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                //重新启动
                restartPreviewAfterDelay(0);
            }
        });
        dialog.show();
    }
}
