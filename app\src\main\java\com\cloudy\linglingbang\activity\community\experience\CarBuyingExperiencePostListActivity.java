package com.cloudy.linglingbang.activity.community.experience;

import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.community.common.PostFlagsEnum;
import com.cloudy.linglingbang.activity.community.post.CommunityUtils;
import com.cloudy.linglingbang.activity.fragment.homePage.today.TodayModel;
import com.cloudy.linglingbang.activity.fragment.homePage.today.adapter.HomeFindAdapter;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.widget.dialog.CommonPromptDialog;
import com.cloudy.linglingbang.app.widget.dialog.alert.BaseAlertDialog;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.user.User;
import com.cloudy.linglingbang.web.user.MyCarWebActivity;

import java.util.ArrayList;
import java.util.List;

import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 提车作业帖列表
 *
 * <AUTHOR>
 * @date 2017/7/13
 */
public class CarBuyingExperiencePostListActivity extends BaseRecyclerViewRefreshActivity<Object> {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<Object> list) {
        HomeFindAdapter adapter = new HomeFindAdapter(this, list);
        adapter.addFlags(PostFlagsEnum.SHOW_ATTENTION);
        return adapter;
    }

    @Override
    public Observable<BaseResponse<List<Object>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return service2.getExperiencePostList(pageNo, pageSize)
                .map(listBaseResponse -> {
                    List<Object> list = new ArrayList<>();
                    if (listBaseResponse.getData() != null) {
                        List<PostCard> data = listBaseResponse.getData();
                        for (PostCard postCard : data) {
                            list.add(new TodayModel.TodayColumnPost(postCard));
                        }
                    }
                    return listBaseResponse.cloneWithData(list);
                });
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        super.onCreateOptionsMenu(menu);
        if (mToolbar != null && mToolbar.getMenu() != null) {
            MenuItem item = mToolbar.getMenu().findItem(R.id.action_right_text);
            if (item != null) {
                item.setTitle(getString(R.string.menu_car_buying_experience_post));
                item.setVisible(true);
            }
        }
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.action_right_text:
                onClickPost();
                return true;
            default:
                return super.onOptionsItemSelected(item);
        }
    }

    @Override
    public RefreshController<Object> createRefreshController() {
        return new RefreshController<Object>(this) {
            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return null;
            }
        };
    }

    /**
     * 点击了发帖
     */
    private void onClickPost() {
        if (AppUtil.checkLogin(this)) {
            if (UserUtils.isAuthenticationUser()) {
                //跳到我的爱车
                IntentUtils.startActivityForResult(this, MyCarWebActivity.class, 0);
            } else {
                //未认证，请求一次服务器，确认是否已认证成功
                getSelfInfo();
            }
        }
    }

    /**
     * 未认证，请求一次服务器，确认是否已认证成功
     */
    private void getSelfInfo() {
        L00bangRequestManager2
                .setSchedulers(L00bangRequestManager2.getServiceInstance().getSelfInfo())
                .subscribe(new ProgressSubscriber<User>(this) {
                    @Override
                    public void onSuccess(User user) {
                        super.onSuccess(user);
                        if (user == null) {
                            return;
                        }
                        //更新新的用户信息
                        User.updateUserShare(user);

                        //额外处理
                        if (UserUtils.isAuthenticationUser()) {
                            //跳到我的爱车
                            IntentUtils.startActivityForResult(CarBuyingExperiencePostListActivity.this, MyCarWebActivity.class, 0);
                        } else {
                            showGoAuthenticationDialog();
                        }
                    }

                });
    }

    /**
     * 显示去认证的对话框
     */
    private void showGoAuthenticationDialog() {
        BaseAlertDialog dialog = new CommonPromptDialog.Builder(this)
                .setButtonTextAndStrokeColor(Color.parseColor("#519BFE"))
                .setIcon(R.drawable.ic_dialog_car_owner_verify_fail)
                .setMessage("您还没有认证管家会员，不能发表提车作业帖哦！快去认证成为管家会员吧，享受更多会员权益～")
                .setPositiveButton("去认证", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        goAuthentication(CarBuyingExperiencePostListActivity.this);
                    }
                })
                .create();
        dialog.show();
        dialog.getAlertController().getMessageView().setMaxLines(3);
    }

    /**
     * 去认证
     */
    private void goAuthentication(Context context) {
        JumpPageUtil.goAuthenticationPage(context);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        CommunityUtils.PostUtils.onActivityResult(this, requestCode, resultCode, data);
    }

}
