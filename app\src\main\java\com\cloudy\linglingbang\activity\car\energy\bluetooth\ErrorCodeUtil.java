package com.cloudy.linglingbang.activity.car.energy.bluetooth;

import java.util.HashMap;

/**
 * 蓝牙车控错误码工具类
 * Created by LiYeWen on 2025/05/15
 */
public class ErrorCodeUtil {

    public static final String LIGHT_ON = "112";
    public static final String DOOR_OPENED = "113";
    public static final String B_DOOR_OPENED = "114";
    public static final String CAR_RUNNING = "115";
    public static final String GEAR_NOT_N = "116";
    public static final String POWER_OFF_ERROR = "117";
    public static final String ACC_ON = "111";
    public static final String KEY_ERROR = "118";
    public static final String LOW_POWER = "119";
    public static final String LR_LIGHT_ERROR = "120";
    public static final String CMD_CONFLICT = "121";
    public static final String WINDOW_ERROR = "122";
    public static final String HORN_ERROR = "123";
    public static final String POWER_ON_ERROR = "124";
    public static final String UN_CHARGE = "125";
    public static final String PARKING_TIME_OUT = "3007";
    public static final String PARKING_OBSTACLE = "3008";
    public static final String PARKING_EXCESSIVE = "3009";
    public static final String PARKING_DOOR_OPENED = "300A";
    public static final String PARKING_ERROR = "300C";
    public static final String PARKING_FUNCTION_ERROR = "300D";
    public static final String PARKING_TYPE_ERROR = "3010";
    public static final String PARKING_UCU_ERROR = "300E";
    public static final String CMD_POWER_OFF = "C0650003";
    public static final String PARKING_POWER_ERROR = "3102";
    public static final String PARKING_REMOTE_POWER_ERROR = "3103";
    public static final String PARKING_NOT_READY = "3104";
    public static final String PARKING_FAILED = "3106";
    public static final String PARKING_MANUAL_ERROR = "3107";
    public static final String PARKING_FUNCTION_ERROR_2 = "3108";
    public static final String PARKING_TIME_OUT_2 = "3107";
    public static final String PARKING_FAILED_2 = "3203";
    public static final String PARKING_MANUAL_ERROR_2 = "3204";
    public static final String PARKING_MODEL_ERROR = "3205";
    public static final String PARKING_FUNCTION_ERROR_3 = "3206";
    public static final String PARKING_TIME_OUT_3 = "3208";
    public static final String IGN1_OUTPUT_ERROR = "E00325";
    public static final String ACC_OUTPUT_ERROR = "E00327";
    public static final String ESCL_UNLOCK_ERROR = "E03213";
    public static final String VOLTAGE_EXCEED_ERROR = "E00328";
    public static final String START_TIMEOUT_ERROR = "E03216";

    public static final String UCU_NO_RESPONSE = "80B";//UCU无响应
    public static final String CRC_VERIFICATION_FAILED = "813";//CRC校验失败
    public static final String BCM_NO_RESPONSE = "816";//BCM超时无响应
    public static final String BLEKEY_VERIFICATION_FAILED = "81e";//用户bleKey校验失败
    public static final String BLEKEY_ABSENT = "87F";//用户bleKey不存在gaw
    private static ErrorCodeUtil instance;

    /**
     * 获取单例对象
     * @return
     */
    public static ErrorCodeUtil getInstance() {
        if (instance == null) {
            synchronized (ErrorCodeUtil.class) {
                instance = new ErrorCodeUtil();
            }
        }
        return instance;
    }

    /**
     * 初始化蓝牙车控错误码
     * @param bleParkingCodeMap
     * @param bleErrorCodeMap
     * @param e260SPErrorMsg
     */
    public void addMapData(HashMap<String, String> bleParkingCodeMap, HashMap<String, String> bleErrorCodeMap,
                           HashMap<Byte, String> e260SPErrorMsg) {
        //蓝牙车控错误码
        addBleErrorCode(bleErrorCodeMap, e260SPErrorMsg);

        bleParkingCodeMap.put(PARKING_TIME_OUT, "遥控泊车超时");
        bleParkingCodeMap.put(PARKING_OBSTACLE, "路径内有障碍物");
        bleParkingCodeMap.put(PARKING_EXCESSIVE, "规划次数过多");
        bleParkingCodeMap.put(PARKING_DOOR_OPENED, "请关好车门");
        bleParkingCodeMap.put(PARKING_ERROR, "指令执行错误");
        bleParkingCodeMap.put(PARKING_FUNCTION_ERROR, "功能故障");
        bleParkingCodeMap.put(PARKING_TYPE_ERROR, "泊出方式，泊出类型错误");
        bleParkingCodeMap.put(PARKING_UCU_ERROR, "网络延迟，请稍后重试");
        bleParkingCodeMap.put(PARKING_POWER_ERROR, "未上高压");
        bleParkingCodeMap.put(PARKING_REMOTE_POWER_ERROR, "未进入遥控上电");
        bleParkingCodeMap.put(PARKING_NOT_READY, "功能未就绪");
        bleParkingCodeMap.put(PARKING_FAILED, "出库失败");
        bleParkingCodeMap.put(PARKING_MANUAL_ERROR, "人工接管，出库失败");
        bleParkingCodeMap.put(PARKING_FUNCTION_ERROR_2, "功能不可用");
        bleParkingCodeMap.put(PARKING_TIME_OUT_2, "一键泊出超时");
        bleParkingCodeMap.put(PARKING_FAILED_2, "泊车失败");
        bleParkingCodeMap.put(PARKING_MANUAL_ERROR_2, "人工接管");
        bleParkingCodeMap.put(PARKING_MODEL_ERROR, "车外泊车模式不可用");
        bleParkingCodeMap.put(PARKING_FUNCTION_ERROR_3, "功能不可用");
        bleParkingCodeMap.put(PARKING_TIME_OUT_3, "一键泊入超时");
    }

    /**
     * 蓝牙车控错误码（提取出来一个方法）
     */
    private void addBleErrorCode(HashMap<String, String> bleErrorCodeMap, HashMap<Byte, String> e260SPErrorMsg) {
        bleErrorCodeMap.put(ACC_ON, "车辆已启动，请下电后重试");
        bleErrorCodeMap.put(LIGHT_ON, "车灯未关闭");
        bleErrorCodeMap.put(POWER_OFF_ERROR, "设置防盗失败");
        bleErrorCodeMap.put(UN_CHARGE, "车辆未充电");
        bleErrorCodeMap.put(POWER_ON_ERROR, "解除防盗失败");
        bleErrorCodeMap.put(HORN_ERROR, "喇叭异常");
        bleErrorCodeMap.put(WINDOW_ERROR, "车窗升降异常");
        bleErrorCodeMap.put(CMD_CONFLICT, "操作过于频繁，请稍后再试");
        bleErrorCodeMap.put(LR_LIGHT_ERROR, "转向灯异常");
        bleErrorCodeMap.put(LOW_POWER, "小电瓶电压低");
        bleErrorCodeMap.put(KEY_ERROR, "智能钥匙异常");
        bleErrorCodeMap.put(GEAR_NOT_N, "档位不在N或P档");
        bleErrorCodeMap.put(DOOR_OPENED, "车门未关，请关门后重试");
        bleErrorCodeMap.put(B_DOOR_OPENED, "后备箱未关，请关闭后重试");
        bleErrorCodeMap.put(CAR_RUNNING, "车辆行驶中");

        bleErrorCodeMap.put("00", "操作失败，请稍后重试");
        bleErrorCodeMap.put("05", "数据长度错误");
        bleErrorCodeMap.put("07", "操作过于频繁，请稍后重试");
        bleErrorCodeMap.put("08", "车辆未响应，请稍后重试");
        bleErrorCodeMap.put("0B", "UCU 无响应");
        bleErrorCodeMap.put("13", "CRC 校验失败");
        bleErrorCodeMap.put("16", "车辆未响应，请稍后重试");
        bleErrorCodeMap.put("1E", "钥匙失效，请重启APP后重试");
        bleErrorCodeMap.put("7F", "钥匙失效，请重启APP后重试");

        //以下是参考五菱汽车新增过来的错误码
        bleErrorCodeMap.put("01", "整车电源未关闭，不能进行此项操作");
        bleErrorCodeMap.put("02", "车门未锁，不能进行此项操作");
        bleErrorCodeMap.put("03", "钥匙未学习，请进站检查");
        bleErrorCodeMap.put("04", "钥匙配置错误，请进站检查");
        bleErrorCodeMap.put(IGN1_OUTPUT_ERROR, "系统错误，请进站检查");
        bleErrorCodeMap.put("06", "系统错误，请进站检查");
        bleErrorCodeMap.put(ACC_OUTPUT_ERROR, "系统错误，请进站检查");
        bleErrorCodeMap.put(VOLTAGE_EXCEED_ERROR, "电压过高，请稍后重试");
        bleErrorCodeMap.put("09", "远程认证失败，请稍后重试");
        bleErrorCodeMap.put("10", "发动机防盗认证失败，请稍后重试");
        bleErrorCodeMap.put("11", "车辆配置错误，请进站检查");
        bleErrorCodeMap.put("12", "请上车启动车辆重新激活该功能");
        bleErrorCodeMap.put(ESCL_UNLOCK_ERROR, "电子转向锁解锁失败，请进站维修");
        bleErrorCodeMap.put("14", "车辆未响应，请稍后重试");
        bleErrorCodeMap.put("15", "车门未关，不能进行此项操作");
        bleErrorCodeMap.put(START_TIMEOUT_ERROR, "发动机未启动，请再次尝试");
        bleErrorCodeMap.put("18", "发动机异常熄火，请稍后重试");
        bleErrorCodeMap.put("19", "系统错误，请进站检查");
        bleErrorCodeMap.put("1A", "防盗报警中，不能进行此项操作");
        bleErrorCodeMap.put("1B", "系统错误，请进站检查");
        bleErrorCodeMap.put("1C", "大灯开关处于非OFF档");
        bleErrorCodeMap.put("1D", "系统错误，请进站检查");
        bleErrorCodeMap.put("20", "远程认证失败，请稍后重试");
        bleErrorCodeMap.put("21", "车辆档位不在P挡，不能进行此项操作");
        bleErrorCodeMap.put("22", "整车不在远程刷新模式，请稍后重试");
        bleErrorCodeMap.put("26", "油量不足，不能进行此项操作");
        bleErrorCodeMap.put("27", "动力电池电量过低，不能进行此项操作");
        bleErrorCodeMap.put("28", "车辆未响应，请稍后重试");
        bleErrorCodeMap.put("2B", "天窗故障，请进站检查");
        bleErrorCodeMap.put("2F", "哎呀，刚开小差了，再试试吧");
        bleErrorCodeMap.put("3C", "任务已完成，发动机已关闭");
        bleErrorCodeMap.put("41", "危险报警灯未关闭，不能进行此项操作");
        bleErrorCodeMap.put("43", "前舱盖未关好，不能进行此项操作");
        bleErrorCodeMap.put("50", "远程认证失败，请稍后重试");
        bleErrorCodeMap.put("51", "远程切换本地模式失败，请在车内重新启动车辆");
        bleErrorCodeMap.put("52", "空调未启动，请稍后重试");
        bleErrorCodeMap.put("53", "功能未开启，请稍后重试");
        bleErrorCodeMap.put("54", "请锁车后再使用此项功能");
        bleErrorCodeMap.put("55", "车辆行驶过程中，不能进行此项操作");
        bleErrorCodeMap.put("56", "手刹未拉，不能进行此项操作");
        bleErrorCodeMap.put("57", "刹车踏板被踩下，不能进行此项操作");
        bleErrorCodeMap.put("58", "油门踏板被踩下，不能进行此项操作");
        bleErrorCodeMap.put("59", "一键启动开关信号异常，不能进行此项操作");
        bleErrorCodeMap.put("5A", "车辆通讯错误，请稍后重试");
        bleErrorCodeMap.put("5B", "远程认证失败，请稍后重试");
        bleErrorCodeMap.put("5C", "暂时不能获取控制状态，请稍后重试");
        bleErrorCodeMap.put("5D", "车内通讯未响应，请稍后重试");
        bleErrorCodeMap.put("5E", "车内通讯未响应，请稍后重试");
        bleErrorCodeMap.put("5F", "车内通讯未响应，请稍后重试");
        bleErrorCodeMap.put("71", "车内通讯未响应，请稍后重试");
        bleErrorCodeMap.put("72", "车内通讯未响应，请稍后重试");
        bleErrorCodeMap.put("73", "车内通讯未响应，请稍后重试");
        bleErrorCodeMap.put("74", "车内通讯未响应，请稍后重试");
        bleErrorCodeMap.put("75", "通讯失败，请稍后重试");
        bleErrorCodeMap.put("77", "计时超时，请稍后重试");
        bleErrorCodeMap.put("FF", "功能不支持");


        e260SPErrorMsg.put((byte) 0x01, "点火档位不在OFF档");
        e260SPErrorMsg.put((byte) 0x02, "车未上锁");
        e260SPErrorMsg.put((byte) 0x03, "PEPS 未学习");
        e260SPErrorMsg.put((byte) 0x04, "PEPS 远程 功能未使能");
        e260SPErrorMsg.put((byte) 0x05, "IGN1 输 出 失败");
        e260SPErrorMsg.put((byte) 0x06, "START 输出 故障");
        e260SPErrorMsg.put((byte) 0x07, "ACC、IGN2 输出失败");
        e260SPErrorMsg.put((byte) 0x08, "电 压 超 出 范围");
        e260SPErrorMsg.put((byte) 0x09, "远程认证 失败");
        e260SPErrorMsg.put((byte) 0x10, "IMMO 认证失败");
        e260SPErrorMsg.put((byte) 0x11, "非自动挡车型");
        e260SPErrorMsg.put((byte) 0x12, "远程上高压失败次数超过阈值（预留）");
        e260SPErrorMsg.put((byte) 0x13, "ESCL 不 能 解锁");
        e260SPErrorMsg.put((byte) 0x14, "PEPS 超时");
        e260SPErrorMsg.put((byte) 0x15, "车门未关");
        e260SPErrorMsg.put((byte) 0x16, "启动尝试超时");
        e260SPErrorMsg.put((byte) 0x18, "存在发动机熄火条件");
        e260SPErrorMsg.put((byte) 0x19, "电机故障");
        e260SPErrorMsg.put((byte) 0x1A, "防盗报警触发");
        e260SPErrorMsg.put((byte) 0x1B, "发生碰撞");
        e260SPErrorMsg.put((byte) 0x1C, "大灯开关处于非 OFF 档");
        e260SPErrorMsg.put((byte) 0x1D, "动力电池故障");
        e260SPErrorMsg.put((byte) 0x20, "鉴权请求 ID 无效");
        e260SPErrorMsg.put((byte) 0x21, "整车档位不在P 档");
        e260SPErrorMsg.put((byte) 0x22, "整车不在远程刷新模式");
        e260SPErrorMsg.put((byte) 0x26, "燃油量低");
        e260SPErrorMsg.put((byte) 0x27, "动力电池SOC 过低");
        e260SPErrorMsg.put((byte) 0x28, "ECM 应答超时");
        e260SPErrorMsg.put((byte) 0x2B, "天窗节点错误");
        e260SPErrorMsg.put((byte) 0x2F, "未知原因");
        e260SPErrorMsg.put((byte) 0x3C, "远 程 上 高 压控制状态超时熄火");
        e260SPErrorMsg.put((byte) 0x41, "危 险 报 警 灯触发");
        e260SPErrorMsg.put((byte) 0x43, "前舱盖未关");
        e260SPErrorMsg.put((byte) 0x50, "控制请求未定义");
        e260SPErrorMsg.put((byte) 0x51, "模式切换");
        e260SPErrorMsg.put((byte) 0x52, "空调故障");
        e260SPErrorMsg.put((byte) 0x53, "座椅加热失败");
        e260SPErrorMsg.put((byte) 0x54, "整车不处于防盗状态");
        e260SPErrorMsg.put((byte) 0x55, "车 速 大 于2km/h 或信号无效");
        e260SPErrorMsg.put((byte) 0x56, "电子手 刹或机械手刹未拉起");
        e260SPErrorMsg.put((byte) 0x57, "刹车踏板踩下");
        e260SPErrorMsg.put((byte) 0x58, "油门踏板踩下");
        e260SPErrorMsg.put((byte) 0x59, "SSB 开关按下");
        e260SPErrorMsg.put((byte) 0x5A, "CAN 总 线BUS OFF");
        e260SPErrorMsg.put((byte) 0x5B, "鉴权状态超出范围");
        e260SPErrorMsg.put((byte) 0x5C, "远程上高压控制状态错误");
        e260SPErrorMsg.put((byte) 0x5D, "手刹信号丢失");
        e260SPErrorMsg.put((byte) 0x5E, "车速信号丢失");
        e260SPErrorMsg.put((byte) 0x5F, "档位信号丢失");
        e260SPErrorMsg.put((byte) 0x71, "油门踏板信号丢 失");
        e260SPErrorMsg.put((byte) 0x72, "燃油量信号丢失");
        e260SPErrorMsg.put((byte) 0x73, "空调远 程控制失败，信号丢失");
        e260SPErrorMsg.put((byte) 0x74, "远程 上高压失败，信号丢失");
        e260SPErrorMsg.put((byte) 0x75, "通讯失败");
        e260SPErrorMsg.put((byte) 0x77, "1.5 小时计时超时");
        e260SPErrorMsg.put((byte) 0xFF, "功能不支持");
    }

}
