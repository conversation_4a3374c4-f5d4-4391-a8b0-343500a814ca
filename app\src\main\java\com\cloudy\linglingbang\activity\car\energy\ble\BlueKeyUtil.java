package com.cloudy.linglingbang.activity.car.energy.ble;

import android.os.CountDownTimer;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;

import com.blankj.utilcode.util.SPUtils;
import com.cloudy.linglingbang.ApplicationLLB;
import com.cloudy.linglingbang.activity.car.energy.ConnectHelper;
import com.cloudy.linglingbang.app.util.NetworkUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.NormalSubscriber;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;

import okhttp3.MediaType;
import okhttp3.RequestBody;

/**
 * <AUTHOR>
 * @describe
 * @date 2021/12/28
 */
public class BlueKeyUtil {
    private static final String TAG = "BlueKeyUtil";

    private static final String FILE_NAME = "temp_blue_key";
    private static final String DATA_KEY = "blue_key_entity_";
    private static final String DATA_KEY_BAK = "blue_key_bak_entity_";
    private static final String USER_DATA_KEY = "user_data_key_";
    private static final String ALL_CAR_INFO = "ALL_car_info";
    private static String TAG_UPDATEBLUEKEY = "===更新蓝牙钥匙===";
    private static String TAG_GETBLUEKEY = "===获取蓝牙钥匙===";
    private static String TAG_QUERYBLUEKEY = "===查询蓝牙钥匙===";
    private static String TAG_SAVEBLUEKEY = "===存储蓝牙钥匙===";
    private static String TAG_CLEARBLUEKEY = "===清除蓝牙钥匙信息===";

    private KeyEntity keyEntity;
    private String mobile;
    //缓存标示：用于区分获取缓存，还是重新解密更新的蓝牙钥匙
    private boolean newOne = true;
    //启动keyStore加密
    private boolean activeKeyStore = true;

    private Encryptor encryptor;
    private Decryptor decryptor;
    private Gson gson = new Gson();

    private String mVin;
    private ConnectTimeOutCount mConnectTimeOutCount;
    public boolean hasGetBlueKeyCompleted = false;//首次进入app是否已经查询到最新的蓝牙钥匙

    private BlueKeyUtil() {
        encryptor = new Encryptor();
        try {
            decryptor = new Decryptor();
        } catch (CertificateException | NoSuchAlgorithmException | KeyStoreException | IOException e) {
            e.printStackTrace();
        }
        if (encryptor == null || decryptor == null) {
            activeKeyStore = false;
        }
        keyEntity = getKeyEntity();
    }

    private static class BlueKeyInstance {
        private static final BlueKeyUtil INSTANCE = new BlueKeyUtil();
    }

    public static BlueKeyUtil getInstance() {
        return BlueKeyInstance.INSTANCE;
    }

    /**
     * 获取蓝牙钥匙 - 读取缓存
     *
     * @return
     */
    public KeyEntity getKeyEntity() {
        KeyEntity data = new KeyEntity();
        com.cloudy.linglingbang.app.log.LogUtils.e(TAG_GETBLUEKEY+"========getKeyEntity====start=====newOne:"+newOne);
        if (newOne) {
            try {
                String decryptText = decryptText();
                if (!TextUtils.isEmpty(decryptText)) {
                    data = (KeyEntity) ParseUtils.deSerialization(decryptText);
                    com.cloudy.linglingbang.app.log.LogUtils.e(TAG_GETBLUEKEY+"=========getKeyEntity====本地钥匙不为空====");
                } else {
                    com.cloudy.linglingbang.app.log.LogUtils.e(TAG_GETBLUEKEY+"=========getKeyEntity===本地钥匙为空=====");
//                    queryBleKey();//非车主直接查询钥匙，但是车主首次需要等更新完钥匙后再去查询
                }
            } catch (IOException | ClassNotFoundException e) {
                e.printStackTrace();
            }
            if (data != null) keyEntity = data;
            if (keyEntity.isDataGood()) newOne = false;
        }
        return keyEntity;
    }


    public void queryBleKey() {
        //离线模式
        if (UserUtils.hasLogin() && !NetworkUtil.isNetworkAvailable(ApplicationLLB.ct())) {
            if (newOne) {
                KeyEntity keyEntityTemp = null ;
                try {
                    String decryptText = decryptText();
                    if (!TextUtils.isEmpty(decryptText)) {
                        keyEntityTemp = (KeyEntity) ParseUtils.deSerialization(decryptText);
                        if (keyEntityTemp != null ) {
                            com.cloudy.linglingbang.app.log.LogUtils.e(TAG_QUERYBLUEKEY+"=====离线模式====keyEntityTemp不为空===newOne："+newOne);
                        }else {
                            com.cloudy.linglingbang.app.log.LogUtils.e(TAG_QUERYBLUEKEY+"=====离线模式====keyEntityTemp为空===");
                        }
                    } else {
                        com.cloudy.linglingbang.app.log.LogUtils.e(TAG_QUERYBLUEKEY+"=====离线模式====keyEntityTemp为空=====");
                    }
                } catch (IOException | ClassNotFoundException e) {
                    e.printStackTrace();
                }
                if (newOne && keyEntityTemp != null && keyEntityTemp.isDataGood()){
                    keyEntity = keyEntityTemp;
                    newOne = false;
                    ConnectHelper.getInstance().setParams(keyEntity);
                    ConnectHelper.getInstance().setParamsSuccess();
                }else {
                    com.cloudy.linglingbang.app.log.LogUtils.e(TAG_QUERYBLUEKEY+"=====离线模式====keyEntityTemp为空=====");
                }
            }
            return;
        }
        Log.e(TAG, TAG_QUERYBLUEKEY+"start===vin: " + mVin + " | mobile:" + mobile);
        if (UserUtils.hasLogin() && !TextUtils.isEmpty(mVin) && !TextUtils.isEmpty(mobile)) {
            if (AntiRepeatUtils.isFastDoubleClick("queryBleKey")) return;
            Log.e(TAG, TAG_QUERYBLUEKEY+"start===queryBleKey===");
            JSONObject root = new JSONObject();
            try {
                root.put("vin", mVin);
                root.put("keyType", "owner");
                root.put("userId", mobile);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), root.toString());

            L00bangRequestManager2
                    .getServiceInstance()
                    .queryBleKey(requestBody)
                    .compose(L00bangRequestManager2.setSchedulers())
                    .subscribe(new NormalSubscriber<GetBleKeyEntity>(ApplicationLLB.ct()) {
                        @Override
                        public void onSuccess(GetBleKeyEntity entity) {
                            super.onSuccess(entity);

                            if (entity!=null) {
                                com.cloudy.linglingbang.app.log.LogUtils.e(TAG_QUERYBLUEKEY+"onSuccess===result:"+entity.toString());
                                if (TextUtils.isEmpty(entity.getBleMac()) || TextUtils.isEmpty(entity.getKeyId())){
                                    ToastUtil.showMessage(ApplicationLLB.ct(),"未查询到蓝牙钥匙");
                                    com.cloudy.linglingbang.app.log.LogUtils.e(TAG_QUERYBLUEKEY+"onSuccess==未查询到蓝牙钥匙===");
                                    return;
                                }
                                if (keyEntity != null) {
                                    keyEntity.setMasterKey(entity.getMasterKey());
                                    keyEntity.setMasterKeyRandom(entity.getKeyMasterRandom());
                                    String a = Integer.toHexString(Integer.parseInt(entity.getKeyId()));
                                    keyEntity.setBleKey(ParseUtils.addZeroForLeft(a, 8));
                                    keyEntity.setMac(entity.getBleMac());
                                    com.cloudy.linglingbang.app.log.LogUtils.e(TAG_QUERYBLUEKEY+"onSuccess==蓝牙钥匙keyEntity：" + keyEntity.toString());
                                    saveKey(keyEntity);
                                    hasGetBlueKeyCompleted = true;
                                    try {
                                        //将蓝牙钥匙保存到本地
                                        SPUtils.getInstance(FILE_NAME).put(DATA_KEY + "unify_car_ct", ParseUtils.serialize(keyEntity));
                                    } catch (IOException e) {
                                        e.printStackTrace();
                                    }
                                }
                            }else {
                                com.cloudy.linglingbang.app.log.LogUtils.e(TAG_QUERYBLUEKEY+"onSuccess==未查询到蓝牙钥匙===");
                                ToastUtil.showMessage(ApplicationLLB.ct(),"未查询到蓝牙钥匙");
                            }
                        }

                        @Override
                        public void onError(Throwable e) {
                            super.onError(e);
                            Log.e(TAG, TAG_QUERYBLUEKEY+"onError:" + e.toString());
                        }

                        @Override
                        public void onFailure(Throwable e) {
                            super.onFailure(e);
                            Log.e(TAG, TAG_QUERYBLUEKEY+"onFailure:" + e.toString());

                        }
                    });
        }
    }

    public void saveUserMobile(String mobile) {
        this.mobile = mobile;
    }

    public void saveVin(String vin) {
        mVin = vin;
        SPUtils.getInstance(FILE_NAME).put(USER_DATA_KEY + mobile, vin);
    }

    public void clearVin() {
        SPUtils.getInstance(FILE_NAME).put(USER_DATA_KEY + mobile, "");
    }

    public String getVin() {
        return SPUtils.getInstance(FILE_NAME).getString(USER_DATA_KEY + mobile, "");
    }


    /**
     * 存储蓝牙钥匙
     *
     * @param keyEntity
     */
    private void saveKey(KeyEntity keyEntity) {
        Log.e(TAG, TAG_SAVEBLUEKEY+"start=======vin:"+getVin() + " | keyEntity.isDataGood()："+keyEntity.isDataGood());
        if (TextUtils.isEmpty(getVin()) || !keyEntity.isDataGood()) {
            return;
        }

        String data = null;
        try {
            data = ParseUtils.serialize(keyEntity);
        } catch (IOException e) {
            e.printStackTrace();
        }
        data = encryptText(data);
        if (!TextUtils.isEmpty(data) && !SPUtils.getInstance(FILE_NAME).getString(DATA_KEY + getVin()).equals(data)) {
            Log.e(TAG, TAG_SAVEBLUEKEY+"===本地蓝牙钥匙变更成功=======");
            SPUtils.getInstance(FILE_NAME).put(DATA_KEY + getVin(), data);
            newOne = true;
            ConnectHelper.getInstance().setParams(keyEntity);
        }
        ConnectHelper.getInstance().setParamsSuccess();
    }

    /**
     * 清除蓝牙钥匙信息
     */
    public void clearBlueKey() {
        Log.e(TAG, TAG_CLEARBLUEKEY+"clearBlueKey=======");
        ConnectHelper.getInstance().setConnectStatus(0);
        SPUtils.getInstance(FILE_NAME).put(DATA_KEY + getVin(), "");
        clearVin();
    }

    /**
     * 重置蓝牙状态
     */
    public void resectBleStatus() {
        ConnectHelper.getInstance().setConnectStatus(0);
    }

    private String decryptText() {
        if (activeKeyStore) {
            try {
                byte[] encryption = encryptor.getEncryption(DATA_KEY + getVin());
                byte[] iv = encryptor.getIv(DATA_KEY + getVin());
                return decryptor.decryptData(DATA_KEY + getVin(), encryption, iv);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            //无效时使用AES加密
            return DecodeUtils.Decrypt2String(DATA_KEY_BAK + getVin(), SPUtils.getInstance(FILE_NAME).getString(DATA_KEY_BAK + getVin()));
        }

        return null;
    }

    private String encryptText(String data) {
        if (TextUtils.isEmpty(data)) return null;
        if (activeKeyStore) {
            try {
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                    final byte[] encryptedText;
                    encryptedText = encryptor.encryptText(DATA_KEY + getVin(), data);
                    return Base64.encodeToString(encryptedText, Base64.DEFAULT);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            String result = DecodeUtils.encrypt2String(DATA_KEY_BAK + getVin(), data);
            SPUtils.getInstance(FILE_NAME).put(DATA_KEY_BAK + getVin(), result);
            return result;
        }
        return null;
    }

    public void startConnectTimeOutCount() {
        if (mConnectTimeOutCount == null) {
            mConnectTimeOutCount = new ConnectTimeOutCount(18000, 1000);
        }
        mConnectTimeOutCount.cancel();
        mConnectTimeOutCount.start();
    }

    public void stopConnectTimeOutCount() {
        if (mConnectTimeOutCount == null) {
            return;
        }
        mConnectTimeOutCount.cancel();
    }

    /**
     * 18秒倒计时
     */
    class ConnectTimeOutCount extends CountDownTimer {

        public ConnectTimeOutCount(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @Override
        public void onTick(long millisUntilFinished) {

        }

        @Override
        public void onFinish() {
            queryBleKey();
        }
    }

}
