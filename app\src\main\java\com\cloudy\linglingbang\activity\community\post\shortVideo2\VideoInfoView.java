package com.cloudy.linglingbang.activity.community.post.shortVideo2;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.text.Layout;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.community.post.PostDetailActivity;
import com.cloudy.linglingbang.activity.community.post.PostReportActivity;
import com.cloudy.linglingbang.activity.community.post.detail.GoodsStickyDialog;
import com.cloudy.linglingbang.activity.community.post.detail.PersonalCardDialog;
import com.cloudy.linglingbang.activity.community.post.shortVideo.ShortVideoCommentDialog;
import com.cloudy.linglingbang.activity.community.post.shortVideo.ShortVideoSlideListActivity;
import com.cloudy.linglingbang.activity.fragment.homePage.live.adapter.UserViewHolder;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.PraiseAnimationUtils;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.TopicUtils;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.ExpressionTextView;
import com.cloudy.linglingbang.app.widget.PraiseCountTextView;
import com.cloudy.linglingbang.app.widget.dialog.CommonListWithBottomCancelDialog;
import com.cloudy.linglingbang.app.widget.dialog.alert.BaseListDialog;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.NormalSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.user.User;

import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentTransaction;
import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 视频信息, 包含用户头像, 用户昵称, 视频标题
 *
 * <AUTHOR>
 * @date 2020/9/16
 */
public class VideoInfoView extends RelativeLayout implements UserViewHolder.UserAttentionChange {

    /**
     * 删除帖子
     */
    @BindView(R.id.tv_delete_post)
    PraiseCountTextView mTvDeletePost;
    /**
     * 点赞数
     */
    @BindView(R.id.tv_praise_count)
    PraiseCountTextView mTvPraiseCount;
    /**
     * 回复数
     */
    @BindView(R.id.tv_reply_count)
    TextView mTvReplyCount;
    /**
     * 标题
     */
    @BindView(R.id.tv_title)
    TextView mTvTitle;
    /**
     * 简介
     */
    @BindView(R.id.tv_content)
    ExpressionTextView mTvContent;
    /**
     * 分享
     */
    @BindView(R.id.tv_share_count)
    TextView mTvShareCount;
    /**
     * 商品icon
     */
    @BindView(R.id.iv_commodity_activity)
    ImageView mIvCommodityActivity;
    /**
     * 广告运营位
     */
    @BindView(R.id.btn_ad)
    TextView mTvAdName;
    /**
     * call名片
     */
    @BindView(R.id.iv_call)
    ImageView mIvCall;
    /**
     * 置顶
     */
    @BindView(R.id.iv_zhi)
    ImageView mIvZhi;
    /**
     * 举报
     */
    @BindView(R.id.tv_report)
    TextView mTvReport;

    /**
     * 4G环境下的提示
     */
    @BindView(R.id.rl_wifi_hint)
    RelativeLayout mRlWifiHint;

    /**
     * 播放按钮
     */
    @BindView(R.id.btn_play_continue)
    Button mBtnPlayContinue;
    /**
     * 播放按钮
     */
    @BindView(R.id.tv_see_all)
    TextView mTvSeeAll;

    UserViewHolder mUserViewHolder;

    /**
     * 是否正在点赞
     */
    private boolean mIsPraising;

    private BaseVideoListAdapter mAdapter;
    private int mIsHideShare;
    private int mOpenFrom;

    private PostCard mPostCard;
    private GoodsStickyDialog.GoodsStickyUtils goodsStickyUtils;

    long mLastTime = 0;
    long mCurTime = 0;

    public VideoInfoView(Context context) {
        this(context, null);
    }

    public VideoInfoView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.layout_video_cotent_view, this, true);
        ButterKnife.bind(this, view);
        mUserViewHolder = new UserViewHolder(view);
        mUserViewHolder.setUserAttentionChange(this);
    }

    public void setVideoInfo(final PostCard postCard) {
        mPostCard = postCard;
        mTvShareCount.setVisibility(mIsHideShare == 0 ? View.VISIBLE : View.GONE);
        mRlWifiHint.setVisibility(View.GONE);
        if (postCard.getPostCommentCount() == 0) {
            mTvReplyCount.setText(R.string.post_video_reply);
        } else {
            mTvReplyCount.setText(AppUtil.getCommentDesc(postCard.getPostCommentCount()));
        }
        if (postCard.getPostShareCount() == 0) {
            mTvShareCount.setText(R.string.post_share_tips);
        } else {
            mTvShareCount.setText(AppUtil.getCommentDesc(postCard.getPostShareCount()));
        }
        setPraiseState(postCard);
        setDeletePostStatus(postCard);
        mTvTitle.setText(postCard.getPostTitle());
        mTvContent.setMaxLines(Integer.MAX_VALUE);
        String content = postCard.getImgTexts().get(0).getText();
        if (!TextUtils.isEmpty(postCard.getTopicName())) {
            content = TopicUtils.getNameWithHashtag(postCard.getTopicName()) + " " + content;
        }
        mTvContent.setText(content);
        mTvContent.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                mTvContent.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                Layout layout = mTvContent.getLayout();
                if (layout != null){
                    if (layout.getLineCount() <= 2) {
                        mTvSeeAll.setVisibility(GONE);
                    } else {
                        final String txt = "[查看全文]";
                        mTvContent.setMaxLines(2);
                        mTvSeeAll.setVisibility(VISIBLE);
                        mTvSeeAll.setText(txt);
                        mTvSeeAll.setOnClickListener(v -> {
                            if (txt.equals(mTvSeeAll.getText())) {
                                mTvSeeAll.setText("[收起]");
                                mTvContent.setMaxLines(Integer.MAX_VALUE);
                            } else {
                                mTvSeeAll.setText(txt);
                                mTvContent.setMaxLines(2);
                            }
                        });
                    }
                }
            }
        });
        if (User.getsUserInstance().hasLogin() && !UserUtils.isSelf(postCard.getAuthor())) {
            mTvReport.setVisibility(View.VISIBLE);
        } else {
            mTvReport.setVisibility(View.GONE);
        }
        /**
         * 如果从推荐登录，则请求接口，告诉已读
         */
        if (mOpenFrom == ShortVideoSlideListActivity.openFrom.VIDEO_FROM_RECOMMEND) {
            mTvReport.setVisibility(View.GONE);
        } else {
            mTvReport.setVisibility(View.VISIBLE);
        }
        //用户相关
        if (postCard.getAuthor() != null) {
            mUserViewHolder.bindTo(postCard.getAuthor(), 0);
        }
        mTvReplyCount.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SensorsUtils.sensorsClickBtn("点击回复(" + postCard.getPostTitle() + ")", "live", "回复");
                DialogFragment dialogFragment = ShortVideoCommentDialog.newInstance(postCard);
                FragmentTransaction transaction = ((BaseActivity) getContext()).getSupportFragmentManager().beginTransaction();
                transaction.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_FADE);
                dialogFragment.show(transaction, "tag");
            }
        });

        //点赞
        mTvPraiseCount.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (AppUtil.checkLogin(getContext(), AppUtil.RegisterChannel.CHANNEL_POST)) {
                    onPraiseClick(postCard);
                }
            }
        });

        //分享
        mTvShareCount.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (getContext() instanceof ShortVideoSlideListActivity2) {
                    SensorsUtils.sensorsClickBtn("点击分享(" + postCard.getPostTitle() + ")", "live", "分享");
                    ((ShortVideoSlideListActivity2) getContext()).sharePost(postCard);
                }
            }
        });

        //举报
        mTvReport.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showReportDeleteDialog();
            }
        });

        mIvZhi.setOnClickListener(null);
        mIvCall.setOnClickListener(null);
        mIvZhi.setVisibility(View.GONE);
        mIvCall.setVisibility(View.GONE);
        //如果是好货互通的视频贴
        if (postCard.getSeries() == 3) {
            //是否是自己
            if (UserUtils.isSelf(postCard.getAuthor())) {
                mIvZhi.setVisibility(View.VISIBLE);
                mIvZhi.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        //是否已置顶 是 查看剩余时间，否设置置顶
                        if (postCard.getIsTop() == 1) {
                            Resources resources = v.getResources();
                            if (postCard.getTopExpireDate() > AppUtil.getServerCurrentTime()) {
                                long time = postCard.getTopExpireDate() - AppUtil.getServerCurrentTime();
                                //展示置顶剩余时间
                                CommonAlertDialog dialog = new CommonAlertDialog(v.getContext(), resources.getString(R.string.text_post_set_ding_haved_time, String.valueOf(time / 3600_000), String.valueOf(time / 1000 / 60 % 60)), null, "知道了", null, null);
                                dialog.show();
                            } else {
                                CommonAlertDialog dialog = new CommonAlertDialog(v.getContext(), resources.getString(R.string.text_post_set_ding_haved), null, resources.getString(R.string.label_driving_rec_close), null, null);
                                dialog.show();
                            }
                        } else {
                            //如果未置顶
                            //设置置顶
                            if (goodsStickyUtils == null) {
                                goodsStickyUtils = new GoodsStickyDialog.GoodsStickyUtils(v.getContext(), postCard.getPostId(), false);
                            }
                            goodsStickyUtils.queryStickyNewDateAndShowDialog(new GoodsStickyDialog.StickyAction() {
                                @Override
                                public void onCall(boolean result, long date) {
                                    // 置顶成功,把置顶失效时间置位null,这样方便判断弹窗即 告诉用户您的帖子已置顶 或者显示置顶剩余时间弹窗
                                    mPostCard.setTopExpireDate(result ? null : mPostCard.getTopExpireDate());
                                    mPostCard.setIsTop(result ? 1 : 0);
                                }
                            });
                        }
                    }
                });
            }
            //个人名片信息不为空
            if (postCard.getCallingCard() != null) {
                mIvCall.setVisibility(View.VISIBLE);
                mIvCall.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        //查看个人名片弹窗
                        PersonalCardDialog dialog = new PersonalCardDialog(v.getContext(), postCard.getCallingCard());
                        dialog.show();
                    }
                });
            }
        }

    }

    private void setDeletePostStatus(PostCard postCard) {
        mTvDeletePost.setVisibility(View.GONE);
        /*if (UserUtils.isSelf(postCard.getAuthor())) {
            mTvDeletePost.setVisibility(View.VISIBLE);
        } else {
            mTvDeletePost.setVisibility(View.GONE);
        }
        mTvDeletePost.setOnClickListener(view -> {
            Dialog dialog = new CommonAlertDialog(getContext(), R.string.post_dialog_delete_confirm, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    L00bangRequestManager2
                            .setSchedulers(L00bangRequestManager2.getInstance().getService().deletePostCard(Long.valueOf(postCard.getPostId())))
                            .subscribe(new ProgressSubscriber<PostCard>(getContext()) {
                                @Override
                                public void onSuccess(PostCard s) {
                                    super.onSuccess(s);
                                    AppUtil.getActivity(getContext()).finish();
                                }
                            });
                }
            });
            dialog.show();
        });*/
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                //设置双击事件呼出点赞动画
                mLastTime = mCurTime;
                mCurTime = System.currentTimeMillis();
                if (mCurTime - mLastTime < 300) {//双击事件
                    mCurTime = 0;
                    mLastTime = 0;
                    PraiseAnimationUtils.doAnimation(this, event);
                    if (mPostCard.getIsPraise() != 1) {
                        onPraiseClick(mPostCard);
                    }
                } else {//单击事件
                }
                break;
        }
        return false;
    }

    /**
     * 设置点赞状态
     */
    public void setPraiseState(PostCard postCard) {
        if (postCard.getPostPraiseCount() == 0) {
            mTvPraiseCount.setText(R.string.post_video_praise);
        } else {
            mTvPraiseCount.setText(AppUtil.getCommentDesc(postCard.getPostPraiseCount()));
        }
        Drawable drawableTop;
        if (postCard.getIsPraise() == 1) {
            drawableTop = getContext().getResources().getDrawable(R.drawable.ic_video_praise);
//            mTvPraiseCount.setTextColor(getContext().getResources().getColor(R.color.color_ff5252));
        } else {
            drawableTop = getContext().getResources().getDrawable(R.drawable.ic_video_un_praise);
//            mTvPraiseCount.setTextColor(getContext().getResources().getColor(R.color.white));
        }
        mTvPraiseCount.setCompoundDrawablesWithIntrinsicBounds(null, drawableTop, null, null);
    }

    /**
     * 点赞操作
     *
     * @param postCard
     */
    public void onPraiseClick(final PostCard postCard) {
        SensorsUtils.sensorsClickBtn("点击点赞(" + postCard.getPostTitle() + ")", "live", "点赞");
        if (postCard.getIsPraise() == 1) {
            ToastUtil.showMessage(getContext(), "您已经赞过了");
        } else {
            if (AppUtil.checkLogin(getContext(), AppUtil.RegisterChannel.CHANNEL_POST) && !mIsPraising) {
                mIsPraising = true;
                //先成功，失败后复原
                postCard.setIsPraise(1);
                //更新点赞数
                int praiseCount = postCard.getPostPraiseCount() + 1;
                postCard.setPostPraiseCount(praiseCount);
                setPraiseState(postCard);
                //点赞动画
                mTvPraiseCount.startAnimation(AnimationUtils.loadAnimation(getContext(), R.anim.post_praise_anim));
//                mAdapter.notifyItemChanged(getAdapterPosition());
                L00bangRequestManager2
                        .getServiceInstance()
                        .praisedPostCard(Long.valueOf(postCard.getPostId()))
                        .compose(L00bangRequestManager2.setSchedulers())
                        .subscribe(new NormalSubscriber<String>(getContext()) {
                            @Override
                            public void onSuccess(String s) {
                                super.onSuccess(s);
                                mIsPraising = false;
                            }

                            @Override
                            public void onFailure(Throwable e) {
                                super.onFailure(e);
                                mIsPraising = false;
                                postCard.setIsPraise(0);
                                //更新点赞数
                                int praiseCount = postCard.getPostPraiseCount() - 1;
                                postCard.setPostPraiseCount(praiseCount);
                                setPraiseState(postCard);
//                                mAdapter.notifyItemChanged(getAdapterPosition());

                            }
                        });
            }
        }
    }

    public void setAdapter(BaseVideoListAdapter adapter) {
        this.mAdapter = adapter;
    }

    public void setOpenFrom(int openFrom) {
        mOpenFrom = openFrom;
    }

    public void setIsHideShare(int isHideShare) {
        mIsHideShare = isHideShare;
    }

    @Override
    public void updateAttention(boolean isAttention, String userStr) {
        if (mAdapter != null) {
            int index = 0;
            for (Object datum : mAdapter.getDataList()) {
                if (datum instanceof PostCard) {
                    PostCard pd = ((PostCard) datum);
                    if (pd.getAuthor() != null && userStr.equals(pd.getAuthor().getUserIdStr())) {
                        UserUtils.updateUserAttention(pd.getAuthor(), isAttention);
                        if (!pd.getPostId().equals(mPostCard.getPostId())) {
                            mAdapter.notifyItemChanged(index);
                        }
                    }
                }
                index++;
            }
        }
    }

    public ImageView getIvCommodityActivity() {
        return mIvCommodityActivity;
    }

    public TextView getTvAdName() {
        return mTvAdName;
    }

    /**
     * 举报和删除的对话框
     */
    public void showReportDeleteDialog() {
        int dataArrayResId;
        if (User.getsUserInstance().hasLogin() && !UserUtils.isSelf(mPostCard.getAuthor())) {
            dataArrayResId = R.array.dialog_choose_report_other_chosen_array;
        } else {
            dataArrayResId = R.array.dialog_choose_report_my_chosen_array;
        }
        Dialog dialog = new CommonListWithBottomCancelDialog(getContext(), dataArrayResId, new BaseListDialog.OnChoiceClickListener() {
            @Override
            public boolean onChoiceClick(int chosenIndex, String chosenText) {
                if (chosenIndex == 0) {
                    reportPost();
                } else if (chosenIndex == 1) {
                    deletePost();
                }
                return false;
            }
        });
        dialog.show();
    }

    /**
     * 举报
     */
    public void reportPost() {
        if (AppUtil.checkLogin(getContext())) {
            if (mPostCard.getProsecutionUncheck() == 1) {
                ToastUtil.showMessage(getContext(), R.string.post_detail_toast_prosecution);
                return;
            }
            //跳转举报详情页
            IntentUtils.startActivityForResult(AppUtil.getActivity(getContext()), PostReportActivity.class, PostDetailActivity.REQUEST_CODE_COMMON, mPostCard.getPostId());
        }
    }

    /**
     * 删除帖子
     */
    public void deletePost() {
        Dialog dialog = new CommonAlertDialog(getContext(), R.string.post_dialog_delete_confirm, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                L00bangRequestManager2
                        .setSchedulers(L00bangRequestManager2.getInstance().getService().deletePostCard(Long.valueOf(mPostCard.getPostId())))
                        .subscribe(new ProgressSubscriber<PostCard>(getContext()) {
                            @Override
                            public void onSuccess(PostCard s) {
                                super.onSuccess(s);
                                AppUtil.getActivity(getContext()).finish();
                            }
                        });
            }
        });
        dialog.show();
    }
}
