package com.cloudy.linglingbang.activity.fragment.store.home.model;

import com.cloudy.linglingbang.model.wrapper.WrapperModelWithType;

/**
 * 标题-查看更多商品
 *
 * <AUTHOR>
 * @date 2020/5/9
 */
public class CommonTitleWithMore extends WrapperModelWithType<String> {
    private final long layoutComponentId;
    /**
     * 更多的商品打包ID
     */
    private int morePackageId;
    /**
     * 商品类型 0:优品，1:福利社商品
     */
    private int productType = 0;
    private String moreText;
    private Object extra;

    /**
     * 更多页面跳转类型  16:优品列表   40：二级页面
     */
    private int moreLinkType;
    /**
     * 更多页面跳转的数据   格式：二级类目ID，更多页面样式，更多页面是否显示排序
     * (更多页面样式 0:一行两列    1:一行一列 ; 更多页面是否显示排序  0:否   1:是)
     */
    private String moreLinkUrl;

    public CommonTitleWithMore(String original) {
        this(original, 0);
    }

    public CommonTitleWithMore(String original, long layoutComponentId) {
        super(original);
        this.layoutComponentId = layoutComponentId;
    }

    public long getLayoutComponentId() {
        return layoutComponentId;
    }

    public int getMorePackageId() {
        return morePackageId;
    }

    public void setMorePackageId(int morePackageId) {
        this.morePackageId = morePackageId;
    }

    public String getMoreText() {
        return moreText;
    }

    public void setMoreText(String moreText) {
        this.moreText = moreText;
    }

    public Object getExtra() {
        return extra;
    }

    public void setExtra(Object extra) {
        this.extra = extra;
    }

    public int getProductType() {
        return productType;
    }

    public void setProductType(int productType) {
        this.productType = productType;
    }

    public int getMoreLinkType() {
        return moreLinkType;
    }

    public void setMoreLinkType(int moreLinkType) {
        this.moreLinkType = moreLinkType;
    }

    public String getMoreLinkUrl() {
        return moreLinkUrl;
    }

    public void setMoreLinkUrl(String moreLinkUrl) {
        this.moreLinkUrl = moreLinkUrl;
    }
}
