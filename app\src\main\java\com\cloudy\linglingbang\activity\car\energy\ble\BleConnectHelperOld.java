package com.cloudy.linglingbang.activity.car.energy.ble;

import static android.app.Activity.RESULT_OK;
import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.AlertDialog;
import android.app.Application;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.location.LocationManager;
import android.os.Build;
import android.os.CountDownTimer;
import android.os.Handler;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.widget.Toast;
import androidx.annotation.RequiresApi;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.clj.fastble.BleManager;
import com.clj.fastble.bluetooth.BleBluetooth;
import com.clj.fastble.bluetooth.MultipleBluetoothController;
import com.clj.fastble.callback.BleGattCallback;
import com.clj.fastble.callback.BleMtuChangedCallback;
import com.clj.fastble.callback.BleNotifyCallback;
import com.clj.fastble.callback.BleScanCallback;
import com.clj.fastble.callback.BleWriteCallback;
import com.clj.fastble.data.BleDevice;
import com.clj.fastble.data.BleScanState;
import com.clj.fastble.exception.BleException;
import com.clj.fastble.scan.BleScanRuleConfig;
import com.clj.fastble.utils.HexUtil;
import com.cloudy.linglingbang.ApplicationLLB;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.car.energy.ConnectStatusListener;
import com.cloudy.linglingbang.activity.car.energy.HexChangeUtils;
import com.cloudy.linglingbang.activity.car.energy.NotifyListener;
import com.cloudy.linglingbang.activity.car.energy.PassThroughListener;
import com.cloudy.linglingbang.activity.car.energy.ScanListener;
import com.cloudy.linglingbang.activity.car.energy.ServiceWriteListener;
import com.cloudy.linglingbang.activity.car.energy.TaskCenter;
import com.cloudy.linglingbang.activity.travel.TravelFragment;
import com.cloudy.linglingbang.activity.travel.utils.StringUtils;
import com.cloudy.linglingbang.activity.welfare.CarControlEvent;
import com.cloudy.linglingbang.app.util.PermissionUtils;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.widget.dialog.alert.FeedBackCommonAlertDialog;
import com.cloudy.linglingbang.constants.AppConstants;
import com.cloudy.linglingbang.event.OperationResultEvent;
import org.greenrobot.eventbus.EventBus;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Random;
import java.util.UUID;

/**
 * 这是原来fastble蓝牙连接框架的工具类，由于H5页面有用到一些不同的功能，不方便做测试验证，所以暂时先保留一份旧的蓝牙连接类
 */
public class BleConnectHelperOld {

    public static final String TAG = "BleConnectHelperOld";

    //授权时的蓝牙通信服务协议（充电桩）
    private static final UUID UUID_SERVICE = UUID.fromString("00000200-0000-1000-8000-00805F9B34FB");
    private static final UUID UUID_WRITE_SERVICE = UUID.fromString("00000300-0000-1000-8000-00805f9b34fb");
    private static final UUID UUID_CHARACTERISTIC_WRITE = UUID.fromString("0000ff04-0000-1000-8000-00805f9b34fb");
    private static final UUID UUID_CHARACTERISTIC_NOTIFY = UUID.fromString("0000FF03-0000-1000-8000-00805F9B34FB");

    //授权时的蓝牙通信服务协议(车控)
    private static final UUID UUID_SERVICE_AUTH = UUID.fromString("0000181A-0000-1000-8000-00805F9B34FB");
    private static final UUID UUID_CHARACTERISTIC_WRITE_AUTH = UUID.fromString("00002A6E-0000-1000-8000-00805F9B34FB");
    private static final UUID UUID_CHARACTERISTIC_NOTIFY_AUTH = UUID.fromString("00002A6F-0000-1000-8000-00805F9B34FB");

    //控制时的蓝牙通信服务协议
    private static final UUID UUID_SERVICE_CONTROL = UUID.fromString("0000182A-0000-1000-8000-00805F9B34FB");
    private static final UUID UUID_CHARACTERISTIC_WRITE_CONTROL = UUID.fromString("00002A7E-0000-1000-8000-00805F9B34FB");
    private static final UUID UUID_CHARACTERISTIC_NOTIFY_CONTROL = UUID.fromString("00002A7F-0000-1000-8000-00805F9B34FB");

    //最大传输单元
    private static final int MTU = 100;
    private static final int REQUEST_CODE_PERMISSION_LOCATION = 2;

    private static volatile BleConnectHelperOld bleConnectHelper;

    private Activity activity;

    private String bleName;
    private final StringBuilder stringBuilder = new StringBuilder();

    private ConnectStatusListener connectStatusListener;
    private ServiceWriteListener serviceWriteListener;
    private PassThroughListener passThroughListener;
    private NotifyListener notifyListener;
    private NotifyListener controlNotifyListener;
    private ServiceWriteListener controlWriteListener;
    private ScanListener scanListener;
    private BleDevice currentDevice;

    private boolean findBle = false;
    private boolean isFirstCmd = true;
    private boolean isConnected = false;
    private boolean isFirstDirect = true;
    private BleDevice lastDevice;

    private int ucuFlag = 0;//0 第一次处理 1 第二次处理
    private static String masterKey;
    private static String bleKey;
    private static String masterKeyRandom;
    private String Randomdata2;
    private String Randomdata1;
    private String cmdName;

    public static HashMap<String, String> bleErrorCodeMap = new HashMap<>();
    public static HashMap<String, String> bleParkingCodeMap = new HashMap<>();
    public static HashMap<Byte,String> e260SPErrorMsg = new HashMap<>();

    private BleDataResponseListener bleDataResponseListener;
    private ParseDataResponseListener parseDataResponseListener;

    public static String ServiceCode = "";

    public static final int UNLOCK_DOOR = 0;
    public static final int POWER_OFF = 1;
    public static final int OPEN_TAILGATE = 2;
    public static final int MODE_PARK_IN = 4;
    public static final int START_PARK = 5;
    public static final int PAUSE_PARK = 6;
    public static final int STOP_PARK = 7;
    public static final int LOCK_DOOR = 8;
    public static final int MODE_PARK_OUT = 9;
    public static final int PARK_MODE_LEFT_H = 11;
    public static final int PARK_MODE_RIGHT_H = 13;
    public static final int PARK_MODE_FORWARD = 14;
    public static final int MODE_PARK_MANUAL = 15;
    public static final int PARK_MODE_BACKWARD = 16;
    public static final int PARK_MODE_FORWARD_V = 17;
    public static final int PARK_MODE_LEFT_V = 10;
    public static final int PARK_MODE_RIGHT_V = 12;
    public static final int PARK_MODE_FORWARD_H = 18;
    public static final int PARK_MODE_LEFTWARD = 19;
    public static final int PARK_MODE_RIGHTWARD = 20;
    public static final int PARK_MODE_UP_LEFT = 21;
    public static final int PARK_MODE_UP_RIGHT = 22;
    public static final int PARK_MODE_DOWN_LEFT = 23;
    public static final int PARK_MODE_DOWN_RIGHT = 24;
    public static final int PARK_MODE_RETURN = 25;
    public static final int ONE_KEY_OUT = 26;
    public static final int START_PARK_OUT = 27;
    public static final int PAUSE_PARK_OUT = 28;
    public static final int STOP_PARK_OUT = 29;
    public static final int START_PARK_IN = 30;
    public static final int PAUSE_PARK_IN = 31;
    public static final int STOP_PARK_IN = 32;
    public static final int SET_OUT_FRONT = 33;
    public static final int SET_OUT_AFTER = 34;
    public static final int SET_OUT_LEFT = 35;
    public static final int SET_OUT_RIGHT = 36;
    public static final int STRAIGHT_FORWARD = 37;
    public static final int STRAIGHT_BACK_2 = 38;

    private final String ACC_ON = "111";
    private final String LIGHT_ON = "112";
    private final String DOOR_OPENED = "113";
    private final String B_DOOR_OPENED = "114";
    private final String CAR_RUNNING = "115";
    private final String GEAR_NOT_N = "116";
    private final String POWER_OFF_ERROR = "117";
    private final String KEY_ERROR = "118";
    private final String LOW_POWER = "119";
    private final String LR_LIGHT_ERROR = "120";
    private final String CMD_CONFLICT = "121";
    private final String WINDOW_ERROR = "122";
    private final String HORN_ERROR = "123";
    private final String POWER_ON_ERROR = "124";
    private final String UN_CHARGE = "125";
    private final String PARKING_TIME_OUT = "3007";
    private final String PARKING_OBSTACLE = "3008";
    private final String PARKING_EXCESSIVE = "3009";
    private final String PARKING_DOOR_OPENED = "300A";
    private final String PARKING_ERROR = "300C";
    private final String PARKING_FUNCTION_ERROR = "300D";
    private final String PARKING_TYPE_ERROR = "3010";
    private final String PARKING_UCU_ERROR = "300E";
    private final String CMD_POWER_OFF = "C0650003";
    private final String IGN1_OUTPUT_ERROR = "E00325";
    private final String ACC_OUTPUT_ERROR = "E00327";
    private final String VOLTAGE_EXCEED_ERROR = "E00328";
    private final String ESCL_UNLOCK_ERROR = "E03213";
    private final String START_TIMEOUT_ERROR = "E03216";
    private final String UCU_NO_RESPONSE = "80B";//UCU无响应
    private final String CRC_VERIFICATION_FAILED = "813";//CRC校验失败
    private final String BCM_NO_RESPONSE = "816";//BCM超时无响应
    private final String BLEKEY_VERIFICATION_FAILED = "81e";//用户bleKey校验失败
    private final String BLEKEY_ABSENT = "87F";//用户bleKey不存在gaw
    private final String PARKING_POWER_ERROR = "3102";
    private final String PARKING_NOT_READY = "3104";
    private final String PARKING_FAILED = "3106";
    private final String PARKING_MANUAL_ERROR = "3107";
    private final String PARKING_FUNCTION_ERROR_2 = "3108";
    private final String PARKING_TIME_OUT_2 = "3107";
    private final String PARKING_FAILED_2 = "3203";
    private final String PARKING_MANUAL_ERROR_2 = "3204";
    private final String PARKING_MODEL_ERROR = "3205";
    private final String PARKING_FUNCTION_ERROR_3 = "3206";
    private final String PARKING_TIME_OUT_3 = "3208";
    private final String PARKING_REMOTE_POWER_ERROR = "3103";
    private int connectFailCount = 0;
    private boolean mAutoConnectBle = false;
    private boolean mUserHandleConnectBle = true;//是否是用户手动连接蓝牙，是的话就显示那些loading和权限提示
    private boolean mBluetoothClosed;

    // 蓝牙通讯失败
    public static String BLE_FAIL = "FFFF";

    private static String MAC = "";
    //是否在界面上显示蓝牙收发日志
    public static boolean SHOW_LOG = false;
    // 蓝牙控制成功
    public static String BLE_CONTROL_SUCCESS = "A956";

    private byte[] socketStringBuilder = new byte[0];

    /**
     * 在HomeActivity 上显示Dialog
     */
    public final static int RESULT_DIALOG = 999;
    public final static int RESULT_FINISH = 1999;
    public final static int RESULT_MAIN_PARK = 2999;
    public final static String RESULT_DIALOG_TITLE = "result_dialog_title";
    public final static String RESULT_DIALOG_MESSAGE = "result_dialog_message";
    TravelFragment mTravelFragment;
    private String mDeviceName;
    private final TimeCount directTimer = new TimeCount(20000, 20000);
    private int isConnect = 0; //0:未连接 1:已连接 2：连接中 3：鉴权中
    private ConnectTimeOutCount mConnectTimeOutCount;


    /**
     * 智慧泊车类型：
     * 0:默认无状态;1:泊入;2:泊出;
     */
    public static int park_type = 0;

    private Handler mHandler = new Handler();
    private static String TAG_CONNECTBLUEKEY = "===连接蓝牙===";
    private static String TAG_DISCONNECTBLUEKEY = "===断开蓝牙===";
    private static String TAG_BLECONTROL = "===蓝牙车控===";
    public static void instance(Application activity) {
        bleConnectHelper = new BleConnectHelperOld(activity);
    }

    public void setActivity(Activity a) {
        activity = a;
    }

    public static BleConnectHelperOld getInstance() {
        if (bleConnectHelper == null) {
            instance(ApplicationLLB.getInstance());
        }
        return bleConnectHelper;
    }

    public BleConnectHelperOld(Application application) {
        BleManager.getInstance().init(application);
        BleManager.getInstance()
                .enableLog(true)
                .setReConnectCount(0, 2000)
                .setConnectOverTime(7000)
                .setOperateTimeout(5000);
        addMapData();
        ToastUtils.setGravity(Gravity.CENTER, 0, 0);
        BleScanRuleConfig scanRuleConfig = new BleScanRuleConfig.Builder()
                .setServiceUuids(null)      // 只扫描指定的服务的设备，可选
//                .setDeviceName(true, name)   // 只扫描指定广播名的设备，可选
                .setAutoConnect(false)      // 连接时的autoConnect参数，可选，默认false         //刚改了
                .setScanTimeOut(10000)              // 扫描超时时间，可选，默认10秒
                .build();
        BleManager.getInstance().initScanRule(scanRuleConfig);
    }

    /**
     * 泊车状态
     */
    public static String BLE_PARKING_STATE = "30";
    public static String BLE_PARKING_OUT_STATE = "31";
    public static String BLE_PARKING_STATE2 = "32";
    public static String BLE_PARKING_ERROR_STATE = "20";

    private void addMapData() {
        //蓝牙泊车错误码
        addBleParkingCode();

        bleErrorCodeMap.put(ACC_ON, "车辆未熄火");
        bleErrorCodeMap.put(LIGHT_ON, "车灯未关闭");
        bleErrorCodeMap.put(POWER_OFF_ERROR, "设置防盗失败");
        bleErrorCodeMap.put(UN_CHARGE, "车辆未充电");
        bleErrorCodeMap.put(POWER_ON_ERROR, "解除防盗失败");
        bleErrorCodeMap.put(HORN_ERROR, "喇叭异常");
        bleErrorCodeMap.put(WINDOW_ERROR, "车窗升降异常");
        bleErrorCodeMap.put(CMD_CONFLICT, "操作过于频繁，请稍后重试");
        bleErrorCodeMap.put(LR_LIGHT_ERROR, "转向灯异常");
        bleErrorCodeMap.put(LOW_POWER, "小电瓶电压低");
        bleErrorCodeMap.put(KEY_ERROR, "智能钥匙异常");
        bleErrorCodeMap.put(GEAR_NOT_N, "档位不在N或P档");
        bleErrorCodeMap.put(DOOR_OPENED, "车门未关紧");
        bleErrorCodeMap.put(B_DOOR_OPENED, "后备箱未关紧");
        bleErrorCodeMap.put(CAR_RUNNING, "车辆行驶中");

        bleErrorCodeMap.put("00", "操作失败，请稍后重试");
        bleErrorCodeMap.put("05", "数据长度错误");
        bleErrorCodeMap.put("07", "操作过于频繁，请稍后重试");
        bleErrorCodeMap.put("08", "指令超时");
        bleErrorCodeMap.put("0B", "UCU 无响应");
        bleErrorCodeMap.put("13", "CRC 校验失败");
        bleErrorCodeMap.put("16", "BCM 超时无响应");
        bleErrorCodeMap.put("1E", "用户 bleKey 校验失败");
        bleErrorCodeMap.put("7F", "用户 bleKey 不存在");

        bleErrorCodeMap.put("01", "整车电源未关闭，不能进行此项操作");
        bleErrorCodeMap.put("02", "车门未锁，不能进行此项操作");
        bleErrorCodeMap.put("03", "钥匙未学习，请进站检查");
        bleErrorCodeMap.put("04", "钥匙配置错误，请进站检查");
        bleErrorCodeMap.put(IGN1_OUTPUT_ERROR, "系统错误，请进站检查");
        bleErrorCodeMap.put("06", "系统错误，请进站检查");
        bleErrorCodeMap.put(ACC_OUTPUT_ERROR, "系统错误，请进站检查");
        bleErrorCodeMap.put(VOLTAGE_EXCEED_ERROR, "电压过高，请稍后重试");
        bleErrorCodeMap.put("09", "远程认证失败，请稍后重试");
        bleErrorCodeMap.put("10", "发动机防盗认证失败，请稍后重试");
        bleErrorCodeMap.put("11", "车辆配置错误，请进站检查");
        bleErrorCodeMap.put("12", "请上车启动车辆重新激活该功能");
        bleErrorCodeMap.put(ESCL_UNLOCK_ERROR, "电子转向锁解锁失败，请进站维修");
        bleErrorCodeMap.put("14", "车辆未响应，请稍后重试");
        bleErrorCodeMap.put("15", "车门未关，不能进行此项操作");
        bleErrorCodeMap.put(START_TIMEOUT_ERROR, "发动机未启动，请再次尝试");
        bleErrorCodeMap.put("18", "发动机异常熄火，请稍后重试");
        bleErrorCodeMap.put("19", "系统错误，请进站检查");
        bleErrorCodeMap.put("1A", "防盗报警中，不能进行此项操作");
        bleErrorCodeMap.put("1B", "系统错误，请进站检查");
        bleErrorCodeMap.put("1C", "大灯开关处于非OFF档");
        bleErrorCodeMap.put("1D", "系统错误，请进站检查");
        bleErrorCodeMap.put("20", "远程认证失败，请稍后重试");
        bleErrorCodeMap.put("21", "车辆档位不在P挡，不能进行此项操作");
        bleErrorCodeMap.put("22", "整车不在远程刷新模式，请稍后重试");
        bleErrorCodeMap.put("26", "油量不足，不能进行此项操作");
        bleErrorCodeMap.put("27", "动力电池电量过低，不能进行此项操作");
        bleErrorCodeMap.put("28", "车辆未响应，请稍后重试");
        bleErrorCodeMap.put("2B", "天窗故障，请进站检查");
        bleErrorCodeMap.put("2F", "哎呀，刚开小差了，再试试吧");
        bleErrorCodeMap.put("3C", "任务已完成，发动机已关闭");
        bleErrorCodeMap.put("41", "危险报警灯未关闭，不能进行此项操作");
        bleErrorCodeMap.put("43", "前舱盖未关好，不能进行此项操作");
        bleErrorCodeMap.put("50", "远程认证失败，请稍后重试");
        bleErrorCodeMap.put("51", "远程切换本地模式失败，请在车内重新启动车辆");
        bleErrorCodeMap.put("52", "空调未启动，请稍后重试");
        bleErrorCodeMap.put("53", "功能未开启，请稍后重试");
        bleErrorCodeMap.put("54", "请锁车后再使用此项功能");
        bleErrorCodeMap.put("55", "车辆行驶过程中，不能进行此项操作");
        bleErrorCodeMap.put("56", "手刹未拉，不能进行此项操作");
        bleErrorCodeMap.put("57", "刹车踏板被踩下，不能进行此项操作");
        bleErrorCodeMap.put("58", "油门踏板被踩下，不能进行此项操作");
        bleErrorCodeMap.put("59", "一键启动开关信号异常，不能进行此项操作");
        bleErrorCodeMap.put("5A", "车辆通讯错误，请稍后重试");
        bleErrorCodeMap.put("5B", "远程认证失败，请稍后重试");
        bleErrorCodeMap.put("5C", "暂时不能获取控制状态，请稍后重试");
        bleErrorCodeMap.put("5D", "车内通讯未响应，请稍后重试");
        bleErrorCodeMap.put("5E", "车内通讯未响应，请稍后重试");
        bleErrorCodeMap.put("5F", "车内通讯未响应，请稍后重试");
        bleErrorCodeMap.put("71", "车内通讯未响应，请稍后重试");
        bleErrorCodeMap.put("72", "车内通讯未响应，请稍后重试");
        bleErrorCodeMap.put("73", "车内通讯未响应，请稍后重试");
        bleErrorCodeMap.put("74", "车内通讯未响应，请稍后重试");
        bleErrorCodeMap.put("75", "通讯失败，请稍后重试");
        bleErrorCodeMap.put("77", "计时超时，请稍后重试");
        bleErrorCodeMap.put("FF", "功能不支持");

        e260SPErrorMsg.put((byte) 0x01,"点火档位不在OFF档");
        e260SPErrorMsg.put((byte) 0x02,"车未上锁");
        e260SPErrorMsg.put((byte) 0x03,"PEPS 未学习");
        e260SPErrorMsg.put((byte) 0x04,"PEPS 远程 功能未使能");
        e260SPErrorMsg.put((byte) 0x05,"IGN1 输 出 失败");
        e260SPErrorMsg.put((byte) 0x06,"START 输出 故障");
        e260SPErrorMsg.put((byte) 0x07,"ACC、IGN2 输出失败");
        e260SPErrorMsg.put((byte) 0x08,"电 压 超 出 范围");
        e260SPErrorMsg.put((byte) 0x09,"远程认证 失败");
        e260SPErrorMsg.put((byte) 0x10,"IMMO 认证失败");
        e260SPErrorMsg.put((byte) 0x11,"非自动挡车型");
        e260SPErrorMsg.put((byte) 0x12,"远程上高压失败次数超过阈值（预留）");
        e260SPErrorMsg.put((byte) 0x13,"ESCL 不 能 解锁");
        e260SPErrorMsg.put((byte) 0x14,"PEPS 超时");
        e260SPErrorMsg.put((byte) 0x15,"车门未关");
        e260SPErrorMsg.put((byte) 0x16,"启动尝试超时");
        e260SPErrorMsg.put((byte) 0x18,"存在发动机熄火条件");
        e260SPErrorMsg.put((byte) 0x19,"电机故障");
        e260SPErrorMsg.put((byte) 0x1A,"防盗报警触发");
        e260SPErrorMsg.put((byte) 0x1B,"发生碰撞");
        e260SPErrorMsg.put((byte) 0x1C,"大灯开关处于非 OFF 档");
        e260SPErrorMsg.put((byte) 0x1D,"动力电池故障");
        e260SPErrorMsg.put((byte) 0x20,"鉴权请求 ID 无效");
        e260SPErrorMsg.put((byte) 0x21,"整车档位不在P 档");
        e260SPErrorMsg.put((byte) 0x22,"整车不在远程刷新模式");
        e260SPErrorMsg.put((byte) 0x26,"燃油量低");
        e260SPErrorMsg.put((byte) 0x27,"动力电池SOC 过低");
        e260SPErrorMsg.put((byte) 0x28,"ECM 应答超时");
        e260SPErrorMsg.put((byte) 0x2B,"天窗节点错误");
        e260SPErrorMsg.put((byte) 0x2F,"未知原因");
        e260SPErrorMsg.put((byte) 0x3C,"远 程 上 高 压控制状态超时熄火");
        e260SPErrorMsg.put((byte) 0x41,"危 险 报 警 灯触发");
        e260SPErrorMsg.put((byte) 0x43,"前舱盖未关");
        e260SPErrorMsg.put((byte) 0x50,"控制请求未定义");
        e260SPErrorMsg.put((byte) 0x51,"模式切换");
        e260SPErrorMsg.put((byte) 0x52,"空调故障");
        e260SPErrorMsg.put((byte) 0x53,"座椅加热失败");
        e260SPErrorMsg.put((byte) 0x54,"整车不处于防盗状态");
        e260SPErrorMsg.put((byte) 0x55,"车 速 大 于2km/h 或信号无效");
        e260SPErrorMsg.put((byte) 0x56,"电子手 刹或机械手刹未拉起");
        e260SPErrorMsg.put((byte) 0x57,"刹车踏板踩下");
        e260SPErrorMsg.put((byte) 0x58,"油门踏板踩下");
        e260SPErrorMsg.put((byte) 0x59,"SSB 开关按下");
        e260SPErrorMsg.put((byte) 0x5A,"CAN 总 线BUS OFF");
        e260SPErrorMsg.put((byte) 0x5B,"鉴权状态超出范围");
        e260SPErrorMsg.put((byte) 0x5C,"远程上高压控制状态错误");
        e260SPErrorMsg.put((byte) 0x5D,"手刹信号丢失");
        e260SPErrorMsg.put((byte) 0x5E,"车速信号丢失");
        e260SPErrorMsg.put((byte) 0x5F,"档位信号丢失");
        e260SPErrorMsg.put((byte) 0x71,"油门踏板信号丢 失");
        e260SPErrorMsg.put((byte) 0x72,"燃油量信号丢失");
        e260SPErrorMsg.put((byte) 0x73,"空调远 程控制失败，信号丢失");
        e260SPErrorMsg.put((byte) 0x74,"远程 上高压失败，信号丢失");
        e260SPErrorMsg.put((byte) 0x75,"通讯失败");
        e260SPErrorMsg.put((byte) 0x77,"1.5 小时计时超时");
        e260SPErrorMsg.put((byte) 0xFF,"功能不支持");
    }

    /**
     * 蓝牙泊车错误码（提取出来一个方法）
     */
    private void addBleParkingCode(){
        bleParkingCodeMap.put(PARKING_TIME_OUT, "遥控泊车超时");
        bleParkingCodeMap.put(PARKING_OBSTACLE, "路径内有障碍物");
        bleParkingCodeMap.put(PARKING_EXCESSIVE, "规划次数过多");
        bleParkingCodeMap.put(PARKING_DOOR_OPENED, "请关好车门");
        bleParkingCodeMap.put(PARKING_ERROR, "指令执行错误");
        bleParkingCodeMap.put(PARKING_FUNCTION_ERROR, "功能故障");
        bleParkingCodeMap.put(PARKING_TYPE_ERROR, "泊出方式，泊出类型错误");
        bleParkingCodeMap.put(PARKING_UCU_ERROR, "网络延迟，请稍后重试");

        //以下是参考宝骏汽车新增过来的错误码
        bleParkingCodeMap.put(PARKING_POWER_ERROR, "未上高压");
        bleParkingCodeMap.put(PARKING_REMOTE_POWER_ERROR, "未进入遥控上电");
        bleParkingCodeMap.put(PARKING_NOT_READY, "功能未就绪");
        bleParkingCodeMap.put(PARKING_FAILED, "出库失败");
        bleParkingCodeMap.put(PARKING_MANUAL_ERROR, "人工接管，出库失败");
        bleParkingCodeMap.put(PARKING_FUNCTION_ERROR_2, "功能不可用");
        bleParkingCodeMap.put(PARKING_TIME_OUT_2, "一键泊出超时");
        bleParkingCodeMap.put(PARKING_FAILED_2, "泊车失败");
        bleParkingCodeMap.put(PARKING_MANUAL_ERROR_2, "人工接管");
        bleParkingCodeMap.put(PARKING_MODEL_ERROR, "车外泊车模式不可用");
        bleParkingCodeMap.put(PARKING_FUNCTION_ERROR_3, "功能不可用");
        bleParkingCodeMap.put(PARKING_TIME_OUT_3, "一键泊入超时");
    }

    private void showDialog(String url, String msg, String errMsg) {
        if (activity == null) { return; }
        FeedBackCommonAlertDialog dialog = new FeedBackCommonAlertDialog(activity, msg, "我知道了", "一键反馈", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
            }
        }, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                EventBus.getDefault().post(new CarControlEvent(1, "近控：  " + url + errMsg + errMsg));
            }
        });
        if (!activity.isDestroyed() && !dialog.isShowing()) {
            dialog.show();
        }
    }

    /**
     * BLE是否已经连接并已鉴权
     * 新增对 Randomdata1 和 Randomdata2 的检查
     *
     * @return
     */
    public boolean isConnectBleWithAuth() {
        return isConnectBle() && isAuthSuccess;
    }

    private boolean isAuthSuccess = false;

    @SuppressLint("LongLogTag")
    private void startScan(String deviceName) {
        findBle = false;
        BleManager.getInstance().scan(new BleScanCallback() {

            @Override
            public void onScanStarted(boolean success) {
                scanListener.onScanStart();
                Log.e(TAG, TAG_CONNECTBLUEKEY+"onScanStarted: " + success);
            }

            @Override
            public void onLeScan(BleDevice bleDevice) {
                super.onLeScan(bleDevice);
            }

            @Override
            public void onScanning(BleDevice bleDevice) {
                Log.e(TAG, TAG_CONNECTBLUEKEY+"onScanning: " + bleDevice.getName());
                if (bleDevice.getName() != null && bleDevice.getName().contains(deviceName)) {
                    if (!BleManager.getInstance().isConnected(bleDevice)) {
                        connectSocketBle(bleDevice);
                    }
                    BleManager.getInstance().cancelScan();
                    findBle = true;
                }
            }

            @Override
            public void onScanFinished(List<BleDevice> scanResultList) {
                Log.e(TAG, TAG_CONNECTBLUEKEY+"onScanFinished: " + scanResultList.toString() + " | findBle:"+findBle);
                if (findBle) {
                    scanListener.onScanFinish("搜索结束，开始链接蓝牙设备");
                } else {
                    scanListener.onScanFinish("未搜索到蓝牙信号");
                }
            }
        });
    }

    /**
     * 设置最大传输单元 默认20
     *
     * @param bleDevice
     */
    @SuppressLint("LongLogTag")
    private void setMtu(BleDevice bleDevice) {
        BleManager.getInstance().setMtu(bleDevice, MTU, new BleMtuChangedCallback() {
            @Override
            public void onSetMTUFailure(BleException exception) {
                Log.e(TAG, TAG_CONNECTBLUEKEY+"==设置最大传输单元===onsetMTUFailure：" + exception.toString());
            }

            @Override
            public void onMtuChanged(int mtu) {
                Log.e(TAG, TAG_CONNECTBLUEKEY+"==设置最大传输单元===onMtuChanged: " + mtu);
                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            openCarNotify(bleDevice);
                        } catch (Exception e) {
                            e.printStackTrace();
                            Log.e("openNotify", "鉴权过程崩溃了 " + e);
                        }
                    }
                }, 100);
            }
        });
    }

    //筛选MAC地址链接
    public void startConnect(int n) {
        Log.e(TAG, TAG_BLECONTROL+"===sendControl===start===n::   " + n + " | isConnectBleWithAuth:"+isConnectBleWithAuth());
        if (isConnectBleWithAuth()) {
            for (BleDevice d : BleManager.getInstance().getAllConnectedDevice()) {
                Log.e(TAG, TAG_BLECONTROL+",sendControl,getAllConnectedDevice,d=" + d + ",getName=" + d.getName() + ",getMac="+d.getMac() + ",MAC="+MAC+",n="+n);
                if (d.getMac().equals(MAC)) {
                    switch (n) {
                        case UNLOCK_DOOR:
                            sendControl(d, BleControlCode.OPEN_DOOR);//开门
                            break;
                        case POWER_OFF:
                            sendControl(d, BleControlCode.POWER_OFF);//下电
                            break;
                        case OPEN_TAILGATE:
                            sendControl(d, BleControlCode.OPEN_TAILGATE);//开尾门
                            break;
                        case MODE_PARK_IN:
                            sendControl(d, BleControlCode.MODE_PARK_IN); //遥控模式
                            break;
                        case START_PARK:
                            sendControl(d, BleControlCode.START_PARK);//开始泊车
                            break;
                        case PAUSE_PARK:
                            sendControl(d, BleControlCode.PAUSE_PARK);//暂停泊车
                            break;
                        case STOP_PARK:
                            sendControl(d, BleControlCode.STOP_PARK);//终止泊车
                            break;
                        case LOCK_DOOR:
                            sendControl(d, BleControlCode.CLOSE_DOOR);//关门
                            break;
                        case MODE_PARK_OUT:
                            sendControl(d, BleControlCode.MODE_PARK_OUT);//遥控泊出
                            break;
                        case PARK_MODE_LEFT_V:
                            sendControl(d, BleControlCode.PARK_MODE_LEFT_V);//泊出方式 左 垂直
                            break;
                        case PARK_MODE_LEFT_H:
                            sendControl(d, BleControlCode.PARK_MODE_LEFT_H);//泊出方式 左 水平 ***
                            break;
                        case PARK_MODE_RIGHT_V:
                            sendControl(d, BleControlCode.PARK_MODE_RIGHT_V);//泊出方式 右 垂直
                            break;
                        case PARK_MODE_RIGHT_H:
                            sendControl(d, BleControlCode.PARK_MODE_RIGHT_H);//泊出方式 右 水平 ***
                            break;
                        case PARK_MODE_FORWARD:
                            sendControl(d, BleControlCode.PARK_MODE_FORWARD);//泊出模式 向前
                            break;
                        case MODE_PARK_MANUAL:
                            sendControl(d, BleControlCode.MODE_PARK_MANUAL);//泊出模式 手动泊车
                            break;
                        case PARK_MODE_BACKWARD:
                            sendControl(d, BleControlCode.PARK_MODE_BACKWARD);//后退
                            break;
                        case PARK_MODE_FORWARD_V:
                            sendControl(d, BleControlCode.PARK_MODE_FORWARD_V);//泊出方式 前 垂直 ***
                            break;
                        case PARK_MODE_FORWARD_H:
                            sendControl(d, BleControlCode.PARK_MODE_FORWARD_H);//泊出方式 前 水平
                            break;
                        case PARK_MODE_LEFTWARD:
                            sendControl(d, BleControlCode.PARK_MODE_LEFTWARD);//向左
                            break;
                        case PARK_MODE_RIGHTWARD:
                            sendControl(d, BleControlCode.PARK_MODE_RIGHTWARD);//向右
                            break;
                        case PARK_MODE_UP_LEFT:
                            sendControl(d, BleControlCode.PARK_MODE_UP_LEFT);//左前
                            break;
                        case PARK_MODE_UP_RIGHT:
                            sendControl(d, BleControlCode.PARK_MODE_UP_RIGHT);//右前
                            break;
                        case PARK_MODE_DOWN_LEFT:
                            sendControl(d, BleControlCode.PARK_MODE_BACK_LEFT);//左后
                            break;
                        case PARK_MODE_DOWN_RIGHT:
                            sendControl(d, BleControlCode.PARK_MODE_BACK_RIGHT);//右后
                            break;
                        case PARK_MODE_RETURN:
                            sendControl(d, BleControlCode.PARK_MODE_RETURN);//回正
                            break;
                        case ONE_KEY_OUT:
                            sendControl(d, BleControlCode.MODE_PARK_OUT_2);//一键泊出
                            break;
                        case START_PARK_OUT:
                            sendControl(d, BleControlCode.START_OUT_2);//开始一键泊出
                            break;
                        case PAUSE_PARK_OUT:
                            sendControl(d, BleControlCode.PAUSE_OUT_2);//暂停一键泊出
                            break;
                        case STOP_PARK_OUT:
                            sendControl(d, BleControlCode.STOP_OUT_2);//终止一键泊出
                            break;
                        case START_PARK_IN:
                            sendControl(d, BleControlCode.START_PARK_2);//开始一键泊入
                            break;
                        case PAUSE_PARK_IN:
                            sendControl(d, BleControlCode.PAUSE_PARK_2);//暂停一键泊入
                            break;
                        case STOP_PARK_IN:
                            sendControl(d, BleControlCode.STOP_PARK_2);//终止一键泊入
                            break;
                        case SET_OUT_FRONT:
                            sendControl(d, BleControlCode.SET_FRONT_2);//设置向前泊出
                            break;
                        case SET_OUT_AFTER:
                            sendControl(d, BleControlCode.SET_AFTER_2);//设置向后泊出
                            break;
                        case SET_OUT_LEFT:
                            sendControl(d, BleControlCode.SET_LEFT_2);//设置向左泊出
                            break;
                        case SET_OUT_RIGHT:
                            sendControl(d, BleControlCode.SET_RIGHT_2);//设置向右泊出
                            break;
                        case STRAIGHT_FORWARD:
                            sendControl(d, BleControlCode.STRAIGHT_FORWARD_2);//直线前进
                            break;
                        case STRAIGHT_BACK_2:
                            sendControl(d, BleControlCode.STRAIGHT_BACK_2);//直线后退
                        default:
                            ToastUtils.showShort("蓝牙已连接");
                            break;
                    }
                }
            }
        } else {
            Log.e(TAG, "sendControl:   " + n);
            startConnect(false, true, null, mTravelFragment);
        }
    }

    public void startConnect() {
        startConnect(false, true, null, mTravelFragment);
    }

    /**
     * @param autoConnectBle 是否是后台自动帮用户连接蓝牙，是自动连接的话，没有权限的话不提示用户授予权限，和那些loading和提示都不展示给用户
     */
    public void startConnect(boolean autoConnectBle, boolean userHandleConnectBle, String deviceName, Fragment fragment) {
        connectFailCount = 0;
        mAutoConnectBle = autoConnectBle;
        mUserHandleConnectBle = userHandleConnectBle;
        try {
            Log.e(TAG, "startConnect: " + MAC + " | autoConnectBle：" + autoConnectBle + " | userHandleConnectBle:" + userHandleConnectBle);
            if (TextUtils.isEmpty(MAC)) {
                BleConnectHelperOld.setParams(BlueKeyUtil.getInstance().getKeyEntity());
            }
            if (activity != null) {
                checkPermissions(deviceName, fragment);
            }

        } catch (Exception e) {
            e.printStackTrace();
            Log.d(TAG, TAG_CONNECTBLUEKEY+"startConnect----Exception: " + e.toString());
        }
    }

    public void startConnect(Fragment fragment) {
        startConnect(false, true, null, fragment);
    }

    public void startConnect(String deviceName) {
        startConnect(false, true, deviceName, mTravelFragment);
    }

    //外部链接监听
    public void setConnectStatusListener(ConnectStatusListener listener) {
        connectStatusListener = listener;
    }

    //授权写入监听
    public void setServiceWriteListener(ServiceWriteListener listener) {
        serviceWriteListener = listener;
    }

    public void setPassThroughListener(PassThroughListener listener) {
        passThroughListener = listener;
    }

    public void setNotifyListener(NotifyListener listener) {
        notifyListener = listener;
    }

    public void setControlNotifyListener(NotifyListener listener) {
        controlNotifyListener = listener;
    }

    public void setControlWriteListener(ServiceWriteListener controlWriteListener) {
        this.controlWriteListener = controlWriteListener;
    }

    public void setScanListener(ScanListener scanListener) {
        this.scanListener = scanListener;
    }

    private List<NotifyListener> mNotifyListeners;

    /**
     * 设置车控的监听。支持多个
     */
    public void addNotifyListener(NotifyListener listener) {
        if (mNotifyListeners == null) {
            mNotifyListeners = new ArrayList<>();
        }
        Log.e(TAG, "==设置车控的监听===addNotifyListener===" + listener.toString());
        mNotifyListeners.add(listener);
    }

    /**
     * 移除车控的监听
     */
    public void removeNotifyListener(NotifyListener listener) {
        Log.e(TAG, "==移除车控的监听===removeNotifyListener===" + listener.toString());
        if (mNotifyListeners != null && mNotifyListeners.size() > 0) {
            mNotifyListeners.remove(listener);
        }
    }

    /**
     * 链接蓝牙设备
     *
     * @param bleDevice
     */
    private void connectSocketBle(final BleDevice bleDevice) {
        BleManager.getInstance().connect(bleDevice, new BleGattCallback() {
            @SuppressLint("LongLogTag")
            @Override
            public void onStartConnect() {
                if (connectStatusListener != null) {
                    connectStatusListener.startConnect();
                    Log.e(TAG, TAG_CONNECTBLUEKEY+"=====startConnect=====");
                }
            }

            @Override
            public void onConnectFail(BleDevice bleDevice, BleException exception) {
                Log.e(TAG, TAG_CONNECTBLUEKEY+"onConnectFail: " + bleDevice.getName() + exception.toString());
                findBle = false;
                if (connectStatusListener != null) {
                    connectStatusListener.onConnectFail(exception.toString(), "");
                    BleManager.getInstance().destroy();
                }
            }

            @Override
            public void onConnectSuccess(final BleDevice bleDevice, final BluetoothGatt gatt, int status) {
                Log.e(TAG, TAG_CONNECTBLUEKEY+"onConnectSuccess: " + bleDevice.getName());
                findBle = true;
                currentDevice = bleDevice;
                lastDevice = bleDevice;
//                setMtu(bleDevice);
                if (connectStatusListener != null) {
                    connectStatusListener.onConnectSuccess(bleDevice, gatt);
                }
                new Thread() {
                    @Override
                    public void run() {
                        super.run();
                        try {
                            sleep(100);
                            openSocketNotify(bleDevice, gatt);
//                            indicator(bleDevice,gatt);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }

                    }
                }.start();
            }

            @SuppressLint("LongLogTag")
            @Override
            public void onDisConnected(boolean isActiveDisConnected, BleDevice bleDevice, BluetoothGatt gatt, int status) {
                if (isActiveDisConnected) {
                    Log.e(TAG, TAG_CONNECTBLUEKEY+"onDisConnected === : 主动断开蓝牙连接");
                }
                connectStatusListener.DisConnect(isActiveDisConnected);
                isFirstCmd = true;
                isFirstDirect = true;
                findBle = false;
                if (currentDevice != null && currentDevice.getName() != null && currentDevice.getName().equals(bleDevice.getName())) {
                    currentDevice = null;
                }
            }
        });
    }

    private void connectCmdBle() {
        //vivo手机在连接前，需要进行扫描操作，否则将无法连接。
        //解决方案：连接前直接进行一次扫描调用，空操作。
        if (BleManager.getInstance().getScanSate() == BleScanState.STATE_IDLE)
            findBle = false;
            BleManager.getInstance().scan(new BleScanCallback() {
                @Override
                public void onScanFinished(List<BleDevice> scanResultList) {
                    Log.e(TAG, TAG_CONNECTBLUEKEY+"====扫描蓝牙====onScanFinished===扫描结束===findBle: " + findBle);
                    if (findBle) {
                        scanListener.onScanFinish("搜索结束，开始链接蓝牙设备");
                    } else {
                        scanListener.onScanFinish("未搜索到蓝牙信号");
                    }
                }

                @Override
                public void onScanStarted(boolean success) {
                    Log.e(TAG, TAG_CONNECTBLUEKEY+"====扫描蓝牙====onScanStarted======success: " + success);
                }

                @Override
                public void onScanning(BleDevice bleDevice) {
//                    Log.e(TAG, "====扫描蓝牙====onScanning====扫描到到蓝牙广播===MAC："+MAC + "------bleDevice.getMac:"+bleDevice.getMac()+"------");
                    if (!StringUtils.nullStrOrEmpty(MAC) && bleDevice.getMac().equals(MAC)) {
                        String scanRecord = "";
                        if (!StringUtils.nullStrOrEmpty(bleDevice.getScanRecord()+"")) {
                            scanRecord = HexUtil.formatHexString(bleDevice.getScanRecord());
                        }
                        Log.e(TAG, TAG_CONNECTBLUEKEY+"====扫描蓝牙====onScanning====扫描到了对应的mac地址蓝牙广播===MAC："+MAC + " | bleDevice.getName:"+bleDevice.getName()+ " | bleDevice.getScanRecord:"+scanRecord+ " | bleDevice.getRssi:"+bleDevice.getRssi()+ " | bleDevice.getTimestampNanos:"+bleDevice.getTimestampNanos());
                    }

            }

                @Override
                public void onLeScan(BleDevice bleDevice) {
//                    Log.e(TAG, "====扫描蓝牙====onLeScan====扫描到到蓝牙广播===MAC："+MAC + "------bleDevice.getMac:"+bleDevice.getMac()+"------");
                    if (!StringUtils.nullStrOrEmpty(bleDevice.getMac()) && bleDevice.getMac().equals(MAC)) {
                        String scanRecord = "";
                        if (!StringUtils.nullStrOrEmpty(bleDevice.getScanRecord()+"")) {
                            scanRecord = HexUtil.formatHexString(bleDevice.getScanRecord());
                        }
                        Log.e(TAG, TAG_CONNECTBLUEKEY+"====扫描蓝牙====onLeScan====扫描到了对应的mac地址蓝牙广播===MAC："+MAC + " | bleDevice.getName:"+bleDevice.getName()+ " | bleDevice.getScanRecord:"+scanRecord+ " | bleDevice.getRssi:"+bleDevice.getRssi()+ " | bleDevice.getTimestampNanos:"+bleDevice.getTimestampNanos());
                    }

            }
        });

        if (!TextUtils.isEmpty(MAC) && !MAC.equals("null")) {
            BleManager.getInstance().connect(MAC, new BleGattCallback() {
                @Override
                public void onStartConnect() {
                    Log.e(TAG,TAG_CONNECTBLUEKEY+"======onStartConnect==开始连接蓝牙==MAC：" + MAC + " | mAutoConnectBle:"+mAutoConnectBle);
                    isConnect = 2;
                    if (connectStatusListener != null) {
                        connectStatusListener.startConnect();
                    }
                }

                @Override
                public void onConnectFail(BleDevice bleDevice, BleException exception) {
                    isConnect = 0;
                    isAuthSuccess = false;
                    connectFailCount++;
                    ucuFlag = 0;
                    findBle = false;
                    BluetoothDevice device = bleDevice.getDevice();
                    try {
                        Log.e(TAG,TAG_CONNECTBLUEKEY+"===onConnectFail===连接蓝牙失败，AutoConnectBle：" + mAutoConnectBle + " | mUserHandleConnectBle:" + mUserHandleConnectBle + " | exception:" + exception.toString() + " | bleDevice.getMac："+bleDevice.getMac() + " | currentMAC:"+MAC+ " | bleDevice.getDevice():"+device + " | currentDevice:"+currentDevice);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    if (device != null ) {
                        Log.e(TAG, TAG_CONNECTBLUEKEY+"===onConnectFail===连接蓝牙失败====Device().getBondState: " + device.getBondState());
                        if (device.getBondState() == BluetoothDevice.BOND_BONDED) {
                            Log.e(TAG, TAG_CONNECTBLUEKEY+"===onConnectFail===连接蓝牙失败====当前蓝牙仍然绑定着未断开");
                        }
                    }

                    if (currentDevice != null) {
                        Log.e(TAG, TAG_CONNECTBLUEKEY+"===onConnectFail===连接蓝牙失败====currentDevice.getDevice().getAddress(): " + currentDevice.getDevice().getAddress() + " | currentDevice.getMac()："+currentDevice.getMac());
                        BluetoothGatt bluetoothGatt = BleManager.getInstance().getBluetoothGatt(currentDevice);
                        Log.e(TAG, TAG_CONNECTBLUEKEY+"===onConnectFail===连接蓝牙失败===bluetoothGatt: " + bluetoothGatt);
                        BleManager.getInstance().destroy();
                    } else {
                        if (mUserHandleConnectBle) {
                            mUserHandleConnectBle = false;
                            mAutoConnectBle = true;
                            if (connectStatusListener != null) {
                                connectStatusListener.onConnectFail(exception.toString(), "");
                                connectFailCount = 0;
                            }
                        } else {
                            if (mAutoConnectBle) {
                                mUserHandleConnectBle = false;
                                //如果是app后台自动帮用户连接蓝牙的操作，出现了异常就自动重连，直到成功或者用户杀死app为止，并且后台连接过程中的一些错误不提示给用户
                                if (connectStatusListener != null) {
                                    connectStatusListener.onConnectFail(exception.toString(), "");
                                }
                            } else {
                                //连接失败时进行两次重连，为解决一下手机的特定问题：
                                //Timeout Exception Occurred 小米连接问题：关闭蓝牙重新打开时，第一次连接必定超时。
                                //BleManager自带的重连机制并不包括超时情况，故手动实现。
                                if (connectFailCount <= 2 && exception.getDescription().contains("Timeout Exception Occurred")) {
                                    connectCmdBle();
                                } else {
                                    if (connectStatusListener != null) {
                                        connectStatusListener.onConnectFail(exception.toString(), "");
                                        connectFailCount = 0;
                                    }
                                }
                            }
                        }
                    }
                }

                @Override
                public void onConnectSuccess(BleDevice bleDevice, BluetoothGatt gatt, int status) {
                    try {
                        LogUtils.e(TAG_CONNECTBLUEKEY+"===onConnectSuccess===连接蓝牙成功====status:"+status + " | currentMAC:"+MAC + " | bleDevice.getMac:"+bleDevice.getMac() + " | bleDevice:"+bleDevice.toString() + " | gatt:"+gatt.toString());
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    currentDevice = bleDevice;
                    connectFailCount = 0;
                    findBle = true;
                    if (connectStatusListener != null) {
                        connectStatusListener.onConnectSuccess(bleDevice, gatt);
                    }
                    setMtu(bleDevice);
                }

                @Override
                public void onDisConnected(boolean isActiveDisConnected, BleDevice device, BluetoothGatt gatt, int status) {
                    LogUtils.e(TAG_CONNECTBLUEKEY+"====onDisConnected===蓝牙断开连接===isActiveDisConnected:"+isActiveDisConnected + " | status:"+status+ " | gatt.getMac:"+device.getMac());
                    isAuthSuccess = false;
                    mAutoConnectBle = false;
                    mUserHandleConnectBle = false;
                    isConnect = 0;
                    findBle = false;
                    ucuFlag = 0;
                    if (connectStatusListener != null) {
                        connectStatusListener.DisConnect(isActiveDisConnected);
                    }
                    if (currentDevice != null && currentDevice.getMac().equals(MAC)) {
                        currentDevice = null;
                    }
                }
            });
        }
    }

    public void bluetoothClosed(boolean bluetoothClosed) {
        mBluetoothClosed = bluetoothClosed;
        if (mBluetoothClosed && isAuthSuccess) {
            isAuthSuccess = false;
            isConnect = 0;
            if (connectStatusListener != null) {
                connectStatusListener.DisConnect(true);
            }
            disconnectDevice();
        }
    }

    public void disconnectDevices() {
        if (BleManager.getInstance() != null) {
            if (currentDevice != null) {
                BluetoothGatt bluetoothGatt = BleManager.getInstance().getBluetoothGatt(currentDevice);
                Log.e(TAG, "disconnectDevices  gatt: " + bluetoothGatt);
            }
            if (BleManager.getInstance().getScanSate() == BleScanState.STATE_SCANNING)
                BleManager.getInstance().cancelScan();
            BleManager.getInstance().disconnectAllDevice();
            Log.e(TAG, TAG_DISCONNECTBLUEKEY+"==================disconnectDevices=========主动断开蓝牙连接===========");
        }

    }

    /**
     * app向UCU发送 KEY ID --第一次通讯
     *
     * @param bleDevice
     */
    public void queryControlQuest(final BleDevice bleDevice) {
        Log.e(TAG, TAG_CONNECTBLUEKEY+"===第一次通讯,app向UCU发送发送KeyId===start===");
        BleManager.getInstance().write(
                bleDevice,
                UUID_SERVICE_AUTH.toString(),
                UUID_CHARACTERISTIC_WRITE_AUTH.toString(),
                genAuthHex(),
                false,
                new BleWriteCallback() {

                    @Override
                    public void onWriteSuccess(final int current, final int total, final byte[] justWrite) {
                        Log.e(TAG, TAG_CONNECTBLUEKEY+"===第一次通讯,app向UCU发送发送KeyId，成功===onWriteSuccess===data：" + HexUtil.formatHexString(justWrite, true));
                        if (serviceWriteListener != null) {
                            serviceWriteListener.onWriteSuccess(bleDevice, HexUtil.formatHexString(justWrite, true));
                        }
                    }

                    @Override
                    public void onWriteFailure(final BleException exception) {
                        Log.e(TAG, TAG_CONNECTBLUEKEY+"===第一次通讯,app向UCU发送发送KeyId，失败===onWriteFailure===exception：" + exception.toString());
                        if (serviceWriteListener != null) {
                            serviceWriteListener.onWriteFailure("");
                        }
                    }
                });
    }

    private void sendMessage(final BleDevice bleDevice, String message) {
        new Thread() {
            @Override
            public void run() {
                super.run();
                try {
                    sleep(500);
                    sendNormalMessage(bleDevice, message);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }

            }
        }.start();
    }

    private void sendNormalMessage(final BleDevice bleDevice, String message) {
        if (message.getBytes().length <= 2) {
            return;
        }
        BleManager.getInstance().write(
                bleDevice,
                UUID_WRITE_SERVICE.toString(),
                UUID_CHARACTERISTIC_WRITE.toString(),
                message.getBytes(),
                false,
                new BleWriteCallback() {

                    @Override
                    public void onWriteSuccess(final int current, final int total, final byte[] justWrite) {
                        Log.e(TAG, TAG_CONNECTBLUEKEY+"onWriteSuccess: " + Arrays.toString(justWrite));
                        serviceWriteListener.onWriteSuccess(bleDevice, new String(justWrite));
                        if (new String(justWrite).contains("CONNECT")) {
                            isConnected = true;
                        } else if (new String(justWrite).contains("IP ERROR")) {
                            BleManager.getInstance().disconnectAllDevice();
                        } else if (new String(justWrite).contains("BLENET=1")) {
                            directTimer.start();
                        } else if (new String(justWrite).contains("BLENET=0")) {
                            BleManager.getInstance().disconnectAllDevice();
                        }
                    }

                    @Override
                    public void onWriteFailure(final BleException exception) {
                        Log.e(TAG, TAG_CONNECTBLUEKEY+"onWriteFailure: " + exception.toString());
                        serviceWriteListener.onWriteFailure("");
                    }
                });
    }

    private void sendSocketMessage(final BleDevice bleDevice, byte[] message) {
        BleManager.getInstance().write(
                bleDevice,
                UUID_WRITE_SERVICE.toString(),
                UUID_CHARACTERISTIC_WRITE.toString(),
                message,
                false,
                new BleWriteCallback() {

                    @Override
                    public void onWriteSuccess(final int current, final int total, final byte[] justWrite) {
                        Log.e(TAG, TAG_CONNECTBLUEKEY+"onWriteSuccess: " + Arrays.toString(justWrite));
                        serviceWriteListener.onWriteSuccess(bleDevice, new String(justWrite));
                    }

                    @Override
                    public void onWriteFailure(final BleException exception) {
                        Log.e(TAG, TAG_CONNECTBLUEKEY+"onWriteFailure: " + exception.toString());
                        serviceWriteListener.onWriteFailure("");
                    }
                });
    }

    public void openSocketNotify(final BleDevice bleDevice, final BluetoothGatt bluetoothGatt) {
        BleManager.getInstance().notify(
                bleDevice,
                UUID_SERVICE.toString(),
                UUID_CHARACTERISTIC_NOTIFY.toString(),
                new BleNotifyCallback() {

                    @Override
                    public void onNotifySuccess() {
                        /**
                         * 已注册控制指令通道,代表已鉴权成功
                         */
                        Log.e(TAG, TAG_CONNECTBLUEKEY+"onControlNotifySuccess: ");
                        if (notifyListener != null) {
                            notifyListener.onNotifySuccess(bleDevice);
                        }
                    }

                    @Override
                    public void onNotifyFailure(final BleException exception) {
                        Log.e(TAG, TAG_CONNECTBLUEKEY+"onControlNotifyFailure: " + exception.toString());
                        /**
                         * 断开所有连接,准备重新连接
                         */
                        if (notifyListener != null) {
                            if (activity != null && !activity.isDestroyed()) {
                                activity.runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        notifyListener.onNotifyFailure(exception.toString());
                                    }
                                });
                            }

                        }
                    }

                    @SuppressLint("LongLogTag")
                    @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
                    @Override
                    public void onCharacteristicChanged(byte[] data) {
                        byte[] b = new byte[0];
                        if (bluetoothGatt != null)
                            b = bluetoothGatt.getService(UUID_SERVICE).getCharacteristic(UUID_CHARACTERISTIC_NOTIFY).getValue();
                        if (notifyListener != null) {
                            notifyListener.onCharacteristicChanged(b);
                            Log.e(TAG, "onCharacteristicChanged:接收数据" + Arrays.toString(data));
                        }
                        if (isFirstCmd) {
                            conformityCmd(data);
                        } else if (isConnected) {
                            conformSocketCmd(data);
                        }
                    }
                });
    }

    public void openCarNotify(final BleDevice bleDevice) {
        Log.e(TAG, "=====openNotify====第一步,注册鉴权通道==start==");
        BluetoothGatt bluetoothGatt = BleManager.getInstance().getBluetoothGatt(bleDevice);
        if (bluetoothGatt != null && bluetoothGatt.getService(UUID_SERVICE_AUTH) == null) {
            //其中有一个坑是在FastBle框架中，创建通知监听的时候会出现BleException { code=102, description='this characteristic not support notify!'}这个错误，这个是因为框架内部过早调用gatt.discoverServices()后导致gatt.getService()时为空导致的。
            // 所以先进行判空，如果为空的话则延迟一秒调用gatt.discoverServices()。之后内部会再次调用BleGattCallback中onConnectSuccess回调，在确保service不为空才进行调用BleManager.getInstance().notify()
            //判断service是否为空
            if (mHandler == null) {
                mHandler = new Handler();
            }
            Log.e(TAG, TAG_CONNECTBLUEKEY+"===openNotify===第一步,注册鉴权通道==service为空==");
            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    bluetoothGatt.discoverServices();
                }
            }, 1000);
            return;
        }
        BleManager.getInstance().notify(
                bleDevice,
                UUID_SERVICE_AUTH.toString(),
                UUID_CHARACTERISTIC_NOTIFY_AUTH.toString(),
                new BleNotifyCallback() {

                    @Override
                    public void onNotifySuccess() {
                        Log.e(TAG, TAG_CONNECTBLUEKEY+"===openCarNotify===第一步,注册鉴权通道,成功====");
                        /**
                         * 第一步,注册鉴权通道,成功
                         */
                        if (notifyListener != null) {
                            notifyListener.onNotifySuccess(bleDevice);
                        }
//                        Log.e("-----------","第一次="+bleDevice.getName()+"  "+bleDevice.getMac()+"  "+bleDevice.getKey());
                        isConnect = 3;
                        queryControlQuest(bleDevice);//1
                    }

                    @Override
                    public void onNotifyFailure(final BleException exception) {
                        Log.e(TAG, TAG_CONNECTBLUEKEY+"===openCarNotify===第一步,注册鉴权通道,失败===exception: " + exception.toString());
                        /**
                         * 注册通道失败
                         */
                        isAuthSuccess = false;
                        isConnect = 0;
                        /**
                         * 断开所有连接,准备重新连接
                         */
                        BleManager.getInstance().disconnectAllDevice();
                        if (notifyListener != null) {
                            if (activity != null && !activity.isDestroyed()) {
                                activity.runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        notifyListener.onNotifyFailure(exception.toString());
                                    }
                                });
                            }
                        }
                    }

                    @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
                    @Override
                    public void onCharacteristicChanged(byte[] data) {
                        byte[] b = new byte[0];
                        if (bluetoothGatt != null)
                            b = bluetoothGatt.getService(UUID_SERVICE_AUTH).getCharacteristic(UUID_CHARACTERISTIC_NOTIFY_AUTH).getValue();
                        Log.e(TAG, "=====onCharacteristicChanged====第一步,注册鉴权通道==鉴权返回结果：" + String.format("onAuthCharacteristicChanged: %s", HexUtil.encodeHexStr(b)));
                        if (notifyListener != null) {

                            notifyListener.onCharacteristicChanged(b);
                        }
                        if (data == null ) {
                            Log.e(TAG,TAG_CONNECTBLUEKEY+"=====onCharacteristicChanged====第一步,注册鉴权通道==data为null===");
                            return;
                        }

                        String result = Deode(data);
                        Log.e(TAG,TAG_CONNECTBLUEKEY+"=====onCharacteristicChanged====第一步,注册鉴权通道===解密鉴权返回结果："+ result);
                        if (result.length() >= 24) {
                            String service = result.substring(0, 4);
                            if ("ffff".equals(service)) {
                                String error = result.substring(12, 20);
                                notifyListener.onNotifyFailure("蓝牙连接失败，请下拉刷新重试");
                                Log.e(TAG, TAG_CONNECTBLUEKEY+"===onAuthCharacteristicChanged===第一步,注册鉴权通道===鉴权失败===错误码：" + error);
                            } else {
                                Log.e(TAG, TAG_CONNECTBLUEKEY+"===onAuthCharacteristicChanged===第一步,注册鉴权通道===CRC校验结果：" + DecodeUtils.getCRC16(result));
                                if ("0000".equals(DecodeUtils.getCRC16(result))) {
                                    Log.e(TAG, TAG_CONNECTBLUEKEY+"====onAuthCharacteristicChanged===第一步,注册鉴权通道===CRC校验结果===CRC校验成功===ucuFlag："+ucuFlag);
                                    if (ucuFlag == 0) {
                                        Randomdata1 = result.substring(16, 24);
                                        Log.e(TAG, TAG_CONNECTBLUEKEY+"====onAuthCharacteristicChanged===第一步,注册鉴权通道===CRC校验结果===CRC校验成功===Randomdata1："+Randomdata1);
                                        if (!TextUtils.isEmpty(Randomdata1)) {
                                            queryAuth(bleDevice);//2
                                            ucuFlag = 1;
                                        }
                                    } else if (ucuFlag == 1) {
                                        /**
                                         * 第二步,注册控制通道
                                         */
                                        Randomdata2 = result.substring(16, 24);
                                        Log.e(TAG, TAG_CONNECTBLUEKEY+"====onAuthCharacteristicChanged===第二步,注册控制指令通道===Randomdata2："+Randomdata2);
                                        if (!TextUtils.isEmpty(Randomdata2)) {
                                            openControlNotify(bleDevice, bluetoothGatt);
                                        } else {
                                            /**
                                             * 获取Randomdata2 失败
                                             */
                                            isAuthSuccess = false;
                                            isConnect = 0;
                                            /**
                                             * 断开所有连接,准备重新连接
                                             */
                                            if (notifyListener != null) {
                                                if (activity != null && !activity.isDestroyed()) {
                                                    activity.runOnUiThread(new Runnable() {
                                                        @Override
                                                        public void run() {
                                                            notifyListener.onNotifyFailure("蓝牙连接失败，请下拉刷新重试");
                                                        }
                                                    });
                                                }

                                            }
                                            BleManager.getInstance().disconnect(bleDevice);

                                        }
                                        ucuFlag = 0;
                                        if (activity != null && !activity.isDestroyed()) {
                                            activity.runOnUiThread(new Runnable() {
                                                @Override
                                                public void run() {
                                                    ToastUtils.showShort("蓝牙连接成功");
                                                }
                                            });
                                        }

                                    }
                                } else {

                                    /**
                                     * 获取Randomdata2 失败
                                     */
                                    isAuthSuccess = false;
                                    isConnect = 0;
                                    /**
                                     * 断开所有连接,准备重新连接
                                     */
                                    if (notifyListener != null) {
                                        if (activity != null && !activity.isDestroyed()) {
                                            activity.runOnUiThread(new Runnable() {
                                                @Override
                                                public void run() {
                                                    notifyListener.onNotifyFailure("蓝牙连接失败，请下拉刷新重试");
                                                }
                                            });
                                        }

                                    }
                                    BlueKeyUtil.getInstance().resectBleStatus();
//                                    BleManager.getInstance().disconnect(bleDevice);
                                    Log.e(TAG, "===onAuthCharacteristicChanged==第一步,注册鉴权通道===鉴权返回CRC校验失败===");
                                }
                            }
                        }
                    }
                });
    }

    /**
     * 注册控制指令通道
     *
     * @param bleDevice
     * @param bluetoothGatt
     */
    public void openControlNotify(final BleDevice bleDevice, final BluetoothGatt bluetoothGatt) {
        Log.e(TAG,"====openControlNotify====第二步,注册控制指令通道===start===");
        BleManager.getInstance().notify(
                bleDevice,
                UUID_SERVICE_CONTROL.toString(),
                UUID_CHARACTERISTIC_NOTIFY_CONTROL.toString(),
                new BleNotifyCallback() {

                    @Override
                    public void onNotifySuccess() {
                        if (BleManager.getInstance().getScanSate() == BleScanState.STATE_SCANNING) {
                            BleManager.getInstance().cancelScan();
                        }
                        stopConnectTimeOutCount();
                        isConnect = 1;
                        /**
                         * 已注册控制指令通道,代表已鉴权成功
                         */
                        isAuthSuccess = true;
                        Log.e(TAG, TAG_CONNECTBLUEKEY+"===openControlNotify===第二步,注册控制指令通道，成功===已注册控制指令通道,代表已鉴权成功，可以使用蓝牙功能了===");
                        if (controlNotifyListener != null) {
                            controlNotifyListener.onNotifySuccess(bleDevice);
                        }
                        if (mNotifyListeners != null) {
                            LogUtils.e("====onControlNotifySuccess====第二步,注册控制指令通道成功,代表已鉴权成功===mNotifyListeners.size()：" + mNotifyListeners.size());
                            for (int i = 0, z = mNotifyListeners.size(); i < z; i++) {
                                NotifyListener listener = mNotifyListeners.get(i);
                                LogUtils.e("====onControlNotifySuccess====第二步,注册控制指令通道成功,代表已鉴权成功===listener：" + listener);
                                if (listener != null) {
                                    listener.onNotifySuccess(bleDevice);
                                }
                            }
                        }
                    }

                    @Override
                    public void onNotifyFailure(final BleException exception) {
                        isConnect = 0;
                        Log.e(TAG, TAG_CONNECTBLUEKEY+"===openControlNotify===第二步,注册控制指令通道，失败===exception：" + exception.toString());
                        /**
                         * 注册控制指令通道失败
                         */
                        isAuthSuccess = false;
                        /**
                         * 断开所有连接,准备重新连接
                         */
                        BleManager.getInstance().disconnect(bleDevice);
                        if (controlNotifyListener != null) {
                            if (activity != null && !activity.isDestroyed()) {
                                activity.runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        controlNotifyListener.onNotifyFailure(exception.toString());
                                    }
                                });
                            }
                        }
                    }

                    @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
                    @Override
                    public void onCharacteristicChanged(byte[] data) {
                        Log.e(TAG, TAG_BLECONTROL+"===onCharacteristicChanged====蓝牙车控结果返回===data： " + HexUtil.formatHexString(data, true));
                        byte[] b = new byte[0];
                        if (bluetoothGatt != null)
                            b = bluetoothGatt.getService(UUID_SERVICE_CONTROL).getCharacteristic(UUID_CHARACTERISTIC_NOTIFY_CONTROL).getValue();
                        if (controlNotifyListener != null) {
                            controlNotifyListener.onCharacteristicChanged(b);
                        }
                        String result = DecodeControl(data);
                        Log.e(TAG, "===onCharacteristicChanged====蓝牙控车结果返回，result： " + result);
                        if (result.length() > 28) {
                            parseData(result);
//                            Log.e(TAG, "错误码: " + result.substring(20, 28));
                        }else {
                            //显示蓝牙错误默认提示
                            showDefaultPrompt();
                        }
                    }
                });
    }

    /**
     * 显示蓝牙错误默认提示
     */
    private void showDefaultPrompt(){
        Log.e(TAG, "showDefaultPrompt, activity = " + activity);
        if(activity != null){
            ToastUtil.showMessage(activity, "未知错误，请重试");
        }
    }

    /**
     * 发送控制车的指令
     *
     * @param bleDevice 蓝牙设备
     */
    public void sendControl(final BleDevice bleDevice, final BleControlCode control) {
        cmdName = control.getName();
        final long operationStartTime = System.currentTimeMillis();
        String url = "BleManager.getInstance().write";
        String operationParam = control.toString();
        final String interfaceName = "蓝牙控制";
        Log.e(TAG, TAG_BLECONTROL+"===发送蓝牙车控指令===start===control：" + control.toString() + " | 发送时间：" +System.currentTimeMillis());
        BleManager.getInstance().write(
                bleDevice,
                UUID_SERVICE_CONTROL.toString(),
                UUID_CHARACTERISTIC_WRITE_CONTROL.toString(),
                genControlHex(control.getCode(), control.getServiceID(), control.getSubFunction()),
                false,
                new BleWriteCallback() {

                    @Override
                    public void onWriteSuccess(final int current, final int total, final byte[] justWrite) {
                        Log.e(TAG, TAG_BLECONTROL+"===发送蓝牙车控指令===onWriteSuccess===：" + HexUtil.formatHexString(justWrite, true));
                        long operationEndTime = System.currentTimeMillis();
                        long timeDifference = operationEndTime - operationStartTime;
                        if (controlWriteListener != null) {
                            controlWriteListener.onWriteSuccess(bleDevice, control.toString());
                            Log.e(TAG, "====蓝牙控车====发送指令成功：" + control.toString());
                            EventBus.getDefault().post(new OperationResultEvent(null, url, operationParam, operationStartTime + "" + operationEndTime + "", timeDifference + "", interfaceName, operationStartTime + "", operationStartTime + "", "", "蓝牙控制成功", 1, "数字钥匙"));

                        }
                    }

                    @Override
                    public void onWriteFailure(final BleException exception) {
                        long operationEndTime = System.currentTimeMillis();
                        long timeDifference = operationEndTime - operationStartTime;
                        MultipleBluetoothController multipleBluetoothController = BleManager.getInstance().getMultipleBluetoothController();
                        BleBluetooth bleBluetooth = multipleBluetoothController.getBleBluetooth(bleDevice);
                        if (controlWriteListener != null) {
                            if (bleBluetooth == null) {
                                Log.e(TAG, TAG_BLECONTROL + "BleBluetooth is null");
                                controlWriteListener.onWriteFailure("蓝牙控制写入失败");
                            } else {
                                controlWriteListener.onWriteFailure(control.toString());
                            }
                        }
                        EventBus.getDefault().post(new OperationResultEvent(null,url,operationParam,operationStartTime+ "" + operationEndTime + "",timeDifference + "",interfaceName,operationStartTime + "",operationStartTime + "","","蓝牙控制失败" + exception.toString(),0,"数字钥匙"));
                        Log.e(TAG, TAG_BLECONTROL+"===发送蓝牙车控指令===onWriteFailure===exception：" + exception.toString() + " | control："+control.toString());
                    }
                });
    }

    /**
     * app第一次返回数据
     *
     * @param bleDevice
     */
    public void queryAuth(final BleDevice bleDevice) {
        Log.e(TAG, TAG_CONNECTBLUEKEY+"===app第一次返回数据，发送鉴权信息===start====Randomdata1:"+Randomdata1);
        BleManager.getInstance().write(
                bleDevice,
                UUID_SERVICE_AUTH.toString(),
                UUID_CHARACTERISTIC_WRITE_AUTH.toString(),
                genHex(Randomdata1),
                false,
                new BleWriteCallback() {

                    @Override
                    public void onWriteSuccess(final int current, final int total, final byte[] justWrite) {
                        Log.e(TAG, TAG_CONNECTBLUEKEY+"====app第一次返回数据，发送鉴权信息==onWriteSuccess===data：" + HexUtil.formatHexString(justWrite, true));
                        if (serviceWriteListener != null) {
                            serviceWriteListener.onWriteSuccess(bleDevice, HexUtil.formatHexString(justWrite, true));
                        }
                    }

                    @Override
                    public void onWriteFailure(final BleException exception) {
                        Log.e(TAG, TAG_CONNECTBLUEKEY+"====app第一次返回数据，发送鉴权信息==onWriteFailure===exception：" + exception.toString());
                        if (serviceWriteListener != null) {
                            serviceWriteListener.onWriteFailure("");
                        }
                    }
                });
    }

    @SuppressLint("LongLogTag")
    public void openBleWay() {
        if (currentDevice != null) {
            sendMessage(currentDevice, "AT+BLENET=1\r\n");
        } else {
            Log.e(TAG, TAG_CONNECTBLUEKEY+"openBleWay: 蓝牙已断开");
        }
    }

    public int getBleDeviceStatus() {
        if (currentDevice != null) {
            return 1;
        } else {
            return 0;
        }
    }

    private void connectService(String data) {
        initListener();
        String[] array = data.split(",");
        if (array.length > 3) {
            String url = array[2].substring(1, array[2].length() - 1);
            String port = array[3].substring(0, array[3].length() - 2);
            TaskCenter.sharedCenter().connect(url, Integer.parseInt(port));
            Log.e(TAG, TAG_CONNECTBLUEKEY+"connectService: 地址" + url + "   端口" + port);
        }
        if (stringBuilder.length() > 0) {
            stringBuilder.delete(0, stringBuilder.length());
        }
    }

    private void initListener() {
        TaskCenter.sharedCenter().setConnectedCallback(new TaskCenter.OnServerConnectedCallbackBlock() {
            @Override
            public void callback() {
                Log.e(TAG, TAG_CONNECTBLUEKEY+"callback: 与服务器连接成功");
                sendNormalMessage(currentDevice, "CONNECT\r\n");
            }
        });
        TaskCenter.sharedCenter().setDisconnectedCallback(new TaskCenter.OnServerDisconnectedCallbackBlock() {
            @Override
            public void callback(IOException e) {
                Log.e(TAG, TAG_CONNECTBLUEKEY+"callback: 与服务器连接断开" + e.getMessage());
                if (!e.toString().contains("断开")) {
                    sendMessage(currentDevice, "IP ERROR\r\n");
                } else {
                    BleManager.getInstance().disconnect(currentDevice);
                }
                isConnected = false;
                isFirstCmd = true;
            }
        });
        TaskCenter.sharedCenter().setReceivedCallback(new TaskCenter.OnReceiveCallbackBlock() {
            @Override
            public void callback(byte[] receicedMessage) {
                if (currentDevice != null) {
                    sendSocketMessage(currentDevice, receicedMessage);
                    Log.e(TAG, TAG_CONNECTBLUEKEY+"callback: " + Arrays.toString(receicedMessage));
                }
            }
        });
    }

    public void disconnectDevice() {
        Log.e(TAG,TAG_DISCONNECTBLUEKEY+"=======disconnectDevice===断开蓝牙连接====currentDevice："+(currentDevice == null));
        if (mHandler != null ) {
            mHandler.removeCallbacksAndMessages(null);
        }
        if (currentDevice != null) {
            directTimer.cancel();
            TaskCenter.sharedCenter().disconnect();
            disconnectDevices();
        } else {
            disconnectDevices();
        }
    }

    public void disconnectDevice(int tag) {
        if (currentDevice != null) {
            BleManager.getInstance().disconnectAllDevice();
            TaskCenter.sharedCenter().disconnect();
            if (tag == 0) {
                BleManager.getInstance().destroy();
            }
            directTimer.cancel();
        }
    }

    private byte[] getHexByte(String content) {
        return HexChangeUtils.HexString2Bytes(HexChangeUtils.str2HexStr(content));
    }

    private byte[] genControlHex(String controlCode, String serviceID, String subFunction) {

        String key = Randomdata1 + Randomdata2 + Randomdata1 + Randomdata2;

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(serviceID)
                .append(subFunction)
                .append(getReserved(8))
                .append(getRandomString(8))
                .append(bleKey)
                .append(BleControlCode.PAYLOAD_LENGTH)
                .append(controlCode);
        String Data_Check = DecodeUtils.getCRC16(stringBuilder.toString());
        stringBuilder.append(Data_Check);
        stringBuilder.append(getReserved(14));

        return DecodeUtils.encrypt(key, stringBuilder.toString());
    }


    private void conformityCmd(byte[] data) {
        String data1 = new String(data);
        if (data[data.length - 1] == 10) {
            stringBuilder.append(data1);
            if (stringBuilder.toString().contains("AT+CIPOPEN=0")) {
                passThroughListener.passThroughOpenSuccess(currentDevice);
                directTimer.cancel();
                isFirstCmd = false;
                connectService(stringBuilder.toString());
            }
        } else {
            stringBuilder.append(data1);
        }
    }

    private void conformSocketCmd(byte[] data) {
        if (data[data.length - 1] == -19) {
            socketStringBuilder = byteMerger(socketStringBuilder, data);
            if (TaskCenter.sharedCenter().isConnected()) {
                TaskCenter.sharedCenter().send(socketStringBuilder);
                Log.e(TAG, TAG_CONNECTBLUEKEY+"SocketPush: " + Arrays.toString(socketStringBuilder));
                socketStringBuilder = new byte[0];
            } else {
                Log.e(TAG, TAG_CONNECTBLUEKEY+"onCharacteristicChanged: 与服务器连接已断开");
            }
        } else {
            socketStringBuilder = byteMerger(socketStringBuilder, data);
        }

    }

    public static byte[] byteMerger(byte[] bt1, byte[] bt2) {
        byte[] bt3 = new byte[bt1.length + bt2.length];
        System.arraycopy(bt1, 0, bt3, 0, bt1.length);
        System.arraycopy(bt2, 0, bt3, bt1.length, bt2.length);
        return bt3;
    }

    private void checkPermissions(String deviceName, Fragment fragment) {
        if (activity == null){ return; }
        mDeviceName = deviceName;
        BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        if (!bluetoothAdapter.isEnabled()) {
            if (fragment instanceof TravelFragment) {
                mTravelFragment = (TravelFragment) fragment;
                mTravelFragment.openBluetooth();
            }
            return;
        }
        //安卓12以上的版本，需要申请蓝牙扫描和连接的权限
        if (PermissionUtils.sdkVersionGreatOrEqual31()) {
            if (!PermissionUtils.havePermissionAndroid12BleBluetoothScan(activity)) {
                ActivityCompat.requestPermissions(activity, PermissionUtils.permissions12_bluetooth_scan, PermissionUtils.ACTIVITY_PERMISSION_BLUETOOTH_ANDROID12);
                return;
            }

            if (!PermissionUtils.havePermissionAndroid12BleBluetoothConnect(activity)) {
                ActivityCompat.requestPermissions(activity, PermissionUtils.permissions12_bluetooth_connect, PermissionUtils.ACTIVITY_PERMISSION_BLUETOOTH_ANDROID12);
                return;
            }
        }

        String[] permissions = {Manifest.permission.ACCESS_FINE_LOCATION};
        List<String> permissionDeniedList = new ArrayList<>();
        for (String permission : permissions) {
            int permissionCheck = ContextCompat.checkSelfPermission(activity, permission);
            if (permissionCheck == PackageManager.PERMISSION_GRANTED) {
                Log.e(TAG,TAG_CONNECTBLUEKEY+"===checkPermissions===赋予了定位权限");
                onPermissionGranted(permission, deviceName);
            } else {
                permissionDeniedList.add(permission);
            }
        }
        if (!permissionDeniedList.isEmpty()) {
            Log.e(TAG,TAG_CONNECTBLUEKEY+"====checkPermissions===没有赋予定位权限");
            String[] deniedPermissions = permissionDeniedList.toArray(new String[permissionDeniedList.size()]);
            ActivityCompat.requestPermissions(activity, deniedPermissions, REQUEST_CODE_PERMISSION_LOCATION);
        }
    }

    private void onPermissionGranted(String permission, String deviceName) {
        switch (permission) {
            case Manifest.permission.ACCESS_FINE_LOCATION:
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !checkGPSIsOpen()) {
                    Log.e(TAG,TAG_CONNECTBLUEKEY+"===checkPermissions===没有赋予GPS权限");
                    new AlertDialog.Builder(activity)
                            .setTitle(R.string.notifyTitle)
                            .setMessage(R.string.gpsNotifyMsg)
                            .setNegativeButton(R.string.cancel,
                                    new DialogInterface.OnClickListener() {
                                        @Override
                                        public void onClick(DialogInterface dialog, int which) {
                                            Toast.makeText(activity, activity.getString(R.string.gpsNotifyMsg), Toast.LENGTH_LONG).show();
                                        }
                                    })
                            .setPositiveButton(R.string.setting,
                                    new DialogInterface.OnClickListener() {
                                        @Override
                                        public void onClick(DialogInterface dialog, int which) {
                                            Intent intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                                            activity.startActivity(intent);
//                                            activity.startActivityForResult(intent, REQUEST_CODE_OPEN_GPS);
                                        }
                                    })
                            .setCancelable(false)
                            .show();
                } else {
                    Log.e(TAG,TAG_CONNECTBLUEKEY+"===checkPermissions===赋予了GPS权限==deviceName:"+deviceName + " | currentDevice:"+currentDevice);
                    if(TextUtils.isEmpty(deviceName)){
                        connectCmdBle();
                    } else {
                        if (currentDevice != null) {
                            BleManager.getInstance().disconnectAllDevice();
                            isConnected = false;
                            isFirstCmd = true;
                            isFirstDirect = true;
                        } else {
                            startScan(deviceName);
                        }
                    }
                }
                break;
        }
    }

    /**
     * 定位权限返回结果
     */
    public void onLocationPermissionPassed(boolean isGranted, Fragment fragment) {
        Log.e(TAG, TAG_CONNECTBLUEKEY+"===权限申请====onLocationPermissionPassed=====mUserHandleConnectBle:"+mUserHandleConnectBle + " | isGranted:"+isGranted);
        //如果定位通过，则判断蓝牙是否打开
        if (isGranted && mUserHandleConnectBle) {
            checkPermissions(null,fragment);
        }else {
            mUserHandleConnectBle = false;
        }
    }

    public void onRequestAndroid12BlePermissionPassed(Fragment fragment, boolean isGranted) {
        Log.e(TAG, TAG_CONNECTBLUEKEY+"===权限申请====onRequestAndroid12BlePermissionPassed=====mUserHandleConnectBle:"+mUserHandleConnectBle + " | isGranted:"+isGranted);
        if (!mUserHandleConnectBle) {
            return;
        }
        if (!isGranted) {
            //扫描和连接权限都没有，说明用户之前拒绝过附近的设备权限授权，此时无法弹授权窗口，那么停止连接流程
            mUserHandleConnectBle = false;
            return;
        }

        if (PermissionUtils.havePermissionAndroid12BleBluetoothScan(activity) && !PermissionUtils.havePermissionAndroid12BleBluetoothConnect(activity)) {
            ActivityCompat.requestPermissions(activity, PermissionUtils.permissions12_bluetooth_connect, PermissionUtils.ACTIVITY_PERMISSION_BLUETOOTH_ANDROID12);
            return;
        }

        if (PermissionUtils.havePermissionAndroid12BleBluetoothConnect(activity) && !PermissionUtils.havePermissionAndroid12BleBluetoothScan(activity)) {
            ActivityCompat.requestPermissions(activity, PermissionUtils.permissions12_bluetooth_scan, PermissionUtils.ACTIVITY_PERMISSION_BLUETOOTH_ANDROID12);
            return;
        }

        ((TravelFragment) fragment).checkPermissions(TravelFragment.ACTIVITY_PERMISSION_LOCATION,
                fragment.getContext().getString(R.string.permission_location_pre),
                fragment.getContext().getString(R.string.permission_location_setting),
                Manifest.permission.ACCESS_FINE_LOCATION);
    }

    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        //请求打开蓝牙权限返回
        if (requestCode == TravelFragment.ACTIVITY_REQUEST_BLUETOOTH) {
            if (resultCode == RESULT_OK) {
                //请求蓝牙钥匙，并初始化蓝牙
                checkPermissions(mDeviceName, mTravelFragment);
            } else {
                if (activity != null)
                    ToastUtil.showMessage(activity, R.string.car_control_need_open_bluetooth);
            }
        }
    }

    /**
     * app首发授权请求。
     *
     * @return
     */
    private byte[] genAuthHex() {

        int currentTme = (int) (System.currentTimeMillis() / 1000);
        byte[] bytes = new byte[4];
        for (int i = bytes.length - 1; i >= 0; i--) {
            bytes[i] = (byte) (currentTme & 0xFF);
            currentTme >>= 8;
        }
        String serviceID = "38C7";
        String subFunction = "0001";
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(serviceID)
                .append(subFunction)
                .append(getReserved(8))
                .append(HexUtil.encodeHexStr(bytes))
                .append(bleKey)
                .append(BleControlCode.PAYLOAD_LENGTH)
                .append(getReserved(12));
        String Data_Check = DecodeUtils.getCRC16(stringBuilder.toString());
        stringBuilder.append(Data_Check);
        stringBuilder.append(getReserved(14));
        byte[] result = HexUtil.hexStringToBytes(stringBuilder.toString());
        return result;
    }

    /**
     * 生成授权测试数据。
     *
     * @return
     */
    private byte[] genHex(String randomdata1) {
        String Service_ID = "38C7";
        String Subfunction = "0002";
        String rollData = "00001122";
        String Random_data = randomdata1;
        String MasterKey = masterKey;
        String MasterKey_random = masterKeyRandom;
        String BleKey = bleKey;
        String Payload_length = "06";
        String Reserved = "000000000000";

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(Service_ID).append(Subfunction).append(rollData).append(Random_data).append(BleKey).append(Payload_length).append(Reserved);
        String Data_Check = DecodeUtils.getCRC16(stringBuilder.toString());
        stringBuilder.append(Data_Check);
        stringBuilder.append("00000000000000");
        stringBuilder.length();

        return DecodeUtils.encrypt(DecodeUtils.XOR(MasterKey, MasterKey_random), stringBuilder.toString());
    }

    /**
     * 解密ucu首发
     *
     * @return 随机字节
     */
    public String Deode(byte[] data) {
        if (data != null && data.length >= 4) {
            String ss = HexUtil.encodeHexStr(data);
            String screct = ss.substring(0, 2);
            String decodeContent = ss.substring(2);
            if ("00".equals(screct)) {
                return HexUtil.encodeHexStr(DecodeUtils.Decrypt(DecodeUtils.XOR(masterKey, masterKeyRandom), decodeContent));
            } else {
                return decodeContent;
            }
        }
        return "000000000000000000000000000000";
    }

    public String getRandomKey() {
        return Randomdata1 + Randomdata2 + Randomdata1 + Randomdata2;
    }

    /**
     * 生成固定长度的保留位（全0字符串）
     *
     * @param length 保留位长度
     * @return 保留位
     */
    private String getReserved(int length) {
        return String.format("%0" + length + "d", 0);
    }

    /**
     * 生成随机字符串（十六进制数）
     *
     * @param length 随机字符串长度
     * @return 随机字符串
     */
    private String getRandomString(int length) {
        String str = "ABCDEF0123456789";
        Random random = new Random();
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(str.length());
            builder.append(str.charAt(number));
        }
        return builder.toString();
    }

    /**
     * 解密
     *
     * @return 随机字节
     */
    public String DecodeControl(byte[] data) {
        if (data != null && data.length >= 4) {
            String ss = HexUtil.encodeHexStr(data);
            String ss2 = ss.substring(2);
            String head = ss.substring(0, 2);
            if ("00".equals(head)) {
                Log.e(TAG, TAG_BLECONTROL+"===randomKey: " + getRandomKey());
                return head + HexUtil.encodeHexStr(DecodeUtils.Decrypt(getRandomKey(), ss2));
            } else {
                return ss;
            }
        }
        return "";
    }

    public BleDataResponseListener getBleDataResponseListener() {
        return bleDataResponseListener;
    }

    public void setBleDataResponseListener(BleDataResponseListener bleDataResponseListener) {
        this.bleDataResponseListener = bleDataResponseListener;
    }

    public void setParseDataResponseListener(ParseDataResponseListener parseDataResponseListener){
        this.parseDataResponseListener = parseDataResponseListener;
    }

    private void parseData(String data) {
        //统一把数据抛给ParkActivity
        if(parseDataResponseListener != null){ parseDataResponseListener.responseParseData(data); }
        String data1 = data.substring(2, 6);
        String data2 = data.substring(2, 10);
        // 蓝牙命令失败
        String errorCode = data.substring(20, 28);

        if (data1.equalsIgnoreCase(BLE_FAIL)) {
            StringBuilder sb = new StringBuilder();
            String code = getErrorCode(errorCode);
            Log.e(TAG, TAG_CONNECTBLUEKEY+"===parseData====蓝牙控车结果返回，失败===错误码："+errorCode + "，code = " + code);
            if (bleErrorCodeMap.containsKey(code)) {
                sb.append(bleErrorCodeMap.get(code));
                Log.e(TAG, TAG_CONNECTBLUEKEY+"===parseData====bleErrorCodeMap sb = " + sb);
                if (bleDataResponseListener != null) {
                    bleDataResponseListener.responseControlError(sb.toString());
                }
            } else if (bleParkingCodeMap.containsKey(code)) {
                sb.append(bleParkingCodeMap.get(code));
                Log.e(TAG, TAG_CONNECTBLUEKEY+"===parseData====bleParkingCodeMap sb = " + sb);
                if (bleDataResponseListener != null) {
                    bleDataResponseListener.responseParkingError(sb.toString());
                }
            } else if (BLE_PARKING_STATE.equals(code)) {
                byte[] codes = HexUtil.hexStringToBytes(errorCode.substring(2));
                switch (codes[0]) {
                    case 0x06://泊车失败
                    case 0x16://未收到ADAS反馈 指令超时
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed();
                        }
                        break;
                    case 0x05://泊车成功结束
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingSuccess();
                        }
                        break;
                    case 0x0F://
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responsePowerOffFailed("下电失败，请回到驾驶座踩刹车接管车辆");
                        }
                        break;
                    default:
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingState(codes[0]);
                        }
                        break;
                }
            }else if (BLE_PARKING_OUT_STATE.equals(code)) {
                LogUtils.e("===parseData====蓝牙控车结果返回-->蓝牙一键泊出2 bleDataResponseListener：" + bleDataResponseListener);
                byte[] codes = HexUtil.hexStringToBytes(errorCode.substring(2));
                switch (codes[0]) {
                    case 0x06://出库失败
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                    case 0x07://人工接管，出库失败
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                    case 0x08://功能不可用
//                        ToastUtils.showShort("UCU Rsp ErrorCode: " + errorCode);//for test 只做测试用途，正式版本请删除或注释此行代码
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                        break;
                    case 0x05://泊车成功结束
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingSuccess();
                        }
                        break;
                    default:
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingState(codes[0]);
                        }
                        break;
                }
            } else if (BLE_PARKING_STATE2.equals(code)) {
                LogUtils.e("===parseData====蓝牙控车结果返回-->蓝牙一键泊车2 bleDataResponseListener：" + bleDataResponseListener);
                byte[] codes = HexUtil.hexStringToBytes(errorCode.substring(2));
                switch (codes[0]) {
                    case 0x03://泊车失败
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                    case 0x04://人工接管
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                    case 0x06://功能不可用
//                        ToastUtils.showShort("UCU Rsp ErrorCode: " + errorCode);//for test 只做测试用途，正式版本请删除或注释此行代码
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                        break;
                    case 0x02://泊车成功结束
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingSuccess();
                        }
                        break;
                    default:
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingState(codes[0]);
                        }
                        break;
                }
            } else if (BLE_PARKING_ERROR_STATE.equals(code)) {
                LogUtils.e("===parseData====E260SP新增错误码--> bleDataResponseListener：" + bleDataResponseListener);
                byte[] codes = HexUtil.hexStringToBytes(errorCode.substring(2));
                if (codes[0] == 0x00) {//无错误
                    if (bleDataResponseListener != null) {
                        bleDataResponseListener.responseParkingSuccess();
                    }
                } else {
                    if (bleDataResponseListener != null) {
                        bleDataResponseListener.responseE260SPParkingFailed(e260SPErrorMsg.get(codes[0]));
                    }
                }
            }
            else {
                //显示蓝牙错误默认提示
                showDefaultPrompt();
            }
        } else if (data1.equalsIgnoreCase(BLE_CONTROL_SUCCESS)) {
            Log.e(TAG, TAG_CONNECTBLUEKEY+"===parseData====蓝牙控车结果返回，成功===" );
            if (bleDataResponseListener != null) {
                bleDataResponseListener.responseControlSuccess(cmdName);
            }

        } else if (data2.equalsIgnoreCase(CMD_POWER_OFF)) {
            Log.e(TAG, TAG_CONNECTBLUEKEY+"===parseData====蓝牙控车结果返回，车辆下电成功===" );
            if (bleDataResponseListener != null) {
                bleDataResponseListener.onPowerOffSuccess();
            }
        }
        else if (data2.equalsIgnoreCase("C0650001") && ServiceCode.equals(AppConstants.SERVICE_CODE_SMART_PARKING_F510C)
        ||ServiceCode.equals(AppConstants.SERVICE_CODE_SMART_PARKING_F511C) || ServiceCode.equals(AppConstants.SERVICE_CODE_SMART_PARKING_F511S)) {
            String parkingStatus1 = data.substring(28, 44);
            String parkingStatus2 = data.substring(44, 60);
            LogUtils.e("parkingStatus1:" + parkingStatus1);
            LogUtils.e("parkingStatus2:" + parkingStatus2);
            byte ipsInfo = Utils.getBits(parkingStatus1, 7, 7, 8);
            byte ipaStatus = Utils.getBits(parkingStatus2, 1, 7, 5);
            byte apoStatus = Utils.getBits(parkingStatus2, 1, 2, 4);
            LogUtils.e("IPS Information智能泊车信息提示:" + Integer.toHexString(ipsInfo));
            LogUtils.e("IPA Operation Status智能泊车辅助运行状态 :" + Integer.toHexString(ipaStatus));
            LogUtils.e("APO Operation Status智能出库运行状态 :" + Integer.toHexString(apoStatus));
            if (bleDataResponseListener != null) {
                if (park_type == 1) {
                    switch (ipaStatus) {
                        case BleCode.PARK_IPA_OFF:
                            break;
                        case BleCode.PARK_IPA_NO_READY:
                        case BleCode.PARK_IPA_READY:
                        case BleCode.PARK_IPA_DETECTING:
                        case BleCode.PARK_IPA_DETECTING_SUSPEND:
                        case BleCode.PARK_IPA_DETECTED:
                        case BleCode.PARK_IPA_BG_DETECTING:
                        case BleCode.PARK_IPA_BG_DETECTED:
                            bleDataResponseListener.responseParkingState((byte) 0x08);
                            break;
                        case BleCode.PARK_IPA_PREPARE:
                            break;
                        case BleCode.PARK_IPA_PARKING:
                            bleDataResponseListener.responseParkingState((byte) 0x01);
                            break;
                        case BleCode.PARK_IPA_PARKING_SUSPEND:
                            if (ipsInfo != (byte) 0x56) { //0x56:障碍物过近，是否开启极窄出库模式
                                bleDataResponseListener.responseParkingState((byte) 0x07);
                            } else {
                                bleDataResponseListener.responseParkingState((byte) 0x11);
                            }
                            break;
                        case BleCode.PARK_IPA_FINISH_SUCCESS:
                            bleDataResponseListener.responseParkingSuccess();
                            break;
                        case BleCode.PARK_IPA_FINISH_FAILURE:
                            bleDataResponseListener.responseParkingFailed((byte) 0x03);
                            break;
                        case BleCode.PARK_IPA_FINISH_TERMINATION:
                            bleDataResponseListener.responseParkingFailed((byte) 0x04);
                            break;
                        case BleCode.PARK_IPA_ERROR_INT_FAULT:
                        case BleCode.PARK_IPA_ERROR_EXT_FAULT:
                            bleDataResponseListener.responseParkingFailed((byte) 0x06);
                            break;
                    }
                } else if (park_type == 2) {
                    switch (apoStatus) {
                        case BleCode.PARK_APO_OFF:
//                            bleDataResponseListener.responseParkingState((byte) 0x0B);
                            break;
                        case BleCode.PARK_APO_NO_READY:
                            bleDataResponseListener.responseParkingState((byte) 0x0B);
                            break;
                        case BleCode.PARK_APO_READY:
                            bleDataResponseListener.responseParkingState((byte) 0x0B);
                            break;
                        case BleCode.PARK_APO_PREPARE:
                            bleDataResponseListener.responseParkingState((byte) 0x01);
                            break;
                        case BleCode.PARK_APO_PARKING_OUT:
                            bleDataResponseListener.responseParkingState((byte) 0x0A);
                            break;
                        case BleCode.PARK_APO_PARKING_OUT_SUSPEND:
                            if (ipsInfo != (byte) 0x56) { //0x56:障碍物过近，是否开启极窄出库模式
                                bleDataResponseListener.responseParkingState((byte) 0x09);
                            } else {
                                bleDataResponseListener.responseParkingState((byte) 0x11);
                            }
                            break;
                        case BleCode.PARK_APO_FINISH_SUCCESS:
                            bleDataResponseListener.responseParkingSuccess();
                            break;
                        case BleCode.PARK_APO_FINISH_FAILURE:
                            bleDataResponseListener.responseParkingFailed((byte) 0x06);
                            break;
                        case BleCode.PARK_APO_FINISH_TERMINATION:
                            bleDataResponseListener.responseParkingFailed((byte) 0x07);
                            break;
                        case BleCode.PARK_APO_ERROR_INT_FAULT:
                        case BleCode.PARK_APO_ERROR_EXT_FAULT:
                            bleDataResponseListener.responseParkingFailed((byte) 0x08);
                            break;
                        case BleCode.PARK_APO_PREPARE_SUSPEND:
                            bleDataResponseListener.responseParkingState((byte) 0x0B);
                            break;
                    }
                } else {
                    LogUtils.e("park_type = " + park_type);
                }
            } else {
                LogUtils.e("bleDataResponseListener is null.");
            }


            String code = errorCode.substring(0, 2);
            if (BLE_PARKING_OUT_STATE.equals(code)) {
                LogUtils.e("===parseData====蓝牙控车结果返回-->蓝牙一键泊出2 bleDataResponseListener：" + bleDataResponseListener);
                byte[] codes = HexUtil.hexStringToBytes(errorCode.substring(2));
                switch (codes[0]) {
                    case 0x06://出库失败
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                    case 0x07://人工接管，出库失败
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                    case 0x08://功能不可用
//                        ToastUtils.showShort("UCU Rsp ErrorCode: " + errorCode);//for test 只做测试用途，正式版本请删除或注释此行代码
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                        break;
                    case 0x05://泊车成功结束
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingSuccess();
                        }
                        break;
                    default:
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingState(codes[0]);
                        }
                        break;
                }
            } else if (BLE_PARKING_STATE2.equals(code)) {
                LogUtils.e("===parseData====蓝牙控车结果返回-->蓝牙一键泊车2 bleDataResponseListener：" + bleDataResponseListener);
                byte[] codes = HexUtil.hexStringToBytes(errorCode.substring(2));
                switch (codes[0]) {
                    case 0x03://泊车失败
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                    case 0x04://人工接管
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                    case 0x06://功能不可用
//                        ToastUtils.showShort("UCU Rsp ErrorCode: " + errorCode);//for test 只做测试用途，正式版本请删除或注释此行代码
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingFailed(codes[0]);
                        }
                        break;
                    case 0x02://泊车成功结束
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingSuccess();
                        }
                        break;
                    default:
                        if (bleDataResponseListener != null) {
                            bleDataResponseListener.responseParkingState(codes[0]);
                        }
                        break;
                }
            }
        }
    }

    private String getErrorCode(String errorCode) {
        String eHead = errorCode.substring(0, 2);
        if ("11".equals(eHead)) {
            byte[] codes = HexUtil.hexStringToBytes(errorCode.substring(2));
            Log.e(TAG, "===parseData====getErrorCode===codes[0]:" +codes[0] + " | codes[1]:"+codes[1]+ " | codes[2]:"+codes[2]);
            if (codes[0] != 0) {
                if ((codes[0] & 0x80) == 0x80) {//电源未关
                    return ACC_ON;
                } else if ((codes[0] & 0x40) == 0x40) {//遥控钥匙 正在使用
                    return KEY_ERROR;
                } else if ((codes[0] & 0x20) == 0x20) {//门开着
                    return DOOR_OPENED;
                } else if ((codes[0] & 0x10) == 0x10) {//转向灯未 关
                    return LIGHT_ON;
                } else if ((codes[0] & 0x8) == 0x8 || (codes[0] & 0x4) == 0x4) {//电动窗故障
                    return WINDOW_ERROR;
                } else if ((codes[0] & 0x1) == 0x1) {//设防状态错误
                    return POWER_OFF_ERROR;
                }
            }
            if (codes[1] != 0) {
                if ((codes[1] & 0x80) == 0x80) {//喇叭故障
                    return HORN_ERROR;
                } else if ((codes[1] & 0x40) == 0x40 || (codes[1] & 0x20) == 0x20 || (codes[1] & 0x10) == 0x10 || (codes[1] & 0x8) == 0x8) {//转向灯 故障
                    return LR_LIGHT_ERROR;
                } else if ((codes[1] & 0x4) == 0x4 || (codes[1] & 0x2) == 0x2) {//电源未关z
                    return ACC_ON;
                } else if ((codes[1] & 0x1) == 0x1) {//车辆正在 运行
                    return CAR_RUNNING;
                }
            }

            if (codes[2] != 0) {
                if ((codes[2] & 0x80) == 0x80) {//车灯未关 闭
                    return LIGHT_ON;
                } else if ((codes[2] & 0x40) == 0x40) {//智能钥匙 在车内
                    return KEY_ERROR;
                } else if ((codes[1] & 0x20) == 0x20) {//整车防盗 认证失败
                    return POWER_OFF_ERROR;
                } else if ((codes[2] & 0x10) == 0x10) {//蓄电池电 压低
                    return LOW_POWER;
                } else if ((codes[1] & 0x8) == 0x8) {//变速箱 当前档 位为非 P
                    return GEAR_NOT_N;
                }
            }

        } else if ("12".equals(eHead)) {
            byte[] codes = HexUtil.hexStringToBytes(errorCode.substring(2));
            if (codes[0] != 0) {//车门未关紧
                return DOOR_OPENED;
            }
            if (codes[1] != 0) {
                if ((codes[1] & 0x80) == 0x80) {//后备箱未 关
                    return B_DOOR_OPENED;
                } else if ((codes[1] & 0x40) == 0x40) {//车辆未充 电
                    return UN_CHARGE;
                } else if ((codes[1] & 0x20) == 0x20) {//电源未关
                    return ACC_ON;
                } else if ((codes[1] & 0x10) == 0x10 || (codes[1] & 0x8) == 0x8) {//设防失败
                    return POWER_OFF_ERROR;
                } else if ((codes[1] & 0x4) == 0x4) {//车灯未关 闭
                    return LIGHT_ON;
                } else if ((codes[1] & 0x2) == 0x2) {//高压解防 错误
                    return POWER_ON_ERROR;
                }
            }

            if (codes[2] != 0) {
                if ((codes[2] & 0x80) == 0x80) {//电源未关
                    return ACC_ON;
                } else if ((codes[2] & 0x40) == 0x40) {//车辆正在 运行
                    return CAR_RUNNING;
                } else if ((codes[1] & 0x20) == 0x20) {//变速箱当 前档位为 非P
                    return GEAR_NOT_N;
                } else if ((codes[2] & 0x10) == 0x10) {//指令冲突
                    return CMD_CONFLICT;
                }
            }

        } else if (BLE_PARKING_STATE.equals(eHead)) {
            byte[] codes = HexUtil.hexStringToBytes(errorCode.substring(2));
            switch (codes[0]) {
//                case 0x07:
//                    return PARKING_TIME_OUT;
                case 0x08:
                    return PARKING_OBSTACLE;
//                case 0x09:
//                    return PARKING_EXCESSIVE;
                case 0x0A:
                    return PARKING_DOOR_OPENED;
//                case 0x0C:
//                    return PARKING_ERROR;
                case 0x0D:
                    return PARKING_FUNCTION_ERROR;
                case 0x10:
                    return PARKING_TYPE_ERROR;
                case 0x0E:
                    return PARKING_UCU_ERROR;
                default:
                    return eHead;
            }
        }else if ("20".equals(eHead)){
            byte[] codes = HexUtil.hexStringToBytes(errorCode.substring(2));
            Log.e(TAG, "codes: ======" + codes + "   ===codes[0] ======" + codes[0]);
            switch (codes[0]){
                case 0x01:
                    return "01";
                case 0x02:
                    return "02";
                case 0x03:
                    return "03";
                case 0x04:
                    return "04";
                case 0x05:
                    return IGN1_OUTPUT_ERROR;
                case 0x06:
                    return "06";
                case 0x07:
                    return ACC_OUTPUT_ERROR;
                case 0x08:
                    return VOLTAGE_EXCEED_ERROR;
                case 0x09:
                    return "09";
                case 0x10:
                    return "10";
                case 0x11:
                    return "11";
                case 0x12:
                    return "12";
                case 0x13:
                    return ESCL_UNLOCK_ERROR;
                case 0x14:
                    return "14";
                case 0x15:
                    return "15";
                case 0x16:
                    return START_TIMEOUT_ERROR;
                case 0x18:
                    return "18";
                case 0x19:
                    return "19";
                case 0x1A:
                    return "1A";
                case 0x1B:
                    return "1B";
                case 0x1C:
                    return "1C";
                case 0x1D:
                    return "1D";
                case 0x20:
                    return "20";
                case 0x21:
                    return "21";
                case 0x22:
                    return "22";
                case 0x26:
                    return "26";
                case 0x27:
                    return "27";
                case 0x28:
                    return "28";
                case 0x2B:
                    return "2B";
                case 0x2F:
                    return "2F";
                case 0x3C:
                    return "3C";
                case 0x41:
                    return "41";
                case 0x43:
                    return "43";
                case 0x50:
                    return "50";
                case 0x51:
                    return "51";
                case 0x52:
                    return "52";
                case 0x53:
                    return "53";
                case 0x54:
                    return "54";
                case 0x55:
                    return "55";
                case 0x56:
                    return "56";
                case 0x57:
                    return "57";
                case 0x58:
                    return "58";
                case 0x59:
                    return "59";
                case 0x5A:
                    return "5A";
                case 0x5B:
                    return "5B";
                case 0x5C:
                    return "5C";
                case 0x5D:
                    return "5D";
                case 0x5E:
                    return "5E";
                case 0x5F:
                    return "5F";
                case 0x71:
                    return "71";
                case 0x72:
                    return "72";
                case 0x73:
                    return "73";
                case 0x74:
                    return "74";
                case 0x75:
                    return "75";
                case 0x77:
                    return "77";
                case (byte) 0xFF:
                    return "FF";
            }

        }
        //以下错误码是参考ios那边添加的
        else if ("05".equals(eHead)){//数据长度错误
            return "05";
        }
        else if ("07".equals(eHead)){//指令冲突
            return CMD_CONFLICT;
        }
        else if ("08".equals(eHead)){//指令超时
            return "08";
        }
        else if ("0B".equals(eHead)){//UCU无响应
            return UCU_NO_RESPONSE;
        }
        else if ("13".equals(eHead)){//CRC校验失败
            return CRC_VERIFICATION_FAILED;
        }
        else if ("16".equals(eHead)){//BCM超时无响应
            return BCM_NO_RESPONSE;
        }
        else if ("1e".equals(eHead)){//用户bleKey校验失败
            return BLEKEY_VERIFICATION_FAILED;
        }
        else if ("7F".equals(eHead)){//用户bleKey不存在gaw
            return BLEKEY_ABSENT;
        }
        return eHead;
    }

    public static void setParams(KeyEntity keyEntity) {
        MAC = keyEntity.getMac();
        masterKey = keyEntity.getMasterKey();
        masterKeyRandom = keyEntity.getMasterKeyRandom();
        bleKey = keyEntity.getBleKey();
    }

    public void setParamsSuccess() {
        if (bleDataResponseListener != null) {
            bleDataResponseListener.setParamsSuccess();
        }
    }

    public static void clearData() {
        MAC = "";
        masterKey = "";
        masterKeyRandom = "";
        bleKey = "";
    }

    private boolean checkGPSIsOpen() {
        LocationManager locationManager = (LocationManager) activity.getSystemService(Context.LOCATION_SERVICE);
        if (locationManager == null) {
            return false;
        }
        return locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
    }

    class TimeCount extends CountDownTimer {

        public TimeCount(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @Override
        public void onTick(long millisUntilFinished) {

        }

        @Override
        public void onFinish() {
            if (currentDevice != null) {
                passThroughListener.passThroughOpenFail(currentDevice.getName(), "超时未返回");
            } else {
                passThroughListener.passThroughOpenFail("", "蓝牙连接断开");
            }
        }
    }

    public interface ParseDataResponseListener{
        void responseParseData(String data);
    }

    public interface BleDataResponseListener {

        /**
         * 返回蓝牙控制成功
         *
         * @param msg
         */
        void responseControlSuccess(String msg);

        /**
         * 返回蓝牙控制失败
         *
         * @param msg
         */
        void responseControlError(String msg);

        /**
         * 返回自动泊车状态
         *
         * @param state
         */
        void responseParkingState(byte state);

        /**
         * 返回自动泊车错误
         *
         * @param msg
         */
        void responseParkingError(String msg);

        /**
         * 车辆下电成功
         */
        void onPowerOffSuccess();

        void responseParkingFailed();

        void responseParkingFailed(byte state);

        void responseE260SPParkingFailed(String msg);

        void responseParkingSuccess();

        void responsePowerOffFailed(String message);

        void setParamsSuccess();
    }

    public boolean isConnectBle() {
        if (TextUtils.isEmpty(MAC)) {
            return false;
        } else {
            return isConnect == 1;
        }
    }

    public int getConnectStatus() {
        if (isConnect == 3) {
            return 2;
        } else {
            return isConnect;
        }
    }

    public void setConnectStatus(int connectStatus) {
        isConnect = connectStatus;
    }

    public void startConnectTimeOutCount() {
        if (mConnectTimeOutCount == null) {
            mConnectTimeOutCount = new ConnectTimeOutCount(18000, 1000);
        }
        mConnectTimeOutCount.cancel();
        mConnectTimeOutCount.start();
    }

    public void stopConnectTimeOutCount() {
        if (mConnectTimeOutCount == null) {
            return;
        }
        mConnectTimeOutCount.cancel();
    }

    /**
     * 18秒倒计时
     */
    class ConnectTimeOutCount extends CountDownTimer {

        public ConnectTimeOutCount(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @Override
        public void onTick(long millisUntilFinished) {

        }

        @Override
        public void onFinish() {
            if (connectStatusListener != null) {
                connectStatusListener.onConnectFail("连接超时", "");
            }
            if (!findBle) {
                showDialog("BleManager.getInstance()", "连接超时，未搜索到蓝牙信号", "连接超时，未搜索到蓝牙信号");
            }
            isConnect = 0;
        }
    }
}

