package com.cloudy.linglingbang.activity.community;

import android.app.Activity;
import android.content.Context;
import android.graphics.Rect;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.newcommunity.LocalCommunityActivity;
import com.cloudy.linglingbang.app.widget.image.ImageLoadUtils;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.community.ContentGroupInfo;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 社区首页进入人车生活的第一步，选择车型
 *
 * <AUTHOR>
 * @date 2019-11-26
 */
public class CommunityPostChooseLabelActivity extends BaseRecyclerViewRefreshActivity<ContentGroupInfo> {
    private int series;

    public static void startActivity(Activity activity, int series) {
        IntentUtils.createStartIntent(activity, CommunityPostChooseLabelActivity.class, IntentUtils.INTENT_EXTRA_COMMON, series);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        series = (int) getOrParseIntentLongExtra();
        super.onCreate(savedInstanceState);
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<ContentGroupInfo> list) {
        return new GoodsLabelAdapter(this, list);
    }

    @Override
    public RefreshController<ContentGroupInfo> createRefreshController() {
        return new RefreshController<ContentGroupInfo>(this) {
            @Override
            protected RecyclerView.LayoutManager createLayoutManager(Context context) {
                return new GridLayoutManager(context, 2);
            }

            @Override
            protected boolean isLoadMoreEnable() {
                return false;
            }

            @Override
            public void initViews(View rootView) {
                super.initViews(rootView);
                int px20 = getResources().getDimensionPixelOffset(R.dimen.normal_20);
                int px35 = getResources().getDimensionPixelOffset(R.dimen.normal_35);
                getRecyclerView().setPadding(px35, px20, px35, px35);

            }

            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                final int px20 = getResources().getDimensionPixelSize(R.dimen.normal_20);
                return new RecyclerView.ItemDecoration() {
                    @Override
                    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                        super.getItemOffsets(outRect, view, parent, state);
                        outRect.bottom = px20;
                        int index = parent.getChildAdapterPosition(view);
                        if (index == -1) {
                            outRect.right = 0;
                            outRect.left = 0;
                            return;
                        }
                        if (index % 2 == 0) {
                            outRect.right = px20 / 2;
                            outRect.left = 0;
                        } else {
                            outRect.left = px20 / 2;
                            outRect.right = 0;
                        }
                    }
                };
            }
        };
    }

    @Override
    public Observable<BaseResponse<List<ContentGroupInfo>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        Map<String, String> map = new HashMap<>(1);
        map.put("series", String.valueOf(series));
        return service2.getContentGroupInfoByType(map);
    }

    final class GoodsLabelAdapter extends BaseRecyclerViewAdapter<ContentGroupInfo> {
        int px10;

        GoodsLabelAdapter(Context context, List<ContentGroupInfo> data) {
            super(context, data);
            px10 = context.getResources().getDimensionPixelOffset(R.dimen.normal_10);
            setOnItemClickListener(new BaseRecyclerViewHolder.OnItemClickListener() {
                @Override
                public void onItemClick(View itemView, int position) {
                    //跳转到车型社区列表页
                    LocalCommunityActivity.startActivity(mContext, mData.get(position), mData);
                    CommunityPostChooseLabelActivity.this.finish();
                }
            });
        }


        @Override
        protected BaseRecyclerViewHolder<ContentGroupInfo> createViewHolder(View itemView) {
            return new ViewHolder(itemView);
        }

        @Override
        protected int getItemLayoutRes(int viewType) {
            return R.layout.item_car_type_with_img;
        }

        class ViewHolder extends BaseRecyclerViewHolder<ContentGroupInfo> {

            private final TextView mTvCarTypeName;
            private final ImageView mIvImg;

            public ViewHolder(View itemView) {
                super(itemView);
                mTvCarTypeName = itemView.findViewById(R.id.tv_car_type_name);
                mIvImg = itemView.findViewById(R.id.iv_img);
            }

            @Override
            public void bindTo(ContentGroupInfo contentGroupInfo, final int position) {
                super.bindTo(contentGroupInfo, position);
                mTvCarTypeName.setText(contentGroupInfo.getContentGroupName());
                ImageLoadUtils.load(mIvImg, contentGroupInfo.getCoverImage(), R.drawable.ic_common_place_holder);
            }
        }
    }
}
