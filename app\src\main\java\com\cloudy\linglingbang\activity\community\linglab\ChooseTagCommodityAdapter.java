package com.cloudy.linglingbang.activity.community.linglab;

import android.content.Context;
import android.view.View;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.widget.banner.AutoResizeHeightImageView;
import com.cloudy.linglingbang.app.widget.image.ImageLoadUtils;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.tag.ChooseTagBean;

import java.util.List;

/**
 * 选择标签推荐商品的adapter
 *
 * <AUTHOR>
 * @date 2021/11/11
 */
class ChooseTagCommodityAdapter extends BaseRecyclerViewAdapter<ChooseTagBean> {
    public ChooseTagCommodityAdapter(Context context, List<ChooseTagBean> data) {
        super(context, data);
    }

    @Override
    protected BaseRecyclerViewHolder<ChooseTagBean> createViewHolder(View itemView) {
        return new ViewHolder(itemView);
    }

    @Override
    protected int getItemLayoutRes(int viewType) {
        return R.layout.item_choose_tag_commodity;
    }

    static class ViewHolder extends BaseRecyclerViewHolder<ChooseTagBean> {
        AutoResizeHeightImageView mCommodityImg;
        TextView mTvCommodityName;

        public ViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            mCommodityImg = itemView.findViewById(R.id.iv_commodity_img);
            mTvCommodityName = itemView.findViewById(R.id.tv_commodity_name);
        }

        @Override
        public void bindTo(ChooseTagBean chooseTagBean, int position) {
            super.bindTo(chooseTagBean, position);
            mTvCommodityName.setText(chooseTagBean.getCommodityName());
            ImageLoadUtils.load(mCommodityImg, chooseTagBean.getCommodityImage());
        }
    }
}
