package com.cloudy.linglingbang.activity.community.post.detail;

import android.content.Context;
import android.content.DialogInterface;
import android.text.Editable;
import android.text.TextWatcher;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonInputDialog;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;

import java.util.HashMap;
import java.util.Map;

/**
 * 帖子详情-备注对话框
 *
 * <AUTHOR>
 * @date 2018/11/22
 */
public class PostDetailRemarkDialog extends CommonInputDialog implements CommonInputDialog.OnInputFinishListener {
    private static final int MAX_LENGTH = 200;
    private String mPostId;
    private TextView mTvNumber;

    public PostDetailRemarkDialog(Context context, String postId) {
        super(context, context.getString(R.string.dialog_post_detail_remark_title), null);
        //最大长度
        setMaxLength(MAX_LENGTH);
        //确认回调
        setOnInputFinishListener(this);
        //隐藏取消按钮
        getAlertController().setButton(DialogInterface.BUTTON_NEGATIVE, null, null);
        getAlertController().setButton(DialogInterface.BUTTON_POSITIVE, context.getString(R.string.common_save));
        mPostId = postId;
    }

    @Override
    protected int getDefaultLayoutResId() {
        return R.layout.dialog_post_detail_remark;
    }

    @Override
    protected void initView() {
        super.initView();
        mEtInput.setHint(R.string.dialog_post_detail_remark_hint);
        mTvNumber = findViewById(R.id.tv_number);
        mTvNumber.setText(String.valueOf(mMaxLength));

        mEtInput.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                int length = s.length();
                if (length > mMaxLength) {
                    //不应该出现
                    length = mMaxLength;
                }
                mTvNumber.setText(String.valueOf(mMaxLength - length));
            }
        });
    }

    @Override
    public boolean onInputFinish(String input) {
        if (input == null || input.length() < 2) {
            //不能小于 2 个字
            ToastUtil.showMessage(mContext, mContext.getString(R.string.dialog_post_detail_remark_too_short));
            return true;
        }
        remarkPost(mPostId, input);
        return true;
    }

    /**
     * 备注帖子
     */
    private void remarkPost(String postIdStr, String input) {
        Long postId = null;
        try {
            postId = Long.valueOf(postIdStr);
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        Map<String, Object> map = new HashMap<>();
        map.put("postId", postId);
        map.put("remarkDesc", input);
        L00bangRequestManager2.getServiceInstance()
                .addPostRemark(map)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<Object>(mContext) {
                    @Override
                    public void onSuccess(Object o) {
                        super.onSuccess(o);
                        //成功后关闭
                        ToastUtil.showMessage(mContext, mContext.getString(R.string.dialog_post_detail_remark_success));
                        PostDetailRemarkDialog.this.dismiss();
                    }
                });
    }
}
