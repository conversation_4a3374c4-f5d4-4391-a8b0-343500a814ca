package com.cloudy.linglingbang.activity.fragment.store.service;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseScrollTabViewPagerFragment;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.app.util.DeprecatedUtils;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.PagerSlidingTabStrip;
import com.cloudy.linglingbang.constants.FinalSensors;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.viewpager.widget.PagerAdapter;

/**
 * 服务产品页面
 *
 * <AUTHOR>
 * @date 2019-11-13
 */
public class ServiceStoreFragment extends BaseScrollTabViewPagerFragment<Fragment> {

    /**
     * 组件页
     */
    private String mPageCode;

    private ServiceStoreOfficialFragment mServiceStoreOfficialFragment;
    private ServiceStore4SFragment mServiceStore4SFragment;


    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_store_home_service_store;
    }

    public static Fragment newInstance(String pageCode) {
        return IntentUtils.setFragmentArgument(new ServiceStoreFragment(), pageCode);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mPageCode = IntentUtils.getFragmentArgument(this);
    }

    @Override
    protected void initViews() {
        //tab
        PagerSlidingTabStrip tabStrip = mRootView.findViewById(R.id.tabs);
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.MATCH_PARENT);
        layoutParams.rightMargin = mRootView.getResources().getDimensionPixelOffset(R.dimen.normal_20);
        tabStrip.setDefaultTabLayoutParams(layoutParams);
        tabStrip.setListener(new PagerSlidingTabStrip.SingleListener() {
            @Override
            public TextView createTextTab(Context context) {
                //需要 Inflate
                return (TextView) DeprecatedUtils.inflateWithNullRoot(LayoutInflater.from(context), R.layout.item_store_home_service_store_tab_button);
            }
        });
        super.initViews();

    }

    @Override
    protected void onItemSelected(int position) {
        super.onItemSelected(position);
        SensorsUtils.sensorsClickBtn("点击" + titles[position], "好物", "好物");
    }

    @Override
    protected PagerAdapter createViewPagerAdapter(final List<Fragment> data, final String[] titles) {
        return new FragmentStatePagerAdapter(getChildFragmentManager()) {
            @Override
            public Fragment getItem(int position) {
                return data.get(position);
            }

            @Override
            public int getCount() {
                return data.size();
            }

            @Nullable
            @Override
            public CharSequence getPageTitle(int position) {
                return titles[position];
            }
        };
    }

    @Override
    protected List<Fragment> createAdapterData() {
        List<Fragment> fragmentList = new ArrayList<>();
        mServiceStoreOfficialFragment = new ServiceStoreOfficialFragment();
        Bundle bundle = new Bundle();
        bundle.putString(IntentUtils.INTENT_EXTRA_COMMON, mPageCode);
        //埋点使用
        bundle.putSerializable(IntentUtils.INTENT_EXTRA_FROM, new SensorsUtils.StoreHomeAnchor(getTitles()[0], getTitles()[0]));
        mServiceStoreOfficialFragment.setArguments(bundle);
        mServiceStore4SFragment = ServiceStore4SFragment.newInstance();
        Bundle bundle2 = new Bundle();
        bundle2.putString(IntentUtils.INTENT_EXTRA_COMMON, mPageCode);
        //埋点使用
        bundle2.putSerializable(IntentUtils.INTENT_EXTRA_FROM, new SensorsUtils.StoreHomeAnchor(getTitles()[1], getTitles()[1]));

        mServiceStore4SFragment.setArguments(bundle2);
        fragmentList.add(mServiceStoreOfficialFragment);
        fragmentList.add(mServiceStore4SFragment);
        return fragmentList;
    }

    @Override
    protected String[] getTitles() {
        return getResources().getStringArray(R.array.store_home_service_store_title_array);
    }



    @Override
    public void onStart() {
        super.onStart();
        SensorsUtils.sensorsViewStart(FinalSensors.STORE_SERVICE_PAGE_NAME);
    }

    @Override
    public void onStop() {
        super.onStop();
        SensorsUtils.sensorsViewEndNew("服务套餐列表", FinalSensors.STORE_SERVICE_PAGE, FinalSensors.STORE_SERVICE_PAGE_NAME);
    }
}
