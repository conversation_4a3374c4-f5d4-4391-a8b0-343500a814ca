package com.cloudy.linglingbang.activity.fragment.store.hotel.order;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.widget.FlowLayout;
import com.cloudy.linglingbang.app.widget.dialog.alert.BaseBottomAlertDialog;
import com.cloudy.linglingbang.app.widget.textview.PressEffectiveCompoundButton;
import com.cloudy.linglingbang.model.hotel.TimeInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * 选择到店时间的弹窗
 *
 * <AUTHOR>
 * @date 2021/9/6
 */
public class SelectTimeDialog extends BaseBottomAlertDialog {

    private FlowLayout mFlowLayout;
    //默认值5

    private final List<TimeInfo> timeList;
    private final List<PressEffectiveCompoundButton> mPressEffectiveCompoundButtonList = new ArrayList<>();

    public SelectTimeDialog(Context context, List<TimeInfo> list) {
        super(context);
        timeList = list;

    }

    @Override
    protected void initView() {
        super.initView();
        mFlowLayout = findViewById(R.id.flow_layout);
        updateUi();

    }

    private void updateUi() {
        mFlowLayout.removeAllViews();
        LayoutInflater inflater = LayoutInflater.from(mContext);
        for (int i = 0; i < timeList.size(); i++) {
            PressEffectiveCompoundButton compoundButton = (PressEffectiveCompoundButton)
                    inflater.inflate(R.layout.item_in_hotel_time, mFlowLayout, false);
            compoundButton.setText(timeList.get(i).getTime());
            int finalI = i;
            compoundButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mOnChooseListener != null) {
                        mOnChooseListener.chooseTime(finalI);
                    }
                    dismiss();
                    notifyFlowLayout(finalI);
                }
            });
            compoundButton.setEnabled(timeList.get(i).getEnable() == 1);
            if (timeList.get(i).getEnable() == 2) {
                compoundButton.setTextColor(mContext.getResources().getColor(R.color.color_3d383a40));
            } else {
                if (timeList.get(i).getCheckStatus() == 2) {
                    compoundButton.setTextColor(mContext.getResources().getColor(R.color.color_383a40));
                } else {
                    compoundButton.setTextColor(mContext.getResources().getColor(R.color.white));
                }
            }
            compoundButton.setChecked(timeList.get(i).getCheckStatus() == 1);
            mFlowLayout.addView(compoundButton);
            mPressEffectiveCompoundButtonList.add(compoundButton);
        }
    }

    private void notifyFlowLayout(int position) {
        for (int i = 0; i < timeList.size(); i++) {
            timeList.get(i).setCheckStatus(2);
        }
        timeList.get(position).setCheckStatus(1);
        for (int i = 0; i < timeList.size(); i++) {
            if (timeList.get(i).getCheckStatus() == 1) {
                mPressEffectiveCompoundButtonList.get(i).setChecked(true);
            } else {
                mPressEffectiveCompoundButtonList.get(i).setChecked(timeList.get(i).getCheckStatus() == 1);
            }
            mPressEffectiveCompoundButtonList.get(i).setEnabled(timeList.get(i).getEnable() == 1);
            if (timeList.get(i).getEnable() == 2) {
                mPressEffectiveCompoundButtonList.get(i).setTextColor(mContext.getResources().getColor(R.color.color_3d383a40));
            } else {
                mPressEffectiveCompoundButtonList.get(i).setTextColor(mContext.getResources().getColor(R.color.color_383a40));
            }
        }
    }

    @Override
    protected int getDefaultLayoutResId() {
        return R.layout.dialog_select_time;
    }

    public void setChooseListener(OnChooseListener chooseListener) {
        mOnChooseListener = chooseListener;
    }

    OnChooseListener mOnChooseListener;

    /**
     * 设置图片删除监听
     */
    public interface OnChooseListener {
        void chooseTime(int index);
    }
}
