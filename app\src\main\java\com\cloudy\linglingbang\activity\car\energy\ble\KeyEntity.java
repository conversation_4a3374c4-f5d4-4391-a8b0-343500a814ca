package com.cloudy.linglingbang.activity.car.energy.ble;

import android.text.TextUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @describe 蓝牙钥匙实体类
 * @date 2021/12/28
 */
public class KeyEntity implements Serializable {
    private String mac;
    private String masterKey;
    private String masterKeyRandom;
    private String bleKey;

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getMasterKey() {
        return masterKey;
    }

    public void setMasterKey(String masterKey) {
        this.masterKey = masterKey;
    }

    public String getMasterKeyRandom() {
        return masterKeyRandom;
    }

    public void setMasterKeyRandom(String masterKeyRandom) {
        this.masterKeyRandom = masterKeyRandom;
    }

    public String getBleKey() {
        return bleKey;
    }

    public void setBleKey(String bleKey) {
        this.bleKey = bleKey;
    }

    public boolean isDataGood() {
        return !TextUtils.isEmpty(mac) && !TextUtils.isEmpty(masterKey) && !TextUtils.isEmpty(masterKeyRandom) && !TextUtils.isEmpty(bleKey);
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("KeyEntity{");
        sb.append("mac='").append(mac).append('\'');
        sb.append(", masterKey='").append(masterKey).append('\'');
        sb.append(", masterKeyRandom='").append(masterKeyRandom).append('\'');
        sb.append(", bleKey='").append(bleKey).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
