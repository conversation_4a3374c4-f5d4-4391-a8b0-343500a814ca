package com.cloudy.linglingbang.activity.store.commodity.adapter.holder

import android.view.View
import com.cloudy.linglingbang.app.widget.SlidingButtonView

/**
 * 购物车操作回调接口
 *
 * <AUTHOR>
 * @date 2022/10/19
 */
interface OnChangeCommodityListener {
    /** 选择或取消选择一个 */
    fun onChangeSingleSelect(cartId: Long, isSelect: Int)

    /** 选择或取消选择多个 */
    fun onChangeSelect(cartIds: MutableList<Long>, isSelect: Int)

    /** 删除一个 */
    fun onDeleteSingle(cartId: Long)

    /** 删除多个 */
    fun onDeleteSingle(cartIds: MutableList<Long>, tips: String)

    /** 修改数量 */
    fun changeNumToCart(cartId: Long, count: Long)

    /** 打开菜单 */
    fun onMenuIsOpen(view: View?)

    /** 菜单移动活关闭 */
    fun onDownOrMove(slidingButtonView: SlidingButtonView?)


}