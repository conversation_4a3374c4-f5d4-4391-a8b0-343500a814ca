package com.cloudy.linglingbang.activity.fragment.store.home;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.aspsine.swipetoloadlayout.OnRefreshListener;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshFragment;
import com.cloudy.linglingbang.activity.basic.IRefreshContext;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.fragment.store.home.adapter.StoreHomeAdapter;
import com.cloudy.linglingbang.activity.fragment.store.home.model.FragmentInitHelper;
import com.cloudy.linglingbang.activity.fragment.store.home.model.StoreHomeConstant;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.util.timer.CountDownManager;
import com.cloudy.linglingbang.constants.FinalSensors;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.server.Ad.Ad2;
import com.cloudy.linglingbang.model.server.Ad.AdRequestUtil2;
import com.cloudy.linglingbang.model.wrapper.AdListWrapper;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 商城的各 tab
 *
 * <AUTHOR>
 * @date 2018/10/10
 */
public class StoreHomeTabFragment extends BaseRecyclerViewRefreshFragment<Object> {
    /**
     * 组件页
     */
    protected String mPageCode;
    /**
     * 用于控制只初始化一次
     */
    private FragmentInitHelper mInitHelper;
    /**
     * 刷新回调
     */
    private OnRefreshListener mOnRefreshListener;
    /**
     * 广告
     */
    protected AdListWrapper mAdListWrapper;
    /**
     * 组件元素
     */
    protected List<Object> mElementList;
    /**
     * 持有以只获取一次，用于区分倒计时
     */
    private String mCountDownTag;

    /**
     * parent 是否可见，默认为 true，用于 resume 时判断是否需要继续
     */
    private boolean mParentVisible = true;

    /**
     * 埋点信息，传给 adapter
     */
    protected SensorsUtils.StoreHomeAnchor mStoreHomeAnchor;

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_store_home_tab;
    }


    public static Fragment newInstance(String pageCode) {
        return IntentUtils.setFragmentArgument(new StoreHomeTabFragment(), pageCode);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        mInitHelper = new FragmentInitHelper(this) {
            @Override
            protected void init() {
                super.init();
                initLoad();
            }
        };
        super.onCreate(savedInstanceState);
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<Object> list) {
        return new StoreHomeAdapter(getContext(), list)
                .setCountDownTag(getCountDownTag())
                .setStoreHomeAnchor(mStoreHomeAnchor);
    }

    @Override
    public void onPause() {
        super.onPause();
        CountDownManager.getInstance().onPause(getCountDownTag());
        setBannerViewTurning(false);
    }

    @Override
    public void onResume() {
        super.onResume();
        /*
        如果在其他 Fragment 转到其他 Activity 并返回
        分发 onResume，当前 isHidden() 为 false，getUserVisibleHint() 为 true
        所以需要再判断 mParentVisible
         */
        if (mParentVisible && getUserVisibleHint()) {
            //如果可见再添加
            CountDownManager.getInstance().onResume(getCountDownTag());
        }
        setBannerViewTurning(true);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        CountDownManager.getInstance().onDestroy(getCountDownTag());
    }

    /**
     * 设置轮播组件滚动或者暂停
     */
    private void setBannerViewTurning(boolean start) {
        RefreshController<Object> refreshController = getRefreshController();
        if (refreshController != null) {
            RecyclerView.Adapter<? extends RecyclerView.ViewHolder> adapter = refreshController.getAdapter();
            if (adapter instanceof StoreHomeAdapter) {
                ((StoreHomeAdapter) adapter).startOrStop(start);
            }
        }
    }

    /**
     * 因为各 Fragment 相同，所以再加上 pageCode
     */
    protected String getCountDownTag() {
        if (mCountDownTag == null) {
            mCountDownTag = CountDownManager.getStringCountDownTag(this) + "-" + mPageCode;
        }
        return mCountDownTag;
    }

    @Override
    public Observable<BaseResponse<List<Object>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        // 由 Controller 实现
        return null;
    }

    @Override
    public RefreshController<Object> createRefreshController() {
        //在 onAttach 调用创建
        mPageCode = IntentUtils.getFragmentArgument(this);
        initAnchor();
        return new StoreHomeTabRefreshController(this, mPageCode);
    }

    public void setOnRefreshListener(OnRefreshListener onRefreshListener) {
        mOnRefreshListener = onRefreshListener;
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if (mInitHelper != null) {
            mInitHelper.checkAndInit();
            if (mInitHelper.isInitialized()) {
                RefreshController<Object> refreshController = getRefreshController();
                if (refreshController != null) {
                    RecyclerView.Adapter<? extends RecyclerView.ViewHolder> adapter = refreshController.getAdapter();
                    if (adapter != null) {
                        List<Object> data = refreshController.getData();
                        if (data != null && data.size() > 0) {
                            //如果是切换引起的，view 已经存在，需要让其重新布局，以调用 adapter 的相关方法
                            adapter.notifyDataSetChanged();
                        }
                    }
                }
            }
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (mInitHelper != null) {
            mInitHelper.setUserVisibleHint(isVisibleToUser);
            if (mInitHelper.isInitialized()) {
                //因为需要 tag，所以初始化以后再获取
                CountDownManager.getInstance().setUserVisibleHint(isVisibleToUser, getCountDownTag());
            }
        }
        setBannerViewTurning(isVisibleToUser);
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        setBannerViewTurning(!hidden);

        if (!TextUtils.isEmpty(mPageCode)) {
            String pageName = "";
            switch (mPageCode) {
                case StoreHomeConstant.PAGE_CODE_LIFE_GOODS:
                    pageName = "优品";
                    break;
                case StoreHomeConstant.PAGE_CODE_SERVICE_GOODS:
                    pageName = "服务";
                    break;
                case StoreHomeConstant.PAGE_CODE_REMOULD_RECOMMEND:
                    pageName = "改装-推荐";
                    break;
                case StoreHomeConstant.PAGE_CODE_GOODS_SERVICE:
                    pageName = "服务-推荐";
                    break;
            }
            if (hidden) {
                SensorsUtils.sensorsViewEndNew(pageName, FinalSensors.BROWSE_LIFE_INFORMATION, "浏览" + pageName);
            } else {
                SensorsUtils.sensorsViewStart(FinalSensors.BROWSE_LIFE_INFORMATION);
            }
        }
    }

    @Override
    public void onParentVisibilityChanged(boolean visible) {
        mParentVisible = visible;
        if (mInitHelper != null) {
            if (mInitHelper.isInitialized()) {
                CountDownManager.getInstance().onVisibilityChanged(visible, getCountDownTag());
            }
        }
        setBannerViewTurning(visible);
    }

    @Override
    public void loadRealView() {
        super.loadRealView();
        //在滑动时触发，实际不加载，只是检查并初始化
        if (mInitHelper != null) {
            mInitHelper.checkAndInit(false);
        }
    }

    private void initLoad() {
        RefreshController<Object> refreshController = getRefreshController();
        if (refreshController != null && refreshController.isFirstLoad()) {
            //因为初始化时不加载，因此在首次展示时加载
            refreshController.manualRefresh();
        }
    }

    //提供方法供子类重写，但要注意，不能使用内部类，因此还使用的 mOnRefreshListener

    /**
     * 刷新时
     */
    protected void onRefresh() {
        getAd();
        if (mOnRefreshListener != null) {
            mOnRefreshListener.onRefresh();
        }
    }

    /**
     * 获取广告的 pageCode 如果不为 0，则加载广告
     */
    protected int getAdPageCode() {
        if (mPageCode.equals(StoreHomeConstant.PAGE_CODE_RECOMMEND)) {
            //推荐
            return Ad2.POSITION.STORE_HOME_TOP_BANNER;
        } else if (mPageCode.equals(StoreHomeConstant.PAGE_CODE_CAR)) {
            //整车
            return Ad2.POSITION.STORE_HOME_CAR;
        }
        return 0;
    }

    /**
     * 获取广告
     */
    private void getAd() {
        int adPageCode = getAdPageCode();
        if (adPageCode <= 0) {
            return;
        }
        AdRequestUtil2.getAdByPageCode(getContext(), adPageCode, true, new AdRequestUtil2.OnLoadAdListener() {
            @Override
            public void onLoadSuccess(List<Ad2> ads, int loadType) {
                mAdListWrapper = new AdListWrapper(ads);
                onUpdateData();
            }

            @Override
            public void onFailure(Throwable e, int loadType) {

            }
        });
    }

    /**
     * 数据更新
     */
    protected void onUpdateData() {
        RefreshController<Object> refreshController = getRefreshController();
        if (refreshController != null) {
            List<Object> data = refreshController.getData();
            if (data == null) {
                data = new ArrayList<>();
            }
            data.clear();
            data.addAll(buildData());
            RecyclerView.Adapter<? extends RecyclerView.ViewHolder> adapter = refreshController.getAdapter();
            if (adapter != null) {
                adapter.notifyDataSetChanged();
            }
        }
    }

    /**
     * 构建用于展示的数据
     */
    protected List<Object> buildData() {
        List<Object> data = new ArrayList<>();

        //广告
        if (mAdListWrapper != null) {
            List<Ad2> adList = mAdListWrapper.getOriginal();
            if (adList != null && adList.size() > 0) {
                data.add(mAdListWrapper);
            }
        }

        //组件元素
        if (mElementList != null) {
            data.addAll(mElementList);
        }
        return data;
    }

    // 补充方法

    /**
     * 初始化埋点
     */
    private void initAnchor() {
        Bundle bundle = getArguments();
        if (bundle != null) {
            mStoreHomeAnchor = (SensorsUtils.StoreHomeAnchor) bundle.getSerializable(IntentUtils.INTENT_EXTRA_FROM);
        }
        if (mStoreHomeAnchor == null) {
            //如果为 null，根据 pageCode 手动创建一个
            //车页面和推荐页面走这里创建
            mStoreHomeAnchor = new SensorsUtils.StoreHomeAnchor(mPageCode, StoreHomeConstant.getPageNameByPageCode(mPageCode));
        }
    }

    public void setStoreHomeAnchor(SensorsUtils.StoreHomeAnchor storeHomeAnchor) {
        mStoreHomeAnchor = storeHomeAnchor;
    }

    protected class StoreHomeTabRefreshController extends StoreHomeRefreshController {
        public StoreHomeTabRefreshController(IRefreshContext<Object> refreshContext, String pageCode) {
            super(refreshContext, pageCode);
        }

        @Override
        protected boolean loadDataAfterInitViews() {
            //不主动调用，由 initHelper 处理
            return false;
        }

        @Override
        public void onRefresh() {
            super.onRefresh();
            StoreHomeTabFragment.this.onRefresh();
        }

        @Override
        protected void onLoadSuccess(int loadPage, List<Object> list, int loadType) {
            mElementList = list;
            List<Object> data = StoreHomeTabFragment.this.buildData();
            super.onLoadSuccess(loadPage, data, loadType);
        }

        @Override
        public int getEmptyImageResId() {
            if (mPageCode.equals(StoreHomeConstant.PAGE_CODE_REMOULD_RECOMMEND)) {
                return R.drawable.ic_store_vip_empty;
            }
            return super.getEmptyImageResId();
        }

        @Override
        public String getEmptyString() {
            if (mPageCode.equals(StoreHomeConstant.PAGE_CODE_REMOULD_RECOMMEND)) {
                return getContext().getString(R.string.store_home_vip_empty);
            }
            return super.getEmptyString();
        }

    }

    @Override
    public void onStart() {
        super.onStart();
        if (!TextUtils.isEmpty(mPageCode)) {
            SensorsUtils.sensorsViewStart(FinalSensors.BROWSE_LIFE_INFORMATION);
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        if (!TextUtils.isEmpty(mPageCode)) {
            String pageName = "";
            switch (mPageCode) {
                case StoreHomeConstant.PAGE_CODE_LIFE_GOODS:
                    pageName = "优品";
                    break;
                case StoreHomeConstant.PAGE_CODE_SERVICE_GOODS:
                    pageName = "服务";
                    break;
                case StoreHomeConstant.PAGE_CODE_REMOULD_RECOMMEND:
                    pageName = "改装-推荐";
                    break;
                case StoreHomeConstant.PAGE_CODE_GOODS_SERVICE:
                    pageName = "服务-推荐";
                    break;
            }
            SensorsUtils.sensorsViewEndNew(pageName, FinalSensors.BROWSE_LIFE_INFORMATION, "浏览" + pageName);
        }

    }
}
