package com.cloudy.linglingbang;

import android.content.Context;
import android.view.View;
import android.widget.TextView;

import com.cloudy.linglingbang.activity.debug.TestActivity;

import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;

import androidx.test.rule.ActivityTestRule;

/**
 * 基类测试
 * 如果需要测试单个方法，只需要继承，并重写 {@link #test()} 即可
 * <p>
 * 如果类中有多个方法需要测试，则需要等待操作的方法应调用 {@link #waitAndLoop()}
 *
 * <AUTHOR>
 * @date 2018/7/12
 */
public class BaseInstrumentedTest {
    /**
     * 持有 Activity
     * <p>
     * 简单的 context 可以使用 InstrumentationRegistry.getTargetContext();
     * 但是该 context 不是 Activity 也不能用于显示对话框
     */
    protected TestActivity mActivity;
    @Rule
    public ActivityTestRule<TestActivity> mActivityRule = new ActivityTestRule<>(TestActivity.class);

    @Before
    public void before() {
        mActivity = mActivityRule.getActivity();
        //将 TextView 的点击事件也设为 test()
        TextView textView = mActivity.getTextView();
        if (textView != null) {
            textView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    test();
                }
            });
        }
    }

    /**
     * 测试后是否需要等待用户操作
     * 如果是，将不会结束测试
     */
    protected boolean needWait() {
        return true;
    }

    /**
     * 实际测试的方法，如果 needWait 将会停留在页面
     */
    @Test
    public void testAndWait() {
        mActivity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                test();
            }
        });
        waitAndLoop();
    }

    /**
     * 循环等待
     * 如果子类的单个测试方法需要等待操作，应调用此方法
     */
    protected void waitAndLoop() {
        while (needWait()) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

        }
    }

    /**
     * 子类可重写要测试的内容
     */
    public void test() {
    }

    //公共方法
    public TestActivity getActivity() {
        return mActivity;
    }

    public Context getContext() {
        return mActivity;
    }
}
