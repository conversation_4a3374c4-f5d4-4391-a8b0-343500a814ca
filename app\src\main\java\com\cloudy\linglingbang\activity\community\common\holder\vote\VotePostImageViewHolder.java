package com.cloudy.linglingbang.activity.community.common.holder.vote;

import android.content.Context;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.community.common.holder.BasePostChildViewHolder;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.model.postcard.PostCard;

/**
 * 投票帖-图片
 *
 * <AUTHOR>
 * @date 2018/8/18
 */
public class VotePostImageViewHolder extends BasePostChildViewHolder {
    private LinearLayout mLlVote;
    private TextView mTvTitle;
    private TextView mTvReward;
    /**
     * 时间虽然在底部部分，但是在一个布局中，所以也可以处理
     */
    private TextView mTvTime;

    public VotePostImageViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        mLlVote = itemView.findViewById(R.id.ll_vote);
        mTvTitle = itemView.findViewById(R.id.tv_title);
        mTvReward = itemView.findViewById(R.id.tv_reward);
        mTvTime = itemView.findViewById(R.id.tv_time);
    }

    @Override
    public void bindTo(PostCard postCard) {
        super.bindTo(postCard);
        if (postCard != null) {
            Context context = mTvTitle.getContext();
            mTvTitle.setText(postCard.getPostTitle());
//            mTvReward.setText(context.getString(R.string.item_vote_post_reward, postCard.getVoteRewardValueOrZero()));
            mTvTime.setText(AppUtil.formatDate(postCard.getVoteExpireTimeOrZero(), context.getString(R.string.item_vote_post_expire_time)));
            long postTypeId = postCard.getPostTypeIdOrNegative();
            int itemBgResId;
            int tvRewardBgResId;
            if (postTypeId == PostCard.PostType.VOTE) {
                itemBgResId = R.drawable.bg_item_vote_post;
                tvRewardBgResId = R.drawable.bg_item_vote_post_reward;
            } else if (postTypeId == PostCard.PostType.QUESTIONNAIRE) {
                itemBgResId = R.drawable.bg_item_questionnaire_post;
                tvRewardBgResId = R.drawable.bg_item_questionnaire_post_reward;
            } else {
                itemBgResId = 0;
                tvRewardBgResId = 0;
            }
            mLlVote.setBackgroundResource(itemBgResId);
            mTvReward.setBackgroundResource(tvRewardBgResId);
        }
    }
}
