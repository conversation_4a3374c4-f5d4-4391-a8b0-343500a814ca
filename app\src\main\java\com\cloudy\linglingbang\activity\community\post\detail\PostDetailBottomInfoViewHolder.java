package com.cloudy.linglingbang.activity.community.post.detail;

import android.app.Dialog;
import android.content.Context;
import android.content.res.Resources;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.community.CarBuyingExperienceFillInfoActivity;
import com.cloudy.linglingbang.activity.community.common.holder.NewPostDetailBottomInfoViewHolder;
import com.cloudy.linglingbang.activity.community.linglab.CommunityDoPostLingLabActivity;
import com.cloudy.linglingbang.activity.community.post.CommunityUtils;
import com.cloudy.linglingbang.activity.community.post.PostDetailActivity;
import com.cloudy.linglingbang.activity.community.post.PostReportActivity;
import com.cloudy.linglingbang.activity.newcommunity.PublishGoodsExchangeInfoActivity;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.util.ViewHolderUtils;
import com.cloudy.linglingbang.app.widget.BaseOnClickListener;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.user.User;

/**
 * 详情底部
 *
 * <AUTHOR>
 * @date 2018/6/26
 */
public class PostDetailBottomInfoViewHolder extends NewPostDetailBottomInfoViewHolder {
    private TextView mTvDelete;
    private TextView mTvReport;
    private TextView mTvEdit;
    private TextView mTvRemark;
    private TextView mTvSticky;
    private TextView mTvLocation;
    private TextView mTvNotifyUsers;
    private TextView mTvChannelName;
    private GoodsStickyDialog.GoodsStickyUtils goodsStickyUtils;

    public PostDetailBottomInfoViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        mTvDelete = itemView.findViewById(R.id.tv_delete);
        mTvEdit = itemView.findViewById(R.id.tv_edit);
        mTvReport = itemView.findViewById(R.id.tv_report);
        mTvRemark = itemView.findViewById(R.id.tv_remark);
        mTvSticky = itemView.findViewById(R.id.tv_sticky);
        mTvNotifyUsers = itemView.findViewById(R.id.tv_notify_users);
        mTvLocation = itemView.findViewById(R.id.tv_location);
        mTvChannelName = itemView.findViewById(R.id.tv_channel_name);

        //备注
        ViewHolderUtils.setOnClickListener(new BaseOnClickListener(true) {
            @Override
            public void onClick(Context context) {
                super.onClick(context);
                onClickTvRemark(context);
            }
        }, mTvRemark);
    }

    @Override
    public void bindTo(final PostCard postCard) {
        super.bindTo(postCard);
        //由于在详情里面，只需要一次设置展示
        if (mPostCard.getAuthor() != null) {
            User author = mPostCard.getAuthor();
            User currentUser = User.getsUserInstance();
            if (currentUser != null && currentUser.getUserIdStr() != null && currentUser.getUserIdStr().equals(author.getUserIdStr())) {
                //当前用户是作者
                //删除
                if (canDeletePostcard(mPostCard)) {
                    mTvDelete.setVisibility(View.VISIBLE);
                    mTvDelete.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            CommunityUtils.PostUtils postUtils = new CommunityUtils.PostUtils(v.getContext(), mPostCard)
                                    .setFinishAfterDelete(true);
                            postUtils.showSelfDeleteConfirmDialog();
                        }
                    });
                } else {
                    mTvDelete.setVisibility(View.GONE);
                }
                //编辑
                if (canEditPostcard(mPostCard)) {
                    mTvEdit.setVisibility(View.VISIBLE);
                    mTvEdit.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            //如果是好货贴，编辑时候从第一步开始编辑
                            if (mPostCard.getSeries() == 3) {
                                PublishGoodsExchangeInfoActivity.startActivity(v.getContext(), mPostCard);
                            } else {
                                goEditPostCard(v.getContext(), mPostCard);
                            }
                        }
                    });
                } else {
                    mTvEdit.setVisibility(View.GONE);
                }
            }
        }
        //举报
        if (canReportPostcard(mPostCard)) {
            mTvReport.setVisibility(View.VISIBLE);
            mTvReport.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (AppUtil.checkLogin(v.getContext())) {
                        if (mPostCard.getProsecutionUncheck() == 1) {
                            ToastUtil.showMessage(v.getContext(), R.string.post_detail_toast_prosecution);
                            return;
                        }
                        //跳转举报详情页
                        IntentUtils.startActivityForResult(AppUtil.getActivity(v.getContext()), PostReportActivity.class, PostDetailActivity.REQUEST_CODE_COMMON, mPostCard.getPostId());
                    }
                }
            });
        } else {
            mTvReport.setVisibility(View.GONE);
        }

        //备注
        ViewHolderUtils.setVisibility(showRemark(mPostCard), mTvRemark);
        //位置
        ViewHolderUtils.setVisibility(showLocation(mPostCard), mTvLocation);
        mTvLocation.setText(mTvLocation.getResources().getString(R.string.post_location, mPostCard.getLocationName()));
        //来源渠道
//        mPostCard.setChannelInfoName("来源：转载至老司机");
        ViewHolderUtils.setVisibility(!TextUtils.isEmpty(mPostCard.getChannelInfoName()), mTvChannelName);
        mTvChannelName.setText(mPostCard.getChannelInfoName());

        //提醒 @用户
        if (TextUtils.isEmpty(mPostCard.getMentionUserNameListStr())) {
            mTvNotifyUsers.setVisibility(View.GONE);
        } else {
            mTvNotifyUsers.setVisibility(View.VISIBLE);
            mTvNotifyUsers.setText(mPostCard.getMentionUserNameListStr());
        }

        //好货互通-置顶
        ViewHolderUtils.setVisibility(showSticky(mPostCard), mTvSticky);
        if (postCard.getTopExpireDate() < AppUtil.getServerCurrentTime()) {
            mTvSticky.setText(R.string.post_operate_top);
        } else {
//            long time = postCard.getTopExpireDate() - AppUtil.getServerCurrentTime();
//            mTvSticky.setText(String.format("剩余置顶：%1s小时", String.valueOf(time / 3600_000)));
            mTvSticky.setText(R.string.post_operate_top);
        }
        mTvSticky.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (showSticky(mPostCard) && AppUtil.getActivity(v.getContext()) != null) {
                    if (mPostCard.getIsTop() == 1) {
                        Resources resources = v.getResources();
                        if (postCard.getTopExpireDate() > AppUtil.getServerCurrentTime()) {
                            long time = postCard.getTopExpireDate() - AppUtil.getServerCurrentTime();
                            //展示置顶剩余时间
                            CommonAlertDialog dialog = new CommonAlertDialog(v.getContext(), resources.getString(R.string.text_post_set_ding_haved_time, String.valueOf(time / 3600_000), String.valueOf(time / 1000 / 60 % 60)), null, "知道了", null, null);
                            dialog.show();
                        } else {
                            CommonAlertDialog dialog = new CommonAlertDialog(v.getContext(), resources.getString(R.string.text_post_set_ding_haved), null, resources.getString(R.string.label_driving_rec_close), null, null);
                            dialog.show();
                        }
                    } else {
                        //设置置顶
                        if (goodsStickyUtils == null) {
                            goodsStickyUtils = new GoodsStickyDialog.GoodsStickyUtils(v.getContext(), postCard.getPostId(), false);
                        }
                        goodsStickyUtils.queryStickyNewDateAndShowDialog(new GoodsStickyDialog.StickyAction() {
                            @Override
                            public void onCall(boolean result, long date) {
                                // 置顶成功,把置顶失效时间置位null,这样方便判断弹窗即 告诉用户您的帖子已置顶 或者显示置顶剩余时间弹窗
                                mPostCard.setTopExpireDate(result ? null : mPostCard.getTopExpireDate());
                                mPostCard.setIsTop(result ? 1 : 0);
                            }
                        });
                    }
                }
            }
        });
    }

    /**
     * 帖子是否可删除
     */
    private boolean canDeletePostcard(PostCard postCard) {
        //课堂帖不可删除
        return postCard != null && postCard.getPostTypeIdOrNegative() != PostCard.PostType.OFFICIAL_COURSE;
    }

    /**
     * 帖子是否可以编辑
     */
    private static boolean canEditPostcard(PostCard postCard) {
        int postTypeId = postCard.getPostTypeIdOrNegative();
        //图文，提车作业帖，话题
        return postTypeId == PostCard.PostType.IMAGE_TEXT || postTypeId == PostCard.PostType.LING_SENSE || postTypeId == PostCard.PostType.CAR_BUYING_EXPERIENCE || postTypeId == PostCard.PostType.TOPIC;
    }

    /**
     * 帖子是否可以举报
     */
    private static boolean canReportPostcard(PostCard postCard) {
        //自己的帖子不可以举报
        return !UserUtils.isSelf(postCard.getAuthor()) && User.getsUserInstance().hasLogin();

//        int postTypeId = postCard.getPostTypeIdOrNegative();
//        视频、图文、签到、话题、技师帖
//        return postTypeId == PostCard.PostType.IMAGE_TEXT || postTypeId == PostCard.PostType.TOPIC || postTypeId == PostCard.PostType.SIGN_IN || postTypeId == PostCard.PostType.VIDEO_POST;
    }

    /**
     * 是否展示备注
     */
    private boolean showRemark(PostCard postCard) {
        return postCard != null
                && postCard.getPostTypeIdOrNegative() == PostCard.PostType.ASK_TECHNICIAN//技师帖
                && UserUtils.isEngineer();//是工程师
    }

    /**
     * 是否展示位置
     */
    private boolean showLocation(PostCard postCard) {
        return postCard != null
                && postCard.getLocationName() != null
                && postCard.getLocationDetail() != null
                && postCard.getLongitude() > 0
                && postCard.getLatitude() > 0;
    }

    /**
     * 是否展示置顶
     */
    private boolean showSticky(PostCard postCard) {
        //去掉了置顶的显示
        return false;
        //return postCard != null && postCard.getSeries() == 3 && UserUtils.isSelf(postCard.getAuthor());
    }

    /**
     * 点击备注
     */
    private void onClickTvRemark(Context context) {
        if (!showRemark(mPostCard)) {
            //可能用户状态变化，不可评论
            return;
        }
        Dialog dialog = new PostDetailRemarkDialog(context, mPostCard.getPostId());
        dialog.show();
    }

    /**
     * 跳转到编辑帖子
     */
    private static void goEditPostCard(Context context, PostCard postCard) {
        int postTypeId = postCard.getPostTypeIdOrNegative();
        switch (postTypeId) {
            case PostCard.PostType.LING_SENSE:
                // TODO: [liuliqiang create at 2021/9/6] 菱感贴编辑入口
                CommunityDoPostLingLabActivity.startActivity(context, postCard, 0, 0);
                break;
            case PostCard.PostType.IMAGE_TEXT:
                //图文
                JumpPageUtil.goEditPost(context, PostCard.PostType.EDIT_POST_CARD, postCard.getChannelId(), String.valueOf(postCard.getPostId()));
                break;
            case PostCard.PostType.CAR_BUYING_EXPERIENCE:
                //提车作业帖
                CarBuyingExperienceFillInfoActivity.startActivity(context, PostCard.PostType.EDIT_CAR_BUYING_POST_CARD, postCard);
                break;
            case PostCard.PostType.TOPIC:
                //话题
                JumpPageUtil.goEditPost(context, PostCard.PostType.EDIT_TOPIC, postCard.getChannelId(), String.valueOf(postCard.getPostId()));
                break;
            default:
                break;
        }
    }

    @Override
    protected boolean needShowPostType() {
        return false;
    }
}
