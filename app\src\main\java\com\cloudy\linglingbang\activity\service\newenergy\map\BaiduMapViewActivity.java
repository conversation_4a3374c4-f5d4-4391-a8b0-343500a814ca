package com.cloudy.linglingbang.activity.service.newenergy.map;

import android.Manifest;
import android.app.Activity;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import com.baidu.location.BDAbstractLocationListener;
import com.baidu.location.BDLocation;
import com.baidu.location.LocationClient;
import com.baidu.location.LocationClientOption;
import com.baidu.mapapi.map.BaiduMap;
import com.baidu.mapapi.map.BitmapDescriptor;
import com.baidu.mapapi.map.BitmapDescriptorFactory;
import com.baidu.mapapi.map.MapStatus;
import com.baidu.mapapi.map.MapStatusUpdateFactory;
import com.baidu.mapapi.map.MapView;
import com.baidu.mapapi.map.Marker;
import com.baidu.mapapi.map.MarkerOptions;
import com.baidu.mapapi.map.OverlayOptions;
import com.baidu.mapapi.model.LatLng;
import com.baidu.mapapi.utils.DistanceUtil;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.app.log.LogUtils;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.FastClickUtil;
import com.cloudy.linglingbang.app.util.map.NavigateUtil;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog;
import com.cloudy.linglingbang.model.car.map.BaiduEocoderResult;
import java.io.Serializable;
import butterknife.BindView;
import butterknife.OnClick;
import pub.devrel.easypermissions.EasyPermissions;

/**
 * 地图
 *
 * <AUTHOR>
 * @date 2019-07-15
 */
public class BaiduMapViewActivity extends BaseActivity implements EasyPermissions.RationaleCallbacks {

    private final String TAG = "BaiduMapViewActivityTag";

    @BindView(R.id.map_View)
    MapView mMapView;
    private BaiduMap mBaiduMap;

    @BindView(R.id.tv_title)
    TextView mTvTitle;
    /**
     * 地图底部车辆信息部分
     */
    @BindView(R.id.tv_car_name)
    TextView mTvCarName;
    @BindView(R.id.tv_car_current_position)
    TextView mTvCarCurrentPosition;
    @BindView(R.id.tv_car_distance)
    TextView mTvCarDistance;
    @BindView(R.id.tv_car_last_time)
    TextView mTvCarLastTime;
    /**
     * 人的位置
     */
    @BindView(R.id.iv_people_location)
    ImageView mIvPeopleLocation;

    /**
     * 标记显示车或者人位置信息
     */
    private int mark = MARK_LOCATION_PEOPLE;

    /**
     * 标记人的位置
     */
    private final static int MARK_LOCATION_PEOPLE = 1;
    /**
     * 标记车的位置
     */
    private final static int MARK_LOCATION_CAR = 2;

    private IntentExtra mIntentExtra;

    private double mDistance = -1;

    private final static int PERMISSION_REQUEST_CODE_LOCATION = 1101;
    /**
     * GPS未开启提示框
     */
    private CommonAlertDialog mGpsDialog;

    private LatLng mcarPoint;
    //定位相关
    private LocationClient mLocClient;
    //定位监听
    private BDAbstractLocationListener mLocationListener = new BDLocationListener();
    //定位结果
    private BDLocation mBDLocation;
    //是否移动到中心
    private boolean isMoveToCenter;

    public static void startActivity(Activity activity, IntentExtra intentExtra) {
        IntentUtils.startActivity(activity, BaiduMapViewActivity.class, intentExtra);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        MapUtils.init(this);
        super.onCreate(savedInstanceState);
        //恢复状态
        if (savedInstanceState != null) {
            mMapView.onCreate(this, savedInstanceState);
            LogUtils.e("onCreate = " + savedInstanceState);
            mMapView.showZoomControls(false);
            mBaiduMap = mMapView.getMap();
            mark = MARK_LOCATION_CAR;
            addMarkerByCarInfo(true);
        }
    }

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_map);
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        //保存状态
        mMapView.onSaveInstanceState(outState);
    }

    @Override
    protected void initialize() {
        Log.e(TAG, "进入地理位置页面");
        mTvTitle.setText("地理位置");
        mIntentExtra = (IntentExtra) getIntentExtra(null);
        if (mIntentExtra == null) {
            onIntentExtraError();
            return;
        }
        if (mBaiduMap == null) {
            mBaiduMap = mMapView.getMap();
        }

        mMapView.showZoomControls(false);
        if (mIntentExtra.lat == 0 || mIntentExtra.lng == 0) {
            getMyLocation(true);
        }else {
            getMyLocation(false);
        }

        getCarLocation();
    }

    /**
     * 定位SDK监听函数
     */
    public class BDLocationListener extends BDAbstractLocationListener {
        @Override
        public void onReceiveLocation(BDLocation bdLocation) {
            Log.e(TAG, "onReceiveLocation");
            if (bdLocation == null) {
                return;
            }

            final int locType = bdLocation.getLocType();
            if (locType == BDLocation.TypeGpsLocation ||locType == BDLocation.TypeNetWorkLocation || locType == BDLocation.TypeOffLineLocation) {
                //定位成功（GPS定位结果、网络定位结果、离线定位结果）
                onLocationSuccess(bdLocation);
            } else {
                Log.e(TAG, "locType = " + locType);
                final String result;
                switch (locType) {
                    case 62:
                        result = "无法定位结果，请关闭飞行模式或打开wifi后重试";
                        break;
                    case 63:
                        result = "网络异常，定位结果无效。";
                        break;
                    case 67:
                        result = "离线定位失败";
                        break;
                    case 68:
                        result = "查找本地离线定位失败";
                        break;
                    case 502:
                        result = "key参数错误";
                        break;
                    case 505:
                        result = "key不存在或者非法";
                        break;
                    case 601:
                        result = "key服务被开发者自己禁用";
                        break;
                    case 602:
                        result = "key code不匹配";
                        break;
                    default:
                        result = "定位失败";
                        break;
                }
                onLocationError(result);
            }
        }
    }

    private void onLocationSuccess(BDLocation entity) {
        mBDLocation = entity;
        Log.e(TAG, "获取当前位置成功,onLocationSuccess,人的经纬度=" + entity.getLongitude() + "," + entity.getLatitude());
        if (entity != null && entity.getLongitude() != 0 && entity.getLatitude() != 0) {
            mTvCarDistance.setVisibility(View.VISIBLE);
            clear();
            mark = MARK_LOCATION_PEOPLE;
            addMarkerByPeopleInfo(isMoveToCenter);
            if (mIntentExtra.lat == 0 || mIntentExtra.lng == 0) {
                mDistance = -1;
                mTvCarDistance.setText(getString(R.string.txt_distance_no));
            }else {
                addMarkerByCarInfo(!isMoveToCenter);
                LatLng peoplePoint = new LatLng(entity.getLatitude(), entity.getLongitude());
                mDistance = DistanceUtil.getDistance(peoplePoint, mcarPoint);
                mTvCarDistance.setText(getString(R.string.txt_distance_car, getDistanceStr(mDistance)));
            }
        } else {
            mDistance = -1;
            mTvCarDistance.setText(getString(R.string.txt_distance_no));
        }
    }

    private void onLocationError(String errMsg) {
        Log.e(TAG, "onLocationError = " + errMsg);
        mDistance = -1;
        mTvCarDistance.setText(getString(R.string.txt_distance_no));
    }

    /**
     * 权限检查完成，准备开始定位
     * @param isGranted
     * @param requestCode
     */
    private void permissionResult(boolean isGranted, int requestCode){
        if (requestCode == PERMISSION_REQUEST_CODE_LOCATION) {
            //有权限
            if (isGranted) {
                // 定位初始化
                if(mLocClient == null){
                    mLocClient = new LocationClient(this);
                    //设置监听，定位回调是 mLocationListener
                    mLocClient.registerLocationListener(mLocationListener);
                    LocationClientOption option = new LocationClientOption();
                    option.setLocationMode(LocationClientOption.LocationMode.Hight_Accuracy);//可选，默认高精度，设置定位模式，高精度，低功耗，仅设备
                    option.setCoorType("gcj02");//可选，默认gcj02，设置返回的定位结果坐标系，如果配合百度地图使用，建议设置为bd09ll;
                    option.setScanSpan(0);//可选，默认0，即仅定位一次，设置发起定位请求的间隔需要大于等于1000ms才是有效的
                    option.setIsNeedAddress(true);//可选，设置是否需要地址信息，默认不需要
                    option.setNeedDeviceDirect(false);//可选，设置是否需要设备方向结果
                    option.setLocationNotify(false);//可选，默认false，设置是否当gps有效时按照1S1次频率输出GPS结果
                    option.setIgnoreKillProcess(true);//可选，默认true，定位SDK内部是一个SERVICE，并放到了独立进程，设置是否在stop的时候杀死这个进程，默认不杀死
                    option.setIsNeedLocationDescribe(true);//可选，默认false，设置是否需要位置语义化结果，可以在BDLocation.getLocationDescribe里得到，结果类似于“在北京天安门附近”
                    option.setOpenGps(true);//可选，设置是否使用gps，默认false，使用高精度和仅用设备两种定位模式的，参数必须设置为true
                    mLocClient.setLocOption(option);

                    mLocClient.start();
                }else {
                    mLocClient.requestLocation();
                }
            } else {
                //如果没有权限，则返回没有权限
                onLocationError("权限被拒绝，请为五菱汽车开启定位权限");
            }
        }
    }

    /**
     * 获取当前位置信息
     */
    private void getMyLocation(boolean isMoveToCenter) {
        if (!AppUtil.isOpenLocationService(this)) {
            showGpsDialog();
            return;
        }
        this.isMoveToCenter = isMoveToCenter;
        //检查是否有权限
        checkPermissions(PERMISSION_REQUEST_CODE_LOCATION, getString(R.string.permission_location_pre), getString(R.string.permission_location_setting), Manifest.permission.ACCESS_FINE_LOCATION);
    }

    /**
     * 距离显示
     * >1000米，显示km，保留一位小数
     * <1000米，显示m，保留整数
     */
    private String getDistanceStr(double distanceOld) {
        String distanceStr;
        if (distanceOld <= 0) {
            distanceStr = "0m";
        } else if (distanceOld < 1000) {
            distanceStr = String.format("%.0f", distanceOld) + "m";
        } else {
            distanceStr = String.format("%.1f", distanceOld / 1000) + "km";
        }
        return distanceStr;
    }

    /**
     * 查询车辆位置信息
     */
    private void getCarLocation() {
        if (mIntentExtra.lat == 0 || mIntentExtra.lng == 0) {
            mIntentExtra.address = getString(R.string.txt_location_failure);
        }else {
            //根据经纬度获取地址信息
            EocoderUtils.getAddressInfo(this, mIntentExtra.lat, mIntentExtra.lng, new EocoderUtils.OnResultListener() {
                @Override
                public void onSuccess(BaiduEocoderResult.EocodeAddressInfo result) {
                    if (!TextUtils.isEmpty(result.getFormatted_address())) {
                        mIntentExtra.address = result.getFormatted_address();
                        clear();
                        addMarkerByCarInfo(true);
                        addMarkerByPeopleInfo(false);
                    } else {
                        mIntentExtra.address = getString(R.string.txt_location_failure);
                    }
                }

                @Override
                public void onFailure(String message) {
                    mIntentExtra.address = getString(R.string.txt_location_failure);
                }
            });
        }

    }

    /**
     * 在地图上添加车的标记
     *
     * @param isToCenter 是否移动至屏幕中心
     */
    private void addMarkerByCarInfo(Boolean isToCenter) {
        if (mIntentExtra == null || mIntentExtra.lat == 0 || mIntentExtra.lng == 0) {
            //为传递过了车辆位置信息
            return;
        }
        LatLng point = mIntentExtra.getLatLng();
        //构建Marker图标
        BitmapDescriptor bitmap = BitmapDescriptorFactory.fromResource(R.drawable.ic_map_view_car_position);
        //构建MarkerOption，用于在地图上添加Marker
        OverlayOptions option = new MarkerOptions()
                .position(point)
                .visible(true)
                .icon(bitmap);
        mcarPoint = point;
        //在地图上添加Marker，并显示
        addMarker(MARK_LOCATION_CAR, point, option, mIntentExtra.address);
        //移动至屏幕中心
        if (isToCenter) {
            mBaiduMap.animateMapStatus(MapStatusUpdateFactory.newMapStatus(new MapStatus.Builder().target(point).zoom(18f).build()));
        }
    }

    /**
     * 在地图上添加人的标记
     *
     * @param isToCenter 是否移动至屏幕中心
     */
    private void addMarkerByPeopleInfo(Boolean isToCenter) {
        if (mBDLocation == null || mBDLocation.getLongitude() == 0 || mBDLocation.getLatitude() == 0) {
            Log.e(TAG, "在地图上添加人的标记,addMarkerByPeopleInfo,未获取到当前位置信息");
            //未获取到当前位置信息
            return;
        }
        //定义Maker坐标点
        LatLng point = new LatLng(mBDLocation.getLatitude(), mBDLocation.getLongitude());
        Log.e(TAG, "在地图上添加人的标记,addMarkerByPeopleInfo,人的经纬度=" + mBDLocation.getLongitude() + "," + mBDLocation.getLatitude());
        //构建Marker图标
        BitmapDescriptor bitmap = BitmapDescriptorFactory
                .fromResource(mark == MARK_LOCATION_PEOPLE ? R.drawable.ic_map_location : R.drawable.ic_map_location_people);
        //构建MarkerOption，用于在地图上添加Marker
        OverlayOptions option = new MarkerOptions()
                .position(point)
                .visible(true)
                .icon(bitmap);
        //在地图上添加Marker，并显示
        String address = mBDLocation.getAddrStr();
        Log.e(TAG, "addMarkerByPeopleInfo,address=" + address);
        addMarker(MARK_LOCATION_PEOPLE, point, option, address);
        //移动至屏幕中心
        if (isToCenter) {
            mBaiduMap.animateMapStatus(MapStatusUpdateFactory.newMapStatus(new MapStatus.Builder().target(point).zoom(18f).build()));
        }
    }

    /**
     * 展示车位置信息
     *
     * @param point   车辆所在经纬度
     * @param address 车辆地址
     */
    private void showCarInfo(final LatLng point, final String address) {
        mTvCarCurrentPosition.setText(address);
        mTvCarName.setText(mIntentExtra.carTypeName);
        if (mBDLocation != null && mBDLocation.getLongitude() != 0 && mBDLocation.getLatitude() != 0) {
            mTvCarDistance.setVisibility(View.VISIBLE);
            LatLng peoplePoint = new LatLng(mBDLocation.getLatitude(), mBDLocation.getLongitude());
            Log.e(TAG, "showCarInfo,人的经纬度=" + mBDLocation.getLongitude() + "," + mBDLocation.getLatitude());
            mDistance = DistanceUtil.getDistance(peoplePoint, mcarPoint);
            mTvCarDistance.setText(getString(R.string.txt_distance_car, getDistanceStr(mDistance)));
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        mMapView.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
        mMapView.onPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.e(TAG, "onDestroy");
        if(mLocClient != null){
            mLocClient.stop();
            if (mLocationListener != null) {
                mLocClient.unRegisterLocationListener(mLocationListener);
            }
            mLocClient = null;
        }
        mMapView.onDestroy();
    }

    @Override
    protected void onPermissionResult(boolean isGranted, int requestCode) {
        super.onPermissionResult(isGranted, requestCode);
        Log.e(TAG, "onPermissionResult, isGranted = " + isGranted + ", requestCode = " +requestCode);
        permissionResult(isGranted, requestCode);
    }

    /**
     * 清除地图上所有标记
     */
    public void clear() {
        mBaiduMap.clear();
    }

    /**
     * GPS提示对话框
     */
    private void showGpsDialog() {
        if (mGpsDialog == null) {
            mGpsDialog = new CommonAlertDialog(this, R.string.txt_tip_start_gps, R.string.permission_btn_setting, R.string.permission_btn_cancel, new DialogInterface.OnClickListener() {

                @Override
                public void onClick(DialogInterface dialog, int which) {
                    if (FastClickUtil.isFastDoubleClick("mGpsDialog",1000)) {
                        Log.e("btn_supplement", "触发了防双击");
                        return;
                    }

                    Intent intent = new Intent(Settings.ACTION_SETTINGS);
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    if (intent.resolveActivity(getPackageManager()) != null) {
                        startActivity(intent);
                    }
                    dialog.dismiss();
                }
            }, null);
        }
        if (mGpsDialog.isShowing()) {
            mGpsDialog.dismiss();
        }
        mGpsDialog.show();
    }

    /**
     * 向百度地图添加Marker，并添加InfoWindow
     *
     * @param latLng  经纬度
     * @param option  添加点
     * @param address 地址信息
     */
    private void addMarker(int type, LatLng latLng, OverlayOptions option, String address) {
//        mcarPoint = latLng;
        //在地图上添加Marker，并显示
        Marker marker = (Marker) mBaiduMap.addOverlay(option);
        if (TextUtils.isEmpty(address)) {
            address = getString(R.string.txt_location_failure);
        }
        if (type == MARK_LOCATION_CAR) {
            if (mIntentExtra.lat == 0 || mIntentExtra.lng == 0) {
                mcarPoint = null;
            }
            showCarInfo(latLng, address);
        }

    }

    @Override
    public void onRationaleAccepted(int requestCode) {

    }

    @Override
    public void onRationaleDenied(int requestCode) {
        Log.e(TAG, "onRationaleDenied, requestCode = " +requestCode);
        permissionResult(false, requestCode);
    }

    public static class IntentExtra implements Serializable {
        private static final long serialVersionUID = 8062148452410079000L;
        /**
         * 经度
         */
        private double lng;
        /**
         * 纬度
         */
        private double lat;
        /**
         * 最新获取时间
         */
        private long lastTime;
        /**
         * 地址信息
         */
        private String address;

        /**
         * 车型名称
         */
        private String carTypeName;
        /**
         * 采集时间
         */
        private String collectTime;

        private String vin;

        /**
         * 坐标类型使用GCJ02
         *
         * @param lng      国标经度 GCJ02
         * @param lat      国标经度 GCJ02
         * @param lastTime 时间戳
         */
        public IntentExtra(double lng, double lat, long lastTime, String carTypeName, String collectTime, String vin) {
            this.lat = lat;
            this.lng = lng;
            this.lastTime = lastTime;
            this.carTypeName = carTypeName;
            this.collectTime = collectTime;
            this.vin = vin;

        }

        /**
         * 坐标类型使用GCJ02
         *
         * @param lng 国标经度 GCJ02
         * @param lat 国标经度 GCJ02
         */
        public IntentExtra(double lng, double lat, String carTypeName) {
            this.lat = lat;
            this.lng = lng;
            this.carTypeName = carTypeName;
        }

        LatLng getLatLng() {
            return new LatLng(this.lat, this.lng);
        }
    }

    /**
     * 导航
     */
    @OnClick(R.id.iv_navigation)
    void onclik() {
        Log.e(TAG, "mcarPoint="+mcarPoint);
        if (FastClickUtil.isFastDoubleClick("iv_navigation",1000)) {
            Log.e("btn_supplement", "触发了防双击");
            return;
        }

        if (mcarPoint != null) {
            SensorsUtils.sensorsClickBtn("点击" + mIntentExtra.carTypeName + "地理位置中寻车导航按钮", "出行地理位置详情页", "出行保客-地理位置详情页-导航按钮");
            NavigateUtil.navigate(BaiduMapViewActivity.this, String.valueOf(mcarPoint.latitude), String.valueOf(mcarPoint.longitude), mIntentExtra.address);
        }
    }

    /**
     * 重新载入车的位置
     */
    @OnClick(R.id.iv_car_position)
    void onclikCarPosition() {
        if (FastClickUtil.isFastDoubleClick("iv_car_position",1000)) {
            Log.e("btn_supplement", "触发了防双击");
            return;
        }

        if (mIntentExtra.lat != 0 && mIntentExtra.lng != 0 ) {
            clear();
            mark = MARK_LOCATION_CAR;
            addMarkerByPeopleInfo(false);
            addMarkerByCarInfo(true);
            //检查地址信息是否获取到，否则再次查询地址信息
            if (TextUtils.isEmpty(mIntentExtra.address) || getString(R.string.txt_location_failure).equals(mIntentExtra.address)) {
                getCarLocation();
            }
        }

        SensorsUtils.sensorsClickBtn("点击" + mIntentExtra.carTypeName + "理位置中车的位置按钮", "出行地理位置详情页", "出行保客-地理位置详情页-位置按钮");
    }

    @OnClick(R.id.iv_back)
    void clickBack() {
        onBackPressed();
    }

    /**
     * 切换到人的位置
     */
    @OnClick(R.id.iv_people_location)
    public void switchToPeople(View view) {
        if (FastClickUtil.isFastDoubleClick("iv_people_location",1000)) {
            Log.e("btn_supplement", "触发了防双击");
            return;
        }

        getMyLocation(true);
    }

    /**
     * 重新定位
     */
    @OnClick(R.id.tv_car_distance)
    public void reStartLocation(View view) {
        if (FastClickUtil.isFastDoubleClick("tv_car_distance",1000)) {
            Log.e("btn_supplement", "触发了防双击");
            return;
        }

        if (mTvCarDistance.getText() != null && mTvCarDistance.getText().toString().equals(getString(R.string.txt_distance_no))) {
            mTvCarDistance.setText(getString(R.string.txt_distance_getting));
            getMyLocation(false);
        }
    }

}