package com.cloudy.linglingbang.activity.club;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ChooseImageController;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.UploadImageController;
import com.cloudy.linglingbang.model.club.EditClubChannel;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.umeng.analytics.MobclickAgent;

import java.io.File;

import androidx.appcompat.widget.Toolbar;
import butterknife.BindView;
import butterknife.OnClick;

/**
 * 车友会资料编辑
 *
 * <AUTHOR>
 * @date 2017/11/20
 */
public class ClubInfoEditActivity extends BaseActivity {

    private String picturePath;
    /**
     * 车友会头像
     */
    @BindView(R.id.iv_header)
    ImageView mIvHeader;

    /**
     * 车友会名称
     */
    @BindView(R.id.et_channel_name)
    EditText mEtChannelName;

    /**
     * 旧的社区名称
     */
    private String mChannelNameOld;
    private String mHeaderUrl;
    private Long mChannelId;

    public final static String EXTRA_CHANNEL_NAME = "channelName";
    public final static String EXTRA_CHANNEL_HEADER = "channelHeader";
    public final static String EXTRA_CHANNEL_ID = "channelId";
    private ChooseImageController mChooseImageController;
    private UploadImageController mUploadImageController;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_club_info_edit);
    }

    @Override
    protected void initialize() {
        setMiddleTitle(getString(R.string.club_info_edit));
        mHeaderUrl = getIntent().getStringExtra(EXTRA_CHANNEL_HEADER);
        mChannelNameOld = getIntent().getStringExtra(EXTRA_CHANNEL_NAME);
        mChannelId = getIntent().getLongExtra(EXTRA_CHANNEL_ID, 0);
        String url = AppUtil.getImageUrlBySize(mHeaderUrl, AppUtil._200X200);
        new ImageLoad(mIvHeader, url)
                .setUserMemoryCache(false)
                .setPlaceholderAndError(R.drawable.ic_common_place_holder)
                .load();
//        ImageLoader.getInstance().displayImage(url, mIvHeader, ImageLoaderUtils.getOptionByCommunity());
        if (mChannelNameOld != null) {
            mEtChannelName.setText(mChannelNameOld);
        }
    }

    /**
     * 点击头像
     */
    @OnClick(R.id.iv_header)
    protected void headerOnclick(View view) {
        if (mChooseImageController == null) {
            mChooseImageController = new ChooseImageController(this, new ChooseImageController.OnChooseImageListener() {
                @Override
                public void onAddImage(String path) {
                    picturePath = path;
                    mIvHeader.setImageURI(null);
                    mIvHeader.setImageURI(Uri.fromFile(new File(path)));
                }
            }).setCropWidthAndHeight(150);
        }
        mChooseImageController.showChooseTypeDialog();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (mChooseImageController != null) {
            mChooseImageController.onActivityResult(requestCode, resultCode, data);
        }

    }

    @Override
    public boolean onPrepareOptionsMenu(Menu menu) {
        menu.clear();
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(R.menu.menu_done, menu);
        mToolbar.setOnMenuItemClickListener(new Toolbar.OnMenuItemClickListener() {
            @Override
            public boolean onMenuItemClick(MenuItem item) {
                CheckCommit();
                return true;
            }
        });
        return true;
    }

    /**
     * 校验信息
     */
    private void CheckCommit() {
        //如果没有填完，则告诉用户没有做任何更改
        if (mEtChannelName.getText() == null) {
            return;
        }
        if (TextUtils.isEmpty(mEtChannelName.getText().toString().trim())) {
            ToastUtil.showMessage(this, getString(R.string.club_input_channel_name));
        } else if (picturePath == null && mEtChannelName.getText().toString().trim().equals(mChannelNameOld)) {
            ToastUtil.showMessage(this, getString(R.string.club_no_change));
        } else {
            if (picturePath != null) {
                UpLoadToService();
            } else {
                commitToServer(null);
            }
        }

    }

    /**
     * 上传照片到服务器
     */
    private void UpLoadToService() {
        if (mUploadImageController == null) {
            mUploadImageController = new UploadImageController(this)
                    .setNeedCompress(true)
                    .setOnUploadSuccessListener(new UploadImageController.OnUploadImageSuccessListener() {
                        @Override
                        public void onUploadSuccess(String result) {
                            picturePath = null;
                            commitToServer(result);
                        }
                    });
        }
        mUploadImageController.upload(picturePath);
    }

    /**
     * 提交到服务器
     */
    private void commitToServer(String url) {
        //添加友盟统计
        MobclickAgent.onEvent(this, "316");
        EditClubChannel editClubChannel = new EditClubChannel();
        if (url != null) {
            editClubChannel.setChannelFavicon(url);
        }
        if (!mEtChannelName.getText().toString().trim().equals(mChannelNameOld)) {
            editClubChannel.setChannelName(mEtChannelName.getText().toString().trim());
        }
        editClubChannel.setChannelId(mChannelId);
        L00bangRequestManager2.getServiceInstance()
                .modifyCarClub(editClubChannel)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<Object>(ClubInfoEditActivity.this) {
                    @Override
                    public void onSuccess(Object o) {
                        super.onSuccess(o);
                        ToastUtil.showMessage(ClubInfoEditActivity.this, getString(R.string.club_commit_to_check));
                        Intent intent = new Intent(ClubInfoEditActivity.this, CarClubDetailActivity.class);
                        intent.putExtra("channelId", mChannelId);
                        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                        startActivity(intent);
                    }
                });
    }

    public static void startActivity(Activity context, String channelHeader, String channelName, Long channelId) {
        Intent intent = new Intent(context, ClubInfoEditActivity.class);
        intent.putExtra(EXTRA_CHANNEL_NAME, channelName);
        intent.putExtra(EXTRA_CHANNEL_HEADER, channelHeader);
        intent.putExtra(EXTRA_CHANNEL_ID, channelId);
        context.startActivity(intent);
    }

    @Override
    protected void onPermissionResult(boolean isGranted, int requestCode) {
        super.onPermissionResult(isGranted, requestCode);
        if (mChooseImageController != null) {
            mChooseImageController.onPermissionResult(isGranted, requestCode);
        }
    }
}
