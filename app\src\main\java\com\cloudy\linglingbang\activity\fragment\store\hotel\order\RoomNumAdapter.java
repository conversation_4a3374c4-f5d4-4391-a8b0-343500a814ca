package com.cloudy.linglingbang.activity.fragment.store.hotel.order;

import android.content.Context;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.app.widget.textview.PressEffectiveCompoundButton;
import com.cloudy.linglingbang.model.hotel.RoomNumInfo;

import java.util.List;

/**
 * 选择房间数量的适配器
 *
 * <AUTHOR>
 * @date 2022/3/26
 */
public class RoomNumAdapter extends BaseRecyclerViewAdapter<RoomNumInfo> {

    public RoomNumAdapter(Context context, List<RoomNumInfo> data) {
        super(context, data);
    }

    @Override
    protected BaseRecyclerViewHolder<RoomNumInfo> createViewHolder(View itemView) {
        return new ViewHolder(itemView);
    }

    @Override
    protected int getItemLayoutRes(int viewType) {
        return R.layout.item_hotel_room_num;
    }

    class ViewHolder extends BaseRecyclerViewHolder<RoomNumInfo> {
        PressEffectiveCompoundButton tv;

        public ViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            tv = itemView.findViewById(R.id.tv_num);
        }

        @Override
        public void bindTo(RoomNumInfo roomNumInfo, int position) {
            super.bindTo(roomNumInfo, position);
            tv.setText(String.valueOf(roomNumInfo.getRoomNum()));
            if (roomNumInfo.getCheckStatus() == 1) {
                tv.setChecked(roomNumInfo.getCheckStatus() == 1);
                tv.setEnabled(false);
            } else {
                tv.setEnabled(true);
                tv.setChecked(roomNumInfo.getCheckStatus() == 1);
            }

            tv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    if (mChooseRoomNumListener != null) {
                        mChooseRoomNumListener.chooseNum(position);
                    }
                }
            });
        }
    }

    private ChooseRoomNumListener mChooseRoomNumListener;

    public void setChooseRoomNumListener(ChooseRoomNumListener chooseRoomNumListener) {
        mChooseRoomNumListener = chooseRoomNumListener;
    }

    public interface ChooseRoomNumListener {
        void chooseNum(int position);
    }
}
