package com.cloudy.linglingbang.activity.store;

import com.cloudy.linglingbang.BaseInstrumentedTest;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.fragment.store.home.StoreHomeFragment;

/**
 * 商城首页测试
 *
 * <AUTHOR>
 * @date 2018/10/17
 */
public class StoreHomeActivityTest extends BaseInstrumentedTest {
    private int times;

    @Override
    public void test() {
        super.test();
        testJump();
    }

    public void testJump() {
        int type = 0;
        switch (times++ % 3) {
            case 1:
                type = StoreHomeFragment.TYPE_COMMODITY;
                break;
            case 0:
            default:
                type = StoreHomeFragment.TYPE_CAR;
                break;
        }
        IntentUtils.startActivity(getActivity(), StoreHomeActivity.class, type);
    }
}