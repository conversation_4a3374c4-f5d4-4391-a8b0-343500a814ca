package com.cloudy.linglingbang.activity.fragment.store.youpin;

import android.content.Context;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.IRefreshContext;
import com.cloudy.linglingbang.activity.fragment.store.home.StoreHomeRefreshController;
import com.cloudy.linglingbang.activity.fragment.store.home.model.CommonTitleWithMore;
import com.cloudy.linglingbang.activity.store.commodity.CommodityListPageCode;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BackgroundSubscriber;
import com.cloudy.linglingbang.model.server.Ad.Ad2;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.recyclerview.widget.RecyclerView;

import static com.cloudy.linglingbang.model.server.Ad.Ad2.LINK_TYPE.STORE_COMMODITY_DETAIL;

/**
 * 优品首页数据加载控制器
 *
 * <AUTHOR>
 * @date 2020/5/11
 */
public class SuperiorProductRefreshController extends StoreHomeRefreshController {

    public SuperiorProductRefreshController(IRefreshContext<Object> refreshContext, String pageCode) {
        super(refreshContext, pageCode);
    }

    @Override
    protected boolean loadDataAfterInitViews() {
        //不主动调用，由 initHelper 处理
        return false;
    }

    @Override
    protected void onLoadSuccess(int loadPage, List<Object> list, int loadType) {
        super.onLoadSuccess(loadPage, list, loadType);
        if (loadPage == 1) {
            getMoreGoods(1);
        }
    }

    @Override
    protected RecyclerView.ItemDecoration createItemDecoration(final Context context) {
        return new StoreHomeElementItemDecoration(context);
    }

    /**
     * 更多好物
     */
    private void getMoreGoods(final int page) {
        if (!isNeedRequestMoreGoods()) {
            return;
        }
        Map<String, Object> map = new HashMap<>(3);
        map.put("pageNo", String.valueOf(page));
        map.put("pageSize", String.valueOf(getPageSize()));
        map.put("pageCode", CommodityListPageCode.PAGE_CODE_MORE_GOODS);
        L00bangRequestManager2.getServiceInstance()
                .findEcCommodityAppList(map)
                .map(ElementUtils.getStoreElementToObjectFun(Ad2.LINK_TYPE.TYPE_NEW_COMMODITY_DETAIL, getMoreLayoutId()))
                .compose(L00bangRequestManager2.<List<Object>>setSchedulers())
                .subscribe(new BackgroundSubscriber<List<Object>>(getContext()) {
                    @Override
                    public void onSuccess(List<Object> objects) {
                        super.onSuccess(objects);
                        if (objects == null || objects.isEmpty()) {
                            swipeToLoadLayout.setLoadingMore(false, true);
                            setLoadMoreEnable(false);
                            return;
                        }
                        int size = objects.size();
                        if (page == 1) {
                            int start = mData.size();
                            //添加更多好物标题
                            CommonTitleWithMore more = new CommonTitleWithMore(getContext().getString(R.string.txt_store_home_more_goods), getMoreLayoutId());
                            mData.add(more);
                            mData.addAll(objects);
                            adapter.notifyItemRangeInserted(start, size + 1);
                            setLoadMoreEnable(size >= getPageSize());
                        } else {
                            onLoadSuccess(page, objects, 0);
                        }
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                        setLoadMoreEnable(false);
                    }
                });
    }

    @Override
    public void getListData(int page) {
        if (page > 1) {
            getMoreGoods(page);
        } else {
            super.getListData(page);
        }
    }

    @Override
    protected boolean isLoadMoreEnable() {
        return isNeedRequestMoreGoods();
    }

    @Override
    protected boolean hasMoreGoods() {
        return true;
    }

    @Override
    protected boolean hasNavTab() {
        return true;
    }
}
