package com.cloudy.linglingbang.activity.store.commodity

import android.content.Context
import android.graphics.Color
import android.graphics.SurfaceTexture
import android.graphics.drawable.Drawable
import android.media.MediaPlayer
import android.media.MediaSync
import android.os.Build
import android.os.Handler
import android.os.Message
import android.text.TextUtils
import android.util.AttributeSet
import android.util.Log
import android.view.*
import android.view.TextureView.SurfaceTextureListener
import android.widget.*
import androidx.annotation.RequiresApi
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import com.aliyun.player.AliListPlayer
import com.aliyun.player.AliPlayerFactory
import com.aliyun.player.IPlayer
import com.aliyun.player.bean.ErrorInfo
import com.aliyun.player.bean.InfoBean
import com.aliyun.player.bean.InfoCode
import com.aliyun.player.nativeclass.CacheConfig
import com.aliyun.player.source.UrlSource
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.community.post.shortVideo2.VideoQuality
import com.cloudy.linglingbang.app.util.AppUtil
import com.cloudy.linglingbang.app.util.ImageLoad
import com.cloudy.linglingbang.model.server.Ad.Ad2
import com.cloudy.linglingbang.model.store.commodity.CenterCommodity
import java.lang.Exception
import java.util.*

/**
 * <AUTHOR>
 * @date 2022/9/23
 */
class CommodityVideoView : FrameLayout, LifecycleEventObserver {
    private var mVideoTime: TextView? = null
    private var mVideoDuring: TextView? = null
    private var mProgressBar: ProgressBar? = null
    private var mLonging: ProgressBar? = null
    private var mProgressLayout: View? = null
    private var mCoverImg: ImageView? = null
    private var mPlayIcon: ImageView? = null
    private var mVideoLayout: FrameLayout? = null
    private var mVideoView: VideoView? = null
    private var payUrl: String? = null
    private var mp: MediaPlayer? = null
    private var mHander: FiexHandler = FiexHandler()

    @JvmOverloads
    constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        initView()
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    constructor(
        context: Context,
        attrs: AttributeSet?,
        defStyleAttr: Int,
        defStyleRes: Int
    ) : super(context, attrs, defStyleAttr, defStyleRes) {
        initView()
    }


    private fun initView() {
        val activity = AppUtil.getActivity(context)
        if (activity is LifecycleOwner) {
            (activity as LifecycleOwner).lifecycle.addObserver(this)
        }
        inflate(context, R.layout.item_commodity_detail_banner_video, this)
        mProgressLayout = findViewById(R.id.ll_progress)
        mCoverImg = findViewById(R.id.ad_image_view)
        mVideoLayout = findViewById(R.id.flow_layout)
        mVideoTime = findViewById(R.id.tv_time)
        mVideoDuring = findViewById(R.id.tv_duration)
        mProgressBar = findViewById(R.id.progress_bar)
        mLonging = findViewById(R.id.pb_refresh)
        mPlayIcon = findViewById(R.id.iv_play_icon)
        mPlayIcon?.setOnClickListener {
            toStartPlay()
        }

    }

    private fun toStartPlay() {
        if (mVideoView == null) {
            mVideoView = VideoView(context)
            initListener()
            this.mp = null
            mVideoLayout?.removeAllViews()
            mVideoLayout?.addView(mVideoView)
            mVideoView?.setVideoPath(payUrl)
        }
        if (this.mp != null) {
            try {
                this.mp?.start()
                toPlayView()
            } catch (e: Exception) {
                this.mp = null
                mVideoView?.stopPlayback()
                mVideoView = null
                toStartPlay()
            }
            return
        }
        mVideoView?.start()
        mLonging?.visibility = VISIBLE
    }

    fun setData(data: Any?) {
        var imag = ""
        if (data is CenterCommodity.MainImg) {
            imag = data.mainImg
            payUrl = data.videoUrl
        }
        mCoverImg?.apply {
            if (!TextUtils.isEmpty(imag)) {
                ImageLoad(this, imag)
                    .setDoNotAnimate()
                    .setDoNotLoadWebp()
                    .setPlaceholderAndError(R.drawable.ic_common_place_holder)
                    .load()
            }
        }

    }

    private fun initListener() {
        mVideoView?.setOnPreparedListener { mp ->
            this.mp = mp
            mp.setVolume(0f, 0f)
            setVideoTime()
            toPlayView()
            mp.setOnVideoSizeChangedListener { _, width, height ->
                val p: ViewGroup.LayoutParams? = mVideoView?.layoutParams
                p?.width = getWidth()
                p?.height = getHeight()
                if (width >= height) {
                    p?.height = height * getWidth() / width
                } else if (width < height) {
                    p?.width = width * getHeight() / width
                }
                if (p is LayoutParams) {
                    p.gravity = Gravity.CENTER
                }
                mVideoView?.layoutParams = p
            }
        }
        mVideoView?.setOnCompletionListener {
            resetView()
        }
        mVideoView?.setOnErrorListener { _, _, _ ->
            resetView()
            false
        }
        mVideoView?.setOnInfoListener { _, _, _ -> false }


    }

    private fun setVideoTime() {
        mProgressBar?.max = mVideoView?.duration ?: 0
        mProgressBar?.progress = mVideoView?.currentPosition ?: 0
        mVideoDuring?.text = getTimeStr(mProgressBar?.max?.toLong() ?: 0L)
        mVideoTime?.text = getTimeStr(mVideoView?.currentPosition?.toLong() ?: 0L)
        mHander.removeMessages(1)
        mHander.mVideoView = this
        mHander.sendEmptyMessageDelayed(1, 100)
    }

    private fun getTimeStr(time: Long): String {
        val seconds = time / 1000
        return String.format(Locale.getDefault(), "%02d:%02d", seconds / 60, seconds % 60)
    }


    fun pause() {
        mVideoView?.pause()
        resetView()
        if (this.mp == null) {
            mVideoView?.stopPlayback()
            mVideoView = null
        }
    }

    private fun toPlayView() {
        mProgressLayout?.visibility = VISIBLE
        mVideoView?.visibility = VISIBLE
        mLonging?.visibility = GONE
        mPlayIcon?.visibility = GONE
        mCoverImg?.visibility = GONE
        setBackgroundColor(Color.BLACK)
    }

    private fun resetView() {
        mPlayIcon?.visibility = VISIBLE
        mCoverImg?.visibility = VISIBLE
        mHander.removeMessages(1)
        setBackgroundColor(Color.TRANSPARENT)
        mVideoView?.visibility = INVISIBLE
        mProgressLayout?.visibility = INVISIBLE
    }

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        when {
            Lifecycle.Event.ON_STOP == event -> {
                pause()
                mVideoView?.stopPlayback()
                mVideoLayout?.removeAllViews()
                mVideoView = null
                this.mp = null
            }
            Lifecycle.Event.ON_START == event -> {
                resetView()
            }
            Lifecycle.Event.ON_DESTROY == event -> {
                pause()
                mVideoView?.stopPlayback()
                mVideoLayout?.removeAllViews()
                mVideoView = null
                this.mp = null
            }
        }
    }

    private class FiexHandler : Handler() {
        var mVideoView: CommodityVideoView? = null
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            mVideoView?.setVideoTime()
        }
    }
}