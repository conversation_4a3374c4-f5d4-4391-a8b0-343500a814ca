package com.cloudy.linglingbang.activity.basic;

import android.content.Intent;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.task.util.TaskNavigateUtils;
import com.cloudy.linglingbang.app.widget.PagerSlidingTabStrip;

import java.util.ArrayList;
import java.util.List;

import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

/**
 * 标签可滑动的viewPagerFragment
 * Created by hanfei on 2016/12/8.
 */
public abstract class BaseScrollTabViewPagerFragment<T> extends BaseFragment implements TaskNavigateUtils.TaskNavigable {

    protected ViewPager mViewPager;
    protected PagerSlidingTabStrip tabs;

    /**
     * 数据，可以是fragment,也可以是view
     */
    protected List<T> mData;
    /**
     * adapter
     */
    protected PagerAdapter mAdapter;

    protected String[] titles;

    protected List<String> mTitleList = new ArrayList<>();

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_base_scroll_tab_view_pager;
    }

    @Override
    protected void initViews() {
        super.initViews();
        mViewPager = mRootView.findViewById(R.id.view_pager);
        tabs = mRootView.findViewById(R.id.tabs);
        mData = createAdapterData();
        titles = getTitles();
        mTitleList = getTitleList();
        if (titles != null) {
            mAdapter = createViewPagerAdapter(mData, titles);
        } else if (mTitleList != null) {
            mAdapter = createViewPagerAdapter(mData, mTitleList);
        }
        mViewPager.setAdapter(mAdapter);
        tabs.setViewPager(mViewPager);
        // 设置Tab是自动填充满屏幕的
        tabs.setShouldExpand(false);
        tabs.setOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                //可以在滑动时就加载实际布局，或者是提供占位图，等展示出来再加载实际布局
                if (positionOffset > 0) {
                    if (position + 1 < mData.size()) {
                        T t = mData.get(position + 1);
                        if (t instanceof LazyFragment) {
                            ((LazyFragment) t).loadRealView();
                        }
                    }
                }
            }

            @Override
            public void onPageSelected(int position) {
                onItemSelected(position);
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
    }

    protected void setUnreadBadgeItem(int position) {
        tabs.setUnreadBadgeItem(position);
    }

    protected void setTabBadgeWithImage(int position, int drawableRes) {
        tabs.showBadgeImage(position, drawableRes);
    }

    protected void onItemSelected(int position) {}

    /**
     * 创建viewPager的adapter
     */
    protected abstract PagerAdapter createViewPagerAdapter(List<T> data, String[] titles);

    /**
     * 创建viewPager的adapter
     */
    protected PagerAdapter createViewPagerAdapter(List<T> data, List<String> titles) {
        return null;
    }

    /**
     * 创建adapter的数据
     */
    protected abstract List<T> createAdapterData();

    /**
     * 创建每个fragment的标题
     */
    protected abstract String[] getTitles();

    /**
     * 创建每个fragment的标题（list）
     */
    protected List<String> getTitleList() {
        return null;
    }

    /**
     * 获取当前展示的 item ，如当前 Fragment
     */
    public T getCurrentItem() {
        if (mViewPager != null && mData != null) {
            int currentItem = mViewPager.getCurrentItem();
            if (currentItem >= 0 && currentItem < mData.size()) {
                return mData.get(currentItem);
            }
        }
        return null;
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        dispatchVisibilityChange(!hidden);
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        dispatchVisibilityChange(isVisibleToUser);
    }

    @Override
    public void onParentVisibilityChanged(boolean visible) {
        super.onParentVisibilityChanged(visible);
        //再次分发
        dispatchVisibilityChange(visible);
    }

    /**
     * 分发给 child
     */
    private void dispatchVisibilityChange(boolean visible) {
        T currentItem = getCurrentItem();
        if (currentItem instanceof BaseFragment) {
            ((BaseFragment) currentItem).onParentVisibilityChanged(visible);
        }
    }

    @Override
    public void checkTaskNavigate(Intent intent) {
        if (mData != null) {
            TaskNavigateUtils.checkFragmentTaskSecondLevelNavigate(this, intent, mViewPager, mData);
        }
    }
}
