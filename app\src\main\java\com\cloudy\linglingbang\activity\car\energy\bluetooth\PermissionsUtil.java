package com.cloudy.linglingbang.activity.car.energy.bluetooth;

import android.Manifest;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.location.LocationManager;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;
import android.widget.Toast;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.car.energy.ConnectHelper;
import com.cloudy.linglingbang.activity.travel.TravelFragment;
import com.cloudy.linglingbang.app.util.AppUtils;
import com.cloudy.linglingbang.app.util.PermissionUtils;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by LiYeWen on 2025/04/29
 * 蓝牙权限检测工具类
 */
public class PermissionsUtil {

    private static PermissionsUtil instance;
//    private static final int REQUEST_CODE_PERMISSION_LOCATION = 2;
    /**
     * 吐司提示的时间（避免短时间内连续执行相同吐司）
     */
    private long mTimeToast;

    private PermissionsUtil(){}

    /**
     * 获取单例对象
     * @return
     */
    public static PermissionsUtil getInstance() {
        if (instance == null) {
            synchronized (PermissionsUtil.class) {
                instance = new PermissionsUtil();
            }
        }
        return instance;
    }

    /**
     * 蓝牙权限检测
     * @param activity          当前的Activity
     * @param isHandleConnect   是否是手动连接，true表示是
     * @return                  返回true表示已拥有蓝牙所有权限
     */
    public boolean checkPermissions(Activity activity, boolean isHandleConnect) {
        Log.e(ConnectHelper.TAG, "蓝牙权限阶段 checkPermissions() " + ConnectHelper.getInstance().getConnectLog());

        if (ConnectUtil.getInstance().getBluetoothAdapter() == null) {
            Log.e(ConnectHelper.TAG, "蓝牙权限阶段 checkPermissions() bluetoothAdapter == null" + ConnectHelper.getInstance().getConnectLog() + ConnectHelper.INTERRUPT);
            return false;
        }

        if (!ConnectUtil.getInstance().getBluetoothAdapter().isEnabled()) {
            Log.e(ConnectHelper.TAG, "蓝牙权限阶段 checkPermissions() !bluetoothAdapter.isEnabled() 蓝牙开关未打开 " + ConnectHelper.getInstance().getConnectLog() + ConnectHelper.INTERRUPT);
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                     if (isHandleConnect) {
                         if(System.currentTimeMillis() - mTimeToast >= 3500){
                             mTimeToast = System.currentTimeMillis();
                             ToastUtil.showMessage(activity, "请先打开蓝牙", Toast.LENGTH_LONG);
                         }else {
                             Log.e(ConnectHelper.TAG, "正在吐司中，不再重复吐司 请先打开蓝牙");
                         }
                    }
                }
            });
            return false;
        }

        //安卓12以上的版本，需要申请蓝牙附近设备和连接的权限
        if (Build.VERSION.SDK_INT >= 31) {
            //检测附近设备权限
            if (!AppUtils.havePermissionAndroid12BleBluetoothScan(activity)) {
                Log.e(ConnectHelper.TAG, "蓝牙权限阶段 checkPermissions() 无附近设备权限(No bluetooth_scan)  " + ConnectHelper.getInstance().getConnectLog() + ConnectHelper.INTERRUPT);
                ActivityCompat.requestPermissions(activity, AppUtils.permissions12_bluetooth_scan, PermissionUtils.ACTIVITY_PERMISSION_BLUETOOTH_ANDROID12);
                return false;
            }

            //检测蓝牙连接权限
            if (!AppUtils.havePermissionAndroid12BleBluetoothConnect(activity)) {
                Log.e(ConnectHelper.TAG, "蓝牙权限阶段 checkPermissions() 无蓝牙连接权限(No bluetooth_connect) " + ConnectHelper.getInstance().getConnectLog() + ConnectHelper.INTERRUPT);
                ActivityCompat.requestPermissions(activity, AppUtils.permissions12_bluetooth_connect, PermissionUtils.ACTIVITY_PERMISSION_BLUETOOTH_ANDROID12);
                return false;
            }
        }

        //检测定位权限
        String[] permissions = {Manifest.permission.ACCESS_FINE_LOCATION};
        List<String> permissionDeniedList = new ArrayList<>();
        for (String permission : permissions) {
            int permissionCheck = ContextCompat.checkSelfPermission(activity, permission);
            if (permissionCheck == PackageManager.PERMISSION_GRANTED) {
                Log.e(ConnectHelper.TAG, "蓝牙权限阶段 checkPermissions() 有定位权限(have access_fine_location) " + ConnectHelper.getInstance().getConnectLog());
                //蓝牙权限最终判断
                return onPermissionGranted(activity, isHandleConnect, permission);
            } else {
                permissionDeniedList.add(permission);
            }
        }
        if (!permissionDeniedList.isEmpty()) {
            Log.e(ConnectHelper.TAG, "蓝牙权限阶段 checkPermissions() 无定位权限(no access_fine_location) " + ConnectHelper.getInstance().getConnectLog() + ConnectHelper.INTERRUPT);
            showPermissionExplainDialog(activity, "五菱汽车即将调用定位权限，以便使用蓝牙近控功能，点击确认以继续!",permissionDeniedList);
        }
        return false;
    }

    /**
     * 蓝牙权限最终判断
     * @param activity          当前的Activity
     * @param isHandleConnect   是否是手动连接，true表示是
     * @param permission        需要申请的权限
     * @return                  返回true表示已拥有蓝牙所有权限
     */
    private boolean onPermissionGranted(Activity activity, boolean isHandleConnect, String permission) {
        if (permission.equals(Manifest.permission.ACCESS_FINE_LOCATION)) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !checkGPSIsOpen(activity)) {
                Log.e(ConnectHelper.TAG, "蓝牙权限阶段 onPermissionGranted() 无GPS权限(no GPS) " + ConnectHelper.getInstance().getConnectLog() + ConnectHelper.INTERRUPT);
                if (isHandleConnect) {
                    new AlertDialog.Builder(activity)
                            .setTitle(R.string.notifyTitle)
                            .setMessage(R.string.gpsNotifyMsg)
                            .setNegativeButton(R.string.cancel,
                                    new DialogInterface.OnClickListener() {
                                        @Override
                                        public void onClick(DialogInterface dialog, int which) {
                                            ToastUtil.showMessage(activity, R.string.gpsNotifyMsg);
                                        }
                                    })
                            .setPositiveButton(R.string.setting,
                                    new DialogInterface.OnClickListener() {
                                        @Override
                                        public void onClick(DialogInterface dialog, int which) {
                                            Intent intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                                            activity.startActivity(intent);
                                        }
                                    })
                            .setCancelable(false)
                            .show();
                }
            } else {
                Log.e(ConnectHelper.TAG, "蓝牙权限阶段 onPermissionGranted() 已拥有蓝牙所有权限(have all permissions) " + ConnectHelper.getInstance().getConnectLog());
                return true;
            }
        }else {
            Log.e(ConnectHelper.TAG, "蓝牙权限阶段 onPermissionGranted() 权限异常(abnormal) " + ConnectHelper.getInstance().getConnectLog() + " permission="+permission);
        }
        return false;
    }

    /**
     * 判断是否有GPS权限
     * @param activity
     * @return
     */
    private boolean checkGPSIsOpen(Activity activity) {
        LocationManager locationManager = (LocationManager) activity.getSystemService(Context.LOCATION_SERVICE);
        if (locationManager == null) {
            return false;
        }
        return locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
    }

    /**
     * 弹起权限使用目的说明弹窗
     * @param preTipStr 权限使用目的说明文本
     */
    private void showPermissionExplainDialog(Activity activity, final String preTipStr, List<String> permissionDeniedList) {
        CommonAlertDialog alertDialog = new CommonAlertDialog(activity, preTipStr,
                "确认", "取消",
                new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        Log.e(ConnectHelper.TAG, "蓝牙权限阶段 showPermissionExplainDialog() onClick 无定位权限(no access_fine_location)");
                        String[] deniedPermissions = permissionDeniedList.toArray(new String[permissionDeniedList.size()]);
                        ActivityCompat.requestPermissions(activity, deniedPermissions, PermissionUtils.ACTIVITY_PERMISSION_BLUETOOTH_ANDROID12);
                    }
                },
                new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                    }
                });
        alertDialog.setCanceledOnTouchOutside(false);
        alertDialog.show();
    }

    /**
     * 五菱汽车这边的处理
     * @param fragment
     * @param isGranted
     */
    public void onRequestAndroid12BlePermissionPassed(Fragment fragment, boolean isGranted) {
        if (!ConnectHelper.getInstance().isHandleConnect()) {
            Log.e(ConnectHelper.TAG, "蓝牙权限阶段 onRequestAndroid12BlePermissionPassed() !ConnectHelper.getInstance().isHandleConnect(), " + ConnectHelper.getInstance().getConnectLog() + ", isGranted = " + isGranted);
            return;
        }
        if (!isGranted) {
            //扫描和连接权限都没有，说明用户之前拒绝过附近的设备权限授权，此时无法弹授权窗口，那么停止连接流程
            Log.e(ConnectHelper.TAG, "蓝牙权限阶段 onRequestAndroid12BlePermissionPassed() !isGranted, " + ConnectHelper.getInstance().getConnectLog() + ", isGranted = " + isGranted);
            return;
        }

        if(fragment == null){
            Log.e(ConnectHelper.TAG, "蓝牙权限阶段 onRequestAndroid12BlePermissionPassed() fragment == null, " + ConnectHelper.getInstance().getConnectLog() + ", isGranted = " + isGranted);
            return;
        }

        if(fragment.getActivity() == null){
            Log.e(ConnectHelper.TAG, "蓝牙权限阶段 onRequestAndroid12BlePermissionPassed() fragment.getActivity() == null, " + ConnectHelper.getInstance().getConnectLog() + ", isGranted = " + isGranted);
            return;
        }

        if (AppUtils.havePermissionAndroid12BleBluetoothScan(fragment.getActivity()) && !AppUtils.havePermissionAndroid12BleBluetoothConnect(fragment.getActivity())) {
            ActivityCompat.requestPermissions(fragment.getActivity(), AppUtils.permissions12_bluetooth_connect, PermissionUtils.ACTIVITY_PERMISSION_BLUETOOTH_ANDROID12);
            return;
        }

        if (AppUtils.havePermissionAndroid12BleBluetoothConnect(fragment.getActivity()) && !AppUtils.havePermissionAndroid12BleBluetoothScan(fragment.getActivity())) {
            ActivityCompat.requestPermissions(fragment.getActivity(), AppUtils.permissions12_bluetooth_scan, PermissionUtils.ACTIVITY_PERMISSION_BLUETOOTH_ANDROID12);
            return;
        }

        ((TravelFragment) fragment).checkPermissions(TravelFragment.ACTIVITY_PERMISSION_LOCATION,
                fragment.getActivity().getString(R.string.permission_location_pre),
                fragment.getActivity().getString(R.string.permission_location_setting),
                Manifest.permission.ACCESS_FINE_LOCATION);
    }

}
