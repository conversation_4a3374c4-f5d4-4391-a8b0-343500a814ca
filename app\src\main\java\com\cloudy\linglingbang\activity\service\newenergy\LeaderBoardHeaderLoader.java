package com.cloudy.linglingbang.activity.service.newenergy;

import android.content.Context;
import android.view.View;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.span.SpanUtils;
import com.cloudy.linglingbang.app.widget.banner.AdRoundImageView;
import com.cloudy.linglingbang.model.server.YesterdayLeaderBoard;

/**
 * 排行榜头部加载
 *
 * <AUTHOR>
 * @date 2019-07-16
 */
public class LeaderBoardHeaderLoader {

    private final View mRootView;
    private final Context mContext;

    private AdRoundImageView mIvHeaderImg;

    private TextView mTvName;
    private TextView mTvCarName;
    private TextView mTvTop;
    private TextView mTvMileage;
    private TextView mTvTotalMileage;
    private TextView mTvTotalSaveMoney;

    private TextView mTvDistance1, mTvDistance2, mTvDistance3;
    private TextView mTvMileage1, mTvMileage2, mTvMileage3;

    private ProgressBar mPbMileage;

    private final String vin;
    private final int totalMileage;
    private final String carTypeName;

    public LeaderBoardHeaderLoader(View rootView, String vin, int totalMileage, String carTypeName) {
        mRootView = rootView;
        this.mContext = rootView.getContext();
        this.vin = vin;
        this.totalMileage = totalMileage;
        this.carTypeName = carTypeName;
        initView();

    }

    private void initView() {
        mIvHeaderImg = mRootView.findViewById(R.id.iv_header_img);
        mTvName = mRootView.findViewById(R.id.tv_name);
        mTvCarName = mRootView.findViewById(R.id.tv_car_name);
        mTvTop = mRootView.findViewById(R.id.tv_top);
        mTvMileage = mRootView.findViewById(R.id.tv_mileage);
        mTvTotalMileage = mRootView.findViewById(R.id.tv_total_mileage);
        mTvTotalSaveMoney = mRootView.findViewById(R.id.tv_total_save_money);
        mPbMileage = mRootView.findViewById(R.id.pb_mileage);

        mTvDistance1 = mRootView.findViewById(R.id.tv_distance_1);
        mTvDistance2 = mRootView.findViewById(R.id.tv_distance_2);
        mTvDistance3 = mRootView.findViewById(R.id.tv_distance_3);

        mTvMileage1 = mRootView.findViewById(R.id.tv_mileage_1);
        mTvMileage2 = mRootView.findViewById(R.id.tv_mileage_2);
        mTvMileage3 = mRootView.findViewById(R.id.tv_mileage_3);
    }

    public void updateView(YesterdayLeaderBoard yesterdayLeaderBoard) {
        //昨日里程
        String yesterdayMileage = mContext.getResources().getString(R.string.txt_remain_mileage, yesterdayLeaderBoard.getMileageOfLastDay());
        SpanUtils.setPartSpanText(mTvMileage, yesterdayMileage, mContext.getResources().getColor(R.color.text_light_color_929292),
                mContext.getResources().getDimension(R.dimen.activity_set_text_24), yesterdayMileage.indexOf("k"), yesterdayMileage.length());

        //总里程
        String totalMileageStr = mContext.getResources().getString(R.string.txt_remain_mileage, totalMileage);
        SpanUtils.setPartSpanText(mTvTotalMileage, totalMileageStr, mContext.getResources().getColor(R.color.text_light_color_929292),
                mContext.getResources().getDimension(R.dimen.activity_set_text_20), totalMileageStr.indexOf("k"), totalMileageStr.length());

        //节省总金额
        mTvTotalSaveMoney.setText(mContext.getString(R.string.invoice_yuan, yesterdayLeaderBoard.getSaveCount()));

        float progress = (float) totalMileage / 15000 * 100;
        int mileageProgress = 0;
        if (progress > 0 && progress < 99) {
            mileageProgress = (int) Math.ceil(progress);
        } else if (progress >= 99 && progress < 100) {
            mileageProgress = (int) Math.floor(progress);
        } else {
            mileageProgress = (int) progress;
        }
        mPbMileage.setProgress(mileageProgress);

        if (totalMileage >= 0 && totalMileage < 5000) {
            mTvMileage1.setEnabled(true);
            mTvDistance1.setEnabled(true);
        } else if (totalMileage >= 5000 && totalMileage < 15000) {
            mTvMileage1.setEnabled(true);
            mTvDistance1.setEnabled(true);
            mTvMileage2.setEnabled(true);
            mTvDistance2.setEnabled(true);
        } else if (totalMileage >= 15000) {
            mTvMileage1.setEnabled(true);
            mTvDistance1.setEnabled(true);
            mTvMileage2.setEnabled(true);
            mTvDistance2.setEnabled(true);
            mTvMileage3.setEnabled(true);
            mTvDistance3.setEnabled(true);
        }

        //昨日排名
        if (yesterdayLeaderBoard.getRank() > 0) {
            String top = mContext.getResources().getString(R.string.txt_yesterday_top, yesterdayLeaderBoard.getRank());
            SpanUtils.setPartSpanText(mTvTop, top, mContext.getResources().getColor(R.color.text_light_color_929292),
                    mContext.getResources().getDimension(R.dimen.activity_set_text_24), top.length() - 1, top.length());
        } else {
            mTvTop.setText("暂无排名");
        }

        String vinStr;
        if (vin.length() > 4) {
            vinStr = vin.substring(vin.length() - 4);
        } else {
            vinStr = vin;
        }
        String catNameStr = mContext.getString(R.string.txt_car_name, carTypeName, vinStr);
        SpanUtils.setPartSpanText(mTvCarName, catNameStr, mContext.getResources().getColor(R.color.text_light_color_929292),
                mContext.getResources().getDimension(R.dimen.activity_set_text_24), catNameStr.indexOf("-"), catNameStr.length());
        mTvName.setText(yesterdayLeaderBoard.getNickname() + "");
        new ImageLoad(mContext, mIvHeaderImg, yesterdayLeaderBoard.getPhoto(), ImageLoad.LoadMode.URL)
                .setPlaceholder(R.drawable.user_head_default_120x120)
                .setErrorImageId(R.drawable.user_head_default_120x120)
                .setCircle(true)
                .load();

    }

}
