package com.cloudy.linglingbang.activity.fragment.store.home.model;

import com.cloudy.linglingbang.model.store.home.StoreElementWaterfallVo;
import com.cloudy.linglingbang.model.wrapper.WrapperModelWithType;

import java.util.List;

/**
 * 为您推荐
 *
 * <AUTHOR>
 * @date 12/18/22
 */
public class ElementStaggerdWrapper extends WrapperModelWithType<List<Object>> {

    public ElementStaggerdWrapper(List<Object> original) {
        super(original, StoreHomeElementEnum.ELEMENT_STAGGERED_LIST.getType());
    }

    private StoreElementWaterfallVo mWaterfallVo;

    public void setWaterfallVo(StoreElementWaterfallVo waterfallVo) {
        mWaterfallVo = waterfallVo;
    }

    public StoreElementWaterfallVo getWaterfallVo() {
        return mWaterfallVo;
    }
}
