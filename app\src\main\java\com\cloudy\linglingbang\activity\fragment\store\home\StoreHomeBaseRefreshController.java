package com.cloudy.linglingbang.activity.fragment.store.home;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Rect;
import android.text.TextUtils;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.IRefreshContext;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.fragment.store.home.model.CommonTitleWithBottomMore;
import com.cloudy.linglingbang.activity.fragment.store.home.model.CommonTitleWithMore;
import com.cloudy.linglingbang.activity.fragment.store.home.model.ElementLifeRecommendWrapper;
import com.cloudy.linglingbang.activity.fragment.store.home.model.ElementStaggerdWrapper;
import com.cloudy.linglingbang.activity.fragment.store.home.model.StoreHomeElementEnum;
import com.cloudy.linglingbang.activity.fragment.store.home.model.StoreHomeElementWrapper;
import com.cloudy.linglingbang.activity.fragment.store.home.model.StoreHomeNavElementWrapper;
import com.cloudy.linglingbang.activity.fragment.store.home.model.StoreHomeSlideElementWrapper;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.store.ecology.EcologyCommodity;
import com.cloudy.linglingbang.model.store.home.StoreLayoutComponent;
import com.cloudy.linglingbang.model.store.home.StoreLayoutElement;
import com.cloudy.linglingbang.model.welfare.Commodity;
import com.cloudy.linglingbang.model.wrapper.CommonModel;

import java.util.ArrayList;
import java.util.List;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 商城首页的加载
 * 注意该类 isLoadMoreEnable 为 false 如果继承该类要改为 true
 *
 * <pre>
 * 加载
 * 加载完成后，重新组装数据
 * 不同数据对应不同的类型，对应不同的 ViewHolder
 * 不同的组件，也会在 Adapter 中处理为不同的类型，
 * 加载不同的布局、占有不同的 span（在 {@link StoreHomeElementEnum} 中定义布局各 span）
 *
 * 分隔符使用 {@link StoreHomeElementItemDecoration} 实现
 * 点击在 {@link com.cloudy.linglingbang.activity.fragment.store.home.adapter.StoreHomeAdapter} 中实现
 *
 * </pre>
 *
 * <AUTHOR>
 * @date 2018/10/10
 */
public class StoreHomeBaseRefreshController extends RefreshController<Object> {
    private final String mPageCode;

    /**
     * 应使用最小公倍数
     */
    protected final static int SPAN_COUNT = 6;
    protected GridLayoutManager.SpanSizeLookup mSpanSizeLookup;

    public StoreHomeBaseRefreshController(IRefreshContext<Object> refreshContext, String pageCode) {
        super(refreshContext);
        mPageCode = pageCode;
    }

    @Override
    public void initViews(View rootView) {
        super.initViews(rootView);
        swipeToLoadLayout.setBackgroundColor(Color.WHITE);
        recyclerView.setBackgroundColor(Color.TRANSPARENT);
        mLlEmpty.setBackgroundColor(Color.TRANSPARENT);
    }

    @Override
    public int getErrorImageResId() {
        return R.drawable.ic_store_empty_list;
    }

    @Override
    public int getEmptyImageResId() {
        return R.drawable.ic_store_empty_list;
    }

    @Override
    protected Observable<BaseResponse<List<Object>>> getListDataFromNet(L00bangService2 service2, final int pageNo, int pageSize) {
        return service2.getLayoutComponentList(mPageCode)
                .map(listBaseResponse -> listBaseResponse.cloneWithData(parseElement(listBaseResponse.getData())));
    }

    /**
     * 重新组装元素
     */
    private List<Object> parseElement(List<StoreLayoutComponent> data) {
        List<Object> result = new ArrayList<>();
        if (data == null) {
            return result;
        }
        for (int i = 0; i < data.size(); i++) {
            //每一个组件
            StoreLayoutComponent storeLayoutComponent = data.get(i);
            if (storeLayoutComponent == null) {
                continue;
            }
            int style = storeLayoutComponent.getStyle();

            if (hasNavTab() && StoreHomeElementEnum.ELEMENT_NAV.getType() == style) {
                if (hasMoreGoods() && isNeedRequestMoreGoods()) {
                    StoreLayoutComponent m = new StoreLayoutComponent();
                    m.setTitle(getContext().getResources().getString(R.string.txt_store_home_more_goods));
                    m.setLayoutComponentId(getMoreLayoutId());
                    storeLayoutComponent.getNavigationComponents().add(m);
                }
                result.add(new CommonModel.Divider(getContext().getResources().getDimensionPixelOffset(R.dimen.normal_30), 1));
                result.add(new StoreHomeNavElementWrapper(storeLayoutComponent.getNavigationComponents(), StoreHomeElementEnum.ELEMENT_NAV.getType()));
                continue;
            } else if (StoreHomeElementEnum.ELEMENT_SLIDE.getType() == style) {
                //添加标题
                StoreHomeSlideElementWrapper slideElementWrapper = new StoreHomeSlideElementWrapper(storeLayoutComponent.getElementVoList(), StoreHomeElementEnum.ELEMENT_SLIDE.getType());
                addTopTitle(result, storeLayoutComponent, slideElementWrapper);
                result.add(slideElementWrapper);
                //底部查看更多商品
                addBottomMore(result, storeLayoutComponent, slideElementWrapper);
                continue;
            } else if (StoreHomeElementEnum.ELEMENT_BANNER_SMALL.getType() == style
                    || StoreHomeElementEnum.ELEMENT_BANNER_BIG.getType() == style
                    || StoreHomeElementEnum.ELEMENT_BANNER_BIG_MATCH_PARENT.getType() == style
            ) {
                //添加标题
                StoreHomeSlideElementWrapper slideElementWrapper = new StoreHomeSlideElementWrapper(storeLayoutComponent.getElementVoList(), storeLayoutComponent.getStyle());
                result.add(slideElementWrapper);
                continue;
            } else if (StoreHomeElementEnum.ELEMENT_LIFE_RECOMMEND.getType() == style) {
                //为您推荐
                ElementLifeRecommendWrapper lifeRecommendWrapper = new ElementLifeRecommendWrapper(storeLayoutComponent.getElementVoList(), storeLayoutComponent.getStyle());
                addTopTitle(result, storeLayoutComponent, lifeRecommendWrapper);
                result.add(lifeRecommendWrapper);
                //底部查看更多商品
                addBottomMore(result, storeLayoutComponent, lifeRecommendWrapper);
                /*lifeRecommendWrapper.setTitle(storeLayoutComponent.getTitle());
                result.add(lifeRecommendWrapper);*/
                result.add(new CommonModel.Divider20());
                continue;
            } else if (StoreHomeElementEnum.MIX_PAY_COMMODITY_HORIZONTAL.getType() == style) {
                //横向滑动 混合支付商品
                //添加标题
                List<StoreLayoutElement> storeLayoutElementList = storeLayoutComponent.getElementVoList();
                for (StoreLayoutElement storeLayoutElement : storeLayoutElementList) {
                    storeLayoutElement.setHorizontalPageStyle(storeLayoutComponent.getHorizontalPageStyleOrZero());
                }
                StoreHomeSlideElementWrapper slideElementWrapper = new StoreHomeSlideElementWrapper(storeLayoutElementList, style);
                addTopTitle(result, storeLayoutComponent, slideElementWrapper);
                result.add(slideElementWrapper);
                //底部查看更多商品
                addBottomMore(result, storeLayoutComponent, slideElementWrapper);
                continue;
            } else if (style == StoreHomeElementEnum.ELEMENT_STAGGERED_LIST.getType()) {
                List<StoreLayoutElement> storeLayoutElementList = storeLayoutComponent.getElementVoList();
                List<Object> list = new ArrayList<>();
                ElementStaggerdWrapper wrapper = new ElementStaggerdWrapper(list);
                for (StoreLayoutElement storeLayoutElement : storeLayoutElementList) {
                    if (storeLayoutElement.getBannerVo() != null) {
                        list.add(storeLayoutElement.getBannerVo());
                    } else if (storeLayoutElement.getWindowVo() != null) {
                        list.add(storeLayoutElement.getWindowVo());
                    } else if (storeLayoutElement.getRecommendVo() != null) {
                        list.add(storeLayoutElement.getRecommendVo());
                    } else if (storeLayoutElement.getProductVo() != null) {
                        list.add(storeLayoutElement);
                    } else if (storeLayoutElement.getWaterfallVo() != null) {
                        wrapper.setWaterfallVo(storeLayoutElement.getWaterfallVo());
                    }
                }
                wrapper.setOriginal(list);

                addTopTitle(result, storeLayoutComponent, null);
                result.add(wrapper);
                continue;
            }
            //元素列表
            List<StoreLayoutElement> elementVoList = storeLayoutComponent.getElementVoList();
            int size;
            if (elementVoList == null || (size = elementVoList.size()) <= 0) {
                continue;
            }
            //添加标题
            addTopTitle(result, storeLayoutComponent, null);
            List<StoreLayoutElement> list14 = null;
            for (int j = 0; j < size; j++) {
                StoreLayoutElement storeLayoutElement = elementVoList.get(j);
                int elementShowModule = storeLayoutElement.getShowModule();
                //1/4元素 n*m icon
                if (StoreHomeElementEnum.ELEMENT_1_4.getType() == elementShowModule
                        || StoreHomeElementEnum.ELEMENT_ICON.getType() == style) {
                    if (list14 == null) {
                        list14 = new ArrayList<>(size);
                    }
                    list14.add(storeLayoutElement);
                    if (j + 1 == size && StoreHomeElementEnum.ELEMENT_ICON.getType() == style) {
                        result.add(new CommonModel.Divider(getContext().getResources().getDimensionPixelOffset(R.dimen.normal_30), 1));
                        result.add(new StoreHomeSlideElementWrapper(new ArrayList<>(list14), style, storeLayoutComponent.getColumnNum()));
                        result.add(new CommonModel.Divider(getContext().getResources().getDimensionPixelOffset(R.dimen.normal_30), 1));
                        list14.clear();
                        list14 = null;
                    }
                    continue;
                }

                if (j == 0) {
                    //当添加第一个元素的时候，进行判断，该判断也可以通过组件类型判断
                    //为了摆脱组件类型的约束，这里直接用元素类型判断

                    //添加分隔符，在中间 banner 的前后不需要添加

                    //如果要添加的元素是广告，不添加分隔符
                    if (elementShowModule != StoreHomeElementEnum.MID_BANNER.getType()) {
                        //要添加的元素不是广告，看前一个是不是广告
                        if (i > 0) {
                            boolean preIsMidBanner = false;
                            if (result.size() > 0) {
                                Object preElement = result.get(result.size() - 1);
                                if (preElement instanceof StoreHomeElementWrapper) {
                                    if (((StoreHomeElementWrapper) preElement).getType() == StoreHomeElementEnum.MID_BANNER.getType()) {
                                        preIsMidBanner = true;
                                    }
                                }
                            }
                            if (!preIsMidBanner) {
                                //前一个不是广告，那么每组元素前，添加一个分隔符
                                result.add(new CommonModel.Divider20());
                            } else {
                                //如果前一个是广告，如果当前是优品，则添加一个占位
                                if (elementShowModule == StoreHomeElementEnum.COMMODITY_1_2.getType()) {
                                    result.add(new CommonModel.Transparent());
                                }
                            }
                        }
                    }
                }
                if (elementShowModule == 7) {//1+2n 中的1
                    storeLayoutElement.setShowModule(StoreHomeElementEnum.COMMODITY_LIST_1_2N.getType());
                } else if (elementShowModule == 6 && style == StoreHomeElementEnum.ELEMENT_WELFARE_RECOMMEND.getType()) {
                    storeLayoutElement.setShowModule(StoreHomeElementEnum.WELFARE_COMMODITY_1_2.getType());
                } else if (elementShowModule == 6 && style == StoreHomeElementEnum.WELFARE_COMMODITY_1_2_V2.getType()) {
                    storeLayoutElement.setShowModule(StoreHomeElementEnum.WELFARE_COMMODITY_1_2_V2.getType());
                }
                StoreHomeElementWrapper storeHomeElementWrapper = new StoreHomeElementWrapper(storeLayoutElement, j);
                storeHomeElementWrapper.setLayoutComponentId(storeLayoutComponent.getLayoutComponentId());
                //添加每一个元素
                result.add(storeHomeElementWrapper);
            }
            if (list14 != null && !list14.isEmpty()) {
                result.add(new CommonModel.Divider(getContext().getResources().getDimensionPixelOffset(R.dimen.normal_26), 1));
                result.add(new StoreHomeSlideElementWrapper(list14, StoreHomeElementEnum.ELEMENT_1_4.getType()));
            }
            //底部查看更多商品
            addBottomMore(result, storeLayoutComponent, null);
        }
        //底部添加留白
        if (!result.isEmpty()) {
            result.add(new CommonModel.Divider(getContext().getResources().getDimensionPixelOffset(R.dimen.normal_30), -1));
        }
        return result;
    }

    private void addTopTitle(List<Object> result, StoreLayoutComponent component, Object extra) {
        String title = component.getTitle();
        if (TextUtils.isEmpty(title)) {
            return;
        }
        if (!TextUtils.isEmpty(component.getTitleIcon())) {
            title = title + "," + component.getTitleIcon();
        }
        //标题不为空，添加标题
        CommonTitleWithMore commonTitleWithMore = new CommonTitleWithMore(title, component.getLayoutComponentId());
        if (component.getMore() == 1) {
            if (component.getMorePosition() == 0) {
                commonTitleWithMore.setMoreText(getContext().getString(R.string.common_element_top_more));
                if (component.getStyle() == StoreHomeElementEnum.ELEMENT_WELFARE_RECOMMEND.getType()) {
                    commonTitleWithMore.setProductType(1);
                } else {
                    commonTitleWithMore.setProductType(0);
                }
                commonTitleWithMore.setMorePackageId(component.getMorePackageId());
                commonTitleWithMore.setMoreLinkType(component.getMoreLinkType());
                commonTitleWithMore.setMoreLinkUrl(component.getMoreLinkUrl());
                commonTitleWithMore.setExtra(extra);
            }
        }
        result.add(commonTitleWithMore);
    }

    private void addBottomMore(List<Object> result, StoreLayoutComponent component, Object extra) {
        if (component.getMore() == 1 && component.getMorePosition() == 1) {
            CommonTitleWithBottomMore bottomMore = new CommonTitleWithBottomMore(component.getTitle(), component.getLayoutComponentId());
            bottomMore.setMoreText(getContext().getString(R.string.txt_store_see_more_goods));
            if (component.getStyle() == StoreHomeElementEnum.ELEMENT_WELFARE_RECOMMEND.getType()) {
                bottomMore.setProductType(1);
            } else {
                bottomMore.setProductType(0);
            }
            bottomMore.setMorePackageId(component.getMorePackageId());
            bottomMore.setExtra(extra);
            bottomMore.setMoreLinkType(component.getMoreLinkType());
            bottomMore.setMoreLinkUrl(component.getMoreLinkUrl());
            result.add(bottomMore);
        }
    }

    @Override
    protected RecyclerView.LayoutManager createLayoutManager(Context context) {
        GridLayoutManager gridLayoutManager = new GridLayoutManager(getContext(), SPAN_COUNT);
        if (mSpanSizeLookup == null) {
            mSpanSizeLookup = new GridLayoutManager.SpanSizeLookup() {
                @Override
                public int getSpanSize(int position) {
                    if (mData != null) {
                        if (position > -1 && position < mData.size()) {
                            Object o = mData.get(position);
                            if (o != null) {
                                if (o instanceof EcologyCommodity || o instanceof Commodity) {
                                    return SPAN_COUNT / 2;
                                } else if (o instanceof StoreHomeElementWrapper) {
                                    float spanRatio = ((StoreHomeElementWrapper) o).getSpanRatio();
                                    if (spanRatio > 0 && spanRatio < 1) {
                                        return (int) (SPAN_COUNT * spanRatio + 0.5);
                                    } else {
                                        return SPAN_COUNT;
                                    }
                                }
                            }
                        }
                    }
                    return SPAN_COUNT;
                }
            };
        }
        gridLayoutManager.setSpanSizeLookup(mSpanSizeLookup);
        return gridLayoutManager;
    }

    @Override
    protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
        return new StoreHomeElementItemDecoration(context);
    }

    @Override
    public String getEmptyString() {
        return getContext().getString(R.string.store_home_empty);
    }

    @Override
    protected boolean isLoadMoreEnable() {
        return false;
    }

    /**
     * 各 item 的间距
     * 因为 RecyclerView 有 match_parent 的分隔线，所以不能设置 RecyclerView 的 padding
     * 因此设置到每个 item
     * 每个 item，可能区分类型，加载不同的布局，也可以同一个布局，代码控制
     * 考虑到主要耗时是 inflate ，以及维护两个布局不方便修改，因此选择代码控制
     * 使用 ItemDecoration 时，会占用 item 的宽高，因此要保证相同 item 的 decoration 所占宽高一致，否则就无法保证 item 大小相同
     */
    public class StoreHomeElementItemDecoration extends RecyclerView.ItemDecoration {
        protected int px10;
        protected int px20;

        private int getModule(int i) {
            if (i >= 0 && mData.size() > i) {
                Object o = mData.get(i);
                if (o instanceof StoreHomeElementWrapper) {
                    StoreHomeElementWrapper elementWrapper = (StoreHomeElementWrapper) o;
                    StoreLayoutElement layoutElement = elementWrapper.getOriginal();
                    if (layoutElement != null) {
                        return layoutElement.getShowModule();
                    }
                }
            }
            return -1;
        }

        public StoreHomeElementItemDecoration(Context context) {
            px10 = context.getResources().getDimensionPixelOffset(R.dimen.normal_10);
            px20 = context.getResources().getDimensionPixelOffset(R.dimen.normal_20);
        }

        @Override
        public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
            super.getItemOffsets(outRect, view, parent, state);
            List<Object> data = getData();
            if (data == null) {
                return;
            }
            int position = parent.getChildAdapterPosition(view);
            if (position < 0 || position >= data.size()) {
                return;
            }
            Object o = data.get(position);
            if (o instanceof StoreHomeElementWrapper) {
                StoreHomeElementWrapper elementWrapper = (StoreHomeElementWrapper) o;
                StoreLayoutElement layoutElement = elementWrapper.getOriginal();
                if (layoutElement != null) {
                    int showModule = elementWrapper.getType();
                    /*
                    判断位置使用 spanIndex，其范围为 [0,spanCount)
                    判断的时候 spanIndex 要再除以相应的比例
                     */
                    int spanIndex = mSpanSizeLookup.getSpanIndex(position, SPAN_COUNT);
                    if (showModule == StoreHomeElementEnum.ELEMENT_1_2.getType()) {
                        if (spanIndex == 0) {
                            //第一个
                            outRect.left = px20 + px20;
                            outRect.right = px10;
                        } else {
                            //第二个
                            outRect.left = px10;
                            outRect.right = px20 + px20;
                        }
                    } else if (showModule == StoreHomeElementEnum.ELEMENT_1_3.getType()) {
                        int pModule = getModule(position - 1);
                        int nModule = getModule(position + 1);
                        int spanSize = SPAN_COUNT / 3;
                        /*
                        1/3 的，需要保证每个 item 的偏移和相等，这相才能保证每个 item 的实际宽度相等
                        因此使用 20,0-10,10-,0,20的偏移
                         */
                        if (spanIndex < spanSize) {       //第一个
                            if (nModule == StoreHomeElementEnum.ELEMENT_2_3.getType()) {
                                outRect.left = px20 + px20;
                                outRect.right = 0;
                            } else {
                                outRect.left = px20 + px20;
                            }
                        } else if (spanIndex < spanSize * 2) {
                            //第二个
                            outRect.left = px20;
                            outRect.right = px20;
                        } else {      //第三个
                            if (pModule == StoreHomeElementEnum.ELEMENT_2_3.getType()) {
                                outRect.right = px20 + px20;
                                outRect.left = 0;
                            } else {
                                outRect.right = px20 + px20;
                            }
                        }
                        outRect.bottom = px20;
                    } else if (showModule == StoreHomeElementEnum.ELEMENT_2_3.getType()) {
                        /*
                        2/3 的，由于两边的 1/3 只负责一边的 padding，因此需要 20,10 或 10,20
                        但是要注意，2/3 的图片高度需要手动计算，与 1/3 保持一致
                         */
                        if (spanIndex == 0) {
                            //第一个
                            outRect.left = px20 + px20;
                            outRect.right = px20;
                        } else if (spanIndex == SPAN_COUNT - SPAN_COUNT / 3f * 2) {
                            //第二个
                            outRect.left = px20;
                            outRect.right = px20 * 2;
                        }
                        outRect.bottom = px20;
                    } else if (showModule == StoreHomeElementEnum.COMMODITY_1_2.getType()) {
                        if (spanIndex < SPAN_COUNT / 2f) {
                            //第一个
                            outRect.left = px20 + px20;
                            outRect.right = px10;
                        } else {
                            //第二个
                            outRect.left = px10;
                            outRect.right = px20 + px20;
                        }
                        outRect.bottom = px20;
                    } else if (showModule == StoreHomeElementEnum.MID_BANNER.getType()) {
                        outRect.left = 0;
                        outRect.right = 0;
                    } else if (showModule == StoreHomeElementEnum.WELFARE_COMMODITY_1_2.getType()
                            || showModule == StoreHomeElementEnum.WELFARE_COMMODITY_1_2_V2.getType()) {
                        if (spanIndex < SPAN_COUNT / 2f) {
                            //第一个
                            outRect.left = px20 + px20;
                            outRect.right = px10 * 8 / 5;
                        } else {
                            //第二个
                            outRect.left = px10 * 8 / 5;
                            outRect.right = px20 + px20;
                        }
                        outRect.bottom = px20;
                    }
                }
            } else if (o instanceof CommonTitleWithMore) {
                outRect.left = px20;
                outRect.right = px20;
            } else if (o instanceof StoreHomeSlideElementWrapper) {
                int type = ((StoreHomeSlideElementWrapper) o).getType();
                if (StoreHomeElementEnum.ELEMENT_SLIDE.getType() == type || type == StoreHomeElementEnum.ELEMENT_1_4.getType()) {
                    outRect.bottom = px20;
                }
            }
        }
    }

    /**
     * 更多好物布局id,虚拟的
     */
    protected final long getMoreLayoutId() {
        return -0xa9999L;
    }

    /**
     * 是否有更多好物
     */
    protected boolean hasMoreGoods() {
        return false;
    }

    /**
     * 是否有导航栏组件
     */
    protected boolean hasNavTab() {
        return false;
    }

    /**
     * 是否需要加载更多好货接口
     */
    protected boolean isNeedRequestMoreGoods() {
        return false;
    }
}
