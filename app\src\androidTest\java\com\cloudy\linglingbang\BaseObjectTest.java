package com.cloudy.linglingbang;

import org.junit.Test;

/**
 * 测试某个对象
 * 在 {@link #getTestObject()} 中对其进行初始化
 * <p>
 * 子类如果直接在 {@link #test()} 中测试，可以直接调用 {@link #mTestObject} 的方法
 * <p>
 * 子类如果使用其他标注为 @{@link Test} 的方法，则应该放在 runOnUiThread 中，否则会未初始化
 *
 * <AUTHOR>
 * @date 2018/7/16
 */
public abstract class BaseObjectTest<T> extends BaseInstrumentedTest {
    public T mTestObject;

    @Override
    public void before() {
        super.before();
        //在主线程初始化
        mActivity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mTestObject = getTestObject();
            }
        });
    }

    /**
     * 初始化，应在主线程执行
     * 子类可直接使用 {@link #mTestObject}
     */
    protected abstract T getTestObject();

}
