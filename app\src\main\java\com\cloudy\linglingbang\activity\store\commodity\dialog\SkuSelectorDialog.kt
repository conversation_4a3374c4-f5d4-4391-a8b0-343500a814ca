package com.cloudy.linglingbang.activity.store.commodity.dialog

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Paint
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import android.widget.RadioGroup
import android.widget.TextView
import androidx.core.view.get
import androidx.recyclerview.widget.RecyclerView
import butterknife.BindView
import butterknife.ButterKnife
import butterknife.OnClick
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.ScanImageActivity
import com.cloudy.linglingbang.activity.fragment.mynew.util.SelfUserInfoLoader
import com.cloudy.linglingbang.activity.fragment.store.ChooseServiceDealerDialog
import com.cloudy.linglingbang.activity.store.commodity.CommodityOpenDialog
import com.cloudy.linglingbang.activity.store.commodity.ShoppingCartActivity
import com.cloudy.linglingbang.activity.store.commodity.adapter.CommodityAttributeAdapter
import com.cloudy.linglingbang.activity.store.commodity.adapter.CommodityAttributeAdapter.CommodityAttributeListener
import com.cloudy.linglingbang.app.log.LogUtils
import com.cloudy.linglingbang.app.receiver.UserInfoChangedHelper
import com.cloudy.linglingbang.app.util.*
import com.cloudy.linglingbang.app.util.LocationHelper.LocCallBack
import com.cloudy.linglingbang.app.util.LocationHelper.LocationEntity
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils
import com.cloudy.linglingbang.app.util.span.TagTextView
import com.cloudy.linglingbang.app.util.timer.CountDownManager
import com.cloudy.linglingbang.app.util.timer.TimeDelta
import com.cloudy.linglingbang.app.widget.banner.AdImageView
import com.cloudy.linglingbang.app.widget.dialog.alert.BaseAlertDialog
import com.cloudy.linglingbang.app.widget.ecologyStore.CountControlerView
import com.cloudy.linglingbang.app.widget.recycler.header.HeaderAndFooterWrapperAdapter
import com.cloudy.linglingbang.app.widget.textview.PressEffectiveCompoundButton
import com.cloudy.linglingbang.constants.AppConstants
import com.cloudy.linglingbang.constants.WebUrlConfigConstant
import com.cloudy.linglingbang.model.SourceModel
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BackgroundSubscriber
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber
import com.cloudy.linglingbang.model.store.Shop4S
import com.cloudy.linglingbang.model.store.Shop4SFullInfo
import com.cloudy.linglingbang.model.store.StockVo
import com.cloudy.linglingbang.model.store.commodity.CartCommodity
import com.cloudy.linglingbang.model.store.commodity.CommoditySku
import com.cloudy.linglingbang.model.store.ecology.CartInfo
import com.google.gson.Gson
import kotlin.math.pow


/**
 * <AUTHOR>
 * @date 2022/9/27
 */
@SuppressLint("NonConstantResourceId")
open class SkuSelectorDialog @JvmOverloads constructor(
    context: Context?,
    theme: Int = R.style.Dialog
) :
    BaseAlertDialog(context, theme), CommodityAttributeListener, CountDownManager.OnTickListener {

    @JvmField
    @BindView(R.id.iv_sku_pic)
    var ivSkuPic: AdImageView? = null

    var mSourceModel: SourceModel? = null


    @JvmField
    @BindView(R.id.tv_sku_name)
    var tvSkuName: TextView? = null

    var openType = CommodityOpenDialog.SELECT
    var referrer: String? = null
    var mCartCommodity: CartCommodity? = null
        set(value) {
            field = value
            commodityId = value?.commodityId
        }

    /**
     * 服务门店
     */
    private var mSelectShop4S: Shop4S? = null
    private var mShopStock = 0

    /**
     * 选择门店弹窗
     */
    var serviceDealerDialog: ChooseServiceDealerDialog? = null


    /**
     * 选择收获方式
     * 0、送货到家
     * 1、选择门店
     * 2、虚拟收货
     */
    private var mReceivingType: Int = CommodityOpenDialog.WayType.TYPE_HOME

    /**
     * 库存
     */
    @JvmField
    @BindView(R.id.tv_stock_count)
    var mTvStockCount: TextView? = null

    @JvmField
    @BindView(R.id.tv_sku_price)
    var tvSkuPrice: TextView? = null

    @JvmField
    @BindView(R.id.tv_sku_original_price)
    var mTvSkuOriginalPrice: TextView? = null

    @JvmField
    @BindView(R.id.tv_deposit_car_price)
    var mTvDepositCarPrice: TextView? = null

    /**
     * 活动
     */
    @JvmField
    @BindView(R.id.ll_activity)
    var llActivity: View? = null

    /**
     * 活动名称
     */
    @JvmField
    @BindView(R.id.tv_activity_name)
    var tvActivityName: TextView? = null

    /**
     * 活动时间
     */
    @JvmField
    @BindView(R.id.tv_activity_time)
    var tvActivityTime: TextView? = null

    @JvmField
    @BindView(R.id.recyclerView)
    var recyclerView: RecyclerView? = null

    /* @JvmField
     @BindView(R.id.btn_ok)
     var btn_ok: Button? = null*/

    @JvmField
    @BindView(R.id.tv_stock_desc)
    var tvStockDesc: TextView? = null

    var commodityId: Long? = null
    var shopId: Long? = null
    var isBenefitcommodity: Int? = null
    var attributeAdapter: CommodityAttributeAdapter? = null
    var adapter: HeaderAndFooterWrapperAdapter<*>? = null


    protected var mSkuCollection: Map<String, CommoditySku.SkuValue>? = null

    private var mSelectSKU: CommoditySku.SkuValue? = null

    /**
     * 收获方式以及门店选择
     */
    protected var mLlWayLayout: View? = null

    /**
     * 安装服务已经保险服务
     */
    private var mLlLayout: LinearLayout? = null
    protected var mFootView: View? = null
    private var mTvSkuStockTip: TextView? = null

    @JvmField
    @BindView(R.id.tv_sku_attribute)
    var mTvSkuAttribute: TextView? = null
    private var mTvSubTitle: TextView? = null
    protected var mTvShopStockTip: TextView? = null
    protected var mCountControlerView: CountControlerView? = null

    var mCommoditySku: CommoditySku? = null

    /**
     * 选择的按钮
     */
    private var mSelectAttributeValues = arrayListOf<CommoditySku.AttributeValue>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        ButterKnife.bind(this)
    }

    private fun querySkuData() {
        val map = HashMap<String, String>()
        map["commodityId"] = if (commodityId == null) "0" else commodityId.toString()
        map["channelCode"] = AppConstants.CHANNEL_SOURCE_ID.toString()
        L00bangRequestManager2.getServiceInstance()
            .getCommoditySkuListApp(map)
            .compose(L00bangRequestManager2.setSchedulers())
            .subscribe(object : ProgressSubscriber<CommoditySku>(context) {
                override fun onSuccess(t: CommoditySku?) {
                    super.onSuccess(t)
                    mCommoditySku = t
                    mSkuCollection = SkuUtil.skuCollection(mCommoditySku)
                    setData()
                    initAdapter()
                    doReceivingWayType()
                    mSelectDefaultSKU()
                    queryShopInfo()
                }
            })
    }

    private fun mSelectDefaultSKU() {
        if (openType != CommodityOpenDialog.NOW_UPDATE_CART) {
            return
        }
        if (mCommoditySku == null || mCartCommodity == null) {
            return
        }
        if (mSelectAttributeValues.isNotEmpty()) {
            return
        }
        mCartCommodity?.apply {
            mReceivingType = logisticsTakeMode
            mCountControlerView?.currentCount = quantity
            if (mReceivingType == CommodityOpenDialog.WayType.TYPE_SELECT_SHOP) {
                //选择门店
                mSelectShop4S = Shop4S()
                mSelectShop4S?.shopId = serviceDealerId
                mSelectShop4S?.shortName = serviceDealerName
                mSelectShop4S?.shopNum = serviceDealerCode
                mFootView?.findViewById<TextView>(R.id.tv_shop_name)?.text = serviceDealerName
            }
            //收货方式选择
            if (mReceivingType == CommodityOpenDialog.WayType.TYPE_SELECT_SHOP || mReceivingType == CommodityOpenDialog.WayType.TYPE_HOME) {
                val temp = mCommoditySku?.logisticsTakeMode?.split(",")
                if (temp?.contains("1") == true && temp.contains("0")) {
                    val rg: RadioGroup? = mFootView?.findViewById(R.id.radio_group)
                    if (mReceivingType == CommodityOpenDialog.WayType.TYPE_SELECT_SHOP) {
                        rg?.check(R.id.radio2)
                    } else {
                        rg?.check(R.id.radio1)
                    }
                }
            }
        }
        var tempSku: CommoditySku.SkuValue? = null
        mCommoditySku?.apply {
            for (skuValue in mCommoditySku?.skuList ?: emptyList()) {
                if (skuValue.commoditySkuId == mCartCommodity?.skuId) {
                    tempSku = skuValue
                    break
                }
            }
            if (tempSku == null) {
                return@apply
            }
            //选择sku属性
            val attrs = tempSku?.skuAttributeValueIds?.split(",")
            attrList?.forEachIndexed { p, attr ->
                attr.lmAttributeValueList?.forEachIndexed { index, attrValue ->
                    if (attrs?.contains(attrValue.attributeValueId.toString()) == true) {
                        onAttributeClickListener(null,p, index)
                    }
                }
            }

            //附加商品
            if (mCartCommodity?.additionList?.isNotEmpty() == true) {
                mSelectSKU?.apply {
                    lmCommoditySkuAdditionVoList?.forEach { additionVo ->
                        additionVo?.skuAdditionAttributes?.forEach { additionAttributeVos ->
                            additionAttributeVos.selectAttributeValues?.isChecked = 0
                            additionAttributeVos.selectAttributeValues = null
                            additionAttributeVos?.skuAdditionAttributeValues?.forEach { value ->
                                mCartCommodity?.additionList?.forEach {
                                    if (value.additionAttributeValueId == it.additionAttributeValueId
                                        && value.additionAttributeId == it.additionAttributeId
                                    ) {
                                        value.isChecked = 1
                                        additionAttributeVos.selectAttributeValues = value
                                    } else {
                                        additionAttributeVos.selectAttributeValues = null
                                        value.isChecked = 0
                                    }
                                }
                            }
                        }
                    }
                    updateFooterView()
                }
            }
        }
    }

    @OnClick(R.id.iv_close)
    fun close() {
        dismiss()
    }

    open fun doBtnOkSensors() {
        referrer?.apply {
            val eventName =
                "点击(" + commodityId + ")(" + (mCommoditySku?.commodityName ?: "") + ")确认"
            SensorsUtils.sensorsClickBtn(eventName, this, "选择SKU弹窗")
        }
    }

    @OnClick(R.id.btn_ok)
    fun viewOnClick() {
        doBtnOkSensors()
        if (!AppUtil.checkLogin(mContext)) {
            return
        }
        if (!checkSelectSku()) {
            return
        }
        when (openType) {
            CommodityOpenDialog.NOW_UPDATE_CART,
            CommodityOpenDialog.SHOPPING_CAR -> {
                commit(openType)
            }

            CommodityOpenDialog.NOW_BUY -> {
                toBuyCommodity()
            }

            else -> {
                dismiss()
            }
        }
    }

    /**
     * 去购买
     */
    private fun toBuyCommodity() {
        val map = HashMap<String, Any>()
        map["commoditySkuId"] = mSelectSKU?.commoditySkuId.toString()
        map["commodityId"] = mSelectSKU?.commodityId.toString()
        map["quantity"] = mCountControlerView?.currentCount ?: 0
        map["originalPrice"] = mSelectSKU?.originalPrice ?: "0"

        map["logisticsTakeMode"] = mReceivingType
        if (mReceivingType == CommodityOpenDialog.WayType.TYPE_SELECT_SHOP) {
            mSelectShop4S?.shopId?.let {
                map["logisticsServiceDealerId"] = it
            }
        }
        if (skuAdditionVoList.isNotEmpty()) {
            val temp = arrayListOf<HashMap<String, Any>>()
            for (values in skuAdditionVoList) {
                values.selectAttributeValues.lmSkuListAdditionAttributeValueVoList.forEach { attr ->
                    val p = HashMap<String, Any>()
                    //附加商品skuId
                    attr.commodityNameAttributeName?.let {
                        p["additionAttributeName"] = values.additionAttributeName
                    }
                    values.additionAttributeId?.let {
                        p["additionAttributeId"] = it
                    }
                    //属性值id
                    values.selectAttributeValues.additionAttributeValueId?.let {
                        p["additionAttributeValueId"] = it
                    }
                    //附加商品skuId
                    attr.limitBuyCountMin.let {
                        p["quantity"] = it
                    }
                    //附加商品skuId
                    attr.additionCommoditySkuId?.let {
                        p["commoditySkuId"] = it
                    }
                    temp.add(p)
                }
            }
            if (temp.isNotEmpty()) {
                map["commoditySkuAdditionList"] = temp
            }
        }
        var p = "?skuData=" + Gson().toJson(map)
        mSourceModel?.let {
            p += "&sensorsInfo=" + Gson().toJson(it, SourceModel::class.java)
        }
        JumpPageUtil.goCommonWeb(
            mContext,
            WebUrlConfigConstant.SHOPPING_CART_CONFIRM + p
        )
        dismiss()
    }

    /**
     * 添加购物车或者修改购物商品信息
     */
    private fun commit(openType: Int) {
        val map = HashMap<String, Any>()
        map["commoditySkuId"] = mSelectSKU?.commoditySkuId.toString()
        map["commodityId"] = mSelectSKU?.commodityId.toString()
        map["quantity"] = mCountControlerView?.currentCount ?: 0
        //物流收货方式
        map["logisticsTakeMode"] = mReceivingType
        if (mReceivingType == CommodityOpenDialog.WayType.TYPE_SELECT_SHOP) {
            mSelectShop4S?.shopId?.let {
                map["logisticsServiceDealerId"] = it
            }
        }
        if (skuAdditionVoList.isNotEmpty()) {
            val temp = arrayListOf<HashMap<String, Any>>()
            for (values in skuAdditionVoList) {
                values.selectAttributeValues.lmSkuListAdditionAttributeValueVoList.forEach { attr ->
                    val p = HashMap<String, Any>()
                    //属性id
                    values.selectAttributeValues.additionAttributeId?.let {
                        p["additionAttributeId"] = it
                    }
                    //属性值id
                    values.selectAttributeValues.additionAttributeValueId?.let {
                        p["additionAttributeValueId"] = it
                    }
                    //附加商品Id
                    attr.commodityId?.let {
                        p["commodityId"] = it
                    }
                    //附加商品skuId
                    attr.additionCommoditySkuId?.let {
                        p["skuId"] = it
                    }
                    temp.add(p)
                }
            }
            if (temp.isNotEmpty()) {
                map["cartAdditions"] = temp
            }
        }
        map["channelSourceId"] = AppConstants.CHANNEL_SOURCE_ID.toString()
        map["commodityClassifyId"] = mCommoditySku?.commodityClassifyId ?: 0
        if (openType == CommodityOpenDialog.NOW_UPDATE_CART) {
            map["cartId"] = mCartCommodity?.cartId.toString()
            L00bangRequestManager2.getServiceInstance()
                .updateToCart(map)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(object : ProgressSubscriber<Boolean>(mContext) {
                    override fun onSuccess(t: Boolean) {
                        super.onSuccess(t)
                        if (mContext is ShoppingCartActivity) {
                            (mContext as ShoppingCartActivity).refreshShoppingCartData()
                        }
                        dismiss()
                    }
                })
        } else {
            L00bangRequestManager2.getServiceInstance()
                .addToCart(map)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(object : ProgressSubscriber<CartInfo>(mContext) {
                    override fun onSuccess(t: CartInfo) {
                        super.onSuccess(t)
                        dismiss()
                        SelfUserInfoLoader.getInstance().cartInfo.count = t.count
                        UserInfoChangedHelper.sendCartInfoChangedBroadcast()
                        mOnSkuSelectListener?.addCarSuccess(t.count)
                        mCountControlerView?.apply {
                            currentCount = minCount
                        }
                        ToastUtil.showMessage(context, R.string.store_commodity_add_cart_success)
                    }
                })
        }
    }


    private val skuAdditionVoList = arrayListOf<CommoditySku.AdditionAttributeVos>()

    private fun checkSelectSku(): Boolean {

        skuAdditionVoList.clear()
        val list: List<CommoditySku.AttributeValueAttr?> = attributeAdapter?.data ?: emptyList()
        for (commodityAttribute in list) {
            if (commodityAttribute?.currentSelectAttributeValue == null) {
                ToastUtil.showMessage(
                    mContext,
                    mContext.resources.getString(
                        R.string.validator_please_choose,
                        commodityAttribute?.attributeName
                    )
                )
                return false
            }
        }
        if (mSelectSKU == null) {
            ToastUtil.showMessage(mContext, R.string.toast_commodity_sku_p_select_attr)
            return false
        }
        //附加商品
        mSelectSKU?.lmCommoditySkuAdditionVoList?.forEach { additionVo ->
            additionVo?.skuAdditionAttributes?.forEach { vo ->
                if (vo.requiredFlag == 1) {
                    if (vo.selectAttributeValues == null) {
                        ToastUtil.showMessage(
                            mContext,
                            mContext.resources.getString(
                                R.string.validator_please_choose,
                                vo.additionAttributeName
                            )
                        )
                        skuAdditionVoList.clear()
                        return false
                    }
                }
                if (vo.selectAttributeValues != null) {
                    skuAdditionVoList.add(vo)
                }
            }

        }
        //收获方式
        if (mReceivingType < 0) {
            ToastUtil.showMessage(mContext, "请选择收货方式")
            return false
        }
        var hasStock: Boolean? = null
        //服务门店
        if (mReceivingType == CommodityOpenDialog.WayType.TYPE_SELECT_SHOP) {
            if (mSelectShop4S == null) {
                ToastUtil.showMessage(mContext, "请" + mTvSubTitle?.text)
                return false
            }
            hasStock = mShopStock > 0 && mShopStock >= (mCountControlerView?.currentCount ?: 0)
        }
        if (!((hasStock != null && hasStock) || mSelectSKU?.skuStock ?: 0 >= mCountControlerView?.currentCount ?: 0)) {
            ToastUtil.showMessage(mContext, mContext.getString(R.string.sku_size_off))
            return false
        }
        return true
    }

    protected fun setData() {
        mCommoditySku?.apply {
            val sku: CommoditySku.SkuValue? = skuList?.get(0)

            ivSkuPic?.createImageLoad(sku?.skuImage ?: commodityMainImage)
                ?.load()
            ivSkuPic?.setOnClickListener {
                val intent = Intent(mContext, ScanImageActivity::class.java)
                intent.putExtra(
                    ScanImageActivity.EXTRA_IMAGE_URLS,
                    arrayOf(sku?.skuImage ?: commodityMainImage)
                )
                mContext.startActivity(intent)
            }
            if (!TextUtils.isEmpty(manageModeStr) && tvSkuName is TagTextView) {
                val l = arrayListOf<String>()
                l.add(manageModeStr)
                (tvSkuName as TagTextView).setTagTextColor("#ffffff")
                (tvSkuName as TagTextView).setTagLayoutRes(R.layout.layout_commodity_tag_new)
                (tvSkuName as TagTextView).setTagsBackgroundStyle(R.drawable.bg_corner4_solid_ea0029)
                (tvSkuName as TagTextView).setTagStart(l, commodityName)
            } else {
                tvSkuName?.text = commodityName
            }
            mTvStockCount?.text = null
            mTvDepositCarPrice?.text = mContext.getString(R.string.commodity_deposit, depositStr)
            mTvDepositCarPrice?.visibility =
                if (TextUtils.isEmpty(depositStr)) View.GONE else View.VISIBLE
            if (minPrice != null && minPrice == maxPrice) {
                tvSkuPrice?.text = ModelUtils.getRmbOrEmptyString(minPrice)
            } else {
                tvSkuPrice?.text = "¥$minPrice-$maxPrice"
            }
            mTvSkuOriginalPrice?.visibility = View.GONE
        }
        //活动
        llActivity?.visibility = View.GONE
        tvStockDesc?.visibility = View.GONE

        if (mCommoditySku?.commodityClassifyId == 0) {
            mTvSubTitle?.setText(R.string.commodity_please_buy_car_shop)
        }
    }

    private fun initFootView() {
        mLlWayLayout = mFootView?.findViewById(R.id.ll_way_layout)
        mLlLayout = mFootView?.findViewById(R.id.ll_layout)
        mTvShopStockTip = mLlWayLayout?.findViewById(R.id.tv_shop_stock_tip)
        mCountControlerView = mFootView?.findViewById(R.id.count_controler_view)
        mCountControlerView?.setNeedToast(true)
        mCountControlerView?.setOnCountChangeListener(object :
            CountControlerView.OnCountChangeListener {
            override fun onReduce(success: Boolean, minCount: Int) {
                if (!success) {
                    ToastUtil.showMessage(
                        mContext,
                        mContext.getString(R.string.commodity_buy_min_count, minCount)
                    )
                }
            }

            override fun onAdd(success: Boolean, maxCount: Int) {
                if (!success) {
                    ToastUtil.showMessage(
                        mContext,
                        mContext.getString(R.string.commodity_buy_max_count, maxCount)
                    )
                }
            }
        })
        mTvSkuStockTip = mFootView?.findViewById(R.id.tv_sku_stock_tip)
        mTvSubTitle = mFootView?.findViewById(R.id.tvSubTitle)
        mFootView?.findViewById<View>(R.id.ll_shop_name)?.setOnClickListener {
            showServiceDialog(findViewById(R.id.tv_shop_name))
        }
        setBuyCountLimit(0, 0)

        //2023/5/30需求变动，显示选择门店，不隐藏，仅帮自动选择。
        //增加自动选择车辆门店的信息功能
        if (AppUtil.getActivity(mContext) != null) {
            LocationHelper.getInstance().requestLocation(
                AppUtil.getActivity(mContext),
                object : LocCallBack {
                    override fun onSuccess(entity: LocationEntity?) {
                        if (entity != null) {
                            setLocationAndRefresh(entity)
                        }
                    }

                    override fun onError(errMsg: String?) {
                        LogUtils.i("location fail,show shop select. msg:$errMsg");
                    }
                }
            )
        }
    }

    override fun getDefaultLayoutResId(): Int {
        return R.layout.dialog_commodity_sku
    }

    override fun isBottomDialog(): Boolean {
        return true
    }

    override fun isNeedSetButton(): Boolean {
        return false
    }

    override fun show() {
        super.show()
        CountDownManager.getInstance().removeOnTickListener(this)
        CountDownManager.getInstance().addOnTickListener(this)
        val window = window
        if (window != null && window.attributes != null) {
            val p = window.attributes
            p.height =
                DeviceUtil.getScreenHeight() - mContext.resources.getDimensionPixelOffset(R.dimen.normal_210)
            window.attributes = p
        }
    }

    /**
     * 检查点击顺序
     */
    private fun checkClickOrder(position: Int):Boolean{
        var check = true;
        val shoppingSelectionField: String? = mCommoditySku?.shoppingSelectionField
        if (position > 0){
            for (i in (position-1) downTo 0){
                val A: Char? = shoppingSelectionField?.get(i)
                if ( A == '0'){
                    check = false;
                }
            }
        }else{
            check = false;
        }
        return check;
    }

    /**
     * 记录点击 字符串
     */
    private fun upClickOrder(position: Int,i: Char){
        val charArray = mCommoditySku?.shoppingSelectionField?.toCharArray()
        if (i == '1') {
            charArray?.set(position, i)
        }else{
            for (j in position until charArray!!.size){
                charArray.set(j, i)
            }
        }
        mCommoditySku?.shoppingSelectionField = java.lang.String(charArray).toString()
    }

    private fun clickToastText(){
        var charArray = mCommoditySku?.shoppingSelectionField?.toCharArray()
        for (i in 0 until charArray!!.size){
            if (charArray[i] == '0'){
                ToastUtil.showMessage(mContext,"请选择" + mCommoditySku?.attrList!![i].attributeName)
                return
            }
        }
    }

    override fun onAttributeClickListener(button: PressEffectiveCompoundButton?,position: Int, i: Int) {
        //  计数字符串 "000000" ， 当一个没点就是"000000" , 如现在点 position = 1的 就校验这个值 ，如 position = 0 的是 1 就允许往下
        //  如果不是 则提示
       if(position > 0 && !checkClickOrder(position)){
            if (button != null) {
                button.isSelected = false
                button.isChecked = false
            }
            clickToastText()
            return
        }
/**************************************************************************************/
/**                               当选项不匹配的时候提示下不匹配                            */
        val buffer = StringBuilder()
        val selected = arrayListOf<CommoditySku.AttributeValue>()
        mCommoditySku?.attrList?.forEach {
            it.lmAttributeValueList?.forEach{
                if(it.checkState == 1){
                    selected.add(it)
                }
            }
        }
        //当前层未空的时候判断一下,添加新的进去并删除原有的选项，当为空就判断当前预期是否符合
        if(mCommoditySku?.attrList?.get(position)?.currentSelectAttributeValue != null) {
            //if (mCommoditySku?.attrList?.get(position)?.lmAttributeValueList?.get(i)?.checkState == 0) {
            selected.remove(mCommoditySku?.attrList?.get(position)?.currentSelectAttributeValue!!)
            selected.add(mCommoditySku?.attrList?.get(position)?.lmAttributeValueList?.get(i)!!)
            //}
        }else if (mCommoditySku?.attrList?.get(position)?.lmAttributeValueList?.get(i)?.checkState == 0){
            selected.add(mCommoditySku?.attrList?.get(position)?.lmAttributeValueList?.get(i)!!)
        }
        sortByAttributeId(selected)
        for (selectedEntity in selected) {
            buffer.append(selectedEntity.attributeValueId).append(",")
        }
        if (buffer.toString() != "") {
            if (mSkuCollection?.get(buffer.substring(0, buffer.length - 1)) == null) {
                if (button != null) {
                    button.isSelected = false
                    button.isChecked = false
                }
                ToastUtil.showMessage(context,"类型不匹配")
                return
            }
        }
/**************************************************************************************/

        val attributeValueList: List<CommoditySku.AttributeValue>? =
            mCommoditySku?.attrList?.get(position)?.lmAttributeValueList

        if (attributeValueList?.get(i)?.checkState == 2) {
            return
        }
        if (attributeValueList?.get(i)?.checkState == 0) {
            attributeValueList.forEachIndexed { tempI, attr ->
                if (tempI == i) {
                    attr.checkState = 1
                    upClickOrder(position,'1')
                    mCommoditySku?.attrList?.get(position)?.currentSelectAttributeValue = attr
                } else {
                    attr.checkState = if (attr.checkState == 2) 2 else 0
                }
            }
        } else {
            attributeValueList?.forEachIndexed { tempI, attr ->
                if (tempI == i) {
                    attr.checkState = 0
                    upClickOrder(position,'0')
                    mCommoditySku?.attrList?.get(position)?.currentSelectAttributeValue = null
                } else {
                    attr.checkState = if (attr.checkState == 2) 2 else 0
                }
            }
        }
        mSelectAttributeValues.clear()
        val charArray = mCommoditySku?.shoppingSelectionField?.toCharArray()
        for (j in 0 until charArray!!.size) {
            val it = mCommoditySku?.attrList?.get(j)
            if (charArray[j] == '1'){
                if (it?.currentSelectAttributeValue != null) {
                    mSelectAttributeValues.add(it.currentSelectAttributeValue)
                }
            }else{
                if (it?.currentSelectAttributeValue != null) {
                    it.currentSelectAttributeValue = null
                }
            }
        }
        //mCommoditySku?.attrList?.forEach {
        //    if (it.currentSelectAttributeValue != null) {
        //        mSelectAttributeValues.add(it.currentSelectAttributeValue)
        //    }
        //}
        updateAttributeButton()
        adapter?.notifyDataSetChanged()
    }

    private fun updateAttributeButton() {
        //处理未选中的按钮
        val count = attributeAdapter?.itemCount ?: 0
        for (i in 0 until count) {
            val list = arrayListOf<Long>()
            val att: CommoditySku.AttributeValueAttr? = attributeAdapter?.data?.get(i)
            att?.lmAttributeValueList?.forEach { attValue ->
                if (mSkuCollection?.get(attValue.attributeValueId.toString()) == null) {
                    attValue.checkState = 2
                } else if (att.currentSelectAttributeValue != null && att.currentSelectAttributeValue.attributeValueId == attValue.attributeValueId) {
                    attValue.checkState = 1
                    list.add(attValue.attributeValueId)
                } else {
                    attValue.checkState = 0
                }
                val cacheSelected = arrayListOf<CommoditySku.AttributeValue>()
                cacheSelected.addAll(mSelectAttributeValues)
                var hasValue: Any? = null
                for (attributeValue in cacheSelected) {
                    if (attributeValue.attributeId == attValue.attributeId) {
                        hasValue = attributeValue
                        break
                    }
                }
                cacheSelected.remove(hasValue)
                cacheSelected.add(attValue)

                sortByAttributeId(cacheSelected)
                val buffer = StringBuilder()
                for (selectedEntity in cacheSelected) {
                    buffer.append(selectedEntity.attributeValueId).append(",")
                }
                if (mSkuCollection?.get(buffer.substring(0, buffer.length - 1)) != null) {
                    attValue.checkState = if (attValue.checkState == 1) 1 else 0
                } else {
                    attValue.checkState = 2
                }
            }
        }
        //处理跳项 不选中。如：已经选择 纯电，410标准型，星焰红，突然取消纯电，接下来的下面选项全部清空
        /*val charArray = mCommoditySku?.shoppingSelectionField?.toCharArray()
        for (i in 0 until charArray!!.size){
           if (charArray[i] == '0'){
                val att: CommoditySku.AttributeValueAttr? = attributeAdapter?.data?.get(i)
                att?.lmAttributeValueList?.forEach { attValue ->
                    //先全部置为 0 未点击状态
                    attValue.checkState = 0
                    if (mSkuCollection?.get(attValue.attributeValueId.toString()) == null) {
                        attValue.checkState = 2
                    }
                }
                att?.currentSelectAttributeValue = null
            }
        }*/

        if (mSelectAttributeValues.size > 0 && mSelectAttributeValues.size == mCommoditySku?.attrList?.size) {
            sortByAttributeId(mSelectAttributeValues)
            //                Collections.sort(attributeList);
            val sb = StringBuilder()
            val sbAttribute = StringBuilder()

            mSelectAttributeValues.forEach {
                sb.append(it.attributeValueId)
                sb.append(",")
                sbAttribute.append(it.attributeValueName)
                sbAttribute.append(",")
            }
            if (sbAttribute.isNotEmpty()) {
                sbAttribute.delete(sbAttribute.length - 1, sbAttribute.length)
            }
            mSkuAttribute = sbAttribute.toString()

            val tempSku = mSkuCollection?.get(sb.substring(0, sb.length - 1))
            if (tempSku != null) {
                val skuAttributeValueIds = tempSku.skuAttributeValueIds?.split(",")
                //再次校验 选中属性与 sku的属性是否对应
                if ((skuAttributeValueIds?.size ?: 0) == mSelectAttributeValues.size) {
                    mSelectSKU = tempSku
                    refreshSkuData()
                    updateFooterView()
                }
            }
        } else {
            mSelectSKU = null
            mSkuAttribute = null
            setData()
            updateFooterView()
        }
    }

    /**
     * 设置购买数量限制
     */
    private fun setBuyCountLimit(limitBuyCountMin: Int, limitTotalBuyCount: Int) {
        setCountControler(limitBuyCountMin, limitTotalBuyCount)
        //话费和整车限制购买数量位1
        if (mCommoditySku?.commodityClassifyId == 0
            || mCommoditySku?.commodityClassifyId == 5
        ) {
            mCountControlerView?.minCount = 1
            mCountControlerView?.maxCount = 1
        }
    }

    private fun setCountControler(min: Int, max: Int) {
        var text1 = ""
        var text2 = ""

        mCountControlerView?.setMaxLimit(false)
        if (max == 0) {
            mCountControlerView?.maxCount = Integer.MAX_VALUE
        } else {
            mCountControlerView?.maxCount = max
            text2 = mContext.getString(R.string.commodity_buy_max_count_tip, max)
        }

        if (min == 0) {
            mCountControlerView?.minCount = 1
        } else {
            if (min > 1) {
                text1 = mContext.getString(R.string.commodity_buy_max_count_tip, min)
            }
            mCountControlerView?.minCount = min
        }
        text1 =
            text1 + (if (!TextUtils.isEmpty(text1) && !TextUtils.isEmpty(text2)) "；" else "") + text2
        mTvSkuStockTip?.text = if (TextUtils.isEmpty(text1)) null else "($text1)"

    }

    @SuppressLint("SetTextI18n")
    private fun updateFooterView() {
        var selectorAttribute = ""
        attributeAdapter?.data?.forEach {
            if (!TextUtils.isEmpty(it?.currentSelectAttributeValue?.attributeValueName)) {
                selectorAttribute += it?.currentSelectAttributeValue?.attributeValueName
                selectorAttribute += " | "
            }
        }
        selectorAttribute = selectorAttribute.replace("  ", " ").trim()
        if (selectorAttribute.endsWith(" |")) {
            selectorAttribute =
                selectorAttribute.subSequence(0, selectorAttribute.length - 2).toString()
        }
        mTvSkuAttribute?.visibility =
            if (TextUtils.isEmpty(selectorAttribute)) View.GONE else View.VISIBLE
        mTvSkuAttribute?.text = "已选：$selectorAttribute"

        setBuyCountLimit(0, 0)
        mLlLayout?.removeAllViews()
        mSelectSKU?.apply {
            lmCommodityAsLimit?.apply {
                if (limitBuyCountFlag == 1) {
                    setBuyCountLimit(limitBuyCountMin, limitTotalBuyCount)
                } else {
                    setBuyCountLimit(0, 0)
                    mTvSkuStockTip?.text = null
                }
            }

            lmActivityCommodityAsLimit?.apply {
                if (activityStatus == 5) {
                    setBuyCountLimit(limitBuyCountMin, limitTotalBuyCount)
                }
            }
            lmCommoditySkuAdditionVoList?.forEach { additionVo ->
                additionVo?.skuAdditionAttributes?.forEach { additionAttributes ->
                    val view = View.inflate(context, R.layout.layout_sku_other_service, null)
                    val commodityAttributeViewHolder =
                        CommodityAttributeAdapter.CommodityAttributeViewHolder(view, null)
                    commodityAttributeViewHolder.mIvTip?.visibility =
                        if (!TextUtils.isEmpty(additionAttributes?.comment)) View.VISIBLE else View.GONE

                    commodityAttributeViewHolder.mIvTip?.setOnClickListener {
                        <EMAIL>(
                            additionAttributes?.comment,
                            additionAttributes.additionAttributeName
                        )
                    }
                    commodityAttributeViewHolder.tvAttributeTitle?.text =
                        additionAttributes?.additionAttributeName
                    commodityAttributeViewHolder.mFlowLayout?.removeAllViews()
                    val inflater = LayoutInflater.from(context)
                    additionAttributes?.skuAdditionAttributeValues?.forEach { commodityAttributeValue ->

                        val compoundButton = inflater.inflate(
                            R.layout.item_commodity_attribute_value1,
                            commodityAttributeViewHolder.mFlowLayout,
                            false
                        ) as PressEffectiveCompoundButton
                        compoundButton.text =
                            commodityAttributeValue.additionAttributeValueName + " | +" + commodityAttributeValue.skuPrice
                        compoundButton.setOnClickListener {
                            if (commodityAttributeValue.isChecked == 1) {
                                commodityAttributeValue.isChecked = 0
                                additionAttributes.selectAttributeValues = null
                                compoundButton.isChecked =
                                    commodityAttributeValue.isChecked == 1
                            } else {

                                additionAttributes.selectAttributeValues?.isChecked = 0
                                val index: Int? =
                                    additionAttributes.skuAdditionAttributeValues?.indexOf(
                                        additionAttributes.selectAttributeValues
                                    )
                                index?.let {
                                    if (it >= 0) {
                                        val compoundButton =
                                            commodityAttributeViewHolder.mFlowLayout?.get(it) as PressEffectiveCompoundButton
                                        compoundButton.isChecked =
                                            additionAttributes.selectAttributeValues?.isChecked == 1
                                    }
                                }
                                commodityAttributeValue.isChecked = 1
                                additionAttributes.selectAttributeValues =
                                    commodityAttributeValue
                                compoundButton.isChecked =
                                    commodityAttributeValue.isChecked == 1
                            }
                        }
                        compoundButton.isChecked = commodityAttributeValue.isChecked == 1
                        commodityAttributeViewHolder.mFlowLayout?.addView(compoundButton)
                    }
                    mLlLayout?.addView(view)
                }
            }
        }
        //请求门店库存
        requestShopStock()
    }

    /**
     * 处理收货UI
     */
    private fun doReceivingWayType() {
        mReceivingType = CommodityOpenDialog.WayType.TYPE_HOME
        // 物流收货方式 0送货到家 1送货到店 2虚拟收货
        val logisticsTakeMode: String = mCommoditySku?.logisticsTakeMode ?: ""
        if (TextUtils.isEmpty(logisticsTakeMode)) {
            mLlWayLayout?.visibility = View.GONE
        } else {
            //首先判断一下 isBenefitcommodity 为 1就显示门店,如果为0 走以前的老代码
            if (isBenefitcommodity == 1){
                mReceivingType = CommodityOpenDialog.WayType.TYPE_SELECT_SHOP
                mLlWayLayout?.findViewById<View>(R.id.tv_title)?.visibility = View.GONE
                mLlWayLayout?.findViewById<View>(R.id.radio_group)?.visibility = View.GONE
            }else{
                val modes = logisticsTakeMode.split(",")
                val t1 = modes.contains("0")
                val t2 = modes.contains("1")
//                  val t3 = modes.contains("2")
                if (!modes.contains("0") && !modes.contains("1")) {
                    mReceivingType = CommodityOpenDialog.WayType.TYPE_VIRTUALLY
                    mLlWayLayout?.visibility = View.GONE
                } else if (t1 && !t2) {
                    mReceivingType = CommodityOpenDialog.WayType.TYPE_HOME
//                送货到家不展示选择
                    mLlWayLayout?.visibility = View.GONE
                } else if (!t1 && t2) {
                    mReceivingType = CommodityOpenDialog.WayType.TYPE_SELECT_SHOP
                    //送货到店 展示选择门店
                    mLlWayLayout?.findViewById<View>(R.id.tv_title)?.visibility = View.GONE
                    mLlWayLayout?.findViewById<View>(R.id.radio_group)?.visibility = View.GONE
                } else {
                    //经销商商品且为整车时隐藏到家
                    if (mCommoditySku?.createDealerId ?: 0 > 0 && mCommoditySku?.commodityClassifyId ?: 0 == 0) {
                        mReceivingType = CommodityOpenDialog.WayType.TYPE_SELECT_SHOP
                        mLlWayLayout?.findViewById<View>(R.id.tv_title)?.visibility = View.GONE
                        mLlWayLayout?.findViewById<View>(R.id.radio_group)?.visibility = View.GONE
                    } else {
                        mReceivingType = -1
                        //送货到店 展示选择门店
                        mLlWayLayout?.visibility = View.VISIBLE
                        mLlWayLayout?.apply {
                            findViewById<View>(R.id.tv_title)?.visibility = View.VISIBLE
                            val radioGroup = findViewById<RadioGroup>(R.id.radio_group)
                            radioGroup?.visibility = View.VISIBLE
                            //默认隐藏门店
                            findViewById<View>(R.id.ll_shop_score)?.visibility = View.GONE
                            findViewById<View>(R.id.ll_shop_name)?.visibility = View.GONE
                            radioGroup?.setOnCheckedChangeListener { _, checkedId ->
                                if (checkedId == R.id.radio1) {
                                    mReceivingType = CommodityOpenDialog.WayType.TYPE_HOME
                                    // 送货到家
                                    findViewById<View>(R.id.ll_shop_score)?.visibility = View.GONE
                                    findViewById<View>(R.id.ll_shop_name)?.visibility = View.GONE
                                } else {
                                    // 选择门店
                                    mReceivingType = CommodityOpenDialog.WayType.TYPE_SELECT_SHOP
                                    findViewById<View>(R.id.ll_shop_score)?.visibility = View.VISIBLE
                                    findViewById<View>(R.id.ll_shop_name)?.visibility = View.VISIBLE
                                }
                            }
                        }
                    }
                }
            }
        }

    }

    /**
     * 更新sku活动UI
     */
    private fun updateActivityInfo(currentTime: Long) {
        mSelectSKU?.apply {
            lmActivityCommodityAsLimit?.apply {

                val tsl = currentTime.toString().length - promotionStartTime.toString().length
                val tel = currentTime.toString().length - promotionEndTime.toString().length
                if (currentTime < promotionStartTime * 10.0.pow(tsl.toDouble()).toLong()) {
                    activityStatus = 4
                } else if (currentTime >= promotionEndTime * 10.0.pow(tel.toDouble()).toLong()) {
                    activityStatus = 6
                    llActivity?.visibility = View.GONE
                    tvStockDesc?.visibility = View.GONE
                    //活动结束，不需要展示了，注销
                    CountDownManager.getInstance().removeOnTickListener(this@SkuSelectorDialog)
                }
                if (activityStatus == 5 || activityStatus == 4 && promotionEndTime > 0) {
                    if (activityStatus != 5 && currentTime > promotionStartTime) {
                        activityStatus = 5
                    }
                    tvStockDesc?.visibility = View.GONE
                    if (activityStatus == 5 && !TextUtils.isEmpty(mSelectSKU?.stockDesc)) {
                        tvStockDesc?.visibility = View.VISIBLE
                        tvStockDesc?.text = mSelectSKU?.stockDesc
                        llActivity?.visibility = View.GONE
                        // 展示库存提示文案 暂时去除 倒计时 避免多次重复刷新UI
//                        CountDownManager.getInstance().removeOnTickListener(this@SkuSelectorDialog)
                        return@apply
                    }
                    llActivity?.visibility = View.VISIBLE

                    tvActivityName?.text = promotionStrategyTypeName
                    val times = StringBuilder()
                    val timeDelta = if (activityStatus == 4) {
                        times.append(mContext.getString(R.string.commodity_new_start_time))
                        TimeDelta.create(
                            promotionStartTime * 10.0.pow(tsl.toDouble()).toLong(),
                            AppUtil.getServerCurrentTime()
                        )
                    } else {
                        times.append(mContext.getString(R.string.commodity_new_end_time))
                        TimeDelta.create(
                            AppUtil.getServerCurrentTime(),
                            promotionEndTime * 10.0.pow(tel.toDouble()).toLong()
                        )
                    }
                    if (timeDelta.days > 0) {
                        times.append(timeDelta.days)
                        times.append("天")
                        times.append(" ")
                    } else {
                        times.append(" ")
                    }

                    if (timeDelta.hours > 0) {
                        if (timeDelta.hours < 10) {
                            times.append("0")
                        }
                        times.append(timeDelta.hours)
                        times.append(":")
                    } else {
                        times.append("00:")
                    }

                    if (timeDelta.minutes < 10) {
                        times.append("0")
                    }
                    times.append(timeDelta.minutes)
                    times.append(":")

                    if (timeDelta.seconds < 10) {
                        times.append("0")
                    }
                    times.append(timeDelta.seconds)
                    tvActivityTime?.text = times
                }
            }
        }
        setSkuPrice()
    }

    private fun setSkuPrice() {
        mTvSkuOriginalPrice?.visibility = View.GONE
        mSelectSKU?.apply {
            var price = originalPrice

            if (lmActivityCommodityAsLimit?.activityStatus == 5) {
                lmActivityCommodityAsLimit?.apply {
                    price = activityPrice
                    if (TextUtils.isEmpty(stockDesc)) {
                        mTvSkuOriginalPrice?.paint?.flags = Paint.STRIKE_THRU_TEXT_FLAG
                        mTvSkuOriginalPrice?.text =
                            mContext.getString(R.string.group_buying_old_price, originalPrice)
                        mTvSkuOriginalPrice?.visibility = View.VISIBLE
                    }
                }
            }
            tvSkuPrice?.text = mContext.getString(R.string.group_buying_old_price, price)
        }
    }

    private fun refreshSkuData() {
        mSelectSKU?.apply {
            ivSkuPic?.createImageLoad(skuImage)?.load()
            ivSkuPic?.setOnClickListener {
                val intent = Intent(mContext, ScanImageActivity::class.java)
                intent.putExtra(
                    ScanImageActivity.EXTRA_IMAGE_URLS,
                    arrayOf(skuImage)
                )
                mContext.startActivity(intent)
            }
            mTvStockCount?.text = if (mCommoditySku?.createSourceType ?: 0 == 1) mContext.getString(
                R.string.commodity_stock_count,
                skuStock
            ) else null
            mTvDepositCarPrice?.text = mContext.getString(R.string.commodity_deposit, depositStr)
            mTvDepositCarPrice?.visibility =
                if (TextUtils.isEmpty(depositStr)) View.GONE else View.VISIBLE
            llActivity?.visibility = View.GONE
            tvStockDesc?.visibility = View.GONE
//            stockDesc = "库存没有了"
//            lmActivityCommodityAsLimit?.apply {
//                activityStatus = 4
//                promotionStartTime = AppUtil.getServerCurrentTime() + 10_000
//                promotionEndTime = AppUtil.getServerCurrentTime() + 20_000
//            }
            CountDownManager.getInstance().removeOnTickListener(this@SkuSelectorDialog)
            CountDownManager.getInstance().addOnTickListener(this@SkuSelectorDialog)
            updateActivityInfo(AppUtil.getServerCurrentTime())
        }
    }

    override fun showTip(url: String?, name: String?) {
        referrer?.apply {
            val eventName =
                "点击($commodityId)(" + (mCommoditySku?.commodityName
                    ?: "") + ")(" + name + ")叹号"
            SensorsUtils.sensorsClickBtn(eventName, referrer, "选择SKU弹窗")
        }
        val mSkuImagePreDialog = SkuImagePreDialog(mContext, url)
        mSkuImagePreDialog.show()
    }


    /**
     * 根据属性id排序
     *
     * @param list
     */
    private fun sortByAttributeId(list: MutableList<CommoditySku.AttributeValue>) {
        for (j in 0 until list.size - 1) {
            for (k in 0 until list.size - 1 - j) {
                var cacheEntity: CommoditySku.AttributeValue
                if (list[k].attributeId > list[k + 1].attributeId) {
                    //交换数据
                    cacheEntity = list[k]
                    list[k] = list[k + 1]
                    list[k + 1] = cacheEntity
                }
            }
        }
    }

    private fun initAdapter() {
        mCommoditySku?.attrList?.forEach { attr ->
            attr?.lmAttributeValueList?.forEach { attributeValue ->
                if (mSkuCollection?.get(attributeValue.attributeValueId.toString()) == null) {
                    attributeValue.checkState = 2
                }
            }
        }


        adapter = HeaderAndFooterWrapperAdapter<Any?>()
        mFootView = View.inflate(context, R.layout.layout_sku_footer, null)
        initFootView()
        attributeAdapter = CommodityAttributeAdapter(
            context, mCommoditySku?.attrList ?: arrayListOf()
        )
        attributeAdapter?.setCommodityAttributeListener(this)
        adapter?.innerAdapter = attributeAdapter
        adapter?.addFooterView(mFootView)
        recyclerView?.adapter = adapter
    }


    fun showDialog() {
        show()
        if (mCommoditySku == null) {
            querySkuData()
        }
    }

    /**
     * 查询经销商信息 并回显
     */
    private fun queryShopInfo() {
        if ((shopId ?: 0) <= 0) {
            return
        }
        val map = HashMap<String, String>(1)
        map["dealerId"] = shopId.toString()
        L00bangRequestManager2.getServiceInstance()
            .getDealerInfo(map)
            .compose(L00bangRequestManager2.setSchedulers())
            .subscribe(object : BackgroundSubscriber<Shop4SFullInfo>(mContext) {
                override fun onSuccess(t: Shop4SFullInfo?) {
                    super.onSuccess(t)
                    shopId = null
                    t?.apply {
                        val modes = mCommoditySku?.logisticsTakeMode?.split(",")
                        if (modes?.contains("1") == true) {
                            if (mSelectShop4S == null) {
                                mSelectShop4S = Shop4S()
                            }
                            mSelectShop4S?.shopId = dealerId
                            mSelectShop4S?.fullName = dealerFullName
                            mSelectShop4S?.shortName = dealerShortName
                            mSelectShop4S?.shopNum = dealerCode
                            //收货方式回显
                            if (modes.contains("0")) {
                                val radioGroup =
                                    mLlWayLayout?.findViewById<RadioGroup>(R.id.radio_group)
                                radioGroup?.check(R.id.radio2)
                            }
                            mReceivingType = CommodityOpenDialog.WayType.TYPE_SELECT_SHOP
                            mFootView?.findViewById<TextView>(R.id.tv_shop_name)?.text =
                                dealerShortName
                        }
                        requestShopStock()
                    }
                }
            })
    }

    private fun setLocationAndRefresh(entity: LocationEntity) {
        val map: MutableMap<String, String> = java.util.HashMap(4)
        map["commodityId"] = commodityId.toString()
        map["lng"] = entity.longitude.toString()
        map["lat"] = entity.latitude.toString()
        if (!TextUtils.isEmpty(entity.cityName)) {
            map["cityName"] = entity.cityName
        }
        //领域服务
        map["channelCode"] = AppConstants.CHANNEL_SOURCE_ID.toString()
        map["sort"] = "1"
        L00bangRequestManager2.getServiceInstance().getFlagshipRefShopByCarType(map)
            .compose(L00bangRequestManager2.setSchedulers<List<Shop4S>>())
            .subscribe(object : BackgroundSubscriber<List<Shop4S?>?>(mContext) {
                override fun onSuccess(shop4S: List<Shop4S?>?) {
                    super.onSuccess(shop4S)
                    if (!shop4S.isNullOrEmpty()) {
                        mSelectShop4S = shop4S[0]
                        mFootView?.findViewById<TextView>(R.id.tv_shop_name)?.text =
                            shop4S[0]?.shortName
                        requestShopStock()

                    }
                }

                override fun onFailure(e: Throwable?) {
                    super.onFailure(e)
                }
            })
    }


    /**
     * 文字的sku详情，用于回显
     */
    private var mSkuAttribute: String? = null

    //声明接口
    private var mOnSkuSelectListener: OnSkuSelectListener? = null

    interface OnSkuSelectListener {
        fun onSkuSelect(attributes: String?, skuId: Long?)

        /**
         * 成功加入购物车
         * 购物车商品数量
         */
        fun addCarSuccess(count: Int)
    }

    //外部进行接口实现
    open fun setOnSkuSelectListener(listener: OnSkuSelectListener?) {
        mOnSkuSelectListener = listener
    }

    /**
     * 展示服务门店对话框
     */
    private fun showServiceDialog(textView: TextView) {
        if (AppUtil.checkActivityIsRun(mContext)) {
            if (serviceDealerDialog == null) {
                serviceDealerDialog = ChooseServiceDealerDialog(
                    mContext,
                    if (mCommoditySku?.commodityClassifyId == 0) "购车门店" else mContext.getString(
                        R.string.shopping_sku_title_service_select
                    )
                )
                //领域服务
                serviceDealerDialog?.setOpenFrom(1)
                serviceDealerDialog?.setOnChooseCompleteListener { shop4S ->
                    mSelectShop4S = shop4S
                    if (shop4S != null) {
                        textView.text = shop4S.shortName
                        requestShopStock()
                    }
                }
                serviceDealerDialog?.setOnShowListener {
                    if (AppUtil.getActivity(mContext) != null) {
                        LocationHelper.getInstance().requestLocation(
                            AppUtil.getActivity(mContext),
                            serviceDealerDialog
                        )
                    }
                }
            } else {
                serviceDealerDialog?.setOnShowListener(null)
            }
            serviceDealerDialog?.setCommodityId(commodityId ?: 0L)
            serviceDealerDialog?.show()
        }
    }

    /**
     * 查询门店库存
     */
    private fun requestShopStock() {
        mTvShopStockTip?.text = null
//        1. 如果是厂家商品
//           只有物流方式是前置库发货和供应商发货的，才展示门店可用库存。
//        2. 如果是经销商商品，
//           正常展示门店可用库存。
        val logisticsSendMode = mCommoditySku?.logisticsSendMode ?: 0
        val createSourceType = mCommoditySku?.createSourceType ?: 0
        val temp = createSourceType == 1 && (logisticsSendMode == 1 || logisticsSendMode == 2)
        val temp2 = createSourceType == 2
        if (!(temp || temp2)) {
            return
        }
        if (mSelectSKU == null || mSelectShop4S == null || mReceivingType != CommodityOpenDialog.WayType.TYPE_SELECT_SHOP) {
            return
        }
        val map = HashMap<String, String>(3)
        map["commodityId"] = commodityId.toString()
        map["commoditySkuId"] = mSelectSKU?.commoditySkuId.toString()
        mSelectShop4S?.shopNum?.let {
            map["dealerCode"] = it
        }
        mSelectSKU?.lmActivityCommodityAsLimit?.promotionActivityId?.let {
            map["activityId"] = it.toString()
        }
        map["channelCode"] = AppConstants.CHANNEL_SOURCE_ID.toString()

        L00bangRequestManager2.getServiceInstance()
            .getFlagshipRefShopByCarTypeStock(map)
            .compose(L00bangRequestManager2.setSchedulers())
            .subscribe(object : BackgroundSubscriber<StockVo>(context) {
                override fun onSuccess(t: StockVo?) {
                    super.onSuccess(t)
                    mShopStock = t?.dealerStock ?: 0
                    mTvShopStockTip?.text =
                        mContext.getString(R.string.commodity_sku_dealer_stock, mShopStock)
                }

                override fun onError(e: Throwable) {
                    super.onError(e)
                    mShopStock = 0
                    mTvShopStockTip?.text =
                        mContext.getString(R.string.commodity_sku_dealer_stock, mShopStock)
                }
            })
    }

    override fun dismiss() {
        super.dismiss()
        CountDownManager.getInstance().removeOnTickListener(this)
        mOnSkuSelectListener?.onSkuSelect(mSkuAttribute, mSelectSKU?.commoditySkuId)
    }

    /**
     * 时间变动
     * 将当前时间统一分发出去，保证所有的监听计算时相同
     */
    override fun onTick(currentTime: Long) {
        updateActivityInfo(currentTime)
    }

    override fun getCountDownTag(): Any {
        return AppUtil.getActivity(mContext) ?: this
    }
}