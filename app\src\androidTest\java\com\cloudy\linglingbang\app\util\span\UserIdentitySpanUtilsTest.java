package com.cloudy.linglingbang.app.util.span;

import android.graphics.Color;
import android.util.TypedValue;
import android.widget.TextView;

import com.cloudy.linglingbang.BaseInstrumentedTest;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.model.user.Author;

/**
 * <AUTHOR>
 * @date 2018/8/18
 */
public class UserIdentitySpanUtilsTest extends BaseInstrumentedTest {
    @Override
    public void test() {
        super.test();
        updateUserNameAndIdentity();
    }

    public void updateUserNameAndIdentity() {
        TextView textView = getActivity().getTextView();
        textView.setPadding(0, 0, 0, 0);
        textView.setBackgroundColor(Color.WHITE);
        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, getContext().getResources().getDimensionPixelSize(R.dimen.activity_set_text_32));

        Author author = new Author();
        author.setNickname("名字");
        author.setIsCarUser(1);
        author.setAudit(1);
        UserIdentitySpanUtils.setUserNameAndIdentity(getActivity().getTextView(), author, true);
    }
}