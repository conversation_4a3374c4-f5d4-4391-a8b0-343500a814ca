package com.cloudy.linglingbang.activity.auth;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.app.imageConfig.RoundedCornersTransformation;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.widget.BaseOnClickListener;
import com.cloudy.linglingbang.app.widget.banner.AdImageView;
import com.cloudy.linglingbang.app.widget.banner.AdRoundImageView;
import com.cloudy.linglingbang.model.auth.AuthClient;
import com.cloudy.linglingbang.model.user.User;

/**
 * 授权
 *
 * <AUTHOR>
 * @date 2019/3/4
 */
public class AuthActivity extends BaseActivity implements AuthHelper.AuthCallback {
    private AuthHelper mAuthHelper;
    /**
     * 应用图标
     */
    private AdImageView mIvAppIcon;
    /**
     * 应用名称
     */
    private TextView mTvAppName;
    /**
     * 头像
     */
    private ImageView mIvUserAvatar;
    /**
     * 昵称
     */
    private TextView mTvUserName;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_auth);
    }

    @Override
    protected void initialize() {
        mAuthHelper = new AuthHelper(this, this);

        mIvAppIcon = findViewById(R.id.iv_app_icon);
        mTvAppName = findViewById(R.id.tv_app_name);
        mIvUserAvatar = findViewById(R.id.iv_user_avatar);
        mTvUserName = findViewById(R.id.tv_user_name);

        AdRoundImageView ivSelfAppIcon = findViewById(R.id.iv_self_app_icon);
        if (ivSelfAppIcon != null) {
            //圆角
            new ImageLoad(this, ivSelfAppIcon, R.drawable.ic_launcher)
                    .setDoNotAnimate()
                    .setPlaceholderAndError(R.drawable.ic_common_place_holder_corner_10)
                    .setCircle(true)
                    .setRadius(ivSelfAppIcon.getCornerRadius())
                    .setCornerType(RoundedCornersTransformation.CornerType.ALL)
                    .load();
        }
        //取消
        findViewById(R.id.tv_cancel).setOnClickListener(new BaseOnClickListener() {
            @Override
            public void onClick(View v) {
                super.onClick(v);
                cancelAuth();
            }
        });

        //授权
        findViewById(R.id.btn_ok).setOnClickListener(new BaseOnClickListener(true) {
            @Override
            public void onClick(Context context) {
                super.onClick(context);
                if (mAuthHelper != null) {
                    mAuthHelper.authorize(AuthActivity.this);
                }
            }
        });

        //获取信息
        mAuthHelper.getAuthClientInfo(this);
        //检查登录
        if (!UserUtils.hasLogin()) {
            //登录
            AppUtil.goLogin(this);
        }
        updateUserUi();
    }

    private void updateUserUi() {
        if (UserUtils.hasLogin()) {
            User user = User.shareInstance();
            String url = AppUtil.getImageUrlBySize(user.getPhoto(), AppUtil._120X120);
            if (mIvUserAvatar != null) {
                ImageLoad.LoadUtils.loadAvatar(mIvUserAvatar.getContext(), mIvUserAvatar, url);
            }
            if (mTvUserName != null) {
                mTvUserName.setText(user.getNickname());
            }
        } else {
            if (mIvUserAvatar != null) {
                mIvUserAvatar.setImageResource(R.drawable.user_head_default_140x140);
            }
            if (mTvUserName != null) {
                mTvUserName.setText("");
            }
        }
    }

    @Override
    public void onLoginSuccess() {
        super.onLoginSuccess();
        updateUserUi();
    }

    /**
     * 取消授权
     */
    private void cancelAuth() {
        if (mAuthHelper != null) {
            mAuthHelper.authCancel(this);
        }
    }

    @Override
    public void onGetAuthClientInfo(AuthClient authClient) {
        if (authClient != null) {
            if (mIvAppIcon != null) {
                mIvAppIcon.createImageLoad(authClient.getClientIcon())
                        .setDoNotLoadWebp()
                        .load();
            }
            if (mTvAppName != null) {
                mTvAppName.setText(authClient.getClientName());
            }
        }
    }

    @Override
    public void onBackPressed() {
        cancelAuth();
    }
}
