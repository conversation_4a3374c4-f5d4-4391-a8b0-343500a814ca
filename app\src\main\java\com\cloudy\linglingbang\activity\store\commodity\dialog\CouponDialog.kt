package com.cloudy.linglingbang.activity.store.commodity.dialog

import android.content.Context
import android.content.DialogInterface
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.store.commodity.CommodityCouponItemView
import com.cloudy.linglingbang.activity.store.commodity.CommodityDetailActivity
import com.cloudy.linglingbang.activity.store.commodity.ReplacementListActivity
import com.cloudy.linglingbang.app.util.AppUtil
import com.cloudy.linglingbang.app.util.DensityUtil
import com.cloudy.linglingbang.app.util.DeviceUtil
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils
import com.cloudy.linglingbang.app.widget.dialog.alert.BaseAlertDialog
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder
import com.cloudy.linglingbang.app.widget.recycler.holder.CommonViewHolder
import com.cloudy.linglingbang.model.SourceModel
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber
import com.cloudy.linglingbang.model.store.commodity.CenterCommodity
import com.cloudy.linglingbang.model.store.ecology.Coupon
import com.cloudy.linglingbang.model.wrapper.CommonModel
import java.util.*

/**
 * 优惠卷弹窗
 *
 * <AUTHOR>
 * @date 2022/9/28
 */
class CouponDialog @JvmOverloads constructor(context: Context?, theme: Int = R.style.Dialog) :
    BaseAlertDialog(context, theme), DialogInterface.OnShowListener {
    private var mTvTittle: TextView? = null
    private var recyclerView: RecyclerView? = null
    private var mList: MutableList<Any>? = null
    var mActivityInfoList: List<CenterCommodity.CommodityActivityInfo>? = null

    /**
     * 是否从购物车列表打开
     */
    var openFromCart = false

    /**
     * 商品id的数组，用逗号隔开
     */
    var mCommodityIds: String? = null
    var commodityClassifyId = -1

    /**
     * 商品名称的数组，用逗号隔开
     */
    var mCommodityNames: String? = null


    override fun initView() {
        super.initView()
        setOnShowListener(this)
        mTvTittle = findViewById(R.id.tv_sku_name)
        recyclerView = findViewById(R.id.recyclerView)
        findViewById<View>(R.id.iv_close).setOnClickListener { dismiss() }
        mList = ArrayList()
        recyclerView?.adapter = Adapter(mContext, mList)
        if (openFromCart) {
            findViewById<View>(R.id.ll_top_tip).visibility = View.VISIBLE
            findViewById<View>(R.id.tv_tip).visibility = View.INVISIBLE
            findViewById<View>(R.id.iv_tip).visibility = View.INVISIBLE
        }
    }

    override fun isBottomDialog(): Boolean {
        return true
    }

    override fun isNeedSetButton(): Boolean {
        return false
    }

    override fun getDefaultLayoutResId(): Int {
        return R.layout.dialog_commodity_coupon
    }

    override fun show() {
        super.show()
        val window = window
        if (window != null && window.attributes != null) {
            val p = window.attributes
            p.height =
                DeviceUtil.getScreenHeight() - mContext.resources.getDimensionPixelOffset(R.dimen.normal_210)
            window.attributes = p
        }
    }

    inner class Adapter(context: Context?, data: List<Any?>?) :
        BaseRecyclerViewAdapter<Any?>(context, data) {
        override fun createViewHolder(itemView: View): BaseRecyclerViewHolder<Any?>? {
            return CouponViewHolder(itemView)
        }

        override fun createViewHolderWithViewType(
            itemView: View?,
            viewType: Int
        ): BaseRecyclerViewHolder<Any?> {
            if (viewType == R.layout.item_commodity_coupon_title) {
                return TitleViewHolder(itemView)
            } else if (viewType == R.layout.item_commodity_coupon_activity) {
                return ActivityInfoViewHolder(itemView)
            } else if (viewType == R.layout.item_store_divider_20) {
                return CommonViewHolder.DividerViewHolder(itemView) as BaseRecyclerViewHolder<Any?>
            }
            return super.createViewHolderWithViewType(itemView, viewType)
        }

        override fun getItemViewType(position: Int): Int {
            when {
                mData[position] is Coupon -> {
                    return R.layout.item_commodity_coupon1
                }
                mData[position] is CenterCommodity.CommodityActivityInfo -> {
                    return R.layout.item_commodity_coupon_activity
                }
                mData[position] is CommonModel.Divider -> {
                    return R.layout.item_store_divider_20
                }
                else -> return R.layout.item_commodity_coupon_title
            }
        }

        override fun getItemLayoutRes(viewType: Int): Int {
            return viewType
        }
    }

    inner class TitleViewHolder(itemView: View?) : BaseRecyclerViewHolder<Any?>(itemView) {
        var tv: TextView? = null
        override fun initItemView(itemView: View?) {
            super.initItemView(itemView)
            tv = itemView?.findViewById(R.id.tv_title);
        }

        override fun bindTo(bean: Any?, position: Int) {
            super.bindTo(bean, position)
            tv?.text = bean.toString()
        }
    }

    inner class ActivityInfoViewHolder(itemView: View?) : BaseRecyclerViewHolder<Any?>(itemView) {
        var tv_des: TextView? = null
        var tv_name: TextView? = null
        var iv_more: ImageView? = null
        var ll_content: LinearLayout? = null
        var rl_content: View? = null
        var mActivityInfo: CenterCommodity.CommodityActivityInfo? = null

        override fun initItemView(itemView: View?) {
            super.initItemView(itemView)
            tv_des = itemView?.findViewById(R.id.tv_des)
            tv_name = itemView?.findViewById(R.id.tv_name)
            iv_more = itemView?.findViewById(R.id.iv_more)
            ll_content = itemView?.findViewById(R.id.ll_content)
            rl_content = itemView?.findViewById(R.id.rl_content)

            rl_content?.setOnClickListener {
                if (AppUtil.checkLogin(itemView?.context))
                    if (showMore()) {
                        val eventName =
                            "点击($mCommodityIds)($mCommodityNames)(" + (mActivityInfo?.promotionName
                                ?: "") + ")促销"
                        mCommodityNames?.apply {
                            SensorsUtils.sensorsClickBtn(
                                eventName,
                                "商品详情页" + if (commodityClassifyId == 0) "(整车)" else "(优品)",
                                "优惠弹窗"
                            )
                        }
                        ReplacementListActivity.startActivity(
                            itemView?.context,
                            mActivityInfo?.promotionActivityId ?: 0
                        )
                    }
            }
        }

        private fun showMore(): Boolean {
            mActivityInfo?.apply {
                if (promotionType == 4 || promotionType == 2 || promotionStrategyType == 12) {
                    return true
                }
            }
            return false
        }

        override fun bindTo(bean: Any?, position: Int) {
            super.bindTo(bean, position)
            mActivityInfo = null
//            iv_more?.visibility = View.GONE
            ll_content?.visibility = View.GONE
            if (bean is CenterCommodity.CommodityActivityInfo) {
                mActivityInfo = bean
                bean.apply {
                    if (showMore()) {
                        //总价类/换购
                        iv_more?.visibility = View.VISIBLE
                    }
                    tv_des?.text = promotionStrategyTypeName
                    tv_name?.text = promotionShowName
                    rl_content?.visibility = View.VISIBLE
                    if (promotionType == 1 && promotionStrategyType == 11) {
                        rl_content?.visibility = View.GONE
                        ll_content?.visibility = View.VISIBLE
                        ll_content?.removeAllViews()
                        for (extCommodity in extCommodityList) {
                            val tv = TextView(itemView.context)
                            tv.tag = extCommodity.commodityId
                            tv.setOnClickListener {
                                if (it.tag is Long) {
                                    val eventName =
                                        "点击($mCommodityIds)($mCommodityNames)(" + (extCommodity?.commodityName
                                            ?: "") + ")促销"
                                    mCommodityNames?.apply {
                                        SensorsUtils.sensorsClickBtn(
                                            eventName,
                                            "商品详情页" + if (commodityClassifyId == 0) "(整车)" else "(优品)",
                                            "优惠弹窗"
                                        )
                                    }
                                    CommodityDetailActivity.startActivity(
                                        context,
                                        it.tag as Long,
                                        SourceModel(
                                            SourceModel.POSITION_TYPE.COMMODITY_COUPON_DIALOG_TYPE,
                                            SourceModel.POSITION_TYPE.COMMODITY_COUPON_DIALOG_TYPE_VALUE
                                        )
                                    )
                                }
                            }
                            tv.textSize = 12f
                            tv.setTextColor(tv.resources.getColor(R.color.color_1D1E23))
                            tv.text =
                                extCommodity.commodityName + "*" + extCommodity.quantity
                            ll_content?.addView(tv)
                        }
                    }
                }
            }

        }
    }

    inner class CouponViewHolder(itemView: View?) : BaseRecyclerViewHolder<Any?>(itemView) {
        var mCouponItemView: CommodityCouponItemView? = null
        override fun initItemView(itemView: View) {
            super.initItemView(itemView)
            mCouponItemView = itemView.findViewById(R.id.coupon_item_view)
        }

        override fun bindTo(coupon: Any?, position: Int) {
            super.bindTo(coupon, position)
            if (coupon is Coupon) {
                if (openFromCart) {
                    mCouponItemView?.couponGetEventName = "点击领取(" + coupon.appCouponName + ")优惠券"
                    mCouponItemView?.eventPage = "购物车"
                    mCouponItemView?.eventPosition = "优惠券弹窗"
                } else {
//                    点击(商品ID)(商品名称)(优惠券ID)(优惠券名称)领取优惠
                    mCouponItemView?.couponGetEventName =
                        "点击($mCommodityIds)($mCommodityNames)(" + coupon.couponId + ")(" + coupon.channelName + ")领取优惠"
                    mCouponItemView?.eventPage =
                        "商品详情页" + if (commodityClassifyId == 0) "(整车)" else "(优品)"
                    mCouponItemView?.eventPosition = "优惠弹窗"
                }
                mCouponItemView?.setData(coupon, position)
            }
        }
    }

    override fun onShow(dialog: DialogInterface?) {
        requestData()
    }

    /**
     * 请求数据
     */
    private fun requestData() {
        L00bangRequestManager2
            .getServiceInstance()
            .getCouponList(2, mCommodityIds.toString())
            .compose(L00bangRequestManager2.setSchedulers<List<Coupon>>())
            .subscribe(object : ProgressSubscriber<List<Coupon>>(mContext) {
                override fun onSuccess(coupons: List<Coupon>) {
                    super.onSuccess(coupons)
                    mList?.clear()
                    mActivityInfoList?.apply {
                        if (size > 0) {
                            mList?.add(mContext.getString(R.string.commodity_sales_promotion))
                            mList?.addAll(this)
                        }
                    }
                    if (coupons.isNotEmpty()) {
                        if (!openFromCart) {
                            mList?.add(mContext.getString(R.string.commodity_can_get_coupon))
                            mList?.add(
                                CommonModel.Divider(
                                    DensityUtil.dip2px(getContext(), 20f),
                                    -1
                                )
                            )
                        }
                        mList?.addAll(coupons)
                    }
                    recyclerView?.adapter?.notifyDataSetChanged()
                }
            })
    }
}