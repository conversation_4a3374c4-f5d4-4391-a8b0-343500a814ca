package com.cloudy.linglingbang.activity.car.energy.ble;

/**
 * <AUTHOR>
 * @describe
 * @date 2021/12/28
 */
public class GetBleKeyEntity {

    /**
     * 蓝⽛钥匙ID，（10进制字符串）
     */
    private String keyId;

    /**
     * 蓝⽛钥匙类型，(字符串，owner、friends)
     */
    private String keyType;

    /**
     * 蓝⽛钥匙主密钥随机数,(16进制字符串)
     */
    private String keyMasterRandom;

    /**
     * 蓝⽛钥匙主密钥，（16进制字符串）
     */
    private String masterKey;

    /**
     * 蓝⽛MAC地址，（冒号隔开的，16进制字符串）
     */
    private String bleMac;

    /**
     * 蓝⽛钥匙失效时间，（时间格式：yyyy-MM-ddHH:mm:ss）当keyType为owner时，该值为null
     */
    private String endTime;

    /**
     * ⽤户ID（字符串）
     */
    private String userId;

    /**
     * ⻋辆VIN码（字符串）
     */
    private String vin;

    /**
     * 当前服务状态值
     */
    private String serviceStatusValue;

    /**
     * 数据采集时间
     */
    private String collectTime;

    public String getKeyId() {
        return keyId;
    }

    public void setKeyId(String keyId) {
        this.keyId = keyId;
    }

    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public String getKeyMasterRandom() {
        return keyMasterRandom;
    }

    public void setKeyMasterRandom(String keyMasterRandom) {
        this.keyMasterRandom = keyMasterRandom;
    }

    public String getMasterKey() {
        return masterKey;
    }

    public void setMasterKey(String masterKey) {
        this.masterKey = masterKey;
    }

    public String getBleMac() {
        return bleMac;
    }

    public void setBleMac(String bleMac) {
        this.bleMac = bleMac;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getServiceStatusValue() {
        return serviceStatusValue;
    }

    public void setServiceStatusValue(String serviceStatusValue) {
        this.serviceStatusValue = serviceStatusValue;
    }

    public String getCollectTime() {
        return collectTime;
    }

    public void setCollectTime(String collectTime) {
        this.collectTime = collectTime;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("GetBleKeyEntity{");
        sb.append("keyId='").append(keyId).append('\'');
        sb.append(", keyType='").append(keyType).append('\'');
        sb.append(", keyMasterRandom='").append(keyMasterRandom).append('\'');
        sb.append(", masterKey='").append(masterKey).append('\'');
        sb.append(", bleMac='").append(bleMac).append('\'');
        sb.append(", endTime='").append(endTime).append('\'');
        sb.append(", userId='").append(userId).append('\'');
        sb.append(", vin='").append(vin).append('\'');
        sb.append(", serviceStatusValue='").append(serviceStatusValue).append('\'');
        sb.append(", collectTime='").append(collectTime).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
