package com.cloudy.linglingbang.activity.fragment.store.youpin;

import com.cloudy.linglingbang.activity.fragment.store.home.model.StoreHomeElementEnum;
import com.cloudy.linglingbang.activity.fragment.store.home.model.StoreHomeElementWrapper;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.store.home.StoreElementCommodity;
import com.cloudy.linglingbang.model.store.home.StoreLayoutElement;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.rxjava3.functions.Function;

/**
 * 优品元素转换
 *
 * <AUTHOR>
 * @date 2020/5/11
 */
public class ElementUtils {

    /**
     * 元素类型转换
     */
    public static Function<BaseResponse<List<StoreElementCommodity>>, BaseResponse<List<Object>>> getStoreElementToObjectFun(final int linkType, final long layoutComponentId) {
        return new Function<BaseResponse<List<StoreElementCommodity>>, BaseResponse<List<Object>>>() {
            @Override
            public BaseResponse<List<Object>> apply(BaseResponse<List<StoreElementCommodity>> listBaseResponse) {
                List<Object> result;
                List<StoreElementCommodity> data = listBaseResponse.getData();
                if (data != null && data.size() > 0) {
                    result = new ArrayList<>();
                    for (StoreElementCommodity commodity : data) {
                        StoreLayoutElement layoutElement = new StoreLayoutElement();
                        //赋值商品与类型
                        layoutElement.setProductVo(commodity);
                        layoutElement.setShowModule(StoreHomeElementEnum.COMMODITY_1_2.getType());
                        layoutElement.setLinkType(linkType);
                        layoutElement.setImage(commodity.getProductMainImage());
                        if (linkType > 0) {
                            layoutElement.setLinkUrl(String.valueOf(commodity.getProductIdOrZero()));
                        }
                        StoreHomeElementWrapper wrapper = new StoreHomeElementWrapper(layoutElement);
                        wrapper.setLayoutComponentId(layoutComponentId);
                        result.add(wrapper);
                    }
                } else {
                    result = new ArrayList<>(0);
                }
                return listBaseResponse.cloneWithData(result);
            }
        };
    }

    /**
     * 元素类型转换
     */
    public static Function<BaseResponse<List<StoreElementCommodity>>, BaseResponse<List<Object>>> getStoreElementToObject(final int linkType, final long layoutComponentId) {
        return new Function<BaseResponse<List<StoreElementCommodity>>, BaseResponse<List<Object>>>() {
            @Override
            public BaseResponse<List<Object>> apply(BaseResponse<List<StoreElementCommodity>> listBaseResponse) {
                List<Object> result;
                List<StoreElementCommodity> data = listBaseResponse.getData();
                if (data != null && data.size() > 0) {
                    result = new ArrayList<>();
                    for (StoreElementCommodity commodity : data) {
                        StoreLayoutElement layoutElement = new StoreLayoutElement();
                        //赋值商品与类型
                        layoutElement.setProductVo(commodity);
                        //layoutElement.setShowModule(StoreHomeElementEnum.COMMODITY_1_2.getType());
                        layoutElement.setLinkType(linkType);
                        layoutElement.setImage(commodity.getProductMainImage());
                        if (linkType > 0) {
                            layoutElement.setLinkUrl(String.valueOf(commodity.getProductIdOrZero()));
                        }
                        StoreHomeElementWrapper wrapper = new StoreHomeElementWrapper(layoutElement);
                        wrapper.setLayoutComponentId(layoutComponentId);
                        result.add(wrapper);
                    }
                } else {
                    result = new ArrayList<>(0);
                }
                return listBaseResponse.cloneWithData(result);
            }
        };
    }

    /**
     * 根据类型转换元素
     */
    public static Function<BaseResponse<List<StoreElementCommodity>>, BaseResponse<List<Object>>> getStoreElementToObjectFun(final int linkType, final long layoutComponentId, int type) {
        return new Function<BaseResponse<List<StoreElementCommodity>>, BaseResponse<List<Object>>>() {
            @Override
            public BaseResponse<List<Object>> apply(BaseResponse<List<StoreElementCommodity>> listBaseResponse) {
                List<Object> result;
                List<StoreElementCommodity> data = listBaseResponse.getData();
                if (data != null && data.size() > 0) {
                    result = new ArrayList<>();
                    for (StoreElementCommodity commodity : data) {
                        StoreLayoutElement layoutElement = new StoreLayoutElement();
                        //赋值商品与类型
                        layoutElement.setProductVo(commodity);
                        layoutElement.setShowModule(type);
                        layoutElement.setLinkType(linkType);
                        layoutElement.setImage(commodity.getProductMainImage());
                        if (linkType > 0) {
                            layoutElement.setLinkUrl(String.valueOf(commodity.getProductIdOrZero()));
                        }
                        StoreHomeElementWrapper wrapper = new StoreHomeElementWrapper(layoutElement);
                        wrapper.setLayoutComponentId(layoutComponentId);
                        result.add(wrapper);
                    }
                } else {
                    result = new ArrayList<>(0);
                }
                return listBaseResponse.cloneWithData(result);
            }
        };
    }
}
