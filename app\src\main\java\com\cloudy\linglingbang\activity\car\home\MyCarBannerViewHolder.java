package com.cloudy.linglingbang.activity.car.home;

import android.content.Context;
import android.view.View;
import android.widget.Button;
import android.widget.RelativeLayout;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.NotchScreenUtils;
import com.cloudy.linglingbang.app.util.StatusBarUtils;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.banner.BannerView;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.server.Ad.Ad2;
import com.cloudy.linglingbang.model.server.Ad.AdJumpUtil2;
import com.cloudy.linglingbang.model.server.Ad.AdRequestUtil2;

import static com.cloudy.linglingbang.app.util.JumpPageUtil.BUTTON_TYPE.APPOINTMENT;
import static com.cloudy.linglingbang.app.util.JumpPageUtil.BUTTON_TYPE.ASK_PRICE;

/**
 * 我的爱车广告
 *
 * <AUTHOR>
 * @date 2020-02-17
 */
public class MyCarBannerViewHolder extends BaseRecyclerViewHolder<MyCarModel.AdListTop> {

    private BannerView mBannerView;

    /**
     * 立即预定
     */
    private Button mBtnOrderNow;

    /**
     * 询价
     */
    private Button mBtnAskPrice;

    /**
     * 预约试驾
     */
    private Button mBtnAppointment;

    private RelativeLayout mRlWeather;

    private Context mContext;

    /**
     * 持有以统一天气
     */
    private OilPriceLoader mOilPriceLoader;

    private boolean isFirstRequestWeather = true;

    private OnCityChangeListener mOnCityChangeListener;

    public MyCarBannerViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        mContext = itemView.getContext();
        mBannerView = itemView.findViewById(R.id.banner_view);
        mBtnOrderNow = itemView.findViewById(R.id.btn_order_now);
        mBtnAskPrice = itemView.findViewById(R.id.btn_ask_price);
        mBtnAppointment = itemView.findViewById(R.id.btn_appointment);
        mRlWeather = itemView.findViewById(R.id.rl_weather);
        //初始化天气信息
        mOilPriceLoader = new OilPriceLoader(itemView) {
            @Override
            protected void onUpdateCity(Context context, long cityId, String cityName) {
                super.onUpdateCity(context, cityId, cityName);
                if (mOnCityChangeListener != null) {
                    mOnCityChangeListener.onCityChange(cityId, cityName);
                }
            }
        };
    }

    @Override
    public void bindTo(final MyCarModel.AdListTop adListTop, int position) {
        super.bindTo(adListTop, position);

        int top = Math.max(NotchScreenUtils.getNotchSafeWH()[1], StatusBarUtils.getStatusBarHeight(mContext));
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(mRlWeather.getLayoutParams());
        lp.setMargins(itemView.getResources().getDimensionPixelSize(R.dimen.normal_40), top, itemView.getResources().getDimensionPixelSize(R.dimen.normal_40), 0);
        mRlWeather.setLayoutParams(lp);

        AdRequestUtil2.bindBannerView(mBannerView, adListTop.getAd2s());
        //展示天气
        if (mOilPriceLoader != null) {
            //第一次请求下天气
            if (isFirstRequestWeather) {
                isFirstRequestWeather = false;
                mOilPriceLoader.refreshOil(mContext);
            }
            //重置是需要刷新，其他时候不需要
            if (adListTop.isNeedRefresh()) {
                mOilPriceLoader.refreshOil(mContext);
            } else {
                mOilPriceLoader.onUpdateOilPrice();
            }
        }

        //立即预定
        mBtnOrderNow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                SensorsUtils.sensorsClickBtn("立即订购", "车-潜客");
                //如果是车款，则跳转到相应的车款
                Ad2 ad2 = isEffectiveAd(adListTop, mBannerView.getCurrentIndex());
                if (ad2 != null) {
                    AdJumpUtil2.goToActivity(mContext, ad2);
                }
            }
        });

        //询价
        mBtnAskPrice.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                SensorsUtils.sensorsClickBtn("询价", "车-潜客");
                Ad2 ad2 = isEffectiveAd(adListTop, mBannerView.getCurrentIndex());
                if (ad2 != null) {
                    Long carTypeId = getCarTypeId(ad2);
                    JumpPageUtil.goAskPriceAndAppointment(mContext, ASK_PRICE, null, carTypeId, null);
                }
            }
        });

        //预约试驾
        mBtnAppointment.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                SensorsUtils.sensorsClickBtn("预约试驾", "车-潜客");
                Ad2 ad2 = isEffectiveAd(adListTop, mBannerView.getCurrentIndex());
                if (ad2 != null) {
                    Long carTypeId = getCarTypeId(ad2);
                    JumpPageUtil.goAskPriceAndAppointment(mContext, APPOINTMENT, null, carTypeId, null);
                }
            }
        });

    }

    /**
     * 是否是有效的广告（该处广告只支持车型，如果不是跳转到车型，则广告无效，返回null）
     */
    public Ad2 isEffectiveAd(MyCarModel.AdListTop adListTop, int index) {
        Ad2 ad2 = adListTop.getAd2s().get(index);
        if (ad2 != null && ad2.getLinkType() == Ad2.LINK_TYPE.CAR_STYLE) {
            return ad2;
        }
        ToastUtil.showMessage(mContext, "车款配置不正确");
        return null;
    }

    /**
     * 从广告中获取carTypeId
     */
    public Long getCarTypeId(Ad2 ad2) {
        String link = ad2.getLinkUrl();
        Long carTypeId = null;
        if (link.contains(",")) {
            String[] params = link.split(",");
            if (params.length > 0) {
                try {
                    carTypeId = Long.parseLong(params[0]);
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                }
            }
//            if (params.length > 1) {
//                try {
//                    shopId = Long.parseLong(params[1]);
//                } catch (NumberFormatException e) {
//                    e.printStackTrace();
//                }
//            }
        } else {
            //不包含，只有 carTypeId
            try {
                carTypeId = Long.parseLong(link);
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
        }
        return carTypeId;
    }

    /**
     * 获取广告轮播图
     */
    public BannerView getBannerView() {
        return mBannerView;
    }

    public void refreshOil() {
        if (mOilPriceLoader != null) {
            mOilPriceLoader.refreshOil(mContext);
        }
    }

    public void setOnCityChangeListener(OnCityChangeListener onCityChangeListener) {
        mOnCityChangeListener = onCityChangeListener;
    }

    public OilPriceLoader getOilLoader() {
        return mOilPriceLoader;
    }
}
