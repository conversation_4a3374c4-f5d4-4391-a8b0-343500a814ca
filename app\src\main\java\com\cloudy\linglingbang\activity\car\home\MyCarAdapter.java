package com.cloudy.linglingbang.activity.car.home;

import android.content.Context;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.widget.banner.BannerView;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.app.widget.recycler.wrapper.ViewHolderType;
import com.cloudy.linglingbang.app.widget.recycler.wrapper.WrapperItemAdapter;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;

/**
 * 爱车（潜客）的adapter
 *
 * <AUTHOR>
 * @date 2020-02-17
 */
public class MyCarAdapter extends WrapperItemAdapter {

    private MyCarBannerViewHolder mMyCarBannerViewHolder;

    private MyCarCarListViewHolder mMyCarCarListViewHolder;

    private OnCityChangeListener mOnCityChangeListener;

    private Fragment mFragment;

    private BannerView mBannerView;

    private OilPriceLoader mOilPriceLoader;

    /**
     * 添加保客刷新标志位，防止调用刷新时，还没有初始化
     */
    private boolean mIsNeedRefreshOilForRetentive;

    /**
     * 添加潜客刷新标志位，防止调用刷新时，还没有初始化
     */
    private boolean mIsNeedRefreshOilForNormal;

    public MyCarAdapter(Context context, List data, OnCityChangeListener onCityChangeListener, Fragment fragment) {
        super(context, data);
        mOnCityChangeListener = onCityChangeListener;
        this.mFragment = fragment;

    }

    @Override
    protected void addViewHolderType(List<ViewHolderType> viewHolderTypeList) {
        super.addViewHolderType(viewHolderTypeList);
        viewHolderTypeList.add(new ViewHolderType(MyCarBannerViewHolder.class, R.layout.item_my_car_banner));
        viewHolderTypeList.add(new ViewHolderType(MyCarRecommendViewHolder.class, R.layout.item_my_car_recommend));
        viewHolderTypeList.add(new ViewHolderType(MyCarFunctionWithTitleViewHolder.class, R.layout.item_my_car_function_recycler_view));
        viewHolderTypeList.add(new ViewHolderType(MyCarDealerViewHolder.class, R.layout.item_my_car_dealer_view));
        viewHolderTypeList.add(new ViewHolderType(MyCarColumnTitleViewHolder.class, R.layout.item_car_column_title));
        viewHolderTypeList.add(new ViewHolderType(MyCarColumnVerticalViewHolder.class, R.layout.item_today_column_post));
        viewHolderTypeList.add(new ViewHolderType(MyCarColumnHorizontalViewHolder.class, R.layout.item_recycler_view));
        viewHolderTypeList.add(new ViewHolderType(MyCarWelfareViewHolder.class, R.layout.item_my_car_welfare));
        viewHolderTypeList.add(new ViewHolderType(MyCarSeeCarOnlineViewHolder.class, R.layout.item_my_car_see_car));
        viewHolderTypeList.add(new ViewHolderType(MyCarCarListViewHolder.class, R.layout.item_my_car_list));
    }

    @Override
    protected BaseRecyclerViewHolder createViewHolderWithViewType(View itemView, int viewType) {
        BaseRecyclerViewHolder holder = super.createViewHolderWithViewType(itemView, viewType);
        if (holder instanceof MyCarBannerViewHolder) {
            setCarBannerViews((MyCarBannerViewHolder) holder);
        } else if (holder instanceof MyCarCarListViewHolder) {
            ((MyCarCarListViewHolder) holder).setFragment(mFragment);
            setMyCarCarListViews((MyCarCarListViewHolder) holder);
        }
        return holder;
    }

    /**
     * 设置潜客的banner以及油价类
     */
    private void setCarBannerViews(MyCarBannerViewHolder holder) {
        mMyCarBannerViewHolder = holder;
        holder.setOnCityChangeListener(mOnCityChangeListener);
        mBannerView = holder.getBannerView();
        mOilPriceLoader = holder.getOilLoader();
        if (mIsNeedRefreshOilForNormal) {
            mOilPriceLoader.refreshOil(mContext);
            mIsNeedRefreshOilForNormal = false;
        }
    }

    /**
     * 设置保客的banner以及油价类
     */
    private void setMyCarCarListViews(MyCarCarListViewHolder holder) {
        this.mMyCarCarListViewHolder = holder;
        holder.setOnCityChangeListener(mOnCityChangeListener);
        mOilPriceLoader = holder.getOilLoader();
        if (mIsNeedRefreshOilForRetentive) {
            mOilPriceLoader.refreshOil(mContext);
            mIsNeedRefreshOilForRetentive = false;
        }
    }

    @Override
    public void onViewAttachedToWindow(@NonNull BaseRecyclerViewHolder holder) {
        super.onViewAttachedToWindow(holder);
        //界面划入时，轮播图开始滚动，油价开始滚动
        if (holder instanceof MyCarBannerViewHolder || holder instanceof MyCarCarListViewHolder) {
            if (getBannerView() != null) {
                getBannerView().changeAdTurning(true);
            }
            if (getOilPriceLoader() != null) {
                getOilPriceLoader().startScroll();
            }
        }
    }

    @Override
    public void onViewDetachedFromWindow(@NonNull BaseRecyclerViewHolder holder) {
        super.onViewDetachedFromWindow(holder);
        //界面划入时，轮播图停止滚动，油价停止滚动
        if (holder instanceof MyCarBannerViewHolder || holder instanceof MyCarCarListViewHolder) {
            if (getBannerView() != null) {
                getBannerView().changeAdTurning(false);
            }
            if (getOilPriceLoader() != null) {
                getOilPriceLoader().stopScroll();
            }
        }
    }



    /**
     * 刷新天气
     */
    public void refreshOilPrice() {
        if (mMyCarBannerViewHolder != null) {
            mMyCarBannerViewHolder.refreshOil();
        }
        if (mMyCarCarListViewHolder != null) {
            mMyCarCarListViewHolder.refreshOil();
        }
    }

    public void refreshOilPriceForNormal() {
        if (mMyCarBannerViewHolder != null) {
            mMyCarBannerViewHolder.refreshOil();
        } else {
            mIsNeedRefreshOilForNormal = true;
        }
    }

    public void refreshOilPriceForRetentive() {
        if (mMyCarCarListViewHolder != null) {
            mMyCarCarListViewHolder.refreshOil();
        } else {
            mIsNeedRefreshOilForRetentive = true;
        }
    }

    /**
     * 获取广告banner
     */
    public BannerView getBannerView() {
        return mBannerView;
    }

    /**
     * 获取添加加载类
     */
    public OilPriceLoader getOilPriceLoader() {
        return mOilPriceLoader;
    }


    public void stopOilScroll() {
        if (mOilPriceLoader != null) {
            mOilPriceLoader.stopScroll();
        }
    }

    public void startOilScroll() {
        if (mOilPriceLoader != null) {
            mOilPriceLoader.startScroll();
        }
    }

    public void resetCity() {
        if (mMyCarBannerViewHolder != null && mMyCarBannerViewHolder.getOilLoader() != null) {
            mMyCarBannerViewHolder.getOilLoader().resetCity();
        }
        if (mMyCarCarListViewHolder != null && mMyCarCarListViewHolder.getOilLoader() != null) {
            mMyCarCarListViewHolder.getOilLoader().resetCity();
        }

    }

    public void updateFragment() {
        if (mMyCarCarListViewHolder != null) {
            mMyCarCarListViewHolder.updateFragment();
        }
    }

    public MyCarBannerViewHolder getCarBannerViewHolder() {
        return mMyCarBannerViewHolder;
    }

    public MyCarCarListViewHolder getMyCarCarListViewHolder() {
        return mMyCarCarListViewHolder;
    }
}
