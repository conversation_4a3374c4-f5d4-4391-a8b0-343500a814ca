package com.cloudy.linglingbang.activity.task.home.widget;

import com.cloudy.linglingbang.BaseInstrumentedTest;
import com.cloudy.linglingbang.ReflectUtils;
import com.cloudy.linglingbang.model.task.TaskInfo;
import com.cloudy.linglingbang.model.task.TaskReminderListInfo;
import com.cloudy.linglingbang.model.task.TaskReward;

import java.util.ArrayList;
import java.util.List;

/**
 * 任务提醒弹窗
 *
 * <AUTHOR>
 * @date 2019/8/23
 */
public class TaskReminderDialogTest extends BaseInstrumentedTest {
    @Override
    public void test() {
        super.test();
        new TaskReminderDialog(getContext(), createTaskReminderListInfo()).show();
    }

    private TaskReminderListInfo createTaskReminderListInfo() {
        TaskReminderListInfo taskReminderListInfo = new TaskReminderListInfo();
        taskReminderListInfo.setGold(999);

        List<TaskInfo> taskInfoList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            TaskInfo taskInfo = new TaskInfo();
            ReflectUtils.setFieldValue(taskInfo, "taskName", "任务" + i);
            ReflectUtils.setFieldValue(taskInfo, "frequency", i + 1);
            ReflectUtils.setFieldValue(taskInfo, "completeStatus", i & 1);

            TaskReward taskReward = new TaskReward();
            ReflectUtils.setFieldValue(taskReward, "rewardValue", i);
            List<TaskReward> taskRewards = new ArrayList<>();
            taskRewards.add(taskReward);
            ReflectUtils.setFieldValue(taskInfo, "taskRewards", taskRewards);

            taskInfoList.add(taskInfo);
        }
        taskReminderListInfo.setTaskUserVoList(taskInfoList);
        return taskReminderListInfo;
    }
}