package com.cloudy.linglingbang.activity.store.commodity.adapter.holder

import android.view.View
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import butterknife.BindView
import butterknife.ButterKnife
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.store.commodity.ReplacementListActivity
import com.cloudy.linglingbang.activity.store.commodity.adapter.ShoppingCartSingleCommodityAdapter
import com.cloudy.linglingbang.app.util.AppUtil
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils
import com.cloudy.linglingbang.app.widget.SlidingButtonView
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder
import com.cloudy.linglingbang.app.widget.recycler.EmptySupportedRecyclerView
import com.cloudy.linglingbang.model.store.commodity.CartInfo

/**
 * 购物车整体的viewHolder
 *
 * <AUTHOR>
 * @date 2022/10/12
 */
class ShoppingCartViewHolder(itemView: View?) : BaseRecyclerViewHolder<CartInfo>(itemView) {

    /**
     * 商品列表
     */
    @JvmField
    @BindView(R.id.recycler_view_cart_commodity)
    var mRecyclerViewCartCommodity: EmptySupportedRecyclerView? = null

    @JvmField
    @BindView(R.id.tv_promotion_show_name)
    var tvPromotionShowName: TextView? = null

    @JvmField
    @BindView(R.id.tv_activity_type)
    var tvActivityType: TextView? = null

    @JvmField
    @BindView(R.id.rl_activity)
    var rlActivity: RelativeLayout? = null

    @JvmField
    @BindView(R.id.tv_into_list)
    var tvIntoList: TextView? = null


    /**
     * 刷新接口的监听
     */
    var needRefreshListener: (Int) -> Unit = {}

    var mOnChangeCommodityListener: OnChangeCommodityListener? = null


    override fun initItemView(itemView: View) {
        super.initItemView(itemView)
        ButterKnife.bind(this, itemView)
    }


    override fun bindTo(bean: CartInfo, position: Int) {
        super.bindTo(bean, position)
        if (bean.totalAmountActivity != null) {
            rlActivity?.visibility = View.VISIBLE
            tvActivityType?.text = bean.totalAmountActivity.promotionStrategyTypeName
            tvPromotionShowName?.text = bean.totalAmountActivity.promotionActivityTips
            tvIntoList?.text = if (bean.totalAmountActivity.isApply) "再逛逛" else "去凑单"
            tvIntoList?.setOnClickListener {
                SensorsUtils.sensorsClickBtn("点击去凑单逛逛", "购物车", "去凑单逛逛按钮")
                if (AppUtil.checkLogin(itemView.context))
                    ReplacementListActivity.startActivity(
                        itemView.context,
                        bean.totalAmountActivity.promotionActivityId.toLong()
                    )
            }
        } else {
            rlActivity?.visibility = View.GONE
        }
        bean.commodityList?.let {
            mRecyclerViewCartCommodity?.layoutManager =
                LinearLayoutManager(mRecyclerViewCartCommodity?.context)
            var adapter = ShoppingCartSingleCommodityAdapter(
                mRecyclerViewCartCommodity?.context,
                it
            )
            adapter.setOnNeedRefreshListener {
                needRefreshListener(it)
            }
            adapter.mOnChangeCommodityListener = object : OnChangeCommodityListener {
                override fun onChangeSingleSelect(cartId: Long, isSelect: Int) {
                    mOnChangeCommodityListener?.onChangeSingleSelect(cartId, isSelect)
                }

                override fun onChangeSelect(cartIds: MutableList<Long>, isSelect: Int) {
                    mOnChangeCommodityListener?.onChangeSelect(cartIds, isSelect)
                }

                override fun onDeleteSingle(cartId: Long) {
                    mOnChangeCommodityListener?.onDeleteSingle(cartId)
                }

                override fun onDeleteSingle(cartIds: MutableList<Long>, tips: String) {
                    mOnChangeCommodityListener?.onDeleteSingle(cartIds, tips)
                }

                override fun changeNumToCart(cartId: Long, count: Long) {
                    mOnChangeCommodityListener?.changeNumToCart(cartId, count)
                }

                override fun onMenuIsOpen(view: View?) {
                    mOnChangeCommodityListener?.onMenuIsOpen(view)
                }

                override fun onDownOrMove(slidingButtonView: SlidingButtonView?) {
                    mOnChangeCommodityListener?.onDownOrMove(slidingButtonView)
                }
            }
            mRecyclerViewCartCommodity?.adapter = adapter
            (mRecyclerViewCartCommodity?.adapter as RecyclerView.Adapter).notifyDataSetChanged()
        }

    }


    fun setOnNeedRefreshListener(e: (Int) -> Unit) {
        this.needRefreshListener = e
    }

}