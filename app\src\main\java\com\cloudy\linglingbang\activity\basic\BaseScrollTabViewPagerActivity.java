package com.cloudy.linglingbang.activity.basic;

import android.content.Intent;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.task.util.TaskNavigateUtils;
import com.cloudy.linglingbang.app.widget.PagerSlidingTabStrip;

import java.util.List;

import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

/**
 * 标签可滑动的viewPagerFragment
 * Created by hanfei on 2016/12/8.
 */
public abstract class BaseScrollTabViewPagerActivity<T> extends BaseActivity {

    protected ViewPager mViewPager;
    protected PagerSlidingTabStrip tabs;

    /**
     * 数据，可以是fragment,也可以是view
     */
    protected List<T> mData;
    /**
     * adapter
     */
    protected PagerAdapter mAdapter;

    protected String[] titles;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.fragment_base_scroll_tab_view_pager);
    }

    @Override
    protected void initialize() {
        mViewPager = findViewById(R.id.view_pager);
        tabs = findViewById(R.id.tabs);
        mData = createAdapterData();
        titles = getTitles();
        mAdapter = createViewPagerAdapter(mData, titles);
        mViewPager.setAdapter(mAdapter);
        tabs.setViewPager(mViewPager);
        // 设置Tab是自动填充满屏幕的
        tabs.setShouldExpand(false);
        tabs.setOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                //可以在滑动时就加载实际布局，或者是提供占位图，等展示出来再加载实际布局
                if (positionOffset > 0) {
                    if (position + 1 < mData.size()) {
                        T t = mData.get(position + 1);
                        if (t instanceof LazyFragment) {
                            ((LazyFragment) t).loadRealView();
                        }
                    }
                }
            }

            @Override
            public void onPageSelected(int position) {
                onItemSelected(position);
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
        checkTaskNavigate(getIntent());
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        checkTaskNavigate(intent);
    }

    protected void setUnreadBadgeItem(int position) {
        tabs.setUnreadBadgeItem(position);
    }

    protected void setTabBadgeWithImage(int position, int drawableRes) {
        tabs.showBadgeImage(position, drawableRes);
    }

    protected void onItemSelected(int position) {}

    /**
     * 创建viewPager的adapter
     */
    protected abstract PagerAdapter createViewPagerAdapter(List<T> data, String[] titles);

    /**
     * 创建adapter的数据
     */
    protected abstract List<T> createAdapterData();

    /**
     * 创建每个fragment的标题
     */
    protected abstract String[] getTitles();

    /**
     * 获取当前展示的 item ，如当前 Fragment
     */
    public T getCurrentItem() {
        if (mViewPager != null && mData != null) {
            int currentItem = mViewPager.getCurrentItem();
            if (currentItem >= 0 && currentItem < mData.size()) {
                return mData.get(currentItem);
            }
        }
        return null;
    }

    @Override
    protected void onStop() {
        super.onStop();
        dispatchVisibilityChange(false);
    }

    @Override
    protected void onStart() {
        super.onStart();
        dispatchVisibilityChange(true);
    }

    /**
     * 分发给 child
     */
    private void dispatchVisibilityChange(boolean visible) {
        T currentItem = getCurrentItem();
        if (currentItem instanceof BaseFragment) {
            ((BaseFragment) currentItem).onParentVisibilityChanged(visible);
        }
    }

    public void checkTaskNavigate(Intent intent) {
        if (mData != null) {
            if (intent == null || mViewPager == null) {
                return;
            }
            int index = TaskNavigateUtils.getFirstLevelIndex(intent);
            if (index >= 0 && index < mData.size()) {
                mViewPager.setCurrentItem(index);
                //因为直接从 viewPager 调用，此时回调到 setUserVisibleHint ，而 onCreate 还未执行
                Object o = mData.get(index);
                if (o instanceof LazyFragment) {
                    ((LazyFragment) o).loadRealView();
                }
            }
        }
    }
}
