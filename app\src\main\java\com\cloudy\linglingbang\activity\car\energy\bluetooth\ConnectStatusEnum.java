package com.cloudy.linglingbang.activity.car.energy.bluetooth;

/**
 * 蓝牙连接状态枚举
 */
public enum ConnectStatusEnum {

    NO_CONNECT(0, "未连接"),
    SUCCESS(1, "已连接(连接并鉴权成功)"),
    CONNECTING(2, "连接中"),
    AUTH(3, "鉴权中");

    ConnectStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    private final int status;
    private final String desc;

    public int getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

}
