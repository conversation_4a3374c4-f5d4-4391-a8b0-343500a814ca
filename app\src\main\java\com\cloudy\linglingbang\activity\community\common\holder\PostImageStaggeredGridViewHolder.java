package com.cloudy.linglingbang.activity.community.common.holder;

import android.app.Activity;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.imageConfig.RoundedCornersTransformation;
import com.cloudy.linglingbang.app.util.DensityUtil;
import com.cloudy.linglingbang.app.util.DeviceUtil;
import com.cloudy.linglingbang.app.widget.banner.AdRoundImageView;
import com.cloudy.linglingbang.app.widget.textview.PressEffectiveCompoundButton;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.postcard.PostCardItem;

/**
 * 帖子图片的viewHolder
 *
 * <AUTHOR>
 * @date 2019-11-27
 */
public class PostImageStaggeredGridViewHolder extends BasePostChildViewHolder {
    /**
     * 封面图
     */
    private AdRoundImageView mIvCover;

    /**
     * 车型
     */
    private PressEffectiveCompoundButton mBtnCarType;

    /**
     * 视频标志
     */
    private ImageView mIvVideoMark;

    /**
     * 跟布局
     */
    private RelativeLayout mRlImage;

    public PostImageStaggeredGridViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        mIvCover = itemView.findViewById(R.id.iv_cover);
        mBtnCarType = itemView.findViewById(R.id.btn_car_type);
        mRlImage = itemView.findViewById(R.id.rl_image);
        mIvVideoMark = itemView.findViewById(R.id.iv_video_mark);
    }

    @Override
    public void bindTo(PostCard postCard) {
        super.bindTo(postCard);
        //如果没有封面，并且帖子内没有图片，则隐藏图片显示，否则显示（先判断是因为帖子内图片要获取图片尺寸，只能用imgTexts里的字段，循环获取。但是这里判断只需要判断images有无即可，所以先判断再在里面赋值）
        String[] images = postCard.getImages();
        if ((postCard.getCoverImageText() == null || TextUtils.isEmpty(postCard.getCoverImageText().getImg())) && (images == null || images.length == 0)) {
            mRlImage.setVisibility(View.GONE);
        } else {
            mRlImage.setVisibility(View.VISIBLE);
            PostCardItem singlePostCardItem = null;
            //如果有封面，获取封面
            if (postCard.getCoverImageText() != null && !TextUtils.isEmpty(postCard.getCoverImageText().getImg())) {
                singlePostCardItem = postCard.getCoverImageText();
            } else {
                for (PostCardItem postCardItem : postCard.getImgTexts()) {
                    if (!TextUtils.isEmpty(postCardItem.getImg())) {
                        singlePostCardItem = postCardItem;
                        break;
                    }
                }
            }
            if (singlePostCardItem != null) {
                int width = 0;
                int height = 0;
                //更新大小
                if (!TextUtils.isEmpty(singlePostCardItem.getWidth()) && !TextUtils.isEmpty(singlePostCardItem.getHeight())) {
                    try {
                        width = Integer.parseInt(singlePostCardItem.getWidth());
                        height = Integer.parseInt(singlePostCardItem.getHeight());
                    } catch (NumberFormatException e) {
                        e.printStackTrace();
                    }
                }
                if (width <= 0 || height <= 0) {
                    width = 345;
                    height = 455;
                } else if (height / width > 16 / 9) {
                    width = 9;
                    height = 16;
                }
                int showWidth = (DeviceUtil.getScreenWidth((Activity) mIvCover.getContext()) - DensityUtil.dip2px(mIvCover.getContext(), 30)) / 2;//获取实际展示的图片宽度
                int showHeight = (int) (showWidth * ((float) height / width));//获取最终图片高度
                ViewGroup.LayoutParams layoutParams = mIvCover.getLayoutParams();
                RelativeLayout.LayoutParams clLayoutParams = (RelativeLayout.LayoutParams) layoutParams;
                clLayoutParams.width = showWidth;//获取实际展示的图片宽度
                clLayoutParams.height = showHeight;//获取最终图片高度
                mIvCover.setLayoutParams(clLayoutParams);
                //加载图片
                mIvCover.createCornerImageLoad(singlePostCardItem.getImg(), RoundedCornersTransformation.CornerType.ALL)
                        .setOverrideSize(showWidth, showHeight)//如果不指定的话，图片会闪动，可能是因为还未正确确定图片大小
                        .load();
            }
            //根据帖子类型做特殊处理
            int postTypeId = Integer.parseInt(postCard.getPostTypeId());
            //先都隐藏
            mBtnCarType.setVisibility(View.GONE);
            mIvVideoMark.setVisibility(View.GONE);
            //如果是提车作业贴，展示标签
            if (postTypeId == PostCard.PostType.CAR_BUYING_EXPERIENCE) {
                if (postCard.getPostCarVo() != null && !TextUtils.isEmpty(postCard.getPostCarVo().getCarTypeName())) {
                    mBtnCarType.setVisibility(View.VISIBLE);
                    mBtnCarType.setText(postCard.getPostCarVo().getCarTypeName());
                }
            } else if (postTypeId == PostCard.PostType.SHORT_VIDEO || postTypeId == PostCard.PostType.VIDEO_POST) {
                //如果是视频帖展示视频标签
                mIvVideoMark.setVisibility(View.VISIBLE);
            }
        }
    }
}
