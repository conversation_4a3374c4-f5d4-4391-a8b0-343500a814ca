package com.cloudy.linglingbang.activity.fragment.store.service;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshFragment;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.store.commodity.CommodityDetailActivity;
import com.cloudy.linglingbang.adapter.store.service.ServiceStore4SAdapter;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.InputMethodUtils;
import com.cloudy.linglingbang.app.util.LocationHelper;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.ClearEditText;
import com.cloudy.linglingbang.app.widget.dialog.ChooseCityDialog;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.constants.FinalSensors;
import com.cloudy.linglingbang.model.CityModel;
import com.cloudy.linglingbang.model.ProvinceModel;
import com.cloudy.linglingbang.model.SourceModel;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.store.service.DealerCityParam;
import com.cloudy.linglingbang.model.store.service.Service4sCommodity;
import com.cloudy.linglingbang.model.store.service.ServiceStoreInfo;
import com.cloudy.linglingbang.model.user.User;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import butterknife.OnClick;
import io.reactivex.rxjava3.core.Observable;

/**
 * 4S店精选
 *
 * <AUTHOR>
 * @date 2019-11-13
 */
public class ServiceStore4SFragment extends BaseRecyclerViewRefreshFragment<ServiceStoreInfo> {

    private DealerCityParam dealerCityParam = new DealerCityParam();

    private Long mCityId = 0L;
    private String mCityName;
    //搜索返回后是否需要刷新数据
    private boolean isRefresh;
    private ChooseCityDialog.ChooseCityUtil mChooseCityUtil;
    @BindView(R.id.et_search_content)
    ClearEditText mEditText;

    @BindView(R.id.tv_search)
    TextView mTvSearch;

    /**
     * 地点
     */
    @BindView(R.id.tv_position)
    TextView mTvPosition;

    /**
     * 埋点信息，传给 adapter
     */
    private SensorsUtils.StoreHomeAnchor mStoreHomeAnchor;

    public static ServiceStore4SFragment newInstance() {
        ServiceStore4SFragment serviceStore4SFragment = new ServiceStore4SFragment();
        return serviceStore4SFragment;
    }

    @Override
    protected void initViews() {
        super.initViews();
        Bundle bundle = getArguments();
        if (bundle != null) {
            mStoreHomeAnchor = (SensorsUtils.StoreHomeAnchor) bundle.getSerializable(IntentUtils.INTENT_EXTRA_FROM);
        }
        getLocation();
        mTvPosition.setCompoundDrawables(null, null, AppUtil.getTintDrawable(mTvPosition.getCompoundDrawables()[2], getResources().getColor(R.color.color_384967)), null);
        mEditText.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                InputMethodUtils.hideInputMethod(v.getContext(), v);
                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                    refresh();
                }
                String content = mEditText.getText().toString();
                if (TextUtils.isEmpty(content)) {
                    mTvSearch.setVisibility(View.VISIBLE);
                    mEditText.setVisibility(View.INVISIBLE);
                }
                return false;
            }
        });
    }

    @OnClick(R.id.tv_position)
    void clickPosition() {
        onClickTvLocation();
    }

    /**
     * 选择位置
     */
    private void onClickTvLocation() {
        if (mChooseCityUtil == null) {
            mChooseCityUtil = new ChooseCityDialog.ChooseCityUtil(getActivity(), new ChooseCityDialog.OnChooseCityListener() {
                @Override
                public boolean onChoseCity(ProvinceModel chosenProvinceModel, CityModel chosenCityModel) {
                    mTvPosition.setText(chosenCityModel.getCityName());
                    refreshData(chosenCityModel);
                    return false;
                }
            });
        }
        mChooseCityUtil.showDialog();
    }

    private void getLocation() {
        if (mCityId == 0) {
            //没有 cityId 的时候，需要获取，如果选择了地址，则不再变化（如用户选择城市，然后退出登录）
            LocationHelper.LocationEntity lastLocation = LocationHelper.getInstance().getLastLocation();
            long cityId = 0;
            String cityName = "";
            if (lastLocation != null) {
                //有定位
                cityId = lastLocation.serverCityId;
                cityName = lastLocation.cityName;
            }
            if (TextUtils.isEmpty(cityName)) {
                //如果城市名为空，则取用户信息，如果不为空，则不管 cityId 是否为空，都可用
                User user = User.getsUserInstance();
                if (UserUtils.hasLogin(user)) {
                    //已登录
                    long userCityId = user.getCityId();
                    String userCityStr = user.getCityStr();
                    if (userCityId > 0 && !TextUtils.isEmpty(userCityStr)) {
                        //不为空，赋值
                        cityId = userCityId;
                        cityName = userCityStr;
                    }
                }
            }
            if (TextUtils.isEmpty(cityName)) {
                //城市名仍为空，取默认
                cityId = 0;
                cityName = "柳州";
            }
            //初始化的时候，直接加载天气，不回调
            //选择城市的时候，回调城市
            mCityId = cityId;
            mCityName = cityName;
            mTvPosition.setText(mCityName);
            dealerCityParam.setCityName(mCityName);
            dealerCityParam.setCityId(mCityId);
            refresh();
        }
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(final List<ServiceStoreInfo> list) {
        ServiceStore4SAdapter serviceStore4SAdapter = new ServiceStore4SAdapter(getActivity(), list);
        serviceStore4SAdapter.setOnItemClickListener(new BaseRecyclerViewHolder.OnItemClickListener() {
            @Override
            public void onItemClick(View itemView, int position) {
                ServiceStoreInfo ServiceStoreInfo = list.get(position);
                long productId = ServiceStoreInfo.getCommodityId();
                if (productId > 0) {
                    String componentName = ServiceStoreInfo.getCommodityName();
                    String commodityCategoryStr = ServiceStoreInfo.getCommodityCategoryStr();
                    SensorsUtils.sensorsClickComponent("点击(" + commodityCategoryStr + ")-(" + componentName + ")", componentName, mStoreHomeAnchor);
//                    JumpPageUtil.goEcologyCommodityDetail(getActivity(), productId, "4s店精选列表", new SourceModel(SourceModel.POSITION_TYPE.SHOP_4S_TYPE, SourceModel.POSITION_TYPE.SHOP_4S_VALUE));
                    CommodityDetailActivity.Companion.startActivity(getActivity(), productId, new SourceModel(SourceModel.POSITION_TYPE.SHOP_4S_TYPE, SourceModel.POSITION_TYPE.SHOP_4S_VALUE));
                }
            }
        });
        return serviceStore4SAdapter;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (dealerCityParam == null) {
            dealerCityParam = new DealerCityParam();
        }

    }

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_store_service_4s;
    }

    @Override
    public RefreshController<ServiceStoreInfo> createRefreshController() {
        return new RefreshController<ServiceStoreInfo>(ServiceStore4SFragment.this) {
            final List<ServiceStoreInfo> otherData = new ArrayList<>();

            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return null;
            }

            @Override
            protected boolean loadDataAfterInitViews() {
                return false;
            }

            @Override
            public void onRefresh() {
                String content = mEditText.getText().toString();
                if (TextUtils.isEmpty(content) && !otherData.isEmpty() && !isRefresh) {
                    int size = mData.size();
                    mData.clear();
                    adapter.notifyItemRangeRemoved(0, size);
                    mData.addAll(otherData);
                    mPageNo = mData.isEmpty() ? 1 : mData.size() / getPageSize() + (mData.size() % getPageSize() == 0 ? 0 : 1);
                    adapter.notifyItemRangeInserted(0, mData.size());
                    otherData.clear();
                    setLoadMoreEnable(mData.size() > 0 && mData.size() % getPageSize() == 0);
                } else {
                    super.onRefresh();
                }
            }

            @Override
            public void initViews(View rootView) {
                super.initViews(rootView);
                swipeToLoadLayout.setBackgroundColor(getContext().getResources().getColor(R.color.white));
                mLlEmpty.setBackgroundColor(getActivity().getResources().getColor(R.color.white));
                int px20 = recyclerView.getResources().getDimensionPixelOffset(R.dimen.normal_20);
                recyclerView.setPadding(px20, px20, px20 + px20, 0);
                recyclerView.setClipChildren(false);
            }

            @Override
            protected void onLoadSuccess(int loadPage, List<ServiceStoreInfo> list, int loadType) {
                String content = mEditText.getText().toString();
                if (loadPage == 1) {
                    if (!TextUtils.isEmpty(content)) {
                        otherData.addAll(mData);
                    } else {
                        otherData.clear();
                    }
                    int size = mData.size();
                    mData.clear();
                    adapter.notifyItemRangeRemoved(0, size);
                }
                super.onLoadSuccess(loadPage, list, loadType);
            }

            @Override
            public int getErrorImageResId() {
                return R.drawable.ic_store_empty_list;
            }

            @Override
            public int getEmptyImageResId() {
                return R.drawable.ic_store_empty_list;
            }

            @Override
            public String getEmptyString() {
                String content = mEditText.getText().toString();
                if (!TextUtils.isEmpty(content)) {
                    return getContext().getString(R.string.store_home_search_result_empty);
                }
                return getContext().getString(R.string.store_home_empty);
            }

            @Override
            protected RecyclerView.LayoutManager createLayoutManager(Context context) {
                return new GridLayoutManager(getActivity(), 2);
            }
        };
    }

    @Override
    public Observable<BaseResponse<List<ServiceStoreInfo>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        String content = mEditText.getText().toString().trim();
        if (!TextUtils.isEmpty(content)) {
            Map<String, String> map = new HashMap<>();
            map.put("pageNo", String.valueOf(pageNo));
            map.put("pageSize", String.valueOf(pageSize));
            if (dealerCityParam.getCityId() != null) {
                map.put("cityId", String.valueOf(dealerCityParam.getCityId()));
            }
            map.put("createType", "1");
            map.put("searchType", "7");
            map.put("keyWord", content);
            return service2.getEcCommoditySearchList(map);
        }
        dealerCityParam.setPageNo(pageNo);
        dealerCityParam.setPageSize(pageSize);
        return service2.getDealerCommodityListForApp(dealerCityParam)
                .map(baseResponse -> {
                    List<ServiceStoreInfo> list;
                    Service4sCommodity service4sCommodity = baseResponse.getData();
                    if (service4sCommodity != null) {
                        list = new ArrayList<>(service4sCommodity.getCommodityAppVoList());
                        dealerCityParam.setCityId(service4sCommodity.getCityId());
                    } else {
                        list = new ArrayList<>();
                    }
                    return baseResponse.cloneWithData(list);
                });

    }

    public void refreshData(CityModel chosenCityModel) {
        if (dealerCityParam == null) {
            dealerCityParam = new DealerCityParam();
        }
        isRefresh = true;
        dealerCityParam.setCityName(chosenCityModel.getCityName());
        dealerCityParam.setCityId(chosenCityModel.getCityId());
        if (getRefreshController() != null) {
            getRefreshController().onRefresh();
        }
    }

    @OnClick(R.id.ll_search_condition)
    public void onViewClick(View view) {
        isRefresh = false;
        mTvSearch.setVisibility(View.GONE);
        mEditText.setVisibility(View.VISIBLE);
        int length = mEditText.getText().toString().length();
        mEditText.setSelection(length);
        InputMethodUtils.showInputMethodByPostDelayed(mEditText);
    }

    @Override
    public void refresh() {
        super.refresh();
        if (getRefreshController() != null) {
            getRefreshController().onRefresh();
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        SensorsUtils.sensorsViewStart(FinalSensors.BROWSE_LIFE_INFORMATION);
    }

    @Override
    public void onStop() {
        super.onStop();
        SensorsUtils.sensorsViewEndNew("4s店精选", FinalSensors.BROWSE_LIFE_INFORMATION, "浏览4s店精选");
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (hidden) {
            SensorsUtils.sensorsViewEndNew("4s店精选", FinalSensors.BROWSE_LIFE_INFORMATION, "浏览4s店精选");
        } else {
            SensorsUtils.sensorsViewStart(FinalSensors.BROWSE_LIFE_INFORMATION);
        }
    }
}
