package com.cloudy.linglingbang.activity.store.commodity

import android.content.Context
import android.content.Intent
import android.graphics.Typeface
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.view.ViewTreeObserver
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import butterknife.BindView
import butterknife.BindViews
import butterknife.OnClick
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity
import com.cloudy.linglingbang.activity.basic.IntentUtils
import com.cloudy.linglingbang.activity.basic.RefreshController
import com.cloudy.linglingbang.activity.fragment.mynew.util.SelfUserInfoLoader
import com.cloudy.linglingbang.activity.store.commodity.adapter.CommodityDetailAdapter
import com.cloudy.linglingbang.activity.store.commodity.adapter.holder.EvaluationTitleViewHolder
import com.cloudy.linglingbang.activity.store.commodity.adapter.holder.ImageAndTextViewHolder
import com.cloudy.linglingbang.activity.store.commodity.adapter.holder.RecommendTitleViewHolder
import com.cloudy.linglingbang.activity.store.commodity.dialog.CouponDialog
import com.cloudy.linglingbang.activity.store.commodity.dialog.SkuSelectorDialog
import com.cloudy.linglingbang.app.log.LogUtils
import com.cloudy.linglingbang.app.receiver.UserInfoChangeReceiver
import com.cloudy.linglingbang.app.util.AppUtil
import com.cloudy.linglingbang.app.util.JumpPageUtil
import com.cloudy.linglingbang.app.util.LocationHelper
import com.cloudy.linglingbang.app.util.NewServiceUtils
import com.cloudy.linglingbang.app.util.NotchScreenUtils
import com.cloudy.linglingbang.app.util.ShareUtil
import com.cloudy.linglingbang.app.util.ShareUtil.ButtonInfo
import com.cloudy.linglingbang.app.util.StatusBarUtils
import com.cloudy.linglingbang.app.util.UserStatusChangeManager
import com.cloudy.linglingbang.app.util.UserUtils
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils
import com.cloudy.linglingbang.app.util.share.AppToShareFactory
import com.cloudy.linglingbang.app.util.share.ShareToPlatformCommodity
import com.cloudy.linglingbang.app.util.timer.CountDownManager
import com.cloudy.linglingbang.constants.FinalSensors
import com.cloudy.linglingbang.constants.WebUrlConfigConstant
import com.cloudy.linglingbang.model.SourceModel
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2
import com.cloudy.linglingbang.model.store.ShopCarTypeInfo
import com.cloudy.linglingbang.model.user.User
import io.reactivex.rxjava3.core.Observable
import java.net.URLEncoder
import java.util.Locale
import kotlin.math.max

/**
 * 新商品详情页面 包括整车和优品
 *
 * <AUTHOR>
 * @date 2022/9/21
 */
open class CommodityDetailActivity : BaseRecyclerViewRefreshActivity<Any>(), CommodityOpenDialog {
    private var adapter: CommodityDetailAdapter? = null
    private var mTitleOffsetHeight = 0
    private var mTitleLayoutHeight = 290
    private var commodityId: Long? = null
    private var shopId: Long? = null
    private var isBenefitcommodity: Int? = null
    private var commoditySelectSkuId: Long? = null
    private var mRefreshController: CommodityDetailRefreshController? = null
    var mSourceModel: SourceModel? = null

    var mSkuSelectorDialog: SkuSelectorDialog? = null
    var mCouponDialog: CouponDialog? = null

    /**
     * 卖点使用
     */
    private val pageDetail: String
        get() {
            val type =
                if (mRefreshController?.mCenterCommodityDetail?.commodityClassifyId == 0) "(整车)" else "(优品)"
            return "商品详情页$type"
        }

    companion object {
        /**
         * key-前向地址
         */
        const val SOURCE_MODEL = "SOURCEMODEL"

        fun startActivity(context: Context, commodityId: Long, sourceModel: SourceModel?) {
            startActivity(context, commodityId, -1, sourceModel)
        }

        fun startActivity(
            context: Context,
            commodityId: Long,
            shopId: Long?,
            sourceModel: SourceModel?
        ) {
            val intent = Intent(context, CommodityDetailActivity::class.java)
            intent.putExtra("commodityId", commodityId)
            if ((shopId ?: 0) > 0) {
                intent.putExtra("shopId", shopId)
            }
            intent.putExtra(SOURCE_MODEL, sourceModel)
            context.startActivity(intent)
        }
    }

    @JvmField
    @BindView(R.id.title_layout)
    var mRlTitleLayoutView: View? = null

    @JvmField
    @BindView(R.id.rl_title)
    var mRlTitleView: View? = null

    @JvmField
    @BindView(R.id.tv_cart_count)
    var mTvCartCountView: TextView? = null

    @JvmField
    @BindViews(
        R.id.tv_tab_commodity,
        R.id.tv_tab_evaluation,
        R.id.tv_tab_detail,
        R.id.tv_tab_recommend
    )
    var mTabList: MutableList<TextView?>? = null

    @JvmField
    @BindViews(
        R.id.view_tab_commodity,
        R.id.view_tab_evaluation,
        R.id.view_tab_detail,
        R.id.view_tab_recommend
    )
    var mTabViewList: MutableList<View?>? = null
    var userStatusChangeManager: UserStatusChangeManager? = null
    override fun loadViewLayout() {
        setContentView(R.layout.activity_commodity)
    }

    override fun initialize() {
        super.initialize()
        commodityId = IntentUtils.getOrParseLongExtra(intent.extras, "commodityId")
        if (commodityId == 0L) {
            onIntentExtraError()
            return
        }
        shopId = IntentUtils.getOrParseLongExtra(intent.extras, "shopId")
        if ((shopId ?: 0L) <= 0L) shopId = -1L
//        intent.getLongExtra("", 0)
        mSourceModel = intent.getSerializableExtra(SOURCE_MODEL) as SourceModel?
        userStatusChangeManager = UserStatusChangeManager(this, object :
            UserInfoChangeReceiver() {
            override fun onClearUser() {
                super.onClearUser()
                mRefreshController?.queryCommodityDetailByUserChange()
                mTvCartCountView?.visibility = View.GONE
            }

            override fun onUpdateCartInfo() {
                super.onUpdateCartInfo()
                setCountView()
            }
        })
        setCountView()
        userStatusChangeManager?.register()
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        StatusBarUtils.setFullScreenTransparentStatusBarAndWhiteText(this)
        val top = max(
            NotchScreenUtils.getNotchSafeWH()[1],
            StatusBarUtils.getStatusBarHeight(this)
        )
        mRlTitleView?.apply {
            setPadding(
                paddingLeft,
                top + paddingTop,
                paddingRight,
                paddingBottom
            )
        }
        mRlTitleLayoutView?.apply {
            setPadding(
                paddingLeft,
                top + paddingTop,
                paddingRight,
                paddingBottom
            )
        }
        mRlTitleLayoutView?.viewTreeObserver?.addOnPreDrawListener(object :
            ViewTreeObserver.OnPreDrawListener {
            override fun onPreDraw(): Boolean {
                mRlTitleLayoutView?.viewTreeObserver?.removeOnPreDrawListener(this)
                mTitleLayoutHeight =
                    mRlTitleLayoutView?.height ?: (mRlTitleLayoutView?.measuredHeight
                        ?: mTitleLayoutHeight)
                return true
            }
        })
        mTitleOffsetHeight = resources.displayMetrics.widthPixels * 500 / 375
        refreshController.recyclerView.addOnScrollListener(object :
            RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (dy == 0) {
                    return
                }
                val mg: StaggeredGridLayoutManager =
                    recyclerView.layoutManager as StaggeredGridLayoutManager
                if (mg.findFirstVisibleItemPositions(null)[0] == 0) {
                    updateBackgroundAlpha(recyclerView.computeVerticalScrollOffset().toFloat())
                }
//                updateTabs(recyclerView)
            }
        })

        mTabList?.forEach {
            it?.visibility = View.INVISIBLE
        }
        mTabViewList?.forEach {
            it?.visibility = View.INVISIBLE
        }
    }

    private fun updateTabs(recyclerView: RecyclerView) {

        val adapter = recyclerView.adapter
        val count = adapter?.itemCount ?: 0
        var indexEvaluation = -1
        var indexRecommend = -1
        var indexDetail = -1
        for (i in 0 until count) {
            if (indexEvaluation < 0 && adapter?.getItemViewType(i) == R.layout.item_commodity_detail_evaluation) {
                indexEvaluation = i
            } else if (indexRecommend < 0 && adapter?.getItemViewType(i) == R.layout.item_commodity_recommond_title) {
                indexRecommend = i
            } else if (indexDetail < 0 && adapter?.getItemViewType(i) == R.layout.item_commodity_detail_text_img) {
                indexDetail = i
            }
        }

        val view = recyclerView.findChildViewUnder(130f, mTitleLayoutHeight.toFloat())
        val viewHolder: RecyclerView.ViewHolder? =
            if (view == null) null else recyclerView.findContainingViewHolder(view)
        var tempIndex = 0
        when (viewHolder) {
            is EvaluationTitleViewHolder -> {
                tempIndex = 1
            }

            is ImageAndTextViewHolder -> {
                tempIndex = 2
            }

            is RecommendTitleViewHolder -> {
                tempIndex = 3
            }

            else -> {

                val index = viewHolder?.layoutPosition ?: 0

                tempIndex = when {
                    index < indexEvaluation -> {
                        0
                    }

                    index < indexDetail -> {
                        1
                    }

                    index < indexRecommend -> {
                        2
                    }

                    else -> 3
                }
            }
        }
        if ((mRlTitleLayoutView?.tag ?: 0) == tempIndex) {
            return
        }
        mRlTitleLayoutView?.tag = tempIndex
        updateTabView(tempIndex)

    }

    private fun updateTabView(index: Int) {

        mTabList?.forEach { tv ->
            tv?.apply {
                textSize = 14f
                typeface = Typeface.DEFAULT
            }

        }
        mTabList?.get(index)?.apply {
            textSize = 15f
            typeface = Typeface.DEFAULT_BOLD
        }
        mTabViewList?.forEach { view ->
            view?.apply { visibility = View.INVISIBLE }
        }
        mTabViewList?.get(index)?.apply {
            visibility = View.VISIBLE
        }
    }

    @OnClick(
        R.id.iv_back, R.id.iv_back3, R.id.iv_share, R.id.iv_share_img, R.id.tv_tab_commodity,
        R.id.tv_tab_evaluation,
        R.id.tv_tab_detail,
        R.id.tv_tab_recommend,
        R.id.btn_service_mm,
        R.id.btn_cart,
        R.id.buy_now,
        R.id.iv_inquiry,
        R.id.btn_add_to_shopping_car,
        R.id.iv_test_drive,
        R.id.iv_finance,
        R.id.buy_now2,
    )
    fun viewClick(view: View) {

        val spName = mRefreshController?.mCenterCommodityDetail?.commodityName ?: ""
        var eventName = "点击($commodityId)($spName)"
        var eventPosition = ""
        //设置注册渠道
        val channel =
            if (mRefreshController?.mCenterCommodityDetail?.commodityClassifyId == 0) AppUtil.RegisterChannel.CHANNEL_COMMODITY_CAR else AppUtil.RegisterChannel.CHANNEL_COMMODITY

        when (view.id) {
            R.id.iv_back, R.id.iv_back3 -> {
                onBackPressed()
                eventName += "返回"
                eventPosition = "返回按钮"

            }

            R.id.iv_share, R.id.iv_share_img -> {
                eventName += "分享"
                eventPosition = "分享按钮"
                toShare()
            }

            R.id.tv_tab_commodity -> {
                eventName += "商品"
                eventPosition = "吸顶分类"
                scrollToPosition(R.layout.item_commodity_detail_banner)
                updateTabView(0)
            }

            R.id.tv_tab_evaluation -> {
                eventName += "评价"
                eventPosition = "吸顶分类"
                scrollToPosition(R.layout.item_commodity_detail_evaluation)
                updateTabView(1)
            }

            R.id.tv_tab_detail -> {
                eventName += "详情"
                eventPosition = "吸顶分类"
                scrollToPosition(R.layout.item_commodity_detail_text_img)
                updateTabView(2)
            }

            R.id.tv_tab_recommend -> {
                eventName += "推荐"
                eventPosition = "吸顶分类"
                scrollToPosition(R.layout.item_commodity_recommond_title)
                updateTabView(3)
            }

            R.id.btn_service_mm -> {
                eventName += "客服"
                eventPosition = "客服按钮"
                mRefreshController?.mCenterCommodityDetail?.apply {
                    if (commodityClassifyId == 0) {
                        val s = ShopCarTypeInfo()
                        s.carTypeName = commodityName
                        val imageList = arrayListOf<ShopCarTypeInfo.MainImg>()
                        val t = ShopCarTypeInfo.MainImg()
                        t.mainImg = lmCommodityAsDetail?.commodityMainImage
                        imageList.add(t)
                        s.mainImgs = imageList

                        if (AppUtil.checkLogin(
                                this@CommodityDetailActivity,
                                AppUtil.RegisterChannel.CHANNEL_COMMODITY_CAR
                            )
                        ) {
                            JumpPageUtil.consultService(
                                this@CommodityDetailActivity,
                                NewServiceUtils.TYPE_MM_CONSULTANT,
                                s
                            )
                        }
                    } else {
                        if (AppUtil.checkLogin(
                                this@CommodityDetailActivity,
                                AppUtil.RegisterChannel.CHANNEL_COMMODITY
                            )
                        ) {
                            JumpPageUtil.consultService(
                                this@CommodityDetailActivity,
                                NewServiceUtils.TYPE_COMMODITY_CONSULTANT,
                                "优品详情"
                            )
                        }
                    }
                }

            }

            R.id.btn_cart -> {
                eventName += "购物车"
                eventPosition = "购物车按钮"
                if (AppUtil.checkLogin(this, channel)) {
                    IntentUtils.startActivity(this, ShoppingCartActivity::class.java)
                }
            }

            R.id.buy_now -> {
                //2023.5.12龙卡需求:特殊处理为询价，优先级比过往字段高
                if (mRefreshController?.mCenterCommodityDetail?.isSpecialButton == 1) {
                    eventName += "询价"
                    eventPosition = "立即购买按钮"
                    JumpPageUtil.goAskPriceAndAppointment(
                        this,
                        JumpPageUtil.BUTTON_TYPE.ASK_PRICE,
                        null,
                        commodityId,
                        shopId
                    )
                } else {
                    eventName += "立即购买"
                    eventPosition = "立即购买按钮"
                    openSkuDialog(CommodityOpenDialog.NOW_BUY)
                }
            }

            R.id.buy_now2 -> {
                eventName += "立即众筹"
                eventPosition = "立即众筹按钮"
                openSkuDialog(CommodityOpenDialog.NOW_BUY)
            }

            R.id.btn_add_to_shopping_car -> {
                if (channel.equals(AppUtil.RegisterChannel.CHANNEL_COMMODITY_CAR)) {
                    //试驾
                    eventName += "试驾"
                    eventPosition = "联系我试驾按钮"

                    JumpPageUtil.goAskPriceAndAppointment(
                        this,
                        JumpPageUtil.BUTTON_TYPE.APPOINTMENT,
                        null,
                        commodityId,
                        shopId
                    )
                } else {
                    eventName += "加入购物车"
                    eventPosition = "加入购物车按钮"
                    openSkuDialog(CommodityOpenDialog.SHOPPING_CAR)
                }

            }

            R.id.iv_inquiry -> {
                eventName += "询价"
                eventPosition = "右侧浮层按钮"
                //询价
                JumpPageUtil.goAskPriceAndAppointment(
                    this,
                    JumpPageUtil.BUTTON_TYPE.ASK_PRICE,
                    null,
                    commodityId,
                    shopId
                )
            }

//            R.id.iv_test_drive -> {
//                //试驾
//                eventName += "试驾"
//                eventPosition = "右侧浮层按钮"
//
//                JumpPageUtil.goAskPriceAndAppointment(
//                    this,
//                    JumpPageUtil.BUTTON_TYPE.APPOINTMENT,
//                    null,
//                    commodityId,
//                    shopId
//                )
//            }

            R.id.iv_finance -> {
                eventName += "金融"
                eventPosition = "右侧浮层按钮"
                //金融
                JumpPageUtil.goCommonWeb(
                    this,
                    String.format(
                        Locale.getDefault(),
                        WebUrlConfigConstant.COMMODITY_FINANCIAL_SERVICES,
                        commodityId.toString() + if (commoditySelectSkuId != null)
                            "&skuId=$commoditySelectSkuId"
                        else ""
                    )
                )
            }
        }
        SensorsUtils.sensorsClickBtn(eventName, pageDetail, eventPosition)
    }

    private fun toShare() {
        if (!AppUtil.checkLogin(this)) {
            return
        }
        mRefreshController?.mCenterCommodityDetail?.apply {
            //分享的标题
            val title: String = commodityName
            //分享的内容
            val content = getString(R.string.store_commodity_share_content)
            //分享的链接
            var shareUrl = String.format(
                Locale.getDefault(),
                WebUrlConfigConstant.COMMODITY_DETAIL_PAGE_SHARE,
                <EMAIL>
            )
            //分享的图片
            val images: MutableList<String> = ArrayList()
            images.add(lmCommodityAsDetail.commodityDetailImage)

            //小程序路径
            //小程序路径
            val miniProgramPath = String.format(
                Locale.getDefault(),
                WebUrlConfigConstant.COMMODITY_DETAIL_PAGE_SHARE_MINI_PROGRAM,
                <EMAIL>,
                URLEncoder.encode(User.shareInstance().userIdStr, "UTF-8")
            )
            //小程序id
            val miniProgramUserName = "gh_bd7a2c3f3483"
            if (TextUtils.isEmpty(shareUrl)) {
                shareUrl = WebUrlConfigConstant.SHARE_LLB
            } else {
                val user = User.getsUserInstance()
                if (UserUtils.hasLogin(user)) {
                    shareUrl = String.format(ShareUtil.POST_SHARE_PARAM, shareUrl, user.userIdStr)
                }
            }
            val mShareUtil = ShareUtil.newInstance()
            mShareUtil.shareType = ShareUtil.ShareType.SHARE_TYPE_COMMODITY
//            "ic_share_commodity_img"
            //采购员暂不可下载海报
            //            "ic_share_commodity_img"
            //采购员暂不可下载海报
            if (!UserUtils.isDscgUser()) {
                mShareUtil.addShareItem(
                    ButtonInfo(
                        "下载海报",
                        AppToShareFactory.SHARE_KEY_COMMODITY_IMAGE,
                        "ic_share_commodity_img"
                    )
                )
            }
            mShareUtil.addShareItem(
                ButtonInfo(
                    "复制链接",
                    AppToShareFactory.SHARE_KEY_POST_COMMODITY_LINK_URL,
                    "ic_share_commodity_post_link_url"
                )
            )
//            shareActivityInfo = CenterCommodity.ShareActivityInfo()
//            shareActivityInfo.activityId = 1
//            shareActivityInfo.activityType = 1
            //缤果是否是有活动
            if ((shareActivityInfo?.activityId ?: 0) > 0) {
                val s = ShareToPlatformCommodity()
                mShareUtil.ecologyCommodity = this
                s.toShare(this@CommodityDetailActivity, mShareUtil)
            } else {
                mShareUtil.share(
                    this@CommodityDetailActivity, shareUrl, content, images, title,
                    miniProgramPath, miniProgramUserName, true, this, mShareUtil.YOUPIN
                )
            }
        }
    }

    private fun scrollToPosition(layoutRes: Int) {
        var index = -1
        val count = refreshController?.adapter?.itemCount ?: 0
        if (count <= 0) {
            return
        }
        for (i in 0 until count) {
            if (layoutRes == refreshController?.adapter?.getItemViewType(i)) {
                index = i
                break
            }
        }
        refreshController?.recyclerView?.apply {
            if (layoutManager is LinearLayoutManager) {
                (layoutManager as LinearLayoutManager).scrollToPositionWithOffset(
                    index,
                    mTitleLayoutHeight
                )
                updateBackgroundAlpha(if (index == 0) 0f else mTitleOffsetHeight.toFloat())
            } else if (layoutManager is StaggeredGridLayoutManager) {
                (layoutManager as StaggeredGridLayoutManager).scrollToPositionWithOffset(
                    index,
                    mTitleLayoutHeight
                )
                updateBackgroundAlpha(if (index == 0) 0f else mTitleOffsetHeight.toFloat())
            }
        }
    }

    override fun createAdapter(list: List<Any>): RecyclerView.Adapter<out RecyclerView.ViewHolder> {
        adapter = CommodityDetailAdapter(this, list)
        return adapter as CommodityDetailAdapter
    }

    override fun getListDataFormNet(
        service2: L00bangService2,
        pageNo: Int,
        pageSize: Int
    ): Observable<BaseResponse<MutableList<Any>>>? {
        return null
    }

    override fun createRefreshController(): RefreshController<Any> {
        mRefreshController = CommodityDetailRefreshController(this)
        mRefreshController?.commodityId = commodityId
        return mRefreshController as CommodityDetailRefreshController
    }

    private fun updateBackgroundAlpha(offset: Float) {
        val height = mTitleOffsetHeight * 90 / 100
        val radio: Float = if (offset >= height) {
            1f
        } else {
            offset / height
        }
        LogUtils.d("updateBackgroundAlpha = $offset")
        LogUtils.d("updateBackgroundAlpha radio = $radio")
        var alpha = (255 * radio).toInt()
        if (alpha < 20) {
            alpha = 0
        }
        mRlTitleView?.alpha = 1 - alpha / 255f
        mRlTitleLayoutView?.alpha = alpha / 255f

        if (radio > 0.75f) {
            StatusBarUtils.setLightStatusBar(this)
        } else {
            StatusBarUtils.setDarkStatusBar(this)
        }
    }

    override fun onPause() {
        super.onPause()
        CountDownManager.getInstance().onPause(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        CountDownManager.getInstance().onDestroy(this)
        userStatusChangeManager?.unregister()
    }

    override fun onResume() {
        super.onResume()
        CountDownManager.getInstance().onResume(this)
    }


    /**
     * 打开sku弹窗
     * openType 0：选择规格，1：加入购物车，2：立即购买
     */
    override fun openSkuDialog(openType: Int) {
        //设置注册渠道
        var channel =
            if (mRefreshController?.mCenterCommodityDetail?.commodityClassifyId == 0) AppUtil.RegisterChannel.CHANNEL_COMMODITY_CAR else AppUtil.RegisterChannel.CHANNEL_COMMODITY
        if (!AppUtil.checkLogin(this, channel)) {
            return
        }
        if (mRefreshController?.mCenterCommodityDetail == null) {
//            ToastUtil.showMessage(this, "数据加载中")
            return
        }
        if (mSkuSelectorDialog == null) {
            mSkuSelectorDialog = SkuSelectorDialog(this)
            mSkuSelectorDialog?.commodityId = commodityId
            mSkuSelectorDialog?.shopId = shopId
            mSkuSelectorDialog?.isBenefitcommodity =
                mRefreshController?.mCenterCommodityDetail?.isBenefitCommodity
            mSkuSelectorDialog?.setOnSkuSelectListener(object :
                SkuSelectorDialog.OnSkuSelectListener {
                override fun onSkuSelect(attributes: String?, skuId: Long?) {
                    commoditySelectSkuId = skuId
                    mRefreshController?.attributes = attributes
                    val count = adapter?.itemCount ?: 0
                    for (i in 0 until count) {
                        if (adapter?.getItemViewType(i) == R.layout.item_commodity_detail_middle_info) {
                            adapter?.notifyItemChanged(i)
                            break
                        }
                    }
                }

                override fun addCarSuccess(count: Int) {
                    mTvCartCountView?.text = count.toString()
                    mTvCartCountView?.visibility = if (count > 0) View.GONE else View.GONE
                }
            })
        }
        mSkuSelectorDialog?.mSourceModel = mSourceModel
        mSkuSelectorDialog?.openType = openType
        mSkuSelectorDialog?.referrer = pageDetail
        mSkuSelectorDialog?.showDialog()
    }

    /**
     * 打开参数弹窗
     */
    override fun openParametersDialog() {
        val eventName =
            "点击($commodityId)(" + (mRefreshController?.mCenterCommodityDetail?.commodityName
                ?: "") + ")参数"
        SensorsUtils.sensorsClickBtn(eventName, pageDetail, "参数")
        var url =
            String.format(WebUrlConfigConstant.COMMODITY_PARAMETER_CONFIG, commodityId.toString())
        if (commoditySelectSkuId != null) {
            url += "&commoditySkuId=$commoditySelectSkuId"
        }
        JumpPageUtil.goCommonWeb(this, url)
    }

    /**
     * 打开优惠卷弹窗
     */
    override fun openCouponDialog() {
        val eventName =
            "点击($commodityId)(" + (mRefreshController?.mCenterCommodityDetail?.commodityName
                ?: "") + ")优惠"
        SensorsUtils.sensorsClickBtn(eventName, pageDetail, "优惠按钮")
        if (mCouponDialog == null) {
            mCouponDialog = CouponDialog(this)
            mCouponDialog?.mCommodityIds = commodityId.toString()
            mCouponDialog?.commodityClassifyId =
                mRefreshController?.mCenterCommodityDetail?.commodityClassifyId ?: -1
            mCouponDialog?.mCommodityNames =
                (mRefreshController?.mCenterCommodityDetail?.commodityName
                    ?: "")
        }
        mCouponDialog?.mActivityInfoList =
            mRefreshController?.mCenterCommodityDetail?.promotionActivity?.activityInfoList
        mCouponDialog?.show()
    }

    override fun onLoginSuccess() {
        super.onLoginSuccess()
        mRefreshController?.queryCommodityDetailByUserChange()
    }

    private fun setCountView() {
        val count = SelfUserInfoLoader.getInstance().cartInfo.count
        if (count == 0) {
            mTvCartCountView?.visibility = View.GONE
        } else {
            mTvCartCountView?.visibility = View.GONE
            mTvCartCountView?.text =
                SelfUserInfoLoader.getInstance().cartInfo.count.toString()
        }
    }

    override fun onPermissionResult(isGranted: Boolean, requestCode: Int) {
        super.onPermissionResult(isGranted, requestCode)
        LocationHelper.getInstance().onPermissionResult(isGranted, requestCode)
    }

    override fun onStart() {
        super.onStart()
        SensorsUtils.sensorsViewStart(FinalSensors.COMMODITY_DETAIL_PAGE_BROWSE_CODE)
    }

    override fun onStop() {
        super.onStop()
        var pName = ""
        var typeName = ""
        mRefreshController?.mCenterCommodityDetail?.commodityName
        var category = ""
        mRefreshController?.mCenterCommodityDetail?.lmCommodityAsDetail?.subordinateCategory
        mRefreshController?.mCenterCommodityDetail?.apply {
            pName = commodityName
            lmCommodityAsDetail?.apply { category = subordinateCategory }
            typeName = if (commodityClassifyId == 0) "整车" else "优品"
        }
        val temp = mSourceModel?.orderSource
        SensorsUtils.sensorsViewEndNew(
            "商品详情页($temp)($typeName)",
            FinalSensors.COMMODITY_DETAIL_PAGE_BROWSE_CODE,
            "浏览商品详情页($category)-($pName)-($commodityId)"
        )
    }

}