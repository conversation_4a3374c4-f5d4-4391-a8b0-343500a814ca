package com.cloudy.linglingbang.activity.community.common;

import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.adapter.newcommunity.CommunityIndexAdapter;
import com.cloudy.linglingbang.model.postcard.PostCard;

import java.util.List;

import androidx.recyclerview.widget.RecyclerView;

/**
 * 新版帖子列表基类
 *
 * <AUTHOR>
 * @date 2018/6/23
 */
public abstract class BasePostListActivity extends BaseRecyclerViewRefreshActivity<PostCard> {
    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<PostCard> list) {
        return new CommunityIndexAdapter(this, list);
    }
}
