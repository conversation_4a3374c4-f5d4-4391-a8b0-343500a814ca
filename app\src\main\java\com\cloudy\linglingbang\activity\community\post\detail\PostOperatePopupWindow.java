package com.cloudy.linglingbang.activity.community.post.detail;

import android.content.Context;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.widget.popup.BaseListPopupWindow;
import com.cloudy.linglingbang.app.widget.recycler.DividerItemDecoration;

import java.util.List;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 帖子操作弹窗
 *
 * <AUTHOR>
 * @date 2018/6/28
 */
public class PostOperatePopupWindow extends BaseListPopupWindow<String> {
    public PostOperatePopupWindow(Context context, List<String> data, OnChoiceClickListener onChoiceClickListener) {
        super(context, data, onChoiceClickListener);
    }

    @Override
    protected int getDefaultLayoutResId() {
        return R.layout.pop_post_operate;
    }

    @Override
    protected int getItemLayoutRes() {
        return R.layout.item_pop_post_operate;
    }

    @Override
    protected RecyclerView.LayoutManager createLayoutManager(Context context) {
        return new LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false);
    }

    @Override
    protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
        return new DividerItemDecoration(context, DividerItemDecoration.VERTICAL_LIST, R.drawable.item_divider_margin_20);
    }
}
