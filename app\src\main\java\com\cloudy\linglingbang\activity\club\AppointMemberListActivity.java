package com.cloudy.linglingbang.activity.club;

import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.adapter.club.AppointMemberListAdapter;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.widget.CancelableEditText;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.club.ChannelMember;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.user.User;
import com.umeng.analytics.MobclickAgent;

import java.util.List;

import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import butterknife.OnClick;
import io.reactivex.rxjava3.core.Observable;

/**
 * 任命副会长的成员列表页
 *
 * <AUTHOR>
 * @date 2017/11/22
 */
public class AppointMemberListActivity extends BaseRecyclerViewRefreshActivity<User> {
    private Long mChannelId;
    private ChannelMember mChannelMember = new ChannelMember();

    public static final int REFRESH_LIST = 1;

    /**
     * 搜索框
     */
    @BindView(R.id.et_search_content)
    CancelableEditText mEtSearchContent;

    /**
     * 搜索按钮
     */
    @BindView(R.id.tv_search)
    TextView mTvSearch;

    @Override
    protected void initialize() {
        super.initialize();
        mChannelId = (Long) getIntentExtra(0L);
        if (mChannelId == null) {
            return;
        }
        mChannelMember = new ChannelMember();
        setMiddleTitle(getResources().getString(R.string.club_appoint_vice_chairman));
        mChannelMember.setChannelId(mChannelId);
        mChannelMember.setMemberLevel(2);//查找所有社区成员中的管家会员

    }

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_appoint_member_list);
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(final List<User> list) {
        AppointMemberListAdapter adapter = new AppointMemberListAdapter(this, list, 0);//任命
        adapter.setOnItemClickListener(new BaseRecyclerViewHolder.OnItemClickListener() {
            @Override
            public void onItemClick(View itemView, int position) {
                //添加友盟统计
                MobclickAgent.onEvent(AppointMemberListActivity.this, "322");
                String userIdStr = list.get(position).getUserIdStr();
                assignVice(userIdStr);
            }
        });
        return adapter;
    }

    /**
     * 任命副会长方法
     *
     * @param userIdStr
     */
    private void assignVice(String userIdStr) {
        ChannelMember channelMember = new ChannelMember();
        channelMember.setAssignUserIdStr(userIdStr);
        channelMember.setAssginFlag(0);
        channelMember.setChannelId(mChannelId);
        L00bangRequestManager2.getServiceInstance()
                .assignVice(channelMember)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<Object>(AppointMemberListActivity.this) {
                    @Override
                    public void onSuccess(Object o) {
                        super.onSuccess(o);
                        ToastUtil.showMessage(AppointMemberListActivity.this, getString(R.string.club_appoint_success));
                        //关闭当前页面，刷新上个页面
                        setResult(REFRESH_LIST);
                        finish();
                    }
                });
    }

    @Override
    public Observable<BaseResponse<List<User>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        mChannelMember.setPageNo(pageNo);
        mChannelMember.setPageSize(pageSize);
        return service2.getClubMembers(mChannelMember);
    }

    /**
     * 点击索索按钮,刷新数据
     *
     * @param view
     */
    @OnClick(R.id.tv_search)
    void onSearchClick(View view) {
        //添加友盟统计
        MobclickAgent.onEvent(this, "323");
        if (mEtSearchContent.getText() == null || TextUtils.isEmpty(mEtSearchContent.getText().toString().trim())) {
            mChannelMember.setNickName(null);
        } else {
            mChannelMember.setNickName(mEtSearchContent.getText().toString());
        }
        getRefreshController().onRefresh();
    }

}
