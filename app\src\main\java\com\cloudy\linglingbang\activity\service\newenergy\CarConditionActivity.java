package com.cloudy.linglingbang.activity.service.newenergy;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.aspsine.swipetoloadlayout.OnRefreshListener;
import com.aspsine.swipetoloadlayout.SwipeToLoadLayout2;
import com.baidu.mapapi.model.LatLng;
import com.blankj.utilcode.util.SPUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.bumptech.glide.Glide;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.car.connected.BanmaCarControlHelper;
import com.cloudy.linglingbang.activity.community.CarBuyingExperienceFillInfoActivity;
import com.cloudy.linglingbang.activity.driving.DrivingLoading;
import com.cloudy.linglingbang.activity.service.newenergy.map.BaiduMapViewActivity;
import com.cloudy.linglingbang.activity.service.newenergy.map.EocoderUtils;
import com.cloudy.linglingbang.activity.travel.DateTimeUtils;
import com.cloudy.linglingbang.activity.travel.UnifyCarCacheUtil;
import com.cloudy.linglingbang.activity.travel.UnifyRemotePowerOnActivity;
import com.cloudy.linglingbang.activity.travel.model.UnifyServiceLists;
import com.cloudy.linglingbang.activity.travel.utils.StringUtils;
import com.cloudy.linglingbang.app.imageConfig.RoundedCornersTransformation;
import com.cloudy.linglingbang.app.log.LogUtils;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.AppUtils;
import com.cloudy.linglingbang.app.util.FastClickUtil;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.PreferenceUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.baiduMap.CoordinateTransform;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.util.span.SpanUtils;
import com.cloudy.linglingbang.app.widget.ChooseLimitSpeedDialog;
import com.cloudy.linglingbang.app.widget.banner.AdImageView;
import com.cloudy.linglingbang.app.widget.banner.AutoResizeHeightImageView;
import com.cloudy.linglingbang.app.widget.banner.AutoResizeLinearLayout;
import com.cloudy.linglingbang.app.widget.dialog.PackingDialog;
import com.cloudy.linglingbang.app.widget.dialog.alert.BaseListDialog;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog2;
import com.cloudy.linglingbang.app.widget.image.ImageLoadUtils;
import com.cloudy.linglingbang.app.widget.service.BatterProgressBar;
import com.cloudy.linglingbang.constants.AppConstants;
import com.cloudy.linglingbang.constants.FinalSensors;
import com.cloudy.linglingbang.model.LimitSpeed;
import com.cloudy.linglingbang.model.car.control.BatteryInsulationInfoBean;
import com.cloudy.linglingbang.model.car.control.CarMileageBean;
import com.cloudy.linglingbang.model.car.control.InfoHealthBean;
import com.cloudy.linglingbang.model.car.control.SGMWDefaultCarStatInfo;
import com.cloudy.linglingbang.model.car.control.SavePackingStateSP;
import com.cloudy.linglingbang.model.car.control.TravelDefaultCarInfo;
import com.cloudy.linglingbang.model.car.control.UnifyControlResult;
import com.cloudy.linglingbang.model.car.control.UnifyRechargeResult;
import com.cloudy.linglingbang.model.car.map.BaiduEocoderResult;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.progress.ProgressCancelListener;
import com.cloudy.linglingbang.model.request.retrofit2.progress.ProgressDialogTool;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BackgroundSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.NormalSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.server.Ad.Ad2;
import com.cloudy.linglingbang.model.server.Ad.AdRequestUtil2;
import com.cloudy.linglingbang.model.server.DefaultCarInfo;
import com.cloudy.linglingbang.model.server.NewEnergyCarInfo;
import com.cloudy.linglingbang.web.CommonWebActivity;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import okhttp3.MediaType;
import okhttp3.RequestBody;

/**
 * 车况页面
 *
 * <AUTHOR>
 * @date 2019-07-15
 */
public class CarConditionActivity extends BaseActivity implements OnRefreshListener, ProgressCancelListener {
    /**
     * 页面title
     */
    @BindView(R.id.tv_title)
    TextView mTvTitle;
    /**
     * 车辆图片
     */
    @BindView(R.id.iv_car_img)
    AutoResizeHeightImageView mIvCarImg;
    /**
     * 剩余电量
     */
    @BindView(R.id.tv_remaining_battery)
    TextView mTvRemainingBattery;
    /**
     * 剩余电量进度条
     */
    @BindView(R.id.pb_battery)
    BatterProgressBar mPbBattery;
    /**
     * 正在充电
     */
    @BindView(R.id.iv_car_healthy_battering)
    ImageView mIvBattering;
    /**
     * 剩余里程数
     */
    @BindView(R.id.tv_remaining_mileage)
    TextView mTvRemainingMileage;
    /**
     * 可行驶里程进度条
     */
    @BindView(R.id.pb_mileage)
    ProgressBar mPbMileage;
    /**
     * 行驶里程数
     */
    @BindView(R.id.tv_driven_distance)
    TextView mTvDrivenDistance;
    /**
     * 昨日行驶里程数
     */
    @BindView(R.id.tv_yesterday_distance)
    TextView mTvYesterdayDistance;


    /**
     * 混动里程
     */
    @BindView(R.id.tv_hybrid_mileage)
    TextView mTvHybridMileage;

    /**
     * 混动里程布局
     */
    @BindView(R.id.ly_hybrid_mileage)
    View mLyHybridMileage;

    /**
     * 充电功率
     */
    @BindView(R.id.tv_charge_power)
    TextView mTvChargePower;

    /**
     * 充电功率布局
     */
    @BindView(R.id.ly_charge_power)
    View mLyChargePower;

    /**
     * 当前位置
     */
    @BindView(R.id.tv_current_position)
    TextView mTvCurrentPosition;

    @BindView(R.id.swipeToLoadLayout)
    SwipeToLoadLayout2 mSwipeToLoadLayout2;

    @BindView(R.id.view_mongolia)
    View view_mongolia;

    @BindView(R.id.rl_month_report)
    RelativeLayout mRlMonthReport;
    @BindView(R.id.rl_speed_limit)
    RelativeLayout mRlSpeedLimit;
    @BindView(R.id.ll_packing)
    LinearLayout mRlPacking;
    @BindView(R.id.cb_packing_status)
    ImageView mCbPackingStatus;
    /**
     * 自动补电的打开状态
     */
    @BindView(R.id.cb_supplement_electric_automatic)
    ImageView mCbSupplementElectricAutomatic;
    /**
     * 自动补电的view
     */
    @BindView(R.id.rl_supplement_electric_automatic)
    LinearLayout mRlSupplementElectricAutomatic;
    @BindView(R.id.tv_limit_speed)
    TextView mTvLimitSpeed;

    //自动补电
    @BindView(R.id.tv_supplement_electric_automatic)
    TextView mTvSupplementElectricAutomatic;
    @BindView(R.id.iv_supplement_electric_automatic)
    ImageView mIvSupplementElectricAutomatic;

    //智能保温
    @BindView(R.id.tv_packing)
    TextView mTvPacking;
    @BindView(R.id.iv_packing)
    ImageView mIvPacking;

    /**
     * 智能补电layout--手动补电
     * 如果自动补电是开启状态就隐藏手动补电
     */
    @BindView(R.id.ll_supplement_electric)
    LinearLayout mLlSupplementElectric;

    /**
     * 补电按钮
     */
    @BindView(R.id.btn_supplement)
    Button mBtnSupplement;
    @BindView(R.id.iv_ad_1)
    AdImageView mIvAd1;
    @BindView(R.id.ll_ad_container)
    AutoResizeLinearLayout mLlAdContainer;

    @BindView(R.id.ll_status)
    LinearLayout mLlStatus;
    @BindView(R.id.tv_status)
    TextView mTvStatus;
    @BindView(R.id.iv_status)
    ImageView mIvStatus;

    @BindView(R.id.rl_location)
    RelativeLayout rl_location;

    /**
     * 平均电耗
     */
    @BindView(R.id.ly_average_electricity)
    LinearLayout mLyAverageElectricity;
    /**
     * 平均电耗设置
     */
    @BindView(R.id.tv_average_electricity)
    TextView mTvAverageElectricity;

    /**
     * 第三行第一个tv显示
     */
    @BindView(R.id.tv_third_one)
    TextView mTvThirdOne;

    /**
     * 第三行第二个tv显示
     */
    @BindView(R.id.tv_third_two)
    TextView mTvThirdTwo;

    /**
     * 平均油耗
     */
    @BindView(R.id.tv_avg_fuel)
    TextView mTextViewAvgFuel;

    /**
     * 平均油耗布局
     */
    @BindView(R.id.ly_avg_fuel)
    LinearLayout mLyAvgFuel;


    private String vin = "";
    private SGMWDefaultCarStatInfo sgmwDefaultCarStatInfo;

    private boolean isShowCarPosition = false;

    private DefaultCarInfo mDefaultCarInfo;

    private ChooseCarDialog chooseCarColorDialog;

    private String mBatteryStatusCollectTime = "";

    //采集时间
    private Long mCollectTime;

    private SavePackingStateSP savePackingStateSP;
    /**
     * 保存保温状态
     */
    public static final String KEY_PACKING_STATE = "packingState";

    private LatLng carPosition;

    private ProgressDialogTool mProgressDialogTool;

    //点击车辆状态显示的吐司内容
    private String statusToast;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_car_condition);
    }

    @Override
    protected void initialize() {
        mProgressDialogTool = new ProgressDialogTool(this, this);
        mProgressDialogTool.initProgressDialog();
        view_mongolia.setVisibility(View.VISIBLE);
        view_mongolia.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
            }
        });

        isShowCarPosition = SPUtils.getInstance().getBoolean(AppConstants.SHOW_CAR_POSITION, false);
        if (isShowCarPosition) {
            rl_location.setVisibility(View.VISIBLE);
        } else {
            rl_location.setVisibility(View.GONE);
        }
        mSwipeToLoadLayout2.setOnRefreshListener(this);
        //getBasicCarInfo();

    }

    @Override
    protected void onResume() {
        super.onResume();

        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                getBasicCarInfo();
                setBatteryStatus();
                getYesterdayMileage();
            }
        },1000);

    }



    /**
     * 走菲凡后端获取新能源车辆信息的接口
     */
    private void getBasicCarInfo() {
        L00bangRequestManager2.getServiceInstance()
                .getDefaultCarStatus()
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new NormalSubscriber<SGMWDefaultCarStatInfo>(this) {
                    @Override
                    public void onSuccess(SGMWDefaultCarStatInfo mSgmwDefaultCarStatInfo) {
                        super.onSuccess(mSgmwDefaultCarStatInfo);
                        Log.e("getServiceBtnLists", "====getServiceBtnLists===11111：");
                        view_mongolia.setVisibility(View.GONE);
                        if (mSwipeToLoadLayout2 != null && mSwipeToLoadLayout2.isRefreshing()) {
                            mSwipeToLoadLayout2.setRefreshing(false);
                        }
                        if (mSgmwDefaultCarStatInfo != null && mSgmwDefaultCarStatInfo.getCarStatus() !=null && mSgmwDefaultCarStatInfo.getCarStatus().size() >0) {
                            vin = mSgmwDefaultCarStatInfo.getCarInfo().getVin();
                            mCollectTime = DateTimeUtils.convertTimeToLong2(mSgmwDefaultCarStatInfo.getCarStatus().get("collectTime"));
                            sgmwDefaultCarStatInfo = mSgmwDefaultCarStatInfo;
                            savePackingStateSP = new SavePackingStateSP(vin, "0".equals(mSgmwDefaultCarStatInfo.getCarStatus().get("batteryStatus")) ? true : false,
                                    DateTimeUtils.convertTimeToLong2(mSgmwDefaultCarStatInfo.getCarStatus().get("collectTime")));
                            setAverageFuel();

                            LogUtils.e("======获取新能源车辆信息的接口====onSuccess===status:" + " | vin:" + vin + "savePackingStateSP:" + savePackingStateSP.toString());
                        } else {
                            Log.e("getCarStatus() ==null", "====getServiceBtnLists===2222：");
                            dismissProgressDialog();
                            return;
                        }
                        //请求接口如果vin有变化就重新赋值
                        if (sgmwDefaultCarStatInfo != null && sgmwDefaultCarStatInfo.getCarInfo() != null && mSgmwDefaultCarStatInfo.getCarStatus() != null && !TextUtils.isEmpty(sgmwDefaultCarStatInfo.getCarStatus().get("batteryStatus"))) {
                            Gson gson = new Gson();
                            savePackingStateSP = new SavePackingStateSP(sgmwDefaultCarStatInfo.getCarInfo().getVin()
                                    , "0".equals(sgmwDefaultCarStatInfo.getCarStatus().get("batteryStatus")) ? true : false, mCollectTime);
                            String savePackingStateSPStr = gson.toJson(savePackingStateSP);
                            if (!TextUtils.isEmpty(savePackingStateSPStr)) {
                                Log.e("保温按钮------", "转换成json之后：====" + savePackingStateSPStr);
                                SGMWDefaultCarStatInfo temp_sgmwDefaultCarStatInfo = gson.fromJson(savePackingStateSPStr, SGMWDefaultCarStatInfo.class);
                                Log.e("保温按钮------", "转换回对象之后：====" + temp_sgmwDefaultCarStatInfo.toString());
                                if (mCollectTime > savePackingStateSP.getCollectTime() || savePackingStateSP.getCollectTime() == null) {
                                    if (savePackingStateSP.getVin().equals(vin)) {
                                        PreferenceUtil.putPreference(CarConditionActivity.this, KEY_PACKING_STATE, savePackingStateSPStr);
                                    } else {
                                        savePackingStateSPStr = null;
                                        PreferenceUtil.putPreference(CarConditionActivity.this, KEY_PACKING_STATE, savePackingStateSPStr);
                                    }
                                }
                            }
                        }
                        Log.e("getServiceBtnLists", "====getServiceBtnLists===11111：");
                        setBatteryStatus();
                        getServiceBtnLists();
                        getInfoHealth();
                        updateView(sgmwDefaultCarStatInfo);
                        dismissProgressDialog();
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                        if (mSwipeToLoadLayout2.isRefreshing()) {
                            mSwipeToLoadLayout2.setRefreshing(false);
                        }
                        dismissProgressDialog();
                        LogUtils.e("======获取新能源车辆信息的接口====onFailure===e:" + e.toString());
                    }
                });

        L00bangRequestManager2.getServiceInstance()
                .getUnifyMindChargeStat(vin)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new NormalSubscriber<UnifyRechargeResult>(this) {
                    @Override
                    public void onSuccess(UnifyRechargeResult result) {
                        super.onSuccess(result);
                        if (result != null && result.getChargingStatus() != null) {
                            mCbSupplementElectricAutomatic.setSelected("1".equals(result.getChargingStatus()) ? true : false);
                        }

                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                    }
                });

    }

    private void setAverageFuel() {
        if ("8".equals(sgmwDefaultCarStatInfo.getCarInfo().getControlView())){
            mLyAvgFuel.setVisibility(View.VISIBLE);
            String averageFuel = getAverageFuel(sgmwDefaultCarStatInfo.getCarStatus())+ " L ";
            SpanUtils.setPartSpanText(mTextViewAvgFuel, averageFuel, getResources().getColor(R.color.text_light_color_929292),
                    getResources().getDimension(R.dimen.activity_set_text_24), averageFuel.indexOf("L"), averageFuel.length());
        }
    }

    /**
     * 获取平均油耗
     *
     * @return
     */
    private String getAverageFuel(Map<String, String> carStatus) {
        String avgFuel = carStatus.get("avgFuel");
        if (StringUtils.nullStrOrEmpty(avgFuel)) {
            return "--";
        } else {
            return avgFuel;
        }
    }

    private void getYesterdayMileage() {
        L00bangRequestManager2.getServiceInstance()
                .getYesterdayMileage()
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new NormalSubscriber<CarMileageBean>(this) {
                    @Override
                    public void onSuccess(CarMileageBean carMileageBean) {
                        super.onSuccess(carMileageBean);
                        String yesterdayMileage = carMileageBean.getTrip() + " km";
                        SpanUtils.setPartSpanText(mTvYesterdayDistance, yesterdayMileage, getResources().getColor(R.color.text_light_color_929292),
                                getResources().getDimension(R.dimen.activity_set_text_24), yesterdayMileage.indexOf("k"), yesterdayMileage.length());

                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                        LogUtils.e("======获取新能源车辆信息的接口====onFailure===e:" + e.toString());
                    }
                });
    }

    /**
     * 统一车联网服务-获取车辆限速结果和电池保温状态结果
     */
//    private void getBatteryInsulationInfo(boolean noShowBatteryStatusLayout) {
//        L00bangRequestManager2.getServiceInstance()
//                .getBatteryInsulationInfo(vin)
//                .compose(L00bangRequestManager2.setSchedulers())
//                .subscribe(new NormalSubscriber<BatteryInsulationInfoBean>(this) {
//                    @Override
//                    public void onSuccess(BatteryInsulationInfoBean batteryInsulationInfoBean) {
//                        super.onSuccess(batteryInsulationInfoBean);
//                        view_mongolia.setVisibility(View.GONE);
//                        if (mSwipeToLoadLayout2 != null && mSwipeToLoadLayout2.isRefreshing()) {
//                            mSwipeToLoadLayout2.setRefreshing(false);
//                        }
//
//                        int status = 0;
//
//                        LogUtils.e("======获取车辆限速结果和电池保温状态结果====onSuccess===");
//                        if (batteryInsulationInfoBean != null) {
//                            if (!StringUtils.nullStrOrEmpty(batteryInsulationInfoBean.getStatus()) && batteryInsulationInfoBean.getStatus().equals("3")) {
//                                //只有统一车控的接口状态是3保温中的时候，才去覆盖云漾的电池状态的值
//                                status = Integer.parseInt(batteryInsulationInfoBean.getStatus());
//                                mDefaultCarInfo.setShowBatteryStatus(status);
//                            }
//                            setBatteryStatus();
//                        }else {
//                            setBatteryStatus();
//                        }
//
//                    }
//
//                    @Override
//                    public void onFailure(Throwable e) {
//                        super.onFailure(e);
//                        LogUtils.e("======获取车辆限速结果和电池保温状态结果====onFailure===e:"+e.toString());
//                        if (mSwipeToLoadLayout2.isRefreshing()) {
//                            mSwipeToLoadLayout2.setRefreshing(false);
//                        }
//                        setBatteryStatus();
//                    }
//                });
//
//    }
    private void setBatteryStatus() {
        if (sgmwDefaultCarStatInfo == null) {
            return;
        }

        int status = -1;
        int batteryStatus = -1;

        if (!StringUtils.nullStrOrEmpty(sgmwDefaultCarStatInfo.getCarStatus().get("status"))) {
            status = Integer.parseInt(sgmwDefaultCarStatInfo.getCarStatus().get("status"));

        }

        if (!StringUtils.nullStrOrEmpty(sgmwDefaultCarStatInfo.getCarStatus().get("batteryStatus"))) {
            batteryStatus = Integer.parseInt(sgmwDefaultCarStatInfo.getCarStatus().get("batteryStatus"));
            ;
        }

        LogUtils.e("======setBatteryStatus========batteryStatus:" + batteryStatus + " | status:" + status);

        // 0:正常 1:加热中  2:充电中  3:保温中   4:保温完成  5:补电中
        if (status == 3) {
            mIvStatus.setImageResource(R.drawable.ic_status_heat_preservation);
            mTvStatus.setText("保温中");
        }

        //接口采集时间
        long btteTime = DateTimeUtils.convertTimeToLong2(sgmwDefaultCarStatInfo.getCarStatus().get("collectTime"));
        //取缓存
        String savePackingStateSPStr = PreferenceUtil.getStringPreference(CarConditionActivity.this, KEY_PACKING_STATE, null);
        if (!TextUtils.isEmpty(savePackingStateSPStr)) {
            try {
                Gson gson = new Gson();
                savePackingStateSP = gson.fromJson(savePackingStateSPStr, SavePackingStateSP.class);
            } catch (JsonSyntaxException e) {
                e.printStackTrace();
                savePackingStateSP = null;
            }
        }
        //一分钟之内
        if (savePackingStateSP != null && savePackingStateSP.getCollectTime() != null) {
            if (System.currentTimeMillis() > savePackingStateSP.getCollectTime() || savePackingStateSP.getCollectTime() != null) {
                if (savePackingStateSP.getCollectTime() > btteTime) {
                    mCbPackingStatus.setSelected(savePackingStateSP.isB());
                    mCbPackingStatus.setVisibility(View.VISIBLE);
                    return;
                }
            }
        }
        mCbPackingStatus.setSelected(batteryStatus == 0);
        mCbPackingStatus.setVisibility(View.VISIBLE);
    }

    /**
     * 获取新能源车辆列表
     */
    private void getEnergyCars() {
        if (mDefaultCarInfo != null) {
            mBatteryStatusCollectTime = mDefaultCarInfo.getBatteryStatusCollectTime();
        }
        L00bangRequestManager2.getServiceInstance()
                .getEnergyCars()
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new NormalSubscriber<List<NewEnergyCarInfo>>(this) {
                    @Override
                    public void onSuccess(List<NewEnergyCarInfo> newEnergyCarInfos) {
                        super.onSuccess(newEnergyCarInfos);
                        view_mongolia.setVisibility(View.GONE);
                        if (mSwipeToLoadLayout2 != null && mSwipeToLoadLayout2.isRefreshing()) {
                            mSwipeToLoadLayout2.setRefreshing(false);
                        }
                        if (newEnergyCarInfos != null && newEnergyCarInfos.size() > 0) {
                            chooseCarColorDialog = new ChooseCarDialog(CarConditionActivity.this, null, newEnergyCarInfos, vin, new ChooseCarDialog.OnChooseColorListener() {
                                @Override
                                public void onChooseCar(NewEnergyCarInfo newEnergyCarInfo) {
                                    vin = newEnergyCarInfo.getVin();
                                    //先查询数据库，如果有数据就显示，没有再请求接口。一分钟内不重复请求
                                    if (sgmwDefaultCarStatInfo != null) {
                                        setBatteryStatus();
                                        updateView(sgmwDefaultCarStatInfo);
                                        //如果大于一分钟，则重新请求数据
                                    } else {
                                        getBasicCarInfo();
                                    }
                                    mTvTitle.setText(newEnergyCarInfo.getCarTypeName());
                                }
                            });
                            chooseCarColorDialog.show();
                        } else {
                            toastMessage("没有可切换车辆");
                        }
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                        if (mSwipeToLoadLayout2.isRefreshing()) {
                            mSwipeToLoadLayout2.setRefreshing(false);
                        }
                    }
                });

    }

    private void updateView(SGMWDefaultCarStatInfo mSGMWDefaultCarStatInfo) {

        view_mongolia.setVisibility(View.GONE);
        mTvTitle.setText(TextUtils.isEmpty(mSGMWDefaultCarStatInfo.getCarInfo().getCarName()) ? mSGMWDefaultCarStatInfo.getCarInfo().getCarTypeName() : mSGMWDefaultCarStatInfo.getCarInfo().getCarName());
        String remainMileage  ;
        if (StringUtils.isEmpty(mSGMWDefaultCarStatInfo.getCarStatus().get("leftMileage"))){
            remainMileage = "--" + " km";
        }else {
            remainMileage = mSGMWDefaultCarStatInfo.getCarStatus().get("leftMileage") + " km";
        }
        SpanUtils.setPartSpanText(mTvRemainingMileage, remainMileage , getResources().getColor(R.color.text_light_color_929292),
                getResources().getDimension(R.dimen.activity_set_text_24), remainMileage.indexOf("k"), remainMileage.length());

        String remainingBattery ;

        //设置剩余电量
        if (!StringUtils.nullStrOrEmpty(mSGMWDefaultCarStatInfo.getCarStatus().get("batterySoc"))){
            remainingBattery = mSGMWDefaultCarStatInfo.getCarStatus().get("batterySoc") + " %";
            mPbBattery.setBatterProgressAndTextColor(Integer.parseInt(mSGMWDefaultCarStatInfo.getCarStatus().get("batterySoc")), mTvRemainingBattery, remainingBattery, mSGMWDefaultCarStatInfo.getCarStatus().get("batteryIndicate"));
            mPbMileage.setProgress(Integer.parseInt(mSGMWDefaultCarStatInfo.getCarStatus().get("batterySoc")));
        }else {
            remainingBattery = "--" + "  %";
        }
        SpanUtils.setPartSpanText(mTvRemainingBattery, remainingBattery, getResources().getColor(R.color.text_light_color_929292),
                getResources().getDimension(R.dimen.activity_set_text_24), remainingBattery.indexOf("%"), remainingBattery.length());

        if (mSGMWDefaultCarStatInfo.getCarStatus().get("vecChrgStsIndOn") != null) {
            if ("1".equals(mSGMWDefaultCarStatInfo.getCarStatus().get("vecChrgStsIndOn"))) {
                mIvBattering.setVisibility(View.VISIBLE);
                if (!StringUtils.nullStrOrEmpty(mSGMWDefaultCarStatInfo.getCarStatus().get("chargePower"))){
                    mLyChargePower.setVisibility(View.VISIBLE);
                    //设置充电功率text
                    String chargePower = mSGMWDefaultCarStatInfo.getCarStatus().get("chargePower") + " kW";
                    SpanUtils.setPartSpanText(mTvChargePower, chargePower , getResources().getColor(R.color.text_light_color_929292),
                            getResources().getDimension(R.dimen.activity_set_text_24), chargePower.indexOf("k"), chargePower.length());
                }else {
                    mLyChargePower.setVisibility(View.GONE);
                }
            } else {
                mIvBattering.setVisibility(View.INVISIBLE);
                mLyChargePower.setVisibility(View.GONE);
            }
        }

        if (!TextUtils.isEmpty(mSGMWDefaultCarStatInfo.getCarStatus().get("position"))) {
            mTvCurrentPosition.setText(mSGMWDefaultCarStatInfo.getCarStatus().get("position"));
        } else {
            if (!StringUtils.isEmpty(mSGMWDefaultCarStatInfo.getCarStatus().get("latitude"))){
                double[] lngLat = CoordinateTransform.transformWGS84ToGCJ02(Double.parseDouble(mSGMWDefaultCarStatInfo.getCarStatus().get("longitude")), Double.parseDouble(mSGMWDefaultCarStatInfo.getCarStatus().get("latitude")));
                carPosition = new LatLng(lngLat[1], lngLat[0]);
                getCarLocation(carPosition.latitude, carPosition.longitude);
            }
        }
        new ImageLoad(this, mIvCarImg, ImageLoadUtils.processImageUrl(mSGMWDefaultCarStatInfo.getCarInfo().getImage()), ImageLoad.LoadMode.URL)
                .setPlaceholderAndError(R.drawable.ic_common_place_holder_corner_10)
                .load();

        String drivingDistance ;

        if (StringUtils.isEmpty(mSGMWDefaultCarStatInfo.getCarStatus().get("mileage"))){
            drivingDistance = "--" + " km";
        }else {
            drivingDistance = mSGMWDefaultCarStatInfo.getCarStatus().get("mileage") + " km";
        }
        SpanUtils.setPartSpanText(mTvDrivenDistance, drivingDistance, getResources().getColor(R.color.text_light_color_929292),
                getResources().getDimension(R.dimen.activity_set_text_24), drivingDistance.indexOf("k"), drivingDistance.length());

        //更新混动里程界面
        String hybridMileage = mSGMWDefaultCarStatInfo.getCarStatus().get("hybridMileage");
        if(TextUtils.isEmpty(hybridMileage)){
            mLyHybridMileage.setVisibility(View.GONE);
        }else {
            //有混动里程数据才显示相关界面
            mLyHybridMileage.setVisibility(View.VISIBLE);
            hybridMileage = hybridMileage + " km";
            SpanUtils.setPartSpanText(mTvHybridMileage, hybridMileage, getResources().getColor(R.color.text_light_color_929292),
                    getResources().getDimension(R.dimen.activity_set_text_24), hybridMileage.indexOf("k"), hybridMileage.length());
        }

        //平均电耗值
        String avgElectronFuel ;
        if (!StringUtils.nullStrOrEmptysOrBlank(mSGMWDefaultCarStatInfo.getCarStatus().get("avgElectronFuel"))){
            avgElectronFuel = mSGMWDefaultCarStatInfo.getCarStatus().get("avgElectronFuel") + " kW·h/100km";
        }else {
            avgElectronFuel = "-- kW·h/100km";
        }
            //支持平均电耗
        if ("1".equals(mSGMWDefaultCarStatInfo.getCarInfo().getSupportAvgElectronFuel())) {
            mLyAverageElectricity.setVisibility(View.VISIBLE);
            SpanUtils.setPartSpanText(mTvAverageElectricity, avgElectronFuel, getResources().getColor(R.color.text_light_color_929292),
                    getResources().getDimension(R.dimen.activity_set_text_24), avgElectronFuel.indexOf("k"), avgElectronFuel.length());
        }else {
            mLyAverageElectricity.setVisibility(View.GONE);
        }

        //限速设置代码
//        if (null == defaultCarInfo.getLimitSpeedDesc()) {
//            mRlSpeedLimit.setVisibility(View.GONE);
//        } else {
//            mRlSpeedLimit.setVisibility(View.VISIBLE);
//            mTvLimitSpeed.setText(defaultCarInfo.getLimitSpeedDesc());
//        }
        //用车报告代码
//        if (defaultCarInfo.getMonthReportOrZero() == 0) {
//            mRlMonthReport.setVisibility(View.GONE);
//        } else {
//            mRlMonthReport.setVisibility(View.VISIBLE);
//        }

        //电池智能保温状态
//        if (null == sgmwDefaultCarStatInfo.getCarStatus().get("batteryStatus")) {
//            mRlPacking.setVisibility(View.GONE);
//        } else {
//            mRlPacking.setVisibility(View.VISIBLE);
//        }

//        getBatteryInsulationInfo((null == sgmwDefaultCarStatInfo.getCarStatus().get("status")));

    }

    /**
     * 根据经纬度获取位置信息
     */
    private void getCarLocation(double lat, double lng) {
        //根据经纬度获取地址信息
        EocoderUtils.getAddressInfo(this, lat, lng, new EocoderUtils.OnResultListener() {
            @Override
            public void onSuccess(BaiduEocoderResult.EocodeAddressInfo result) {
                if (!TextUtils.isEmpty(result.getFormatted_address())) {
                    mTvCurrentPosition.setText(result.getFormatted_address());
                } else {
                    String address = getString(R.string.txt_location_failure);
                    mTvCurrentPosition.setText(address);
                }
            }

            @Override
            public void onFailure(String message) {
                String address = getString(R.string.txt_location_failure);
                mTvCurrentPosition.setText(address);
            }
        });

    }

    /**
     * 点击title选择车辆 (2023/08/01接到需求取消此点击
     *)
     */
//    @OnClick(R.id.tv_title)
//    void clickTitle() {
//        if (!AppUtil.checkLogin(this)) {
//            return;
//        }
//        if (chooseCarColorDialog != null) {
//            chooseCarColorDialog.show();
//        } else {
//            getEnergyCars();
//        }
//
//    }

    /**
     * 点击车辆位置
     */
    @OnClick(R.id.rl_location)
    void clickCarPosition() {
        if (FastClickUtil.isFastDoubleClick("rl_location",1000)) {
            Log.e("btn_supplement", "触发了防双击");
            return;
        }

        if (!AppUtil.checkLogin(this)) {
            return;
        }
        if (sgmwDefaultCarStatInfo != null) {
            if (TextUtils.isEmpty(sgmwDefaultCarStatInfo.getCarStatus().get("latitude")) || TextUtils.isEmpty(sgmwDefaultCarStatInfo.getCarStatus().get("longitude"))) {
                SensorsUtils.sensorsClickBtn("查看爱车位置按钮", "新能源车辆显示页");
                LatLng latLng = new LatLng(0, 0);
                BaiduMapViewActivity.IntentExtra intentExtra = new BaiduMapViewActivity.IntentExtra(latLng.longitude, latLng.latitude, System.currentTimeMillis(), sgmwDefaultCarStatInfo == null ? "" : sgmwDefaultCarStatInfo.getCarInfo().getCarTypeName(), sgmwDefaultCarStatInfo == null ? "" : sgmwDefaultCarStatInfo.getCarStatus().get("collectTime"), sgmwDefaultCarStatInfo.getCarInfo().getVin());
                BaiduMapViewActivity.startActivity(this, intentExtra);
                return;
            }
            SensorsUtils.sensorsClickBtn("查看爱车位置按钮", "新能源车辆显示页");
            BaiduMapViewActivity.IntentExtra intentExtra = new BaiduMapViewActivity.IntentExtra(carPosition.longitude, carPosition.latitude, System.currentTimeMillis(), sgmwDefaultCarStatInfo == null ? "" : sgmwDefaultCarStatInfo.getCarInfo().getCarTypeName(), sgmwDefaultCarStatInfo == null ? "" : sgmwDefaultCarStatInfo.getCarStatus().get("collectTime"), sgmwDefaultCarStatInfo.getCarInfo().getVin());
            BaiduMapViewActivity.startActivity(this, intentExtra);
        } else {
            toastMessage("获取车辆信息失败，请稍后再试~");
        }
    }

    @OnClick(R.id.iv_back)
    void clickBack() {
        onBack();
    }


    //月报查询
    @OnClick(R.id.rl_month_report)
    void clickMonthReport() {
        if (FastClickUtil.isFastDoubleClick("rl_month_report",1000)) {
            Log.e("btn_supplement", "触发了防双击");
            return;
        }
        if (!AppUtil.checkLogin(this)) {
            return;
        }
        if (!TextUtils.isEmpty(vin)) {
            SensorsUtils.sensorsClickBtn("用车报告按钮（月报）", "新能源车辆显示页");
            CommonWebActivity.startActivity(this, mDefaultCarInfo.getMonthReportUrl() + vin);
        }
    }

    /**
     * 限速枚举
     */
    @OnClick(R.id.rl_speed_limit)
    void clickSpeedLimit() {
        if (FastClickUtil.isFastDoubleClick("rl_speed_limit",1000)) {
            Log.e("btn_supplement", "触发了防双击");
            return;
        }
        if (!AppUtil.checkLogin(this)) {
            return;
        }
        getEnergyCarsLimitSpeed();
    }

    @OnClick(R.id.cb_packing_status)
    void clickPacking() {
        if (FastClickUtil.isFastDoubleClick("cb_packing_status",1000)) {
            Log.e("btn_supplement", "触发了防双击");
            return;
        }
        if (!AppUtil.checkLogin(this)) {
            return;
        }
        if (!mCbPackingStatus.isSelected()) {
            PackingDialog packingDialog = new PackingDialog(this, R.string.dialog_packing_txt, R.string.btn_packing_open_txt, R.string.btn_packing_not_open_txt, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialogInterface, int i) {
                    if (FastClickUtil.isFastDoubleClick("packingDialog",1000)) {
                        Log.e("btn_supplement", "触发了防双击");
                        return;
                    }

                    toastMessage("正在发起电池智能保温设置，请稍后~");
                    setParkHeart("1");
                }
            });
            packingDialog.setCanceledOnTouchOutside(false);
            packingDialog.show();


        } else {
            PackingDialog packingDialog = new PackingDialog(this, R.string.dialog_packing_close_txt, R.string.btn_packing_close_txt, R.string.btn_packing_not_close_txt, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialogInterface, int i) {
                    if (FastClickUtil.isFastDoubleClick("packingDialog",1000)) {
                        Log.e("btn_supplement", "触发了防双击");
                        return;
                    }
                    toastMessage("正在发起电池智能保温设置，请稍后~");
                    setParkHeart("0");
                }
            });
            packingDialog.setCanceledOnTouchOutside(false);
            packingDialog.show();

        }

    }

    /**
     * 点击蓄电池补电帮助
     */
    @OnClick(R.id.iv_supplement_electric_help)
    void clickSupplementHelp() {
        if (FastClickUtil.isFastDoubleClick("iv_supplement_electric_help",1000)) {
            Log.e("btn_supplement", "触发了防双击");
            return;
        }
        Dialog dialog = new CommonAlertDialog2(this, getString(R.string.battery_supplement_electricity_help_title), getString(R.string.battery_supplement_electricity_content), "我知道了", null, null, null);
        dialog.show();
    }

    /**
     * 点击智能保温帮助
     */
    @OnClick(R.id.iv_packing_help)
    void clickPackingHelp() {
        if (FastClickUtil.isFastDoubleClick("iv_packing_help",1000)) {
            Log.e("btn_supplement", "触发了防双击");
            return;
        }
        Dialog dialog = new CommonAlertDialog2(this, getString(R.string.battery_packing_help_title), getString(R.string.battery_packing_content), "我知道了", null, null, null);
        dialog.show();
    }

    /**
     * 点击车辆状态
     */
    @OnClick(R.id.ll_status)
    void clickLlStatus() {
        if (FastClickUtil.isFastDoubleClick("ll_status",1000)) {
            Log.e("btn_supplement", "触发了防双击");
            return;
        }
        if (sgmwDefaultCarStatInfo != null && !"5".equals(sgmwDefaultCarStatInfo.getCarStatus().get("status"))) {
            String msg = sgmwDefaultCarStatInfo.getCarStatus().get("statusToast");
            Dialog dialog = new CommonAlertDialog2(this, null, msg, "我知道了", null, null, null);
            dialog.show();
        }
//        if (mDefaultCarInfo.getShowBatteryStatusOrZero() > 0) {
//            int status = mDefaultCarInfo.getShowBatteryStatusOrZero();
//            String msg = null;
//            if (status == 1) {
//                msg = getString(R.string.msg_heating);
//            } else if (status == 2) {
//                msg = getString(R.string.msg_charging);
//            } else if (status == 3) {
//                msg = getString(R.string.msg_heat_preservation);
//            } else if (status == 4) {
//                msg = getString(R.string.msg_heat_insulation_completed);
//            } else if (status == 5) {
//                msg = getString(R.string.msg_supplement_electricity);
//            }
//            Dialog dialog = new CommonAlertDialog2(this, null, msg, "我知道了", null, null, null);
//            dialog.show();
//        }
    }

    /**
     * 点击自动补电的开关
     */
    @OnClick(R.id.cb_supplement_electric_automatic)
    void clickSupplementElectricAutomatic() {
        if (FastClickUtil.isFastDoubleClick("cb_supplement_electric_automatic",1000)) {
            Log.e("btn_supplement", "触发了防双击");
            return;
        }
        //如果是打开状态
        if (mCbSupplementElectricAutomatic.isSelected()) {
            //关闭的请求
            setSmartCharge("0");
        } else {//打开操作
            OpenAutomaticSupplementElectricityDialog dialog = new OpenAutomaticSupplementElectricityDialog(CarConditionActivity.this);
            dialog.show();
            dialog.setButtonPostListener(new OpenAutomaticSupplementElectricityDialog.ButtonPostListener() {
                @Override
                public boolean onToPositive() {
                    if (dialog.getCbIsChecked()) {
                        dialog.dismiss();
                        setSmartCharge("1");
                    } else {
                        ToastUtil.showMessage(CarConditionActivity.this, "请先阅读并同意《12V蓄电池自动补电免责声明》");
                    }
                    return false;
                }
            });

        }
    }


    /**
     * 打开自动补电（走菲凡接口）
     */
    private void setSmartCharge(String action) {
        mProgressDialogTool.initProgressDialog();
        DrivingLoading.show(this);
        JSONObject root = new JSONObject();
        try {
            root.put("vin", vin);
            root.put("chargingStatus", action);
        } catch (Exception e) {
            e.printStackTrace();
        }
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), root.toString());
        L00bangRequestManager2.getServiceInstance()
                .queryUnifyMindCharge(requestBody)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new NormalSubscriber<UnifyRechargeResult>(this) {
                    @Override
                    public void onSuccess(UnifyRechargeResult result) {
                        super.onSuccess(result);
                        dismissProgressDialog();
                        if ("1".equals(action)) {
                            //如果是打开自动补电成功，则隐藏手动补电
                            mLlSupplementElectric.setVisibility(View.GONE);
                        } else {//否则显示手动补电
                            //关闭自动补电后，请求基础信息的接口确定是否显示手动补电
                            mLlSupplementElectric.setVisibility(View.VISIBLE);
                            getBasicCarInfo();
                            getYesterdayMileage();
                        }
                        mCbSupplementElectricAutomatic.setSelected(!mCbSupplementElectricAutomatic.isSelected());
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                        DrivingLoading.dismiss();
                        dismissProgressDialog();
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        DrivingLoading.dismiss();
                    }
                });
    }


    /**
     * 点击补电
     */
    @OnClick(R.id.btn_supplement)
    void clickSupplement() {
        if (FastClickUtil.isFastDoubleClick("btn_supplement",1000)) {
            Log.e("btn_supplement", "触发了防双击");
            return;
        }
        if (FastClickUtil.isFastDoubleClick("btn_supplement",1000)) {
            Log.e("btn_supplement", "触发了防双击");
            return;
        }
        onKeyPowerUp();
    }

    /**
     * 打开手动补电（走菲凡接口）
     */
    private void onKeyPowerUp(){
        mProgressDialogTool.initProgressDialog();
        JSONObject root = new JSONObject();
        try {
            root.put("status","1");
            root.put("vin",vin);
        } catch (Exception e) {
            e.printStackTrace();
        }
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), root.toString());
        L00bangRequestManager2.getServiceInstance()
                .queryUnifyCarRecharge(requestBody)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<UnifyControlResult>(this) {
                    @Override
                    public void onSuccess(UnifyControlResult result) {
                        super.onSuccess(result);
                        dismissProgressDialog();
                        mBtnSupplement.setEnabled(false);
                        ToastUtil.showMessage(CarConditionActivity.this, "手动一键补电成功");
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                        dismissProgressDialog();
                        //非离线模式下检测接口报错次数
                        if (AppUtils.hasNetWork2(CarConditionActivity.this)) {
                            UnifyCarCacheUtil.getInstance().isExceptionNumberMany(CarConditionActivity.this, e);
                        }
                    }
                });
    }

    /**
     * 统一车联网服务-获取车辆体检状态信息
     */
    private void getInfoHealth() {
        L00bangRequestManager2.getServiceInstance()
                .infoHealth(vin)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new NormalSubscriber<InfoHealthBean>(this) {
                    @Override
                    public void onSuccess(InfoHealthBean result) {
                        super.onSuccess(result);
                        if (result !=null){
                            if (!TextUtils.isEmpty(result.getStatusToast() )){
                                statusToast = result.getStatusToast();
                            }
                            if (!StringUtils.nullStrOrEmpty(sgmwDefaultCarStatInfo.getCarStatus().get("status")) &&
                                    Integer.parseInt(sgmwDefaultCarStatInfo.getCarStatus().get("status")) > 0 && !StringUtils.isEmpty(result.getStatus())) {
                                mLlStatus.setVisibility(View.VISIBLE);
                                int status = Integer.parseInt(result.getStatus());
                                if (status == 1) {
                                    mIvStatus.setImageResource(R.drawable.ic_status_heating);
                                    mTvStatus.setText(result.getStatusName());
                                } else if (status == 2) {
                                    mIvStatus.setImageResource(R.drawable.ic_status_charging);
                                    mTvStatus.setText(result.getStatusName());
                                } else if (status == 3) {
                                    mTvStatus.setText(result.getStatusName());
                                } else if (status == 4) {
                                    mIvStatus.setImageResource(R.drawable.ic_status_insulation_completed);
                                    mTvStatus.setText(result.getStatusName());
                                } else if (status == 5) {
                                    mIvStatus.setImageResource(R.drawable.ic_status_electricity);
                                    mTvStatus.setText(result.getStatusName());
                                }else {
                                    mLlStatus.setVisibility(View.GONE);
                                }
                            } else {
                                mLlStatus.setVisibility(View.GONE);
                            }
                            if ("1".equals(result.getBanRecharge())){
                                mBtnSupplement.setEnabled(false);
                            }else {
                                mBtnSupplement.setEnabled(true);
                            }
                        }
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                    }
                });
    }



    /**
     * 设置新能源电池智能保温
     */
    private void setParkHeart(String status) {
        mProgressDialogTool.initProgressDialog();
        JSONObject root = new JSONObject();
        try {
            root.put("status", status);
            root.put("vin", mDefaultCarInfo.getVin());
        } catch (Exception e) {
            e.printStackTrace();
        }
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), root.toString());

        L00bangRequestManager2.createUseConnectionTimeOutService()
                .setParkHeart(requestBody)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new NormalSubscriber<UnifyControlResult>(this) {
                    @Override
                    public void onSuccess(UnifyControlResult unifyControlResult) {
                        super.onSuccess(unifyControlResult);
                        dismissProgressDialog();
                        if (unifyControlResult != null) {
                            runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    mCollectTime = DateTimeUtils.convertTimeToLong2(unifyControlResult.getCollectTime());
                                    mCbPackingStatus.setSelected(!mCbPackingStatus.isSelected());
                                    savePackingStateSP = new SavePackingStateSP(vin, mCbPackingStatus.isSelected(), mCollectTime);
                                    savePackingState();
                                    ToastUtil.showMessage(CarConditionActivity.this, "设置成功");
                                    LogUtils.e("=======设置新能源电池智能保温开关====onSuccess1====status：" + status + "按钮状态" + savePackingStateSP.toString());
                                }
                            });
                        }
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                        dismissProgressDialog();
                        LogUtils.e("=======设置新能源电池智能保温开关====onFailure====e：" + e.toString());
                    }
                });

    }

    /**
     * 统一车控-获取车控按钮列表
     */
    public void getServiceBtnLists() {
        if (FastClickUtil.isFastDoubleClick("getserviceBtn")) return;
        JSONObject root = new JSONObject();
        JSONArray array = new JSONArray();
        array.put("service_new_energy");
        try {
            root.put("servicePositionCodeList", array);
            root.put("vin", vin);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Log.e("getServiceBtnLists", "====getServiceBtnLists===parm：" + root.toString());
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), root.toString());
        L00bangRequestManager2.getServiceInstance()
                .getControlBtnList(requestBody)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new NormalSubscriber<List<UnifyServiceLists>>(CarConditionActivity.this) {
                    @Override
                    public void onSuccess(List<UnifyServiceLists> statList) {
                        super.onSuccess(statList);
                        dismissProgressDialog();
                        if(CarConditionActivity.this == null || CarConditionActivity.this.isDestroyed()){
                            Log.e("getServiceBtnLists", "CarConditionActivity.this = " + CarConditionActivity.this);
                            return;
                        }
                        Log.e("getServiceBtnLists", "====getServiceBtnLists===parm111：" + statList.get(0).getServiceList().size());
                        ArrayList<String> list = new ArrayList<>();
                        list.clear();
                        if (statList.get(0).getServiceList().size() < 1) {
                            mRlPacking.setVisibility(View.GONE);
                            mRlSupplementElectricAutomatic.setVisibility(View.GONE);
                            return;
                        }
                        for (int i = 0; i < statList.get(0).getServiceList().size(); i++) {
                            if ("autoHeat".equals(statList.get(0).getServiceList().get(i).getServiceCode())) {
                                list.add("autoHeat");
                                mTvPacking.setText(statList.get(0).getServiceList().get(i).getServiceStatusList().get(0).getServiceStatusName());
                                Glide.with(CarConditionActivity.this).load(statList.get(0).getServiceList().get(i).getServiceStatusList().get(0).getServiceStatusImage()).into(mIvPacking);

                            }
                            if ("autoCharge".equals(statList.get(0).getServiceList().get(i).getServiceCode())) {
                                list.add("autoCharge");
                                mTvSupplementElectricAutomatic.setText(statList.get(0).getServiceList().get(i).getServiceStatusList().get(0).getServiceStatusName());
                                Glide.with(CarConditionActivity.this).load(statList.get(0).getServiceList().get(i).getServiceStatusList().get(0).getServiceStatusImage()).into(mIvSupplementElectricAutomatic);
                            }

                        }
                        //保温
                        if (list.contains("autoHeat")){
                            mRlPacking.setVisibility(View.VISIBLE);
                        }else {
                            mRlPacking.setVisibility(View.GONE);
                        }

                        //补电
                        if (list.contains("autoCharge")){
                            mRlSupplementElectricAutomatic.setVisibility(View.VISIBLE);
                            if (!mCbSupplementElectricAutomatic.isSelected()){
                                mLlSupplementElectric.setVisibility(View.VISIBLE);
                            }else {
                                mLlSupplementElectric.setVisibility(View.GONE);
                            }
                        }else {
                            mRlSupplementElectricAutomatic.setVisibility(View.GONE);
                            mLlSupplementElectric.setVisibility(View.GONE);
                        }

                    }

                    @Override
                    public void onError(Throwable e) {
                        super.onError(e);
                        dismissProgressDialog();
                    }

                });
    }


    /**
     * 获取新能源车辆限速数据
     */
    private void getEnergyCarsLimitSpeed() {
        L00bangRequestManager2.createUseConnectionTimeOutService()
                .getLimitSpeedList()
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new NormalSubscriber<List<LimitSpeed>>(this) {
                    @Override
                    public void onSuccess(final List<LimitSpeed> limitSpeeds) {
                        super.onSuccess(limitSpeeds);
                        ChooseLimitSpeedDialog chooseLimitSpeedDialog = new ChooseLimitSpeedDialog(CarConditionActivity.this,
                                limitSpeeds, new BaseListDialog.OnChoiceClickListener() {
                            @Override
                            public boolean onChoiceClick(int chosenIndex, String chosenText) {
                                ToastUtil.showMessage(CarConditionActivity.this, "正在发起车辆限速设置，稍后请刷新页面查看结果");
                                setLimitSpeed(limitSpeeds.get(chosenIndex).getValue(), chosenText);
                                return false;
                            }
                        });
                        chooseLimitSpeedDialog.show();
                    }

                });

    }

    /**
     * 获取新能源车辆限速数据
     */
    private void setLimitSpeed(Integer limitSpeed, final String speedStr) {
        L00bangRequestManager2.getServiceInstance()
                .setLimitSpeed(mDefaultCarInfo.getVin(), limitSpeed)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new BackgroundSubscriber<Boolean>(this) {
                    @Override
                    public void onSuccess(Boolean booleans) {
                        super.onSuccess(booleans);
                        if (booleans) {
                            runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    mTvLimitSpeed.setText(speedStr);
                                    ToastUtil.showMessage(CarConditionActivity.this, "设置成功\n当前最高车速为" + speedStr);
                                }
                            });
                        }
                    }

                    @Override
                    protected boolean showToastOnError() {
                        return true;
                    }
                });

    }


    private void toastMessage(String s) {
        ToastUtil.showMessage(this, s);
    }

    @Override
    protected void onStart() {
        super.onStart();
        SensorsUtils.sensorsViewStart(FinalSensors.ENERGY_VEHICLE_PAGE);
    }

    /**
     * 离开页面的时候设置默认爱车
     * 如果不是优先显示的就设置
     */
    @Override
    protected void onStop() {
        super.onStop();
        if (mDefaultCarInfo != null && !mDefaultCarInfo.isPriorityShow()) {
            SensorsUtils.sensorsViewEndNew(FinalSensors.ENERGY_VEHICLE_PAGE_NAME, FinalSensors.ENERGY_VEHICLE_PAGE, "浏览详情页" + mDefaultCarInfo == null ? "" : mDefaultCarInfo.getCarTypeName());
            setDefaultCar();
        }

    }

    private void setDefaultCar() {
        L00bangRequestManager2.getServiceInstance()
                .setEnergyFavoriteCar(mDefaultCarInfo.getEnergyCarIdOrZero())
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new BackgroundSubscriber<Object>(this) {
                    @Override
                    public void onSuccess(Object object) {
                        super.onSuccess(object);
                    }
                });

    }

    @Override
    public void onRefresh() {
        if (sgmwDefaultCarStatInfo != null) {
            //如果大于一分钟，则重新请求数据
            mProgressDialogTool.initProgressDialog();
            getBasicCarInfo();
            getYesterdayMileage();
            mSwipeToLoadLayout2.setRefreshing(false);
        } else {
            mProgressDialogTool.initProgressDialog();
            getBasicCarInfo();
            getYesterdayMileage();
        }
    }

    /**
     * 保存标签信息至草稿
     */
    private void savePackingState() {
        Gson gson = new Gson();
        String savePackingStateSPStr = gson.toJson(savePackingStateSP);
        if (!TextUtils.isEmpty(savePackingStateSPStr)) {
            Log.e("保温按钮------", "转换成json之后：====" + savePackingStateSPStr);
            SGMWDefaultCarStatInfo temp_sgmwDefaultCarStatInfo = gson.fromJson(savePackingStateSPStr, SGMWDefaultCarStatInfo.class);
            Log.e("保温按钮------", "转换回对象之后：====" + temp_sgmwDefaultCarStatInfo.toString());
            PreferenceUtil.putPreference(CarConditionActivity.this, KEY_PACKING_STATE, savePackingStateSPStr);
        }
    }

    public void dismissProgressDialog() {
        if (mProgressDialogTool != null) {
            mProgressDialogTool.dismissProgressDialog();
        }
    }


    @Override
    public void onCancelProgress() {

    }
}
