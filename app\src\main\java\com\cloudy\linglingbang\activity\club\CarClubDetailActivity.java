package com.cloudy.linglingbang.activity.club;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.community.CommunityMemberActivity;
import com.cloudy.linglingbang.activity.community.MoreProvider;
import com.cloudy.linglingbang.activity.fragment.club.ClubDetailIndicatorFragment;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.DeprecatedUtils;
import com.cloudy.linglingbang.app.util.DeviceUtil;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.ShareUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog;
import com.cloudy.linglingbang.app.widget.textview.PressEffectiveButton;
import com.cloudy.linglingbang.constants.WebUrlConfigConstant;
import com.cloudy.linglingbang.model.channel.Channel;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.NormalSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.umeng.analytics.MobclickAgent;

import java.util.ArrayList;
import java.util.List;

import androidx.core.view.MenuItemCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import butterknife.BindView;
import butterknife.OnClick;
import io.reactivex.rxjava3.core.Observable;

import static com.cloudy.linglingbang.R.id.ll_manage_club;

/**
 * 车友会首页
 *
 * <AUTHOR>
 * @date 2017/11/13
 */
public class CarClubDetailActivity extends BaseActivity implements View.OnClickListener {
    /**
     * 车友会头像按钮
     */
    @BindView(R.id.iv_riders_head)
    ImageView mIvRidersHead;

    /**
     * 申请加入按钮
     */
    @BindView(R.id.btn_apply_join)
    PressEffectiveButton mBtnApplyJoin;

    /**
     * 车友会名称
     */
    @BindView(R.id.tv_club_name)
    TextView mTvClubName;

    /**
     * 帖子数
     */
    @BindView(R.id.tv_post_count)
    TextView mTvPostCount;

    /**
     * 活动数
     */
    @BindView(R.id.tv_activity_count)
    TextView mTvActivityCount;

    /**
     * 车友会等级
     */
    @BindView(R.id.iv_club_gradle)
    ImageView mIvClubGradle;

    /**
     * 人数
     */
    @BindView(R.id.tv_member_count)
    TextView mTvMemberCount;

    /**
     * 车友会Id
     */
    private Long mChannelId;
    /**
     * 车友会
     */
    private Channel mChannel;
    private PopupWindow mPopWindow;
    private MoreProvider mMessageProvider;
    private boolean isLoadPoint;//是否加载过红点信息，防止重复加载
    private FragmentManager fragmentManager;
    private Fragment fragment;
    private ShareUtil mShareUtil;

    public static final int REQUEST_REFRESH_POINT = 1;//车友会主页刷新小红点的requestCode
    public static final int REQUEST_REFRESH_STATUS = 2;//申请状态修改，用于更改取消申请按钮状态

    public static final String EXTRA_CHANNEL_ID = "channelId";

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_car_club_detail);
    }

    @Override
    protected void initialize() {
        setMiddleTitle("");
        mChannelId = getOrParseIntentLongExtra(EXTRA_CHANNEL_ID);
        if (mChannelId == 0) {
            ToastUtil.showMessage(this, getString(R.string.club_get_id_error));
            return;
        }
        fragmentManager = getSupportFragmentManager();
        fragment = fragmentManager.findFragmentById(R.id.content);
        if (fragment == null) {
            addListFragment();//添加下面的列表
        }
        requestClubData();

    }

    /**
     * 添加下部分列表fragment
     */
    protected void addListFragment() {
        FragmentTransaction transaction = fragmentManager.beginTransaction();
        fragment = ClubDetailIndicatorFragment.newInstance(mChannelId.toString());
        //设置监听，显示和隐藏发帖按钮
        if (fragment instanceof ClubDetailIndicatorFragment) {
            ((ClubDetailIndicatorFragment) fragment).setOnPageChageListenr(new ClubDetailIndicatorFragment.OnSelectPostPageListener() {
                @Override
                public void onPostPageShow() {
                    if (mChannel != null && mChannel.getIsAttended() == 1) {
                        setPostButtonVisibility(View.VISIBLE);
                    } else {
                        setPostButtonVisibility(View.GONE);
                    }
                }
            });
        }
        transaction.add(R.id.content, fragment);
        transaction.show(fragment);
        transaction.commit();
    }

    /**
     * 请求车友会数据
     */
    private void requestClubData() {
        Observable<BaseResponse<Channel>> result = L00bangRequestManager2
                .getServiceInstance()
                .getChannelDetails(mChannelId.toString());
        L00bangRequestManager2
                .setSchedulers(result)
                .subscribe(new ProgressSubscriber<Channel>(this) {
                    @Override
                    public void onSuccess(Channel channel) {
                        super.onSuccess(channel);
                        mChannel = channel;
                        if (mChannel != null) {
                            fillData();
                        }
                    }
                });
    }

    private void fillData() {
        String url = AppUtil.getImageUrlBySize(mChannel.getChannelFavicon(), AppUtil._120X120);
        new ImageLoad(mIvRidersHead, url)
                .setUserMemoryCache(false)
                .setPlaceholderAndError(R.drawable.ic_common_place_holder)
                .load();
//        ImageLoader.getInstance().displayImage(url, mIvRidersHead, ImageLoaderUtils.getOptionByCommunity());
        mTvClubName.setText(mChannel.getChannelName());
        if (mChannel.getIsAttended() == 0) {
            mBtnApplyJoin.setVisibility(View.VISIBLE);
        } else {
            mBtnApplyJoin.setVisibility(View.INVISIBLE);
        }

        int level = mChannel.getCarClubLevel();
        mIvClubGradle.setImageResource(R.drawable.ic_car_club_level);
        mIvClubGradle.setImageLevel(level);

        mTvPostCount.setText(getResources().getString(R.string.club_post_count, AppUtil.getCommentDesc(mChannel.getPostCount())));
        mTvActivityCount.setText(getResources().getString(R.string.club_activity_count, AppUtil.getCommentDesc(mChannel.getActivityCount())));
        mTvMemberCount.setText(AppUtil.getCommentDesc(mChannel.getMemberCount()));
        if (!isLoadPoint && mMessageProvider != null && mChannel.getUnReadMsgCount() > 0) {
            mMessageProvider.setUnreadPointVisible(View.VISIBLE);
        }
        setShowAttentionStatus();

    }

    /**
     * 设置申请加入按钮状态
     */
    private void setShowAttentionStatus() {
        if (mChannel.getIsAttended() != 1) {
            mBtnApplyJoin.setVisibility(View.VISIBLE);
            //如果未加入，则显示申请加入
            if (mChannel.getIsAttended() == 0) {
                mBtnApplyJoin.setText(R.string.club_apply_join);
            } else {
                mBtnApplyJoin.setText(R.string.club_apply_cancel);
            }
            setPostButtonVisibility(View.GONE);
        } else {
            mBtnApplyJoin.setVisibility(View.INVISIBLE);
            setPostButtonVisibility(View.VISIBLE);
        }
    }

    /**
     * 设置发帖按钮的隐藏和显示
     *
     * @param visible
     */
    private void setPostButtonVisibility(int visible) {
        if (fragment != null && fragment instanceof ClubDetailIndicatorFragment) {
            ((ClubDetailIndicatorFragment) fragment).setPostButtonVisibility(visible);
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(R.menu.menu_more, menu);
        MenuItem item = menu.findItem(R.id.it_message);
        mMessageProvider = (MoreProvider) MenuItemCompat.getActionProvider(item);
        mMessageProvider.setCallBackListener(new MoreProvider.OnListener() {
            @Override
            public void execute() {
                if (AppUtil.checkLogin(CarClubDetailActivity.this)) {
                    //友盟统计
                    MobclickAgent.onEvent(CarClubDetailActivity.this, "309");
                    if (mChannel != null) {
                        showPopup();
                    } else {
                        ToastUtil.showMessage(CarClubDetailActivity.this, getString(R.string.club_data_uploading));
                    }
                }
            }
        });
        return true;
    }

    /**
     * 展示申请的popupWindow
     */
    private void showPopup() {
        View contentView = DeprecatedUtils.inflateWithNullRoot(LayoutInflater.from(this), R.layout.pop_window_club_detail);
        mPopWindow = new PopupWindow(contentView);
        mPopWindow.setWidth(ViewGroup.LayoutParams.WRAP_CONTENT);
        mPopWindow.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
        mPopWindow.setFocusable(true);
        mPopWindow.setBackgroundDrawable(DeprecatedUtils.createEmptyBitmapDrawable());
        mPopWindow.showAsDropDown(mToolbar, DeviceUtil.getScreenWidth(this), 0);
        final WindowManager.LayoutParams params = getWindow().getAttributes();
        params.alpha = 0.7f;
        getWindow().setAttributes(params);
        mPopWindow.setOnDismissListener(new PopupWindow.OnDismissListener() {//设置popupWindow关闭监听
            @Override
            public void onDismiss() {
                params.alpha = 1f;
                getWindow().setAttributes(params);
            }
        });
        contentView.findViewById(ll_manage_club).setOnClickListener(this);
        contentView.findViewById(R.id.ll_share_club).setOnClickListener(this);
        contentView.findViewById(R.id.ll_exit_club).setOnClickListener(this);
        if (mChannel != null) {
            //如果是会长或者副会长,显示管理
            if (mChannel.getUserRoleId() == Channel.RoleType.PRESIDENT || mChannel.getUserRoleId() == Channel.RoleType.VICE_PRESIDENT) {
                contentView.findViewById(R.id.ll_manage_club).setVisibility(View.VISIBLE);
                contentView.findViewById(R.id.view).setVisibility(View.VISIBLE);

            }
            //已经加入的并且不是会长的，显示退出
            if (mChannel.getUserRoleId() != Channel.RoleType.PRESIDENT && mChannel.getIsAttended() == 1) {
                contentView.findViewById(R.id.view_1).setVisibility(View.VISIBLE);
                contentView.findViewById(R.id.ll_exit_club).setVisibility(View.VISIBLE);
            }
            if (mChannel.getUnReadMsgCount() > 0) {
                contentView.findViewById(R.id.iv_manage_point).setVisibility(View.VISIBLE);
            }
        }
    }

    public static void startActivity(Context context, Long clubId) {
        Intent intent = new Intent(context, CarClubDetailActivity.class);
        intent.putExtra(EXTRA_CHANNEL_ID, clubId);
        context.startActivity(intent);
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        //当页面加载完成时，设定图标值
        if (hasFocus && mChannel != null && !isLoadPoint) {
            if (mMessageProvider != null && mChannel.getUnReadMsgCount() > 0) {
                mMessageProvider.setUnreadPointVisible(View.VISIBLE);
            }
            isLoadPoint = true;
        }
    }

    /**
     * 进入车友会会员页面
     */
    @OnClick(R.id.ll_member_count)
    protected void onMemberCountClick(View view) {
        CommunityMemberActivity.goMember(this, mChannel, CommunityMemberActivity.TYPE_CLUB);
    }

    @OnClick(R.id.iv_club_gradle)
    protected void clubGradle(View view) {
//        ToastUtil.showMessage(getApplicationContext(), "等级详情---敬请期待哦");
        JumpPageUtil.goCommonWebWithFormat(this, WebUrlConfigConstant.BALANCE_CAR_CLUB_LEVEL, String.valueOf(mChannelId));
    }

    /**
     * 点击申请加入或者取消申请
     */
    @OnClick(R.id.btn_apply_join)
    protected void applyJoinClick(View view) {
        if (mChannel == null) {
            return;
        }
        if (AppUtil.checkLogin(this)) {
            //是申请加入，则再次判断状态，可以申请，则跳转到申请页面
            if (mChannel.getIsAttended() == 0) {
                //添加友盟统计
                MobclickAgent.onEvent(this, "308");
                Observable<BaseResponse<Object>> result = L00bangRequestManager2
                        .getServiceInstance()
                        .checkHasJoinCarClub();
                L00bangRequestManager2
                        .setSchedulers(result)
                        .subscribe(new NormalSubscriber<Object>(this) {
                            @Override
                            public void onSuccess(Object channel) {
                                super.onSuccess(channel);
                                IntentUtils.startActivityForResult(CarClubDetailActivity.this, ApplyJoinClubActivity.class, REQUEST_REFRESH_STATUS, mChannelId);
                            }
                        });

            } else {
                //否则，如果是取消申请
                //添加友盟统计
                MobclickAgent.onEvent(this, "318");
                Dialog dialog = new CommonAlertDialog(CarClubDetailActivity.this, getString(R.string.club_cancel_join_mack_sure), new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        L00bangRequestManager2
                                .getServiceInstance()
                                .cancelJoinApply()
                                .compose(L00bangRequestManager2.setSchedulers())
                                .subscribe(new NormalSubscriber<Object>(CarClubDetailActivity.this) {
                                    @Override
                                    public void onSuccess(Object o) {
                                        super.onSuccess(o);
                                        ToastUtil.showMessage(CarClubDetailActivity.this, getString(R.string.club_cancel_join_success));
                                        mChannel.setIsAttended(0);
                                        setShowAttentionStatus();
                                    }
                                });
                    }
                });
                dialog.show();
            }
        }

    }

    @Override
    public void onClick(View v) {
        if (mPopWindow != null) {
            mPopWindow.dismiss();
        }
        switch (v.getId()) {
            case ll_manage_club:
                if (mChannel != null) {
                    CarClubManageActivity.startActivity(this, mChannel, REQUEST_REFRESH_POINT);
                }
                //添加友盟统计
                MobclickAgent.onEvent(this, "311");
                break;
            case R.id.ll_share_club://点击分享
                shareCommunity();
                //添加友盟统计
                MobclickAgent.onEvent(this, "310");
                break;
            case R.id.ll_exit_club://退出车友会
                Dialog dialog = new CommonAlertDialog(this, getString(R.string.club_make_sure_exit), new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        exitClub();
                    }
                });
                dialog.show();
                //添加友盟统计
                MobclickAgent.onEvent(this, "319");
                break;
        }

    }

    /**
     * 退出车友会
     */
    private void exitClub() {
        L00bangRequestManager2.getServiceInstance()
                .exitChannel(mChannel.getChannelId())
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<String>(CarClubDetailActivity.this) {
                    @Override
                    public void onSuccess(String s) {
                        super.onSuccess(s);
                        ToastUtil.showMessage(CarClubDetailActivity.this, getString(R.string.club_exit_success));
                        mChannel.setIsAttended(0);
                        mChannel.setUserRoleId(Channel.RoleType.MEMBER);
                        setShowAttentionStatus();
                    }
                });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            //从车友会管理返回如果在车友会管理点击了会员申请，则清空小红点
            //如果是更新小红点状态
            if (requestCode == REQUEST_REFRESH_POINT) {
                int count = data.getIntExtra(CarClubManageActivity.EXTRA_UNREAD_COUNT, 0);
                mChannel.setUnReadMsgCount(count);
                if (mMessageProvider != null) {
                    if (mChannel.getUnReadMsgCount() == 0) {
                        mMessageProvider.setUnreadPointVisible(View.GONE);
                    } else {
                        mMessageProvider.setUnreadPointVisible(View.VISIBLE);
                    }
                }//如果是更新申请按钮状态
            } else if (requestCode == REQUEST_REFRESH_STATUS) {
                mChannel.setIsAttended(2);
                setShowAttentionStatus();
            } else {
                if (mShareUtil != null) {
                    mShareUtil.onActivityResult(requestCode, resultCode, data);
                }

            }
        }

    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        Long channelId = intent.getLongExtra("channelId", 0);
        if (channelId != 0) {
            this.mChannelId = channelId;
            //重新请求车友会数据
            requestClubData();
            //重新加载列表
            ((ClubDetailIndicatorFragment) fragment).refreshList();
        }
    }

    /**
     * 分享车友会
     */
    private void shareCommunity() {
        String url;
        final String content;
        if (mChannel.getShareUrl() != null) {
            url = mChannel.getShareUrl();
        } else {
            url = WebUrlConfigConstant.SHARE_LLB;
        }
        content = getString(R.string.club_share_desc);
        List<String> imagePath = new ArrayList<>();
        String shareImg = AppUtil.getImageUrlBySize(mChannel.getChannelFavicon(), AppUtil._120X120);
        if (shareImg != null) {
            imagePath.add(shareImg);
        }
        String title = mChannel.getChannelName();
        mShareUtil = ShareUtil.newInstance();
        mShareUtil.setShareType(ShareUtil.ShareType.SHARE_TYPE_COMMUNITY);
        mShareUtil.share(this, url, content, imagePath, title, false);
    }

}
