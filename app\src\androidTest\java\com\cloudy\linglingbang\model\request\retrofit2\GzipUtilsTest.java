package com.cloudy.linglingbang.model.request.retrofit2;

import com.cloudy.linglingbang.BaseInstrumentedTest;
import com.cloudy.linglingbang.app.log.LogUtils;

import org.junit.Assert;

/**
 * <AUTHOR>
 * @date 2019/5/14
 */
public class GzipUtilsTest extends BaseInstrumentedTest {
    @Override
    public void test() {
        super.test();
        String source = "test";
        String compressStr = GzipUtils.compress(source);
        LogUtils.d("compressStr:" + compressStr);
        String decompressStr = GzipUtils.decompress(compressStr);
        LogUtils.d("decompressStr:" + decompressStr);
        Assert.assertEquals(source, decompressStr);
    }
}