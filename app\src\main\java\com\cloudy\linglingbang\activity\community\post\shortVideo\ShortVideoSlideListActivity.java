package com.cloudy.linglingbang.activity.community.post.shortVideo;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.aliyun.player.IPlayer;
import com.bumptech.glide.Glide;
import com.cloudy.aliyunshortvideo.JniLibUtil;
import com.cloudy.aliyunshortvideo.widget.ShortVideoPostPlayer;
import com.cloudy.linglingbang.ApplicationLLB;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.app.util.NetworkUtil;
import com.cloudy.linglingbang.app.util.ShareUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.constants.WebUrlConfigConstant;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BackgroundSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.NormalSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.user.User;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.OrientationHelper;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import butterknife.OnClick;

/**
 * 滑动的短视频帖
 *
 * <AUTHOR>
 * @date 2018/11/14
 */
public class ShortVideoSlideListActivity extends BaseActivity implements ShortVideoPostPlayer.OnVideoOperateListener {
    /**
     * 列表
     */
    @BindView(R.id.rv_video_list)
    RecyclerView mRecyclerView;

    /**
     * 返回键
     */
    @BindView(R.id.iv_back)
    ImageView mIvBack;

    public static final String INTENT_EXTRA_COLUMN_ID = "INTENT_EXTRA_COLUMN_ID";

    /**
     * 帖子列表
     */
    List<PostCard> mPostCards;

    public static final String TAG = "ViewPagerActivity";
    private ViewPagerLayoutManager mLayoutManager;
    private ShortVideoAdapter mAdapter;
    private int mCurrentPosition;
    private ShortVideoPlayerManager mPlayerManager;
    private long mPostId;
    private int shareVisible;
    private ShareUtil mShareUtil;
    private boolean mIsPlayed;
    private int mPageNo;
    private final int mPageSize = 20;
    private long mColumnId;
    private Runnable mRunnable;
    //帖子打开来源
    private int mOpenFrom;
    /**
     * 是否加载更多
     */
    private boolean mIsLoadMore;
    /**
     * 是否正在加载
     */
    private boolean mIsRefreshing;

    public static class openFrom {
        /**
         * 正常进入
         */
        public static final int VIDEO_FROM_NORMAL = 0;

        /**
         * 从推荐列表进入
         */
        public static final int VIDEO_FROM_RECOMMEND = 1;

    }

    @Override
    protected void loadViewLayout() {
        getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
        setFullScreen(true);
        setContentView(R.layout.activity_short_video_slide_list);
    }

    /**
     * 跳转到短视频帖子详情
     *
     * @param postId 帖子id
     */
    public static void startActivity(Context activity, String postId) {
        Intent intent = new Intent(activity, ShortVideoSlideListActivity.class);
        intent.putExtra("postId", postId);
        activity.startActivity(intent);
    }

    public static void startActivity(Context activity, String postId, int shareVisible) {
        Intent intent = new Intent(activity, ShortVideoSlideListActivity.class);
        intent.putExtra("postId", postId);
        intent.putExtra("shareVisible", shareVisible);
        activity.startActivity(intent);
    }

    /**
     * 跳转到短视频详情滑动列表
     *
     * @param postCards 帖子列表
     * @param position 当前播放位置
     */
    public static void startActivity(Context context, List<PostCard> postCards, int position) {
        VideoListManager.getInstance().setList(postCards, position);
        IntentUtils.startActivity(context, ShortVideoSlideListActivity.class);
    }

    /**
     * 跳转到短视频详情滑动列表
     *
     * @param postCards 帖子列表
     * @param position 当前播放位置
     */
    public static void startActivity(Context context, List<PostCard> postCards, int position, long columnId) {
        VideoListManager.getInstance().setList(postCards, position);
        Intent intent = new Intent(context, ShortVideoSlideListActivity.class);
        intent.putExtra(INTENT_EXTRA_COLUMN_ID, columnId);
        context.startActivity(intent);
    }

    @Override
    protected void onStart() {
        super.onStart();
        //神策埋点
        //SensorsUtils.sensorsViewStart(FinalSensors.POST_DETAIL);
    }

    @Override
    protected void initialize() {
        //模拟帖子列表数据
        mPostCards = new ArrayList<>();
        mPlayerManager = ShortVideoPlayerManager.getInstance(this);
        mPlayerManager.setOnVideoOperateListener(this);
        mLayoutManager = new ViewPagerLayoutManager(this, OrientationHelper.VERTICAL);
        mAdapter = new ShortVideoAdapter(this, mPostCards);
        mRecyclerView.setLayoutManager(mLayoutManager);
        mRecyclerView.setAdapter(mAdapter);
        initListener();

        String postIdString = getIntent().getStringExtra("postId");
        shareVisible = getIntent().getIntExtra("shareVisible", 0);
        mColumnId = getIntent().getLongExtra(INTENT_EXTRA_COLUMN_ID, 0);
        //如果postId不为空，则说明是帖子详情页面
        if (postIdString != null) {
            try {
                mPostId = Long.valueOf(postIdString);
                mOpenFrom = openFrom.VIDEO_FROM_NORMAL;
            } catch (Exception exception) {
                exception.printStackTrace();
            }
            requestData();
        } else {
            //否则是列表页面
            mOpenFrom = openFrom.VIDEO_FROM_RECOMMEND;
            int count = VideoListManager.getInstance().getCardList().size();
            final int position = VideoListManager.getInstance().getPosition();
            final List<PostCard> list = VideoListManager.getInstance().getCardList();
            mCurrentPosition = position;
            if (count > 0 && position < count) {
                mPostCards.addAll(list);
                mAdapter.notifyDataSetChanged();
                mRecyclerView.scrollToPosition(mCurrentPosition);
                //mColumnId不为0，则说明是列表；如果%20=0，则说明刚好请求完一页，可以加载更多
                if (mColumnId != 0 && count % 20 == 0) {
                    mIsLoadMore = true;
                    mPageNo = count / 20 + 1;
                    //如果刚好是最后一条，则调用加载更多
                    if (position == count - 1) {
                        requestListData();
                    }
                } else {
                    mIsLoadMore = false;
                }
            }

        }
    }

    /**
     * 请求帖子数据
     */
    private void requestData() {
        L00bangRequestManager2
                .getServiceInstance()
                .getPostDetails(mPostId)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<PostCard>(this) {
                    @Override
                    public void onSuccess(PostCard postCard) {
                        super.onSuccess(postCard);
                        if (postCard != null) {
                            mPostCards.add(postCard);
                            mAdapter.notifyDataSetChanged();
                        }
                    }
                });
    }

    private void initListener() {
        mLayoutManager.setOnViewPagerListener(new OnViewPagerListener() {
            @Override
            public void onInitComplete() {
                Log.e(TAG, "onInitComplete" + mCurrentPosition);
                if (!mIsPlayed) {
                    Log.e(TAG, "onInitCompletePlay" + mCurrentPosition);
                    prePlayVideo(mCurrentPosition);
                    mIsPlayed = true;
                }
            }

            @Override
            public void onPageRelease(boolean isNext, int position) {
                Log.e(TAG, "释放位置:" + position + " 下一页:" + isNext);
                int index = 0;
                if (isNext) {
                    index = 0;
                } else {
                    index = 1;
                }
                mPlayerManager.onPositionRelease();
                View itemView = mRecyclerView.getChildAt(index);
                ShortVideoPostViewHolder shortVideoPostViewHolder = (ShortVideoPostViewHolder) mRecyclerView.getChildViewHolder(itemView);
                if (shortVideoPostViewHolder == null) {
                    return;
                }
                shortVideoPostViewHolder.mRlWifiHint.setVisibility(View.GONE);
                shortVideoPostViewHolder.mRlVideo.removeAllViews();
                if (mRunnable != null) {
                    shortVideoPostViewHolder.mRlVideo.removeCallbacks(mRunnable);
                }
//                releaseVideo(index);
//                mPlayerManager.removePlayer();
            }

            @Override
            public void onPageSelected(int position, boolean isBottom) {
                mPlayerManager.onPageSelect();
                Log.e(TAG, "选中位置:" + position + "  是否是滑动到底部:" + isBottom);
                //如果和上一个相同，则说明没有滑动到下一个，停止的时候继续播放这个视频
                if (mCurrentPosition != position || mPlayerManager.getPlayerView().getPlayerState() != IPlayer.started) {
                    prePlayVideo(position);
                    //如果可以加载更多，则在倒数第三条的时候开始加载
                    if (mIsLoadMore && mPostCards.size() - position < 4 && !mIsRefreshing) {
                        requestListData();
                    }
                }
                /*else{
                    mPlayerManager.getPlayerView().start();
                }*/
                mCurrentPosition = position;
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                    if (!ShortVideoSlideListActivity.this.isDestroyed()) {
                        Glide.with(ShortVideoSlideListActivity.this).resumeRequests();
                    }
                }
                Log.e(TAG, "onPageSelected" + position);
            }

            @Override
            public void onPageScrolled() {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                    if (!ShortVideoSlideListActivity.this.isDestroyed()) {
                        Glide.with(ShortVideoSlideListActivity.this).pauseRequests();
                    }
                }

                Log.e(TAG, "onPageScrolled");
                //滑动的时候暂停播放视频
//                mPlayerManager.getPlayerView().pause();
                mPlayerManager.onPageScrolled();
            }
        });
    }

    /**
     * 请求列表数据
     */
    private void requestListData() {
        Log.e(TAG, "request");
        mIsRefreshing = true;
        L00bangRequestManager2
                .getServiceInstance()
                .getPostColumns(mColumnId, mPageNo, mPageSize)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new NormalSubscriber<List<PostCard>>(this) {
                    @Override
                    public void onSuccess(List<PostCard> postCards) {
                        super.onSuccess(postCards);
                        if (postCards != null && postCards.size() > 0) {
                            int position = mPostCards.size();
                            mPostCards.addAll(postCards);
                            mAdapter.notifyItemRangeInserted(position, postCards.size());
                            mIsRefreshing = false;
                            if (postCards.size() < mPageSize) {
                                mIsLoadMore = false;
                            } else {
                                mPageNo++;
                            }
                        } else {
                            mIsLoadMore = false;
                            mIsRefreshing = false;
                        }
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                        ToastUtil.showMessage(ShortVideoSlideListActivity.this, "加载失败~");
                    }
                });
    }

    @OnClick(R.id.iv_back)
    protected void onBackClick(View view) {
        onBack();
    }

    /**
     * 播放视频前准备(判断网络环境等)
     */
    private void prePlayVideo(final int position) {
        View itemView = mRecyclerView.getChildAt(0);
        final ShortVideoPostViewHolder shortVideoPostViewHolder = (ShortVideoPostViewHolder) mRecyclerView.getChildViewHolder(itemView);
        if (shortVideoPostViewHolder == null) {
            return;
        }

        final RelativeLayout rlContainer = shortVideoPostViewHolder.mRlVideo;
        if (NetworkUtil.getNetWorkStates(this) != NetworkUtil.TYPE_WIFI && !ApplicationLLB.getInstance().isAllowPlayWithoutWifi()) {
            shortVideoPostViewHolder.mRlWifiHint.setVisibility(View.VISIBLE);
            shortVideoPostViewHolder.mBtnPlayContinue.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    doPlay(position, rlContainer);
                    shortVideoPostViewHolder.mRlWifiHint.setVisibility(View.GONE);
                    ApplicationLLB.getInstance().setAllowPlayWithoutWifi(true);

                }
            });
            return;
        }
        shortVideoPostViewHolder.mRlWifiHint.setVisibility(View.GONE);
        doPlay(position, rlContainer);
    }

    /**
     * 播放视频
     */
    private void doPlay(final int position, final RelativeLayout rlContainer) {
        if (mPostCards.get(position).getImgTexts().get(0).getImg() != null) {
            mPlayerManager.getPlayerView().showCover(mPostCards.get(position).getImgTexts().get(0).getImg());
        }
        if (rlContainer != null) {
            if (!JniLibUtil.loadAliFfmpegLib(this)) {
                return;
            }
            mRunnable = new Runnable() {
                @Override
                public void run() {
                    try {
                        String videoId = mPostCards.get(position).getImgTexts().get(0).getVideoId();
                        mPlayerManager.play(videoId, mPostCards.get(position).getImgTexts().get(0).getImg());
                        rlContainer.addView(mPlayerManager.getPlayerView());
                        //增加围观数
                        requestReadCount(position);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            };

            rlContainer.postDelayed(mRunnable, 500);
        }
    }

    /**
     * 请求服务器增加帖子阅读数
     */
    private void requestReadCount(int position) {
        if (mOpenFrom == ShortVideoSlideListActivity.openFrom.VIDEO_FROM_RECOMMEND) {
            L00bangRequestManager2.getServiceInstance()
                    .updatePostShowCount(Long.valueOf(mPostCards.get(position).getPostId()))
                    .compose(L00bangRequestManager2.setSchedulers())
                    .subscribe(new BackgroundSubscriber<Object>(this) {
                        @Override
                        public void onSuccess(Object o) {
                            super.onSuccess(o);
                        }
                    });
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (mPlayerManager.getPlayerView() != null) {
            mPlayerManager.getPlayerView().pause();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (mPlayerManager.getPlayerView() != null) {
            mPlayerManager.getPlayerView().start();
        }
    }

    /**
     * 释放视频，在manager中处理，该方法不再调用
     *
     * @param index
     */
    private void releaseVideo(int index) {
        View itemView = mRecyclerView.getChildAt(index);
        // TODO: [hanfei create at 2018/11/15] 判断为空
        ShortVideoPostViewHolder shortVideoPostViewHolder = (ShortVideoPostViewHolder) mRecyclerView.getChildViewHolder(itemView);
        if (mPlayerManager.getPlayerView() != null) {
            try {
                mPlayerManager.getPlayerView().stop();
                mPlayerManager.getPlayerView().release();
            } catch (Exception e) {
                e.printStackTrace();
            }
            Log.d("111111", "释放了");
            shortVideoPostViewHolder.mRlVideo.removeView(mPlayerManager.getPlayerView());
            removeVideoView();
        }
    }

    private void removeVideoView() {
        LinearLayoutManager layoutManager = (LinearLayoutManager) mRecyclerView.getLayoutManager();
        for (int i = 0; i < layoutManager.getChildCount(); i++) {
            View childAt = layoutManager.getChildAt(i);
            if (childAt != null) {
                RecyclerView.ViewHolder childViewHolder = mRecyclerView.getChildViewHolder(childAt);
                if (childViewHolder instanceof ShortVideoPostViewHolder) {
                    ((ShortVideoPostViewHolder) childViewHolder).mRlVideo.removeAllViews();

                   /* for (int j = 0; j < ((ShortVideoPostViewHolder) childViewHolder).mRlVideo.getChildCount(); j++) {
                        if (((ShortVideoPostViewHolder) childViewHolder).mRlVideo.getChildAt(j) instanceof AliyunVodPlayerView) {
                            ((AliyunVodPlayerView) (((ShortVideoPostViewHolder) childViewHolder).mRlVideo.getChildAt(j))).stop();
                            ((AliyunVodPlayerView) (((ShortVideoPostViewHolder) childViewHolder).mRlVideo.getChildAt(j))).release();
                            ((ShortVideoPostViewHolder) childViewHolder).mRlVideo.removeAllViews();
                        }
                    }*/
//                        ((ShortVideoSlideListActivity.ViewHolder)childViewHolder).mRlVideo.removeView(mPlayerManager.getPlayerView());
                }
            }
        }
    }

    /**
     * 双击视频执行该操作
     */
    @Override
    public void onDoubleClick() {
        if (mPostCards != null && mPostCards.size() > mCurrentPosition) {
            onPraiseClick(mCurrentPosition);
        }
    }

    @Override
    public void onPermissionExpired() {

    }

    class ShortVideoAdapter extends BaseRecyclerViewAdapter<PostCard> {

        public ShortVideoAdapter(Context context, List<PostCard> data) {
            super(context, data);
        }

        @Override
        protected BaseRecyclerViewHolder<PostCard> createViewHolder(View itemView) {
            BaseRecyclerViewHolder<PostCard> viewHolder = new ShortVideoPostViewHolder(itemView, ShortVideoSlideListActivity.this, mOpenFrom, shareVisible);
            viewHolder.setAdapter(this);
            return viewHolder;
        }

        @Override
        protected int getItemLayoutRes(int viewType) {
            return R.layout.item_short_video_slide;
        }
    }

    /**
     * 设置全屏显示（隐藏状态栏）
     */
    private void setFullScreen(boolean enable) {
        if (enable) {
            WindowManager.LayoutParams attrs = getWindow().getAttributes();
            attrs.flags |= WindowManager.LayoutParams.FLAG_FULLSCREEN;
            getWindow().setAttributes(attrs);
        } else {
            WindowManager.LayoutParams attrs = getWindow().getAttributes();
            attrs.flags &= ~WindowManager.LayoutParams.FLAG_FULLSCREEN;
            getWindow().setAttributes(attrs);
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        //神策埋点
        //目前只统计第一个帖子
        //只用正常进入时统计
        if (mOpenFrom == openFrom.VIDEO_FROM_NORMAL && mPostCards != null && mPostCards.size() > 0) {
            //SensorsChannelUtils.sensorsViewEndPostDetail(mPostCards.get(0));
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mPlayerManager != null) {
            mPlayerManager.release();
            mPlayerManager = null;
            mLayoutManager.setOnViewPagerListener(null);
        }
    }

    /**
     * 分享
     */
    public void sharePost(int position) {
        PostCard postCard = mPostCards.get(position);
        if (postCard != null) {
            if (mShareUtil == null) {
                mShareUtil = ShareUtil.newInstance();
            }
            String postContent = "";
            if (postCard.getImgTexts() != null && postCard.getImgTexts().size() > 0) {
                postContent = postCard.getImgTexts().get(0).getText();
            }
            String shareUrl;
            if (postCard.getPostTypeId() != null && postCard.getPostTypeId().equals(String.valueOf(PostCard.PostType.SHORT_VIDEO))) {
                shareUrl = String.format(Locale.getDefault(), WebUrlConfigConstant.POST_DETAIL_SHORT_VIDEO_SHARE_PATTERN, postCard.getPostId());
            } else {
                shareUrl = String.format(Locale.getDefault(), WebUrlConfigConstant.POST_DETAIL_SHARE_PATTERN, postCard.getPostId());
            }
            String shareTitle = TextUtils.isEmpty(postCard.getPostTitle()) ? getString(R.string.share_post_default_title) : postCard.getPostTitle();
            boolean isChangeTitle = false;
            //如果标题是空，咋替换标题为内容
            if (TextUtils.isEmpty(postCard.getPostTitle())) {
                isChangeTitle = true;
            }
            String[] images = postCard.getImages();
            if (shareUrl == null) {
                shareUrl = WebUrlConfigConstant.SHARE_LLB;
            } else {
                User user = User.getsUserInstance();
                if (UserUtils.hasLogin(user)) {
                    shareUrl = String.format(ShareUtil.POST_SHARE_PARAM, shareUrl, User.getsUserInstance().getUserIdStr());
                }
            }
            if (postContent == null) {
                postContent = "";
            }
            if (postContent.length() > 50) {
                postContent = postContent.substring(0, 50);
            }
            mShareUtil.setShareType(ShareUtil.ShareType.SHARE_TYPE_POST);
            mShareUtil.setShareActivityId(mPostId);
            mShareUtil.setPostType(PostCard.PostType.SHORT_VIDEO);
            mShareUtil.share(this, shareUrl, postContent, (images != null && images.length > 0) ? Arrays.asList(images) : null, shareTitle, isChangeTitle);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE_COMMON && resultCode == Activity.RESULT_OK) {
            //帖子举报成功后返回设置状态
            //若不为空则直接设置举报状态，否则重新获取
            String postId = data.getStringExtra("postId");
            if (postId != null) {
                for (PostCard postCard : mPostCards) {
                    if (postCard.getPostId().equals(postId)) {
                        postCard.setProsecutionUncheck(1);
                        mAdapter.notifyDataSetChanged();
                    }
                }
            }
        } else {
            if (mShareUtil != null) {
                mShareUtil.onActivityResult(requestCode, resultCode, data);
            }
        }

    }

    /**
     * 点赞操作
     */
    private void onPraiseClick(final int position) {
        if (!User.shareInstance().hasLogin() || mPostCards.get(position).getIsPraise() == 1) {
            //如果没有登录或者点过赞，则不做任何操作
            return;
        } else {
            View itemView = mRecyclerView.getChildAt(0);
            ShortVideoPostViewHolder shortVideoPostViewHolder = (ShortVideoPostViewHolder) mRecyclerView.getChildViewHolder(itemView);
            shortVideoPostViewHolder.onPraiseClick(mPostCards.get(position));
        }
    }
}
