package com.cloudy.linglingbang.activity.share;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.app.im.RongUserHelper;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.model.chat.GroupInfo;
import com.cloudy.linglingbang.model.chat.GroupListBean;
import com.cloudy.linglingbang.model.chat.PostShareMessage;
import com.cloudy.linglingbang.model.chat.UserInfoAttribute;
import com.cloudy.linglingbang.model.chat.UserInfoExtra;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.user.ClerkVo;
import com.cloudy.linglingbang.model.user.User;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Nullable;
import androidx.lifecycle.MediatorLiveData;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.OnClick;
import io.reactivex.rxjava3.core.Observable;
import io.rong.imkit.userinfo.RongUserInfoManager;
import io.rong.imlib.RongIMClient;
import io.rong.imlib.model.Conversation;
import io.rong.imlib.model.Group;
import io.rong.imlib.model.UserInfo;

import static com.cloudy.linglingbang.activity.share.ShareSearchUserActivity.RESULT_FINISH;

/**
 * <AUTHOR>
 * @date 2022/4/12
 */
public class ShareListActivity extends BaseRecyclerViewRefreshActivity<Object> {
    /**
     * 最近聊天
     */
    private List<Object> mNativeUserList;
    /**
     * 全部群聊
     */
    private List<Object> mNativeGroupList;
    private MediatorLiveData<Integer> mResultLiveData;

    private final static int USER_SEARCH_REQUEST_CODE = 201;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_share_list);
    }

    @Override
    protected void initialize() {
        super.initialize();
        if (getIntentExtra(null) == null) {
            onIntentExtraError();
            return;
        }
        mResultLiveData = new MediatorLiveData<>();
        mResultLiveData.observe(this, status -> {
            if (status == null) {
                return;
            }
            if (status == 0) {
                ToastUtil.showMessage(this, R.string.txt_share_success);
                //发送成功 分享成功
                finish();
            } else if (status == 1) {
                ToastUtil.showMessage(this, R.string.share_cancel);
                // 取消分享
                finish();
            } else {
                ToastUtil.showMessage(getApplicationContext(), "分享失败");
            }
        });
        findViewById(R.id.tv_cancel).setOnClickListener(v -> onBack());
    }

    @Override
    public RefreshController<Object> createRefreshController() {
        return new RefreshController<Object>(this) {
            @Override
            public void initViews(View rootView) {
                setEmptyBackgroundColorId(R.color.transparent);
                setEmptyString(rootView.getResources().getString(R.string.txt_share_list_empty_tip));
                super.initViews(rootView);
                swipeToLoadLayout.setBackgroundColor(Color.TRANSPARENT);

            }

            @Override
            public int getErrorImageResId() {
                return R.drawable.ic_post_share_place;
            }

            @Override
            public int getEmptyImageResId() {
                return getErrorImageResId();
            }

            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return null;
            }

            @Override
            protected void onLoadSuccess(int loadPage, List<Object> list, int loadType) {
                super.onLoadSuccess(loadPage, list, loadType);
                if (mNativeGroupList == null) {
                    mNativeGroupList = new ArrayList<>();
                }
                if (loadPage <= 1) {
                    mNativeGroupList.clear();
                    if (list.size() > 0) {
                        mNativeGroupList.add(getResources().getString(R.string.txt_share_list_all_group_chat));
                    }
                }
                mNativeGroupList.addAll(list);
                notifyChatDataList();
            }

            @Override
            public void getListData(int page) {
                if (page <= 1) {
                    queryChatUser();
                }
                super.getListData(page);
            }
        };
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<Object> list) {
        return new ShareListAdapter(this, list, mResultLiveData, (PostShareMessage) getIntentExtra(null));
    }

    @Override
    public Observable<BaseResponse<List<Object>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return service2.groupList(pageNo, pageSize).map(listBaseResponse -> {
            List<Object> list = new ArrayList<>();
            if (listBaseResponse.getData() != null) {
                list.addAll(listBaseResponse.getData());
            }
            return listBaseResponse.cloneWithData(list);
        });
    }

    /**
     * 处理聊天记录
     */
    private void notifyChatDataList() {
        if (getRefreshController() == null) {
            return;
        }
        int size = getRefreshController().getData().size();
        getRefreshController().getData().clear();
        if (size > 0) {
            getRefreshController().getAdapter().notifyItemRangeRemoved(0, size);
        }
        size = 0;
        //单聊
        if (mNativeUserList != null && !mNativeUserList.isEmpty()) {
            size = mNativeUserList.size();
            getRefreshController().getData().addAll(mNativeUserList);
        }
        //全部群聊
        if (mNativeGroupList != null && !mNativeGroupList.isEmpty()) {
            size = mNativeGroupList.size();
            getRefreshController().getData().addAll(mNativeGroupList);
        }
        if (size > 0) {
            getRefreshController().getAdapter().notifyItemRangeInserted(0, size);
        }
    }

    @OnClick(R.id.tv_cancel)
    void onCancel() {
        finish();
    }

    @OnClick(R.id.ed_content)
    void onToUserSearch() {
        Intent intent = getIntent();
        intent.setClass(this, ShareSearchUserActivity.class);
        IntentUtils.startActivityForResult(this, intent, USER_SEARCH_REQUEST_CODE);
    }

    /**
     * 查询3条(单聊/私聊)聊天用户
     */
    private void queryChatUser() {
        long timeStamp = 0;
        int count = 3;
        Conversation.ConversationType[] conversationTypes = {Conversation.ConversationType.PRIVATE, Conversation.ConversationType.GROUP};
        RongIMClient.getInstance().getConversationListByPage(new RongIMClient.
                ResultCallback<List<Conversation>>() {

            @Override
            public void onSuccess(List<Conversation> conversations) {
                if (conversations == null || conversations.isEmpty()) {
                    return;
                }
                if (mNativeUserList == null) {
                    mNativeUserList = new ArrayList<>(conversations.size());
                } else {
                    mNativeUserList.clear();
                }
                for (Conversation conversation : conversations) {
                    if (conversation.getConversationType() == Conversation.ConversationType.GROUP) {
                        GroupListBean groupListBean = new GroupListBean();
                        groupListBean.setGroupId(Integer.parseInt(conversation.getTargetId()));
                        makeGroupDate(groupListBean);
                        mNativeUserList.add(groupListBean);
                    } else {
                        User user = new User();
                        user.setUserIdStr(conversation.getTargetId());
                        makeUserDate(user);
                        mNativeUserList.add(user);
                    }

                }
                if (!mNativeUserList.isEmpty()) {
                    mNativeUserList.add(0, getResources().getString(R.string.txt_share_list_private_chat));
                }
                notifyChatDataList();
            }

            @Override
            public void onError(RongIMClient.ErrorCode ErrorCode) {
                if (mNativeUserList == null) {
                    return;
                }
                if (!mNativeUserList.isEmpty()) {
                    mNativeUserList.clear();
                    notifyChatDataList();
                }
            }
        }, timeStamp, count, conversationTypes);

    }

    private void makeGroupDate(GroupListBean groupListBean) {
        Group userInfo = RongUserInfoManager.getInstance().getGroupInfo(String.valueOf(groupListBean.getGroupId()));
        GroupInfo infoAttribute = RongUserHelper.getInstance().getGroupInfo(String.valueOf(groupListBean.getGroupId()));
        if (userInfo != null) {
            groupListBean.setGroupName(userInfo.getName());
            if (userInfo.getPortraitUri() != null) {
                groupListBean.setGroupIcon(userInfo.getPortraitUri().toString());
            }
        } else if (infoAttribute != null) {
            groupListBean.setGroupName(infoAttribute.getName());
            if (infoAttribute.getPortrait() != null) {
                groupListBean.setGroupIcon(infoAttribute.getPortrait().toString());
            }
        } else {
            groupListBean.setGroupName("群聊");
        }
    }

    /**
     * 填充用户数据
     */
    private void makeUserDate(User user) {
        UserInfo userInfo = RongUserInfoManager.getInstance().getUserInfo(user.getUserIdStr());
        UserInfoAttribute infoAttribute = RongUserHelper.getInstance().getUserInfo(user.getUserIdStr());
        UserInfoExtra userInfoExtra = null;
        if (userInfo != null) {
            user.setNickname(userInfo.getName());
            if (userInfo.getPortraitUri() != null) {
                user.setPhoto(userInfo.getPortraitUri().toString());
            }
            userInfoExtra = UserInfoExtra.build(userInfo.getExtra());
        } else if (infoAttribute != null) {
            userInfoExtra = UserInfoExtra.build(infoAttribute.getExtra());
            user.setNickname(infoAttribute.getNickName());
            user.setPhoto(infoAttribute.getPhoto());
        } else {
            user.setNickname(user.getUserIdStr());
        }
        if (userInfoExtra != null && userInfoExtra.getClerkVo() != null) {
            ClerkVo clerkVo = userInfoExtra.getClerkVo();
            if (clerkVo.isConsultant()
                    && !TextUtils.isEmpty(clerkVo.getRealName())
                    && !TextUtils.isEmpty(clerkVo.getDealerShortName())) {
                user.setNickname(clerkVo.getRealName() + "-" + clerkVo.getDealerShortName());
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == USER_SEARCH_REQUEST_CODE) {
            if (resultCode == RESULT_OK) {
                mResultLiveData.setValue(0);
            } else if (resultCode == RESULT_CANCELED) {
                mResultLiveData.setValue(1);
            } else {
                if (resultCode != RESULT_FINISH) {
                    mResultLiveData.setValue(-1);
                }
            }
        }

    }

    @Override
    protected void onBack() {
        mResultLiveData.setValue(1);
//        super.onBack();
    }
}
