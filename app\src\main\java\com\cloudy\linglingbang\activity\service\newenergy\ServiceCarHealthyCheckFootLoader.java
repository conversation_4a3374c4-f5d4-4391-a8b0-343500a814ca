package com.cloudy.linglingbang.activity.service.newenergy;

import android.content.Context;
import android.view.View;
import android.widget.TextView;

import com.cloudy.linglingbang.R;

/**
 * 车辆体检页面foot的加载
 *
 * <AUTHOR>
 * @date 2019-08-09
 */
public class ServiceCarHealthyCheckFootLoader {

    private View mFootView;
    private Context mContext;
    private TextView mTvTips;

    public ServiceCarHealthyCheckFootLoader(View footView) {
        this.mFootView = footView;
        this.mContext = mFootView.getContext();
        initView();
    }

    private void initView() {
        mTvTips = mFootView.findViewById(R.id.tv_foot_car_healthy_status_tip);
    }

    public void initData() {
        mTvTips.setText(mContext.getResources().getString(R.string.txt_check_car_healthy_tip));
    }
}
