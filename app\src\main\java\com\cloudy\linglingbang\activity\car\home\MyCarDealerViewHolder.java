package com.cloudy.linglingbang.activity.car.home;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.store.CarTypeUtils;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.util.map.NavigateUtil;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.MyCarConsultantView;
import com.cloudy.linglingbang.app.widget.banner.AutoResizeHeightImageView;
import com.cloudy.linglingbang.app.widget.image.ImageLoadUtils;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.constants.WebUrlConfigConstant;
import com.cloudy.linglingbang.model.store.DealerInfo;
import com.cloudy.linglingbang.model.store.Employee;

import java.util.Locale;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 爱车-4S店的viewHolder
 *
 * <AUTHOR>
 * @date 2020-02-20
 */
public class MyCarDealerViewHolder extends BaseRecyclerViewHolder<DealerInfo> {

    private Context mContext;

    /**
     * 4S点标题
     */
    @BindView(R.id.tv_shop_title)
    TextView mTvShopTitle;

    /**
     * 4S店名称
     */
    @BindView(R.id.tv_dealer_name)
    TextView mTvDealerName;

    /**
     * 4s店副标题
     */
    @BindView(R.id.tv_shop_subtitle)
    TextView mTvShopSubtitle;

    /**
     * 4S店照片
     */
    @BindView(R.id.iv_shop_photo)
    AutoResizeHeightImageView mIvShopPhoto;

    /**
     * 4s店距离
     */
    @BindView(R.id.tv_distance)
    TextView mTvDistance;

    /**
     * 4s店地址
     */
    @BindView(R.id.tv_dealer_address)
    TextView mTvDealerAddress;

    /**
     * 顾问头像
     */
    @BindView(R.id.ll_adviser_header)
    LinearLayout mLlAdviserHeader;

    /**
     * 顾问整体视图view
     */
    @BindView(R.id.ll_adviser_view)
    LinearLayout mLlAdviserView;

    /**
     * 查看所有的顾问
     */
    @BindView(R.id.tv_see_all_consultant)
    TextView mTvSeeAllConsultant;

    /**
     * 查看所有4S店
     */
    @BindView(R.id.tv_see_all_shop)
    TextView mTvSeeAllShop;

    /**
     * 根布局
     */
    @BindView(R.id.ll_root)
    LinearLayout mLlRoot;

    /**
     * vr看车
     */
    @BindView(R.id.iv_my_car_vr)
    ImageView mIvMyCarVr;

    public MyCarDealerViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        mContext = itemView.getContext();
        ButterKnife.bind(this, itemView);
    }

    @Override
    public void bindTo(final DealerInfo dealerInfo, int position) {
        super.bindTo(dealerInfo, position);
        mTvDealerName.setText(dealerInfo.getDealerShortName());
        mTvDistance.setText(String.valueOf(dealerInfo.getDistance()));
        if (dealerInfo.getDistance() > 0) {
            double distance = dealerInfo.getDistance() / 1000;
            mTvDistance.setText(mContext.getString(R.string.item_dealer_distance_formatter, distance));
        } else {
            mTvDistance.setText("");
        }
        if (!TextUtils.isEmpty(dealerInfo.getContactAddress())) {
            mTvDealerAddress.setText(dealerInfo.getContactAddress());
        }
        new ImageLoad(mIvShopPhoto, ImageLoadUtils.processImageUrl(dealerInfo.getCoverUrl()))
                .setPlaceholderAndError(R.drawable.ic_shop_place_holder)
                .load();
        String subTitle = (String) (UserUtils.isRetentiveCustomer() ? mContext.getResources().getText(R.string.my_car_shop_subtitle_retentive) : mContext.getResources().getText(R.string.my_car_shop_subtitle_normal));
        mTvShopSubtitle.setText(subTitle);
        String title = (String) (UserUtils.isRetentiveCustomer() ? mContext.getResources().getText(R.string.my_car_shop_title_retentive) : mContext.getResources().getText(R.string.my_car_shop_title_normal));
        //设置vr图标是否显示
        if (dealerInfo.getVraddress() == null || TextUtils.isEmpty(dealerInfo.getVraddress().trim())) {
            mIvMyCarVr.setVisibility(View.GONE);
        } else {
            mIvMyCarVr.setVisibility(View.VISIBLE);
            mIvMyCarVr.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    CarTypeUtils.openVr(mContext, dealerInfo.getVraddress());
                }
            });
        }
        mTvShopTitle.setText(title);
        //设置顾问头像
        mLlAdviserHeader.removeAllViews();
        if (dealerInfo.getEmployeeVos() != null && dealerInfo.getEmployeeVos().size() > 0) {
            mLlAdviserView.setVisibility(View.VISIBLE);
            addConsultantView(dealerInfo.getEmployeeVos().get(0), 0);
            if (dealerInfo.getEmployeeVos().size() > 1) {
                addConsultantView(dealerInfo.getEmployeeVos().get(1), mContext.getResources().getDimensionPixelSize(R.dimen.normal_26));
            }
            if (dealerInfo.getEmployeeVos().size() > 2) {
                addConsultantView(dealerInfo.getEmployeeVos().get(2), mContext.getResources().getDimensionPixelSize(R.dimen.normal_26));
            }
        } else {
            mLlAdviserView.setVisibility(View.GONE);
        }

        int paddingBottom = (int) (UserUtils.isRetentiveCustomer() ? mContext.getResources().getDimension(R.dimen.normal_70) : mContext.getResources().getDimension(R.dimen.normal_10));
        mLlRoot.setPadding(0, mLlRoot.getPaddingTop(), 0, paddingBottom);

        //点击查看全部顾问
        mTvSeeAllConsultant.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                String url = String.format(Locale.getDefault(), WebUrlConfigConstant.ALL_CONSULTANT_4S, dealerInfo.getDealerId());
                JumpPageUtil.goCommonWeb(mContext, url);

            }
        });
        //点击定位
        mTvDistance.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (dealerInfo.getLatitude() != 0 && dealerInfo.getLongitude() != 0) {
                    NavigateUtil.navigate(mContext, String.valueOf(dealerInfo.getLatitude()), String.valueOf(dealerInfo.getLongitude()), dealerInfo.getDealerShortName() != null ? dealerInfo.getDealerShortName() : "");
                } else {
                    ToastUtil.showMessage(mContext, "4S店地理位置信息获取失败");
                }

            }
        });

        //点击名称
        mTvDealerName.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                goShopHomePage(dealerInfo.getDealerId());
                SensorsUtils.sensorsClickBtn("点击经销商" + dealerInfo.getDealerShortName(), "出行", "附近门店");
            }
        });
        //点击照片
        mIvShopPhoto.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                goShopHomePage(dealerInfo.getDealerId());
                SensorsUtils.sensorsClickBtn("点击经销商" + dealerInfo.getDealerShortName(), "出行", "附近门店");
            }
        });
        //查看所有4S店
        mTvSeeAllShop.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                StringBuilder sb = new StringBuilder(WebUrlConfigConstant.FRAGMENT_4S);
                if (!TextUtils.isEmpty(dealerInfo.getCityName())) {
                    sb.append("?cityName=" + dealerInfo.getCityName());
                }
                if (dealerInfo.getCityId() != null && dealerInfo.getCityId() != 0) {
                    sb.append("&cityId=" + dealerInfo.getCityId());
                }
                if (dealerInfo.getUserLng() != null && dealerInfo.getUserLng() != 0) {
                    sb.append("&lng=" + dealerInfo.getLongitude());
                }
                if (dealerInfo.getUserLat() != null && dealerInfo.getUserLat() != 0) {
                    sb.append("&lat=" + dealerInfo.getUserLat());
                }
                String url = sb.toString();
                JumpPageUtil.goCommonWeb(mContext, url);
            }
        });

    }

    private void goShopHomePage(long dealerId) {
        if (dealerId == 0) {
            ToastUtil.showMessage(mContext, "4S店信息错误，请重试！");
            return;
        }
        JumpPageUtil.goDealerDetail(mContext, dealerId);
    }

    protected void addConsultantView(Employee employee, int leftPadding) {
        MyCarConsultantView myCarConsultantView = new MyCarConsultantView(mContext);
        myCarConsultantView.setUser(employee);
        myCarConsultantView.setPadding(leftPadding, 0, 0, 0);
        mLlAdviserHeader.addView(myCarConsultantView);
    }
}
