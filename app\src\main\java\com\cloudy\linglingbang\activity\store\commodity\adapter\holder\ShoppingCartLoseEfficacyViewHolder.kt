package com.cloudy.linglingbang.activity.store.commodity.adapter.holder

import android.view.View
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import butterknife.BindView
import butterknife.ButterKnife
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.store.commodity.adapter.ShoppingCartLoseEfficacyItemAdapter
import com.cloudy.linglingbang.activity.store.commodity.adapter.ShoppingCartSingleGiftAdapter
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils
import com.cloudy.linglingbang.app.util.timer.CountDownManager
import com.cloudy.linglingbang.app.widget.SlidingButtonView
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder
import com.cloudy.linglingbang.model.store.commodity.CartCommodity
import com.cloudy.linglingbang.model.store.commodity.CartInfo

/**
 * 失效商品的ViewHolder
 *
 * <AUTHOR>
 * @date 2022/10/18
 */
class ShoppingCartLoseEfficacyViewHolder(itemView: View?) :
    BaseRecyclerViewHolder<CartInfo>(itemView) {

    @JvmField
    @BindView(R.id.tv_remove_lose_efficacy)
    var tvRemoveLoseEfficacy: TextView? = null

    @JvmField
    @BindView(R.id.recycler_view_lose_efficacy)
    var recyclerViewLoseEfficacy: RecyclerView? = null

    var mOnChangeCommodityListener: OnChangeCommodityListener? = null


    override fun initItemView(itemView: View) {
        super.initItemView(itemView)
        ButterKnife.bind(this, itemView)
    }

    override fun bindTo(bean: CartInfo?, position: Int) {
        super.bindTo(bean, position)
        bean?.apply {
            recyclerViewLoseEfficacy?.layoutManager =
                LinearLayoutManager(recyclerViewLoseEfficacy?.context)
            var adapter = ShoppingCartLoseEfficacyItemAdapter(
                recyclerViewLoseEfficacy?.context,
                commodityList
            )
            adapter.mOnChangeCommodityListener = object : OnChangeCommodityListener {
                override fun onChangeSingleSelect(cartId: Long, isSelect: Int) {
                    mOnChangeCommodityListener?.onChangeSingleSelect(cartId, isSelect)
                }

                override fun onChangeSelect(cartIds: MutableList<Long>, isSelect: Int) {
                    mOnChangeCommodityListener?.onChangeSelect(cartIds, isSelect)
                }

                override fun onDeleteSingle(cartId: Long) {
                    mOnChangeCommodityListener?.onDeleteSingle(cartId)
                }

                override fun onDeleteSingle(cartIds: MutableList<Long>, tips: String) {
                    mOnChangeCommodityListener?.onDeleteSingle(cartIds, tips)
                }

                override fun changeNumToCart(cartId: Long, count: Long) {
                    mOnChangeCommodityListener?.changeNumToCart(cartId, count)
                }

                override fun onMenuIsOpen(view: View?) {
                    mOnChangeCommodityListener?.onMenuIsOpen(view)
                }

                override fun onDownOrMove(slidingButtonView: SlidingButtonView?) {
                    mOnChangeCommodityListener?.onDownOrMove(slidingButtonView)
                }

            }
            recyclerViewLoseEfficacy?.adapter = adapter
            tvRemoveLoseEfficacy?.setOnClickListener {
                SensorsUtils.sensorsClickBtn("点击清空失效商品", "购物车", "清空失效按钮")
                commodityList?.apply {
                    var list = mutableListOf<Long>()
                    for (commodity in commodityList) {
                        list.add(commodity.cartId)
                    }
                    mOnChangeCommodityListener?.onDeleteSingle(list, "确认清空失效商品吗？")
                }
            }
        }


    }


}