package com.cloudy.linglingbang.activity.community.post;

import android.content.Context;
import android.graphics.Rect;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.widget.recycler.DividerItemDecoration;

import androidx.recyclerview.widget.RecyclerView;

/**
 * 头部不用分隔符
 *
 * <AUTHOR> create at 2016/10/25 11:56
 */
public class PostDetailItemDecoration extends DividerItemDecoration {
    public int getHeaderCount() {
        return mHeaderCount;
    }

    public void setHeaderCount(int headerCount) {
        mHeaderCount = headerCount;
    }

    private int mHeaderCount;

    public PostDetailItemDecoration(Context context, int headerCount) {
        super(context, DividerItemDecoration.VERTICAL_LIST, R.drawable.item_divider);
        mHeaderCount = headerCount;
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        super.getItemOffsets(outRect, view, parent, state);
        int position = parent.getChildAdapterPosition(view);
        //-1因为顶部最后一条是有的
        if (position < mHeaderCount - 1) {
            outRect.set(0, 0, 0, 0);
        } else {
            outRect.set(0, 0, 0, mDivider.getIntrinsicHeight());
        }
    }
}
