package com.cloudy.linglingbang.activity.fragment.store.hotel.order;

import android.content.Context;
import android.view.View;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.hotel.OrderInfo;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 我的订单
 *
 * <AUTHOR>
 * @date 2022/3/18
 */
public class MyOrderActivity extends BaseRecyclerViewRefreshActivity<OrderInfo> {

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_my_order);
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<OrderInfo> list) {
        MyOrderAdapter myOrderAdapter = new MyOrderAdapter(this, list);
        myOrderAdapter.setOnItemClickListener(new BaseRecyclerViewHolder.OnItemClickListener() {
            @Override
            public void onItemClick(View itemView, int position) {
                OrderDetailActivity.startActivity(MyOrderActivity.this, list.get(position).getId());
            }
        });
        return myOrderAdapter;
    }

    @Override
    public Observable<BaseResponse<List<OrderInfo>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        Map<String, Integer> map = new HashMap<>();
        map.put("pageNo", pageNo);
        map.put("pageSize", pageSize);
        return service2.hotelOrderPage(map);
    }

    @Override
    public RefreshController<OrderInfo> createRefreshController() {
        return new RefreshController<OrderInfo>(MyOrderActivity.this) {
            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return null;
            }
        }.setEmptyImageResId(R.drawable.common_list_empty)
                .setEmptyString("您还没有订单哦~");
    }

    class MyOrderAdapter extends BaseRecyclerViewAdapter<OrderInfo> {

        public MyOrderAdapter(Context context, List<OrderInfo> data) {
            super(context, data);
        }

        @Override
        protected BaseRecyclerViewHolder<OrderInfo> createViewHolder(View itemView) {
            return new MyOrderViewHolder(itemView);
        }

        @Override
        protected int getItemLayoutRes(int viewType) {
            return R.layout.item_order_jinjiang;
        }

        class MyOrderViewHolder extends BaseRecyclerViewHolder<OrderInfo> {

            private TextView mTvHotelName;
            private TextView mTvOrderNum;
            private TextView mTvOrderStatus;
            private TextView mTvInData;
            private TextView mTvOutData;
            private TextView mTvRoomStyle;
            private TextView mTvOrderTotalPrice;
            private TextView mTvInHotelPayPrice;
            private TextView mTvOrderData;
            private TextView mTvPayPrice;
            private TextView mTvPayStyle;
            private TextView mTvPay;
            private TextView mTvCancelOrder;

            public MyOrderViewHolder(View itemView) {
                super(itemView);
            }

            @Override
            protected void initItemView(View itemView) {
                super.initItemView(itemView);
                mTvHotelName = itemView.findViewById(R.id.tv_hotel_name);
                mTvOrderNum = itemView.findViewById(R.id.tv_order_num);
                mTvOrderStatus = itemView.findViewById(R.id.tv_order_status);
                mTvInData = itemView.findViewById(R.id.tv_in_data);
                mTvOutData = itemView.findViewById(R.id.tv_out_data);
                mTvRoomStyle = itemView.findViewById(R.id.tv_room_style);
                mTvOrderTotalPrice = itemView.findViewById(R.id.tv_order_total_price);
                mTvInHotelPayPrice = itemView.findViewById(R.id.tv_in_hotel_pay_price);
                mTvOrderData = itemView.findViewById(R.id.tv_order_data);
                mTvPayPrice = itemView.findViewById(R.id.tv_pay_price);
                mTvPayStyle = itemView.findViewById(R.id.tv_pay_style);
                mTvPay = itemView.findViewById(R.id.tv_pay);
                mTvCancelOrder = itemView.findViewById(R.id.tv_cancel_order);

            }

            @Override
            public void bindTo(OrderInfo orderInfo, int position) {
                super.bindTo(orderInfo, position);
                mTvHotelName.setText(orderInfo.getInnName());
                mTvOrderNum.setText(mContext.getResources().getString(R.string.jinjiang_order_num, orderInfo.getOrderCode()));
                mTvOrderStatus.setText(orderInfo.getOrderStatusName());
                mTvInData.setText(mContext.getResources().getString(R.string.jinjiang_in_data, orderInfo.getDtArrorig()));
                mTvOutData.setText(mContext.getResources().getString(R.string.jinjiang_out_data, orderInfo.getDtDeporig()));
                mTvRoomStyle.setText(mContext.getResources().getString(R.string.jinjiang_room_style, orderInfo.getRoomTypeName()));
                mTvOrderTotalPrice.setText(mContext.getResources().getString(R.string.jinjiang_order_total_price, orderInfo.getOrderPriceStr()));
                mTvInHotelPayPrice.setText(mContext.getResources().getString(R.string.jinjiang_order_in_hotel_price, orderInfo.getReachStorePriceStr()));
                mTvOrderData.setText(mContext.getResources().getString(R.string.jinjiang_order_data, orderInfo.getOrderTime()));
                if (orderInfo.getPayType().equals("1")) {//1是现金支付
                    mTvPayStyle.setText("支付订金：");
                    mTvPayPrice.setText(orderInfo.getDepositPrice());
                } else {
                    mTvPayStyle.setText("支付Ling值：");
                    mTvPayPrice.setText(orderInfo.getLingValueStr());
                }
                mTvPay.setVisibility(View.GONE);

                if (orderInfo.getOrderStatus() == 2 || orderInfo.getOrderStatus() == 1) {
                    mTvCancelOrder.setVisibility(View.VISIBLE);
                } else {
                    mTvCancelOrder.setVisibility(View.GONE);
                }

                mTvCancelOrder.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        requestCancelOrder(orderInfo.getId());
                    }
                });
            }
        }

        private void requestCancelOrder(Long orderId) {
            L00bangRequestManager2.getServiceInstance()
                    .cancelOrder(orderId)
                    .compose(L00bangRequestManager2.setSchedulers())
                    .subscribe(new ProgressSubscriber<Boolean>(mContext) {
                        @Override
                        public void onSuccess(Boolean b) {
                            super.onSuccess(b);
                            //刷新数据
                            getRefreshController().onRefresh();
                        }
                    });
        }
    }
}
