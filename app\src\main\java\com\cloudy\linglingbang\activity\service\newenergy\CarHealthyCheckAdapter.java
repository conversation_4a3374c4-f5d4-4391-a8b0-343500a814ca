package com.cloudy.linglingbang.activity.service.newenergy;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.widget.dialog.CarCheckDetailDialog;
import com.cloudy.linglingbang.app.widget.image.ImageLoadUtils;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.server.CarHealthyCheckInfo;

import java.util.List;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 车检适配器
 *
 * <AUTHOR>
 * @date 2019-08-06
 */
public class CarHealthyCheckAdapter extends BaseRecyclerViewAdapter<CarHealthyCheckInfo> {
    public static final int TYPE_MOTOR_OR_BATTER = 1;
    public static final int TYPE_MONOR_DOOR_OR_LIGHT = 2;
    public static final int TYPE_SMART_CHARGING = 3;
    public static final int TYPE_FOUR_ITEM = 4;
    private final Context mContext;
    private OnChildItemClickListener mOnChildItemClickListener;

    public CarHealthyCheckAdapter(Context context, List<CarHealthyCheckInfo> data) {
        super(context, data);
        this.mContext = context;
    }

    @Override
    protected BaseRecyclerViewHolder<CarHealthyCheckInfo> createViewHolder(View itemView) {
        return null;
    }

    @Override
    protected BaseRecyclerViewHolder<CarHealthyCheckInfo> createViewHolderWithViewType(View itemView, int viewType) {
        BaseRecyclerViewHolder<CarHealthyCheckInfo> holder;
        switch (viewType) {
            case TYPE_MOTOR_OR_BATTER:
            case TYPE_FOUR_ITEM:
                holder = new MotorOrBatterHolder(itemView);
                break;
            case TYPE_MONOR_DOOR_OR_LIGHT:
                holder = new DoorOrLightHolder(itemView);
                break;
            case TYPE_SMART_CHARGING:
                holder = new SmartChargingHolder(itemView);
                break;
            default:
                holder = null;
                break;
        }
        return holder;
    }

    @Override
    protected int getItemLayoutRes(int viewType) {
        if (TYPE_MOTOR_OR_BATTER == viewType) {
            return R.layout.item_car_healthy_check;
        } else if (TYPE_MONOR_DOOR_OR_LIGHT == viewType) {
            return R.layout.item_car_healthy_check_door_or_light;
        } else if (TYPE_SMART_CHARGING == viewType) {
            return R.layout.item_car_healthy_check_smart_charging;
        } else if (TYPE_FOUR_ITEM == viewType) {
            return R.layout.item_car_healthy_check;
        } else {
            return R.layout.item_car_healthy_check;
        }

    }

    @Override
    public int getItemViewType(int position) {
        String type = mData.get(position).getType();
        if ("1".equals(type)) {
            return TYPE_MOTOR_OR_BATTER;
        } else if ("2".equals(type)) {
            return TYPE_MONOR_DOOR_OR_LIGHT;
        } else if ("3".equals(type)) {
            return TYPE_SMART_CHARGING;
        } else if ("4".equals(type)) {
            return TYPE_FOUR_ITEM;
        } else {
            return TYPE_MOTOR_OR_BATTER;
        }
    }

    /**
     * 电机和电池适配器
     * type=1或者4时候的holder，改为动态展示item项目
     */
    class MotorOrBatterHolder extends BaseRecyclerViewHolder<CarHealthyCheckInfo> {
        /** 状态图标 */
        @BindView(R.id.iv_bg_car_healthy_check)
        ImageView mBgMotorOrBatteryCheckStatus;
        /** 图标 */
        @BindView(R.id.iv_car_healthy_motor)
        ImageView mIvMotorOrBattery;
        //底部的waring
        @BindView(R.id.tv_des_car_healthy_motor)
        TextView mTvBadDes;

        @BindView(R.id.rv_car_healthy)
        RecyclerView mRvCarHealthy;

        int motorOrBatteryBadCount = 0;
        StringBuilder mStringBuilder = new StringBuilder();

        public MotorOrBatterHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            ButterKnife.bind(this, itemView);
        }

        @Override
        public void bindTo(CarHealthyCheckInfo carHealthyCheckInfo, int position) {
            super.bindTo(carHealthyCheckInfo, position);
            //状态图标根据itemlist中第一个数据的itemstatus来判断,如果为0则显示正常图片,为1则显示异常图标,如果itemlist中第一个数据的itemstatus为-1的话,显示空图标
            motorOrBatteryBadCount = 0;
            //1 电机综合评分,电机温度,电机功率 0 表示不正常,1 表示正常,电池综合评分,电池温度,电池电压 0 表示差,1 表示优
            //状态图标
            if (carHealthyCheckInfo.getItemList() != null && !carHealthyCheckInfo.getItemList().isEmpty()) {
                if (1 == carHealthyCheckInfo.getItemList().get(0).getItemStatus()) {
                    mBgMotorOrBatteryCheckStatus.setImageResource(R.drawable.ic_car_healthy_check_good);
                } else if (0 == carHealthyCheckInfo.getItemList().get(0).getItemStatus()) {
                    mBgMotorOrBatteryCheckStatus.setImageResource(R.drawable.ic_car_healthy_check_bad);
                } else {
                    mBgMotorOrBatteryCheckStatus.setImageResource(R.drawable.ic_car_healthy_check_empty);
                }
                if (carHealthyCheckInfo.getUrl() != null) {
                    ImageLoadUtils.load(mIvMotorOrBattery, carHealthyCheckInfo.getUrl());
                }
                //只要包含异常,就显示
                //暂时去掉异常的显示
                /*mStringBuilder = new StringBuilder();
                mStringBuilder.append("「");
                if (carHealthyCheckInfo.getItemList().size() > 1) {
                    for (int i = 1; i < carHealthyCheckInfo.getItemList().size(); i++) {
                        if (carHealthyCheckInfo.getItemList().get(i).getItemStatus() == 0) {
                            mStringBuilder.append(carHealthyCheckInfo.getItemList().get(i).getItemName()).append("、");
                        }
                    }
                }

                for (CarHealthyCheckInfo.ItemListBean itemlistbean : carHealthyCheckInfo.getItemList()) {
                    if (itemlistbean.getItemStatus() == 0) {
                        motorOrBatteryBadCount++;
                    }
                }
                if (motorOrBatteryBadCount > 0) {
                    mTvBadDes.setVisibility(View.VISIBLE);
                    String badItemDes = mStringBuilder.substring(0, mStringBuilder.length() - 1);
                    mTvBadDes.setText(String.format("%s%s", badItemDes, mContext.getString(R.string.txt_check_car_healthy_des)));
                } else {
                    mTvBadDes.setVisibility(View.GONE);
                }*/

                mRvCarHealthy.setLayoutManager(new LinearLayoutManager(mContext));
                mRvCarHealthy.setAdapter(new CarHealthyItemAdapter(mContext, carHealthyCheckInfo.getItemList()));

            }

        }
    }

    /**
     * 门和车灯适配器
     */
    class DoorOrLightHolder extends BaseRecyclerViewHolder<CarHealthyCheckInfo> {
        /**
         * 状态图标
         */
        @BindView(R.id.iv_bg_car_healthy_check_head_light)
        ImageView mBgDoorOrHeadLight;
        /**
         * 图标
         */
        @BindView(R.id.iv_car_healthy_head_light)
        ImageView mIvDoorOrHeadLight;
        /**
         * 模块名,取modelName字段
         */
        @BindView(R.id.tv_check_car_healthy_head_light)
        TextView mTvDoorOrHeadLightName;
        /**
         * 如果状态为正常的话显示
         */
        @BindView(R.id.peb_check_car_healthy_head_light_status)
        TextView mPebDoorOrHeadLightStatus;
        /**
         * 如果状态为差的话显示,并点击弹窗显示详情
         */
        /*@BindView(R.id.peb_car_healthy_check_head_light)
        PressEffectiveButton mPebDoorOrHeadLight;*/

        int badCount = 0;//未关闭或未锁数量
        int emptyCount = 0;//未统计数量
        private CarHealthyCheckInfo mCarHealthyCheckInfo;

        public DoorOrLightHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            ButterKnife.bind(this, itemView);
        }

        @Override
        public void bindTo(CarHealthyCheckInfo carHealthyCheckInfo, int position) {
            super.bindTo(carHealthyCheckInfo, position);
            mCarHealthyCheckInfo = carHealthyCheckInfo;
            badCount = 0;
            emptyCount = 0;
            if (carHealthyCheckInfo.getItemList() != null && !carHealthyCheckInfo.getItemList().isEmpty()) {
                for (CarHealthyCheckInfo.ItemListBean itemListBean : carHealthyCheckInfo.getItemList()) {
                    if (1 == itemListBean.getItemStatus()) {
                        badCount++;
                    }
                    if (-1 == itemListBean.getItemStatus()) {
                        emptyCount++;
                    }
                }
                //如果badCount==0说明没有异常情况,否则是有异常
                if (badCount == 0) {
                    mBgDoorOrHeadLight.setImageResource(R.drawable.ic_car_healthy_check_good);
                    mPebDoorOrHeadLightStatus.setVisibility(View.VISIBLE);
                    mPebDoorOrHeadLightStatus.setTextColor(mContext.getResources().getColor(R.color.color_384967));
                    mPebDoorOrHeadLightStatus.setText(mContext.getResources().getString(R.string.txt_check_car_healthy_normal));
                } else {
                    mBgDoorOrHeadLight.setImageResource(R.drawable.ic_car_healthy_check_bad);
                    mPebDoorOrHeadLightStatus.setVisibility(View.GONE);
                    mPebDoorOrHeadLightStatus.setTextColor(mContext.getResources().getColor(R.color.color_e4002b));
                    mPebDoorOrHeadLightStatus.setText(String.format(mContext.getResources().getString(R.string.txt_check_car_healthy_bad_des), badCount));
                }
            }
            if (carHealthyCheckInfo.getUrl() != null) {
                ImageLoadUtils.load(mIvDoorOrHeadLight, carHealthyCheckInfo.getUrl());
            }
            mTvDoorOrHeadLightName.setText(carHealthyCheckInfo.getModelName());
            mPebDoorOrHeadLightStatus.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    CarCheckDetailDialog carCheckDetailDialog = new CarCheckDetailDialog(mContext, mCarHealthyCheckInfo, badCount, emptyCount);
                    if (carCheckDetailDialog.isShowing()) {
                        carCheckDetailDialog.dismiss();
                    }
                    carCheckDetailDialog.show();
                }
            });
        }
    }

    /**
     * 智能充电适配器
     */
    class SmartChargingHolder extends BaseRecyclerViewHolder<CarHealthyCheckInfo> {
        /** 状态图标 */
        @BindView(R.id.iv_bg_car_healthy_check_smart_charging)
        ImageView mBgSmartCharging;
        /** 功能图标 */
        @BindView(R.id.iv_car_healthy_head_smart_charging)
        ImageView mIvChargingIcon;
        /** 模块名 */
        @BindView(R.id.tv_check_car_healthy_head_smart_charging)
        TextView mTvCharingName;
        /** 电池状态描述 */
        @BindView(R.id.tv_check_car_healthy_smart_charging_des)
        TextView mTvSmartChargingDes;
        /** 问号 */
        @BindView(R.id.iv_car_healthy_check_smart_charging)
        ImageView mIvSmartChargingIntro;
        /** 去补电按钮 */
        @BindView(R.id.peb_smart_charging_status)
        TextView pebChargingStatus;

        public SmartChargingHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            ButterKnife.bind(this, itemView);
        }

        @Override
        public void bindTo(CarHealthyCheckInfo carHealthyCheckInfo, int position) {
            super.bindTo(carHealthyCheckInfo, position);
            if (carHealthyCheckInfo.getItemList() != null && !carHealthyCheckInfo.getItemList().isEmpty()) {
                //智能补电，00: 正常 10000: 亏电，亏电蓄 200: 补电中 300:补电结束
                int itemStatus = carHealthyCheckInfo.getItemList().get(0).getItemStatus();
                if (0 == itemStatus || 200 == itemStatus || 300 == itemStatus) {
                    mBgSmartCharging.setImageResource(R.drawable.ic_car_healthy_check_good);
                    pebChargingStatus.setVisibility(View.GONE);
                    mTvSmartChargingDes.setTextColor(mContext.getResources().getColor(R.color.color_384967));//正常情况字体蓝色
                } else {//其他全为异常，但是只有是10000的时候显示去补电
                    mBgSmartCharging.setImageResource(R.drawable.ic_car_healthy_check_bad);
                    mTvSmartChargingDes.setTextColor(mContext.getResources().getColor(R.color.color_e4002b));//异常情况字体红色
                    if (10000 == itemStatus) {
                        pebChargingStatus.setVisibility(View.VISIBLE);//去充电按钮显示
                    } else {
                        pebChargingStatus.setVisibility(View.GONE);
                    }
                }

                mTvSmartChargingDes.setText(carHealthyCheckInfo.getItemList().get(0).getItemStatusStr());
                mTvCharingName.setText(carHealthyCheckInfo.getModelName());
            }

            if (carHealthyCheckInfo.getUrl() != null) {
                ImageLoadUtils.load(mIvChargingIcon, carHealthyCheckInfo.getUrl());
            }

            mIvSmartChargingIntro.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    if (mOnChildItemClickListener != null) {
                        mOnChildItemClickListener.onChildItemClickListener(view);
                    }
                }
            });
            pebChargingStatus.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    if (mOnChildItemClickListener != null) {
                        mOnChildItemClickListener.onChildItemClickListener(view);
                    }
                }
            });
        }
    }

    public interface OnChildItemClickListener {
        void onChildItemClickListener(View view);
    }

    /**
     * 设置子控件项点击事件
     */
    public void setOnChildItemClickedListener(OnChildItemClickListener onChildItemClickedListener) {
        this.mOnChildItemClickListener = onChildItemClickedListener;
    }
}
