package com.cloudy.linglingbang.activity.community.post.detail;

import android.content.Context;
import android.graphics.Rect;
import android.view.View;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.community.common.holder.BasePostChildViewHolder;
import com.cloudy.linglingbang.app.imageConfig.RoundedCornersTransformation;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.banner.AdRoundImageView;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.SourceModel;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.postcard.PostCommodity;
import com.cloudy.linglingbang.model.server.Ad.AdJumpUtil2;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 标签商品
 *
 * <AUTHOR>
 * @date 2021/11/15
 */
public class PostTagCommodityViewHolder extends BasePostChildViewHolder {
    private RecyclerView mRvTagCommodity;

    public PostTagCommodityViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        int px34 = itemView.getContext().getResources().getDimensionPixelOffset(R.dimen.normal_34);
        mRvTagCommodity = itemView.findViewById(R.id.rv_tag_commodity);
        mRvTagCommodity.setLayoutManager(new LinearLayoutManager(itemView.getContext(), LinearLayoutManager.HORIZONTAL, false));
        mRvTagCommodity.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                int position = parent.getChildAdapterPosition(view);
                if (position == 0) {
                    outRect.left = 0;
                }
                outRect.right = px34;
                RecyclerView.Adapter<?> adapter = parent.getAdapter();
                if (adapter != null && position == adapter.getItemCount() - 1) {
                    outRect.right = 0;
                }
            }
        });
    }

    @Override
    public void bindTo(PostCard postCard) {
        super.bindTo(postCard);
        List<PostCommodity> postCommodityList = postCard.getPostCommodityList();
        if (postCommodityList == null || postCommodityList.size() == 0) {
            mRvTagCommodity.setVisibility(View.GONE);
        } else {
            mRvTagCommodity.setVisibility(View.VISIBLE);
            CommodityAdapter commodityAdapter = new CommodityAdapter(mRvTagCommodity.getContext(), postCommodityList);
            commodityAdapter.setOnItemClickListener(new BaseRecyclerViewHolder.OnItemClickListener() {
                @Override
                public void onItemClick(View itemView, int position) {
                    if (commodityAdapter.getData().get(position).getLinkType() == null) {
                        return;
                    }
                    SensorsUtils.sensorsClickBtn("点击(" + postCard.getPostId() + ")" + "(" + commodityAdapter.getData().get(position).getCommodityName() + ")", "帖子详情页", "帖子详情页-商品");
                    AdJumpUtil2.goToActivity(itemView.getContext(), commodityAdapter.getData().get(position).getLinkType(), commodityAdapter.getData().get(position).getLinkUrl(), "", "帖子详情商品", new SourceModel("1", mPostCard.getPostId()));
                }
            });
            mRvTagCommodity.setAdapter(commodityAdapter);
        }

    }

    class CommodityAdapter extends BaseRecyclerViewAdapter<PostCommodity> {

        public CommodityAdapter(Context context, List<PostCommodity> data) {
            super(context, data);
        }

        @Override
        protected BaseRecyclerViewHolder<PostCommodity> createViewHolder(View itemView) {
            return new ViewHolder(itemView);
        }

        @Override
        protected int getItemLayoutRes(int viewType) {
            return R.layout.item_post_tag_commodity;
        }

        class ViewHolder extends BaseRecyclerViewHolder<PostCommodity> {

            private TextView mTvCommodityName;
            private AdRoundImageView mIvCommodityImg;

            public ViewHolder(View itemView) {
                super(itemView);
            }

            @Override
            protected void initItemView(View itemView) {
                super.initItemView(itemView);
                mTvCommodityName = itemView.findViewById(R.id.tv_commodity_name);
                mIvCommodityImg = itemView.findViewById(R.id.iv_commodity_img);
            }

            @Override
            public void bindTo(PostCommodity postCommodity, int position) {
                super.bindTo(postCommodity, position);
                mTvCommodityName.setText(postCommodity.getCommodityName());

                //加载图片
                mIvCommodityImg.createCornerImageLoad(postCommodity.getCommodityImage(), RoundedCornersTransformation.CornerType.ALL)
                        .load();

            }
        }
    }
}
