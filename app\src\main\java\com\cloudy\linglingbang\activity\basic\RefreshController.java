package com.cloudy.linglingbang.activity.basic;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.aspsine.swipetoloadlayout.OnLoadMoreListener;
import com.aspsine.swipetoloadlayout.OnRefreshListener;
import com.aspsine.swipetoloadlayout.SwipeToLoadLayout2;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.community.common.BasePostAdapter;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.widget.recycler.DividerItemDecoration;
import com.cloudy.linglingbang.app.widget.recycler.EmptySupportedRecyclerView;
import com.cloudy.linglingbang.app.widget.recycler.WrapContentLayoutManager;
import com.cloudy.linglingbang.app.widget.recycler.header.HeaderAndFooterWrapperAdapter;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BackgroundSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BaseSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.NormalSubscriber;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.rxjava3.core.Observable;

/**
 * 控制下拉刷新和加载更多
 *
 * <AUTHOR> create at 2016/10/13 9:54
 * @see #initViews(View)
 * @see #loadDataAfterInitViews()
 * @see #setPageSize(int)
 * @see #showEmptyInfo()
 */
public class RefreshController<T> implements OnRefreshListener, OnLoadMoreListener {
    /**
     * 默认页大小
     */
    public static final int DEFAULT_PAGE_SIZE = 20;
    /**
     * 联网加载
     */
    public static final int LOAD_TYPE_NET = 0;
    /**
     * 加载缓存
     */
    public static final int LOAD_TYPE_CACHE = 1;
    private final IRefreshContext<T> mRefreshContext;
    protected SwipeToLoadLayout2 swipeToLoadLayout;
    /**
     * 当前页数，加载下一页时是skip+1
     */
    protected int mPageNo = 0;

    /**
     * 每页显示的条数
     */
    protected int mPageSize = DEFAULT_PAGE_SIZE;

    protected List<T> mData;
    protected EmptySupportedRecyclerView recyclerView;
    protected RecyclerView.Adapter<? extends RecyclerView.ViewHolder> adapter;
    protected View mLlEmpty;
    protected ImageView mIvEmpty;
    protected TextView mTvEmpty;
    private boolean mIsFirstLoad = true;
    //滚动到底部的提示，在滚动到底部时提示并置为true，在向上滑动时置为false
    protected boolean mBottomToastLock;
    /**
     * 数据为空时，显示的文字
     * 对于这几个空信息、错误信息，一开始我想用protected，要想改的话直接继承重写。
     * 这样就可以省去一个字段，一个set方法，后来发现有2个问题，
     * 1是如果只想重写某一个方法，就要继承，这样会多出一个类。
     * 2是扩展性不强，如果需要多次修改为不同的值，那么protected就是不能满足了。
     * 于是还是要字段加get、set方法实现。但这又有问题，如果我已经重写了父类，本可以直接return，但却要新增一个局部变量，
     * 然后调用set方法，再return，显得不够精简。
     * 这时候，也可以重写get方法返回指定值，但这样很不好。
     * 最后的做法时，让set方法返回this,这样就可以链式了。
     */
    private String mEmptyString;
    /**
     * 数据为空时，显示的图片id
     */
    private int mEmptyImageResId;
    /**
     * 有错误时，显示的图片id
     */
    private int mErrorImageResId;

    /**
     * 数据为空时，背景颜色Id
     */
    private int mEmptyBackgroundColorId;
    /**
     * 是否显示到底了的提示
     */
    protected boolean mShowBottomToast = true;

    public RefreshController(IRefreshContext<T> refreshContext) {
        mRefreshContext = refreshContext;
        mEmptyString = mRefreshContext.getContext().getString(R.string.blank_txt);
        mEmptyImageResId = R.drawable.circle_no_post;
        mErrorImageResId = R.drawable.circle_no_post;
        mEmptyBackgroundColorId = R.color.app_bg;
    }

    protected View mRootView;

    public void initViews(View rootView) {
        mRootView = rootView;
        swipeToLoadLayout = setSwipeToLoadLayout();
        recyclerView = rootView.findViewById(R.id.recycler_view);
        recyclerView.setLayoutManager(createLayoutManager(mRefreshContext.getContext()));

        if (mData == null) {
            mData = new ArrayList<>();
        } else {
            mData.clear();
        }
        adapter = mRefreshContext.createAdapter(mData);
        recyclerView.setAdapter(adapter);

        boolean isPostAdapter = false;
        if (adapter instanceof BasePostAdapter) {
            isPostAdapter = true;
        } else if (adapter instanceof HeaderAndFooterWrapperAdapter) {
            if (((HeaderAndFooterWrapperAdapter) adapter).getInnerAdapter() instanceof BasePostAdapter) {
                isPostAdapter = true;
            }
        }
        //添加分隔线，多加一个判断，如果是帖子就不添加了，不然所有的帖子都需要改
        if (!isPostAdapter) {
            RecyclerView.ItemDecoration itemDecoration = createItemDecoration(mRefreshContext.getContext());
            if (itemDecoration != null) {//判断是否为空，以便子类可以设置不加分割线
                recyclerView.addItemDecoration(itemDecoration);
            }
        }

        mLlEmpty = rootView.findViewById(R.id.ll_empty);
        //默认先隐藏
        mLlEmpty.setVisibility(View.GONE);
        mIvEmpty = rootView.findViewById(R.id.iv_empty);
        mTvEmpty = rootView.findViewById(R.id.tv_empty_desc);

        /*
          在这里需要进行初始化的设置，按原来的逻辑，在为空时，会调用 showEmptyInfo
          但是如果是调用的 notifyItemRemoved 则会转到 com.cloudy.linglingbang.app.widget.recycler.EmptySupportedRecyclerView.showEmptyView
          该方法中只是将其显示出来，不设置相关属性
         */
        if (getEmptyImageResId() > 0) {
            mIvEmpty.setImageResource(getEmptyImageResId());
        }
        if (!TextUtils.isEmpty(getEmptyString())) {
            mTvEmpty.setText(getEmptyString());
        }
        if (getEmptyBackgroundColorId() > 0) {
            mLlEmpty.setBackgroundResource(getEmptyBackgroundColorId());
        }
        recyclerView.setEmptyView(mLlEmpty);

        swipeToLoadLayout.setOnRefreshListener(this);
        swipeToLoadLayout.setOnLoadMoreListener(this);

        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    if (!ViewCompat.canScrollVertically(recyclerView, 1)) {
                        onScrollToBottom();
                    }
                }
            }

            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                if (dy < 0) {
                    //向上滚动
                    mBottomToastLock = false;
                }
            }
        });
        swipeToLoadLayout.setRefreshEnabled(isRefreshEnable());
        //首次加载的时候，不知道是否有更多，默认是false
        //会在刷新完成后根据数据判断是否可用，如果需要，可以在initViews之后开启
        //这里的坑是，如果刷新时加载0，在刷新过程中加载更多就会加载1，回载完成后，由于是第1页
        //执行刷新完成，而不会把加载更多隐藏。
        //如果刷新1页，在过程中加载更多为2页，也是不好的，所以置为不可加载更多
        //也可以在每次刷新时置为不可用
        swipeToLoadLayout.setLoadMoreEnabled(false);
//        swipeToLoadLayout.setLoadMoreEnabled(isLoadMoreEnable());
        if (loadDataAfterInitViews()) {
            if (showRefreshingWhenLoadDataAfterInitViews()) {
                //这里不用调用onRefresh,setRefreshing会调用滚动，滚动完成后会调用onRefresh()
                //swipeToLoadLayout.setRefreshing(true);
                swipeToLoadLayout.manualRefresh();
            } else {
                onRefresh();
            }
        }
    }

    /**
     * 滑动到底部时的操作
     */
    protected void onScrollToBottom() {
        if (swipeToLoadLayout.isLoadMoreEnabled()) {
            swipeToLoadLayout.setLoadingMore(true);
        } else {
            if (mShowBottomToast) {
                if (!mBottomToastLock) {
                    mBottomToastLock = true;
                    ToastUtil.showMessage(mRefreshContext.getContext(), "到底了~");
                }
            }
        }
    }

    protected SwipeToLoadLayout2 setSwipeToLoadLayout() {
        if (mRootView != null) {
            return (SwipeToLoadLayout2) mRootView.findViewById(R.id.swipeToLoadLayout);
        } else {
            return null;
        }
    }

    /**
     * 分割线
     *
     * @param context the context
     * @return 默认为 {@linkplain DividerItemDecoration}，用的是R.drawable.item_divider
     */
    protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
        return new DividerItemDecoration(context, DividerItemDecoration.VERTICAL_LIST, R.drawable.item_divider);
    }

    /**
     * 是否在初始化view完成后加载数据，默认为true。
     * <br/>
     *
     * @see #initViews(View)
     */
    protected boolean loadDataAfterInitViews() {
        return true;
    }

    /**
     * 在初始化完成时，如果加载数据，是否显示出正在刷新。
     * <br/>{@linkplain #loadDataAfterInitViews()}
     *
     * @return 默认为true
     */
    protected boolean showRefreshingWhenLoadDataAfterInitViews() {
        return true;
    }

    /**
     * 加载更多，加载当前页的下一页
     */
    @Override
    public void onLoadMore() {
        getListData(mPageNo + 1);
    }

    /**
     * 刷新，加载第0页
     */
    @Override
    public void onRefresh() {
        getListData(0);
    }

    /**
     * 主动调用刷新，滚动到顶部，然后调用手动刷新
     */
    public void manualRefresh() {
        if (recyclerView != null) {
            recyclerView.scrollToPosition(0);
        }

        if (swipeToLoadLayout != null) {
            swipeToLoadLayout.manualRefresh();
        }
    }

    /**
     * recycler view 的LayoutManage
     *
     * @param context context
     * @return 默认为 {@linkplain LinearLayoutManager}
     */
    protected RecyclerView.LayoutManager createLayoutManager(Context context) {
        return new WrapContentLayoutManager(context);
    }

    /**
     * 获取数据
     *
     * @param page 页数
     */
    public void getListData(int page) {
        if (page < 1) {
            page = 1;
        }
        final int loadPage = page;
        if (loadPage == 1 && mIsFirstLoad) {
            //使用缓存的过程
            mIsFirstLoad = false;
            if (useCacheOnLoadFirstPage()) {
                //使用缓存的service
                Observable<BaseResponse<List<T>>> result = getListDataFromNet(
                        L00bangRequestManager2.createUseCacheService(true),
                        loadPage,
                        getPageSize());

                if (result != null) {
                    //缓存用的后台加载，且不处理错误
                    result
                            .compose(L00bangRequestManager2.setSchedulers(true))
                            .subscribe(createSubscriber(mRefreshContext.getContext(), loadPage, LOAD_TYPE_CACHE));
                }
            }
        }

        Observable<BaseResponse<List<T>>> result = getListDataFromNet(
                L00bangRequestManager2.getServiceInstance(),
                loadPage,
                getPageSize());

        if (result != null) {//判断不为空,防止出现空指针
            result
                    .compose(L00bangRequestManager2.setSchedulers())
                    .subscribe(createSubscriber(mRefreshContext.getContext(), loadPage, LOAD_TYPE_NET));
        }
    }

    protected Observable<BaseResponse<List<T>>> getListDataFromNet(L00bangService2 service2, int pageNo, int pageSize) {
        return mRefreshContext.getListDataFormNet(service2, pageNo, pageSize);
    }

    /**
     * 创建一个subscriber
     *
     * @param context  the context
     * @param loadPage 加载页数
     * @param loadType 加载类型
     * @return subscriber
     */
    protected BaseSubscriber<List<T>> createSubscriber(Context context, int loadPage, int loadType) {
        if (loadType == LOAD_TYPE_NET) {
            return createNormalSubscriber(context, loadPage);
        } else {
            return createCacheSubscriber(context, loadPage);
        }
    }

    /**
     * 创建正常加载的subscriber
     *
     * @param context  the context
     * @param loadPage 加载页数
     * @return subscriber
     */
    protected BaseSubscriber<List<T>> createNormalSubscriber(Context context, final int loadPage) {
        return new NormalSubscriber<List<T>>(context) {
            @Override
            public void onSuccess(List<T> list) {
                super.onSuccess(list);
                onRequestSuccess((List<T>) list, loadPage, LOAD_TYPE_NET);
            }

            @Override
            public void onFailure(Throwable e) {
                super.onFailure(e);
                onLoadFail(loadPage, e);
            }
        };
    }

    protected void onRequestSuccess(List<T> list, int loadPage, int loadTypeNet) {
        if (list == null) {
            //防止服务端data返回为null时，出现刷新动画无法停止的问题
            list = new ArrayList<>();
        }
        onLoadSuccess(loadPage, list, loadTypeNet);
    }

    /**
     * 创建加载缓存的subscriber
     *
     * @param context  the context
     * @param loadPage 加载页数
     * @return subscriber
     */
    protected BaseSubscriber<List<T>> createCacheSubscriber(Context context, final int loadPage) {
        return new BackgroundSubscriber<List<T>>(context) {
            @Override
            public void onSuccess(List<T> list) {
                super.onSuccess(list);
                onRequestSuccess(list, loadPage, LOAD_TYPE_CACHE);
            }

            @Override
            public void onFailure(Throwable e) {
                super.onFailure(e);
                //读取缓存不处理错误
            }
        };
    }

    /**
     * 第一次加载时是否使用缓存，默认为false
     * <br/>注意只有get可以缓存。
     * <br/>http://stackoverflow.com/questions/32727599/cache-post-requests-with-okhttp
     * <br/>https://github.com/square/okhttp/blob/master/okhttp/src/main/java/okhttp3/Cache.java#L231%5D
     *
     * @return 默认为false
     */
    protected boolean useCacheOnLoadFirstPage() {
        return false;
    }

    /**
     * 加载数据完成
     *
     * @param loadPage 加载的页数，如果是1，即为刷新，否则为加载更多。
     *                 <br/>加载更多时，页数与当前页数+1比较，可以判断是否会重复加载
     * @param list     加载结果，可以对其排序等。
     * @param loadType 0为默认{@linkplain #LOAD_TYPE_NET}，1为读取缓存{@linkplain #LOAD_TYPE_CACHE}
     */
    protected void onLoadSuccess(int loadPage, List<T> list, int loadType) {
        if (recyclerView == null || list == null || mData == null) {
            return;
        }
        //设置要在onLoadMoreComplete()中的setLoadingMore之后
        //默认-1不处理
        int hasMore = -1;
        if (loadPage == 1) {
            //不保留，都刷新吧，否则注意equals的实现
//            if (list.size() != 0 && list.equals(mData)) {
//                //不为空时，相等不处理，只恢复
//                swipeToLoadLayout.setRefreshing(false);
//            } else {
            mPageNo = loadPage;
            /*
              注意此处取list的size，如果重写onLoadSuccess方法时，不要再往list里面添加数据
             */
            int size = list.size();
            mData.clear();
            int firstPageSizeOffset = getFirstPageSizeOffset();
            for (int i = 0; i < firstPageSizeOffset; i++) {
                mData.add(null);
            }
            mData.addAll(list);
            if (size == 0) {
                showEmptyInfo();
            }
            if (size < mPageSize) {
                hasMore = 0;
            } else {
                hasMore = 1;
            }
            onRefreshComplete();
        } else {
            int size = 0;
            //不是第1页
            if (loadPage == mPageNo + 1) {
                //加载的是下一页
                size = list.size();
                if (size != 0) {
                    //更新页数
                    mPageNo = loadPage;
                    mData.addAll(list);
                    if (size < mPageSize) {
                        hasMore = 0;
                    }
                } else {
                    hasMore = 0;
                }
            }//else 可能是重复加载的，不处理
            onLoadMoreComplete(size);
        }
        if (hasMore == 0) {
            setLoadMoreEnable(false);
        } else if (hasMore == 1) {
            setLoadMoreEnable(true);
        }
        onLoadDataComplete(loadPage, loadType);//加载完成时，调用方法，子类可以执行一些操作
    }

    /**
     * 获取第一页时的偏移值，这个需求是这样的
     * 如果在正式的列表前有别的item，类似header，为了让header占位，需添加几个为null的数据
     * 比如第1个data为null，当position为0时，显示的是header
     * 第2个data才开始显示数据
     * <p>
     * 曾有一个bug，加载第一页时，因为header数较多，导致加载出第一页list.size大于要加载的数量，误判为可以加载更多
     *
     * @return 默认为0
     */
    protected int getFirstPageSizeOffset() {
        return 0;
    }

    /**
     * 加载完成时，调用方法，子类可以执行一些操作
     */
    protected void onLoadDataComplete(int loadPage, int loadType) {
    }

    /**
     * 加载失败
     *
     * @param loadPage 加载的页数
     * @param e        error
     */
    public void onLoadFail(int loadPage, Throwable e) {
        if (loadPage == 1) {
            showErrorInfo(e.getMessage());
            onRefreshComplete();
        } else {
            onLoadMoreComplete();
        }
    }

    /**
     * 设置刷新是否可用，不要直接调用SwipeToLayout的方法
     *
     * @param enable 是否可用
     */
    public void setRefreshEnable(boolean enable) {
        if (isRefreshEnable()) {
            swipeToLoadLayout.setRefreshEnabled(enable);
        }
    }

    /**
     * 设置加载更多是否可用，用于在加载到的数据数量小于每页数量时。不要直接调用SwipeToLayout的方法
     * 注意要在{@linkplain #onLoadMoreComplete()}之后调用
     *
     * @param enable 是否可用
     */
    public void setLoadMoreEnable(boolean enable) {
        if (isLoadMoreEnable()) {
            swipeToLoadLayout.setLoadMoreEnabled(enable);
        }
    }

    /**
     * 刷新完成时，执行{@linkplain RecyclerView.Adapter#notifyDataSetChanged() 通知数据变化}
     * 和{@linkplain com.aspsine.swipetoloadlayout.SwipeToLoadLayout#setRefreshing(boolean) 完成刷新}
     */
    @SuppressLint("NotifyDataSetChanged")
    protected void onRefreshComplete() {
        //Adapter的notifyDataSetChanged方法调用，增加没有滚动且没有计算布局的判断
        AppUtil.adapterNotifyDataSetChanged(recyclerView, adapter);
        swipeToLoadLayout.setRefreshing(false);
    }

    /**
     * 加载完成时，执行{@linkplain RecyclerView.Adapter#notifyDataSetChanged() 通知数据变化}
     * 和{@linkplain SwipeToLoadLayout2#setLoadingMore(boolean, boolean) 完成加载更多并保持位置}
     */
    @SuppressLint("NotifyDataSetChanged")
    protected void onLoadMoreComplete() {
        //Adapter的notifyDataSetChanged方法调用，增加没有滚动且没有计算布局的判断
        AppUtil.adapterNotifyDataSetChanged(recyclerView, adapter);
        swipeToLoadLayout.setLoadingMore(false, true);
    }

    /**
     * 传递一个size，用于子类如果想调用notifyItemRangeInserted方法使用
     */
    protected void onLoadMoreComplete(int size) {
        onLoadMoreComplete();
    }

    /**
     * 显示没有数据时文字
     */
    protected void showEmptyInfo() {
        showErrorInfo(getEmptyString(), getEmptyImageResId());
    }

    /**
     * 强制显示空信息，用于adapter带有header等情况
     */
    public void forceShowEmptyInfo(boolean showEmpty, int deltaMargin) {
        if (showEmpty) {
            //先设置空数据信息
            showEmptyInfo();
            View emptyView = getRecyclerView().getEmptyView();
            if (emptyView != null) {
                RelativeLayout rlTop = emptyView.findViewById(R.id.rl_top);
                LinearLayout ll = (LinearLayout) rlTop.getParent();
                if (ll != null) {
                    //设置top margin
                    RecyclerView.Adapter<? extends RecyclerView.ViewHolder> adapter = getAdapter();
                    int header = 0;
                    if (adapter instanceof HeaderAndFooterWrapperAdapter) {
                        header = ((HeaderAndFooterWrapperAdapter) adapter).getHeadersCount();
                    }
                    int topMargin = 0;
                    for (int i = 0; i < header; i++) {
                        View view = getRecyclerView().getChildAt(i);
                        if (view != null) {
                            topMargin += view.getHeight();
                        }
                    }
                    if (ll.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
                        ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) ll.getLayoutParams();
                        layoutParams.topMargin = topMargin + deltaMargin;
                        ll.setLayoutParams(layoutParams);
                    }
                }
                //将其设为显示
                emptyView.setVisibility(View.VISIBLE);
            }
        } else {
            if (getRecyclerView().getEmptyView() != null) {
                getRecyclerView().getEmptyView().setVisibility(View.GONE);
            }
        }
    }

    public IRefreshContext<T> getRefreshContext() {
        return mRefreshContext;
    }

    public Context getContext() {
        return mRefreshContext.getContext();
    }

    /**
     * 显示错误信息
     */
    protected void showErrorInfo(String text) {
        showErrorInfo(text, getErrorImageResId());
    }

    protected void showErrorInfo(String text, int imageResId) {
        if (TextUtils.isEmpty(text)) {
            //由于image相对于text定位，所以不要设为gone
            mTvEmpty.setVisibility(View.INVISIBLE);
        } else {
            mTvEmpty.setVisibility(View.VISIBLE);
            mTvEmpty.setText(text);
        }
        if (imageResId > 0) {
            mIvEmpty.setImageResource(imageResId);
        }
    }

    public int getPageSize() {
        return mPageSize;
    }

    /**
     * 设置每页大小
     *
     * @param pageSize 默认为0，由服务器决定
     */
    public RefreshController<T> setPageSize(int pageSize) {
        mPageSize = pageSize;
        return this;
    }

    public int getEmptyImageResId() {
        return mEmptyImageResId;
    }

    public RefreshController<T> setEmptyImageResId(int emptyImageResId) {
        mEmptyImageResId = emptyImageResId;
        return this;
    }

    public String getEmptyString() {
        return mEmptyString;
    }

    public RefreshController<T> setEmptyString(String emptyString) {
        mEmptyString = emptyString;
        return this;
    }

    public int getEmptyBackgroundColorId() {
        return mEmptyBackgroundColorId;
    }

    public RefreshController<T> setEmptyBackgroundColorId(int emptyBackgroundColorId) {
        mEmptyBackgroundColorId = emptyBackgroundColorId;
        return this;
    }

    /**
     * 我一向不喜欢用string和int来区分不同的string参数，原因是有时候某个字段是int，setText的时候本应该显示其值string，却因为是int被当做了string id
     *
     * @param emptyStringResId 字符串资源id
     */
    public RefreshController<T> setEmptyStringResId(int emptyStringResId) {
        mEmptyString = mRefreshContext.getContext().getString(emptyStringResId);
        return this;
    }

    public int getErrorImageResId() {
        return mErrorImageResId;
    }

    public RefreshController<T> setErrorImageResId(int errorImageResId) {
        mErrorImageResId = errorImageResId;
        return this;
    }

    public List<T> getData() {
        return mData;
    }

    /**
     * 下拉刷新是否可用
     *
     * @return 默认为true
     */
    protected boolean isRefreshEnable() {
        return true;
    }

    /**
     * 上拉加载更多是否可用
     *
     * @return 默认为true
     */
    protected boolean isLoadMoreEnable() {
        return true;
    }

    public boolean isFirstLoad() {
        return mIsFirstLoad;
    }

    public void setFirstLoad(boolean firstLoad) {
        mIsFirstLoad = firstLoad;
    }

    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> getAdapter() {
        return adapter;
    }

    public EmptySupportedRecyclerView getRecyclerView() {
        return recyclerView;
    }

    public SwipeToLoadLayout2 getSwipeToLoadLayout() {
        return swipeToLoadLayout;
    }

    public boolean isShowBottomToast() {
        return mShowBottomToast;
    }

    public RefreshController<T> setShowBottomToast(boolean showBottomToast) {
        mShowBottomToast = showBottomToast;
        return this;
    }
}
