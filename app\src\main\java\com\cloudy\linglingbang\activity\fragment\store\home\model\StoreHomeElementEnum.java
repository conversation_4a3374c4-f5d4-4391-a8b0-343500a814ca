package com.cloudy.linglingbang.activity.fragment.store.home.model;

import com.cloudy.linglingbang.R;

import androidx.annotation.Nullable;

/**
 * 元素枚举
 *
 * <AUTHOR>
 * @date 2018/10/18
 */
public final class StoreHomeElementEnum {

    /**
     * 商品大图
     */
    @Deprecated
    public static StoreHomeElementEnum BIG_IMAGE = new StoreHomeElementEnum(1, R.layout.item_store_home_element_big_image);

    /**
     * 中间 banner
     */
    public static StoreHomeElementEnum MID_BANNER = new StoreHomeElementEnum(2, R.layout.item_store_home_element_ad);

    /**
     * 元素 1/2
     */
    public static StoreHomeElementEnum ELEMENT_1_2 = new StoreHomeElementEnum(3, R.layout.item_store_home_element_1_2, 1f / 2);

    /**
     * 元素 1/3
     */
    public static StoreHomeElementEnum ELEMENT_1_3 = new StoreHomeElementEnum(4, R.layout.item_store_home_element_1_3, 1f / 3);

    /**
     * 元素 2/3
     */
    public static StoreHomeElementEnum ELEMENT_2_3 = new StoreHomeElementEnum(5, R.layout.item_store_home_element_2_3, 2f / 3);

    /**
     * 商品 1/2
     */
    public static StoreHomeElementEnum COMMODITY_1_2 = new StoreHomeElementEnum(6, R.layout.item_store_home_commodity_1_2, 1f / 2);
    /**
     * 福利社商品 商品 1/2
     */
    public static StoreHomeElementEnum WELFARE_COMMODITY_1_2 = new StoreHomeElementEnum(100006, R.layout.item_commodity, 1f / 2);
    /**
     * 元素 1*4
     */
    public static StoreHomeElementEnum ELEMENT_1_4 = new StoreHomeElementEnum(8, R.layout.item_recycler_view);
    /**
     * 可左右滑动元素
     */
    public static StoreHomeElementEnum ELEMENT_SLIDE = new StoreHomeElementEnum(9, R.layout.item_recycler_view);
    /**
     * 经销商运营位元素
     */
    public static StoreHomeElementEnum ELEMENT_DEALER_SHIP = new StoreHomeElementEnum(10, R.layout.item_store_home_element_dealer);
    /**
     * 新商品列表 1+2N
     * 目前只画了一只图片（1+2N 中的1），2n 有商品组件（商品 1/2）组成
     */
    public static StoreHomeElementEnum COMMODITY_LIST_1_2N = new StoreHomeElementEnum(11, R.layout.item_store_home_element_1_2n);
    /**
     * 导航栏元素
     */
    public static StoreHomeElementEnum ELEMENT_NAV = new StoreHomeElementEnum(12, R.layout.item_store_home_element_nav);
    /**
     * 轮播运营位组件(大) 图片比例是670*552
     */
    public static StoreHomeElementEnum ELEMENT_BANNER_BIG = new StoreHomeElementEnum(13, R.layout.item_store_home_element_banner);
    /**
     * 轮播运营位组件(小) 图片比例是670*226
     */
    public static StoreHomeElementEnum ELEMENT_BANNER_SMALL = new StoreHomeElementEnum(14, R.layout.item_store_home_element_banner);
    /**
     * 推荐商品
     */
    public static StoreHomeElementEnum ELEMENT_LIFE_RECOMMEND = new StoreHomeElementEnum(15, R.layout.item_store_home_element_life_recommend);
    /**
     * 福利社商品 列表 1+2N
     * 目前只画了一只图片（1+2N 中的1），2n 有商品组件（商品 1/2）组成
     */
    public static StoreHomeElementEnum ELEMENT_WELFARE_RECOMMEND = new StoreHomeElementEnum(16, R.layout.item_store_home_element_1_2n);
    /**
     * 福利社商品 商品 1/2
     * v2.0
     */
    public static StoreHomeElementEnum WELFARE_COMMODITY_1_2_V2 = new StoreHomeElementEnum(17, R.layout.item_commodity_v2, 1f / 2);
    /**
     * 混合支付横向滑动商品
     * v1.0
     */
    public static StoreHomeElementEnum MIX_PAY_COMMODITY_HORIZONTAL = new StoreHomeElementEnum(18, R.layout.item_recycler_view, 1f / 2);

    /**
     * 轮播运营位组件(大全屏) 图片比例是750*445
     * 新增加的运营位图片比例
     */
    public static StoreHomeElementEnum ELEMENT_BANNER_BIG_MATCH_PARENT = new StoreHomeElementEnum(19, R.layout.item_store_home_element_banner_match_parent);

    /**
     * icon N*M 图标
     */
    public static StoreHomeElementEnum ELEMENT_ICON = new StoreHomeElementEnum(20, R.layout.item_recycler_view);

    /**
     * 瀑布流商品组件
     */
    public static StoreHomeElementEnum ELEMENT_STAGGERED_LIST = new StoreHomeElementEnum(21, R.layout.item_recycler_view);

    public static StoreHomeElementEnum[] values;

    static {
        values = new StoreHomeElementEnum[]{
                BIG_IMAGE,
                MID_BANNER,
                ELEMENT_1_2,
                ELEMENT_1_3,
                ELEMENT_2_3,
                COMMODITY_1_2,
                WELFARE_COMMODITY_1_2,
                WELFARE_COMMODITY_1_2_V2,
                ELEMENT_1_4,
                ELEMENT_SLIDE,
                ELEMENT_DEALER_SHIP,
                COMMODITY_LIST_1_2N,
                ELEMENT_NAV,
                ELEMENT_BANNER_BIG,
                ELEMENT_LIFE_RECOMMEND,
                ELEMENT_BANNER_SMALL,
                MIX_PAY_COMMODITY_HORIZONTAL,
                ELEMENT_BANNER_BIG_MATCH_PARENT,
                ELEMENT_ICON,
                ELEMENT_STAGGERED_LIST,
        };
    }

    /**
     * 类型，与服务器保持统一
     * 没有使用 original 是为了防止不连续及交换位置
     */
    private final int mType;
    /**
     * 布局资源 id
     */
    private final int mLayoutResId;
    /**
     * span 所占比例
     */
    private final float mSpanRatio;

    StoreHomeElementEnum(int type, int layoutResId) {
        this(type, layoutResId, 0);
    }

    StoreHomeElementEnum(int type, int layoutResId, float spanRatio) {
        mType = type;
        mLayoutResId = layoutResId;
        mSpanRatio = spanRatio;
    }

    public static StoreHomeElementEnum valueOf(int type) {
        for (StoreHomeElementEnum componentEnum : values) {
            if (componentEnum.getType() == type) {
                return componentEnum;
            }
        }
        return null;
    }

    public int getType() {
        return mType;
    }

    public int getLayoutResId() {
        return mLayoutResId;
    }

    public float getSpanRatio() {
        return mSpanRatio;
    }

    @Override
    public boolean equals(@Nullable Object obj) {
        if (obj instanceof StoreHomeElementEnum) {
            StoreHomeElementEnum that = (StoreHomeElementEnum) obj;
            return getType() == that.getType();
        }
        return super.equals(obj);
    }
}
