package com.cloudy.linglingbang.activity.store.commodity.adapter

import android.content.Context
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import butterknife.BindView
import butterknife.ButterKnife
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.store.commodity.CommodityDetailActivity
import com.cloudy.linglingbang.app.widget.banner.AdRoundImageView
import com.cloudy.linglingbang.app.widget.banner.BannerView
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder
import com.cloudy.linglingbang.model.SourceModel
import com.cloudy.linglingbang.model.store.commodity.CartCommodity

/**
 * 总价类赠品列表的adapter
 *
 * <AUTHOR>
 * @date 2022/10/13
 */
class ShoppingCartTotalGiftAdapter(context: Context?, data: List<CartCommodity>?) :
    BaseRecyclerViewAdapter<CartCommodity>(context, data) {


    override fun createViewHolder(itemView: View?): BaseRecyclerViewHolder<CartCommodity> {
        return ShoppingCarTotalGiftViewHolder(itemView)
    }

    override fun getItemLayoutRes(viewType: Int): Int {
        return R.layout.item_shopping_cart_total
    }


    inner class ShoppingCarTotalGiftViewHolder(itemView: View?) :
        BaseRecyclerViewHolder<CartCommodity>(itemView) {

        @JvmField
        @BindView(R.id.iv_sku_pic)
        var ivSkuPic: AdRoundImageView? = null

        @JvmField
        @BindView(R.id.tv_sku_name)
        var tvSkuName: TextView? = null


        @JvmField
        @BindView(R.id.tv_commodity_price)
        var tvCommodityPrice: TextView? = null

        @JvmField
        @BindView(R.id.tv_count)
        var tvCount: TextView? = null

        @JvmField
        @BindView(R.id.ll_root)
        var llRoot: LinearLayout? = null


        override fun initItemView(itemView: View) {
            super.initItemView(itemView)
            ButterKnife.bind(this, itemView)

        }


        override fun bindTo(bean: CartCommodity?, position: Int) {
            super.bindTo(bean, position)
            bean?.apply {
                ivSkuPic?.createImageLoad(skuImage)?.load()
                tvSkuName?.text = commodityName
                tvCommodityPrice?.text = "¥$sellPriceStr"
                tvCount?.text = "X$quantity"
                llRoot?.setOnClickListener {
                    CommodityDetailActivity.startActivity(llRoot?.context!!, commodityId,
                        SourceModel(
                            SourceModel.POSITION_TYPE.SHOPPING_CAR_TYPE,
                            SourceModel.POSITION_TYPE.SHOPPING_CAR_VALUE
                        )
                    )
                }

            }
        }

    }
}