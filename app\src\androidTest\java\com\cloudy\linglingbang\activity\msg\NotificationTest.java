package com.cloudy.linglingbang.activity.msg;

import com.cloudy.linglingbang.BaseInstrumentedTest;
import com.cloudy.linglingbang.app.util.NotificationUtils;
import com.cloudy.linglingbang.app.util.ToastUtil;

/**
 * 权限检查测试
 *
 * <AUTHOR>
 * @date 2018/8/3
 */
public class NotificationTest extends BaseInstrumentedTest {
    @Override
    public void test() {
        super.test();
        if (!NotificationUtils.isNotificationEnabled(getContext())) {
            NotificationUtils.openNotificationSetting(getContext());
        } else {
            ToastUtil.showMessage(getContext(), "有权限");
        }
    }
}
