package com.cloudy.linglingbang.activity.fragment.store.home;

import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.aspsine.swipetoloadlayout.OnRefreshListener;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseScrollTabViewPagerFragment;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.fragment.store.home.model.FragmentInitHelper;
import com.cloudy.linglingbang.activity.fragment.store.home.model.StoreHomeConstant;
import com.cloudy.linglingbang.app.util.DeprecatedUtils;
import com.cloudy.linglingbang.app.util.ViewHolderUtils;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.PagerSlidingTabStrip;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BackgroundSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.store.home.StoreLayoutPage;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.viewpager.widget.PagerAdapter;
import io.reactivex.rxjava3.observers.ResourceObserver;

/**
 * 经销商专区
 *
 * <AUTHOR>
 * @date 2020/5/9
 */
public class StoreDealerFragment extends BaseScrollTabViewPagerFragment<Fragment> {
    /**
     * 组件页
     */
    private String mPageCode;
    /**
     * 二组页名数组
     */
    private String[] mPageNameArray;
    /**
     * 二组页 code 列表
     */
    private List<String> mPageCodeList;
    /**
     * 用于初始化
     */
    private FragmentInitHelper mInitHelper;
    /**
     * 空布局
     */
    private LinearLayout mLlEmpty;

    public static Fragment newInstance(String pageCode) {
        return IntentUtils.setFragmentArgument(new StoreDealerFragment(), pageCode);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        mPageCode = IntentUtils.getFragmentArgument(this);
        mInitHelper = new FragmentInitHelper(this) {
            @Override
            public void init() {
                //不调用 super 的 init 赋值
                //super.init();
                initLayoutPages(false);
            }
        };
        super.onCreate(savedInstanceState);
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_store_home_dealer;
    }

    @Override
    protected PagerAdapter createViewPagerAdapter(final List<Fragment> data, final String[] titles) {
        return new FragmentStatePagerAdapter(getChildFragmentManager()) {
            @Override
            public Fragment getItem(int position) {
                return data.get(position);
            }

            @Override
            public int getCount() {
                return data.size();
            }

            @Nullable
            @Override
            public CharSequence getPageTitle(int position) {
                return titles[position];
            }
        };
    }

    @Override
    protected void initViews() {
        //空布局
        mLlEmpty = mRootView.findViewById(R.id.ll_empty);
        mLlEmpty.setBackgroundColor(Color.TRANSPARENT);
        TextView tvEmpty = mLlEmpty.findViewById(R.id.tv_empty_desc);
        ImageView ivEmpty = mLlEmpty.findViewById(R.id.iv_empty);
        tvEmpty.setText(R.string.store_home_empty);
        ivEmpty.setImageResource(R.drawable.ic_store_empty_list);
        ViewHolderUtils.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                initLayoutPages(false);
            }
        }, tvEmpty, ivEmpty);
        //tab
        PagerSlidingTabStrip tabStrip = mRootView.findViewById(R.id.tabs);
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.MATCH_PARENT);
        layoutParams.rightMargin = mRootView.getResources().getDimensionPixelOffset(R.dimen.normal_30);
        tabStrip.setDefaultTabLayoutParams(layoutParams);
        tabStrip.setListener(new PagerSlidingTabStrip.SingleListener() {
            @Override
            public TextView createTextTab(Context context) {
                //需要 Inflate
                return (TextView) DeprecatedUtils.inflateWithNullRoot(LayoutInflater.from(context), R.layout.item_store_home_tab_button);
            }
        });
        //获取或是调用 super
        if (mInitHelper != null) {
            if (!mInitHelper.isInitialized()) {
                //还未初始化，不调用父类的初始化
                mInitHelper.checkAndInit();
                return;
            }
        }
        super.initViews();
    }

    @Override
    protected List<Fragment> createAdapterData() {
        List<Fragment> fragmentList = new ArrayList<>();
        for (int i = 0; i < mPageCodeList.size(); i++) {
            String pageCode = mPageCodeList.get(i);
            //长度一致，不需判断
            String pageName = mPageNameArray[i];

            //设置参数
            StoreHomeTabFragment fragment = new StoreHomeTabFragment();
            Bundle bundle = new Bundle();
            bundle.putString(IntentUtils.INTENT_EXTRA_COMMON, pageCode);
            bundle.putSerializable(IntentUtils.INTENT_EXTRA_FROM, new SensorsUtils.StoreHomeAnchor(pageCode, StoreHomeConstant.getPageNameByPageCode(mPageCode) + "-" + pageName, mPageCode));
            fragment.setArguments(bundle);

            //设置刷新加调
            fragment.setOnRefreshListener(new OnRefreshListener() {
                @Override
                public void onRefresh() {
                    initLayoutPages(true);
                }
            });
            fragmentList.add(fragment);
        }
        return fragmentList;
    }

    @Override
    protected String[] getTitles() {
        //首次打开的埋点
        SensorsUtils.sensorsClickBtn(mPageNameArray[0], "商城-" + StoreHomeConstant.getPageNameByPageCode(mPageCode));
        return mPageNameArray;
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (mInitHelper != null) {
            mInitHelper.setUserVisibleHint(isVisibleToUser);
        }
    }

    /**
     * 初始化各二级页
     *
     * @param refresh 是否来自下拉刷新
     */
    private void initLayoutPages(boolean refresh) {
        ResourceObserver<List<StoreLayoutPage>> subscriber;
        if (refresh) {
            //刷新，后台加载
            subscriber = new BackgroundSubscriber<List<StoreLayoutPage>>(getContext()) {
                @Override
                public void onSuccess(List<StoreLayoutPage> storeLayoutPages) {
                    super.onSuccess(storeLayoutPages);
                    parseLayoutPages(storeLayoutPages, true);
                }

                @Override
                public void onFailure(Throwable e) {
                    super.onFailure(e);
                    showEmptyInfo();
                }
            };
        } else {
            subscriber = new ProgressSubscriber<List<StoreLayoutPage>>(getContext()) {
                @Override
                public void onSuccess(List<StoreLayoutPage> storeLayoutPages) {
                    super.onSuccess(storeLayoutPages);
                    parseLayoutPages(storeLayoutPages, false);
                }

                @Override
                public void onFailure(Throwable e) {
                    super.onFailure(e);
                    showEmptyInfo();
                }
            };
        }
        L00bangRequestManager2.getServiceInstance()
                .getLayoutPage(mPageCode)
                .compose(L00bangRequestManager2.<List<StoreLayoutPage>>setSchedulers())
                .subscribe(subscriber);
    }

    /**
     * 解析各二级页
     */
    private void parseLayoutPages(List<StoreLayoutPage> storeLayoutPages, boolean refresh) {
        if (isDetached()) {
            /*
            已经移除了，不应该更新 fragment，防止 adapter.restoreState 中的 fragment 无法恢复
            直接 return，下一次 setUserVisibleHint 重新初始化
             */
            return;
        }
        int size;
        if (storeLayoutPages != null && (size = storeLayoutPages.size()) > 0) {
            hiddenEmptyInfo();
            //有布局页才置为初始化，否则每次切过来都初始化一下
            if (mInitHelper != null) {
                mInitHelper.setInitialized(true);
            }
            String[] pageNameArray = new String[size];
            List<String> pageCodeList = new ArrayList<>(size);
            for (int i = 0; i < size; i++) {
                StoreLayoutPage layoutPage = storeLayoutPages.get(i);
                pageNameArray[i] = layoutPage.getPageName();
                pageCodeList.add(layoutPage.getPageCode());
            }
            //赋值完成，判断是否相同
            if (Arrays.equals(pageNameArray, mPageNameArray) && pageCodeList.equals(mPageCodeList)) {
                return;
            }
            mPageNameArray = pageNameArray;
            mPageCodeList = pageCodeList;
            //调用 super
            super.initViews();
            if (refresh) {
                if (mViewPager != null) {
                    //重新赋为 0，如果不赋为 0 ，那么顺序变化、个数减少时，都不太好处理
                    mViewPager.setCurrentItem(0);
                    if (tabs != null) {
                        tabs.setSelectedPosition(0);
                        tabs.updateTabStyles();
                    }
                }
            }
        } else {
            showEmptyInfo();
        }
    }

    private void showEmptyInfo() {
        if (mViewPager.getAdapter() == null || mViewPager.getAdapter().getCount() <= 0) {
            mLlEmpty.setVisibility(View.VISIBLE);
        }
    }

    private void hiddenEmptyInfo() {
        mLlEmpty.setVisibility(View.GONE);
    }

}
