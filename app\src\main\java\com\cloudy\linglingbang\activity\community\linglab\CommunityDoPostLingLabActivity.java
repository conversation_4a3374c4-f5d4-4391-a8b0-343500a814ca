package com.cloudy.linglingbang.activity.community.linglab;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.text.InputFilter;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.baidu.mapapi.search.core.PoiInfo;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.community.CommunityDoPostImageTextActivity;
import com.cloudy.linglingbang.activity.community.HotTopicListActivity;
import com.cloudy.linglingbang.activity.community.SelectLocationActivity;
import com.cloudy.linglingbang.app.log.LogUtils;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.DeviceUtil;
import com.cloudy.linglingbang.app.util.InputLengthLimitFilter;
import com.cloudy.linglingbang.app.util.NetworkUtil;
import com.cloudy.linglingbang.app.util.PreferenceUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.TopicUtils;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.ExpressionEditText;
import com.cloudy.linglingbang.app.widget.dialog.alert.BaseListDialog;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog3;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonListDialog;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonSelectListDialog;
import com.cloudy.linglingbang.app.widget.item.CommonItemWithLeftIcon;
import com.cloudy.linglingbang.app.widget.recycler.EmptySupportedRecyclerView;
import com.cloudy.linglingbang.constants.AppConstants;
import com.cloudy.linglingbang.db.GreenDaoManager;
import com.cloudy.linglingbang.greendao.PostCardDraftDao;
import com.cloudy.linglingbang.greendao.PostCardItemDao;
import com.cloudy.linglingbang.model.ImageModel;
import com.cloudy.linglingbang.model.community.ColumnInfo;
import com.cloudy.linglingbang.model.community.ContentGroupInfo;
import com.cloudy.linglingbang.model.community.Topic;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.postcard.PostCardDraft;
import com.cloudy.linglingbang.model.postcard.PostCardItem;
import com.cloudy.linglingbang.model.postcard.PostCommodity;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.tag.LingLabImageBean;
import com.cloudy.linglingbang.model.user.User;
import com.donkingliang.imageselector.utils.ImageSelector;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import butterknife.BindView;
import butterknife.OnClick;

import static com.cloudy.linglingbang.R.string.community_do_post_dialog_save_no_exit;
import static com.cloudy.linglingbang.R.string.community_do_post_dialog_save_yes;

/**
 * 社区发菱感帖页面
 *
 * <AUTHOR>
 * @date 2017/12/28
 */

public class CommunityDoPostLingLabActivity extends CommunityDoPostImageTextActivity {

    @BindView(R.id.ll_bottom)
    LinearLayout mLlBottom;

//    @BindView(R.id.iv_position)
//    ImageView mIvPosition;
//    @BindView(R.id.tv_position)
//    TextView mTvPosition;
    /**
     * 内容文本
     */
    @BindView(R.id.et_text)
    ExpressionEditText mEtText;

    /**
     * 标题
     */
    @BindView(R.id.et_image_text_post_title)
    EditText mEtPostTitle;
    @BindView(R.id.tv_topic_name)
    TextView mTVTopicName;

    @BindView(R.id.tv_topic)
    CommonItemWithLeftIcon mTvTopic;
    //    @BindView(R.id.ll_car)
//    LinearLayout mLlCar;
    @BindView(R.id.tv_select_car)
    CommonItemWithLeftIcon mTVSelectCar;
//    @BindView(R.id.iv_car)
//    ImageView mIvCar;

    @BindView(R.id.rv_img)
    EmptySupportedRecyclerView mRvImg;

    @BindView(R.id.rl_header)
    RelativeLayout mRlHeader;

    /**
     * 存草稿按钮
     */
    @BindView(R.id.iv_save_draft)
    ImageView mIvSaveDraft;

    private final int COLUMN_NUMBER = 3;

    public List<LingLabImageBean> mLingLabImageBeanList;
    private int mImageSize;

    private LingLabImageAdapter mLingLabImageAdapter;

    public static final int REQUEST_CODE_SELECT_TOPIC = 10;

    private Topic mTopic;
    public static final String TOPIC_KEY = "topic";
    public static final String GROUP_INFO_KEY = "groupInfo";
    //车型信息
    private ContentGroupInfo groupInfo;
    private Intent mUserNotify;

    private static PostCardDraftDao mPostCardDraftDao;

    private Long mDraftId;

    public static final String EXTRA_DRAFT = "extra_draft";
    public static final String EXTRA_TOPIC = "extra_topic";

    /**
     * 是否来自话题
     */
    private boolean mIsFromTopic = false;

    @Override
    public void loadViewLayout() {
        setContentView(R.layout.activity_community_do_post_ling_lab);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onClickBack();
            }
        });
    }

    @Override
    protected int getMenu_publish() {
        return R.menu.menu_ling_lab_publish;
    }

    @Override
    public void initialize() {
//        int top = Math.max(NotchScreenUtils.getNotchSafeWH()[1], StatusBarUtils.getStatusBarHeight(this));
//        mRlHeader.setPadding(mRlHeader.getPaddingLeft(), mRlHeader.getPaddingTop() + top, mRlHeader.getPaddingRight(), mRlHeader.getPaddingBottom());
        mPostCardDraftDao = GreenDaoManager.getInstance().getDaoSession().getPostCardDraftDao();
        mLingLabImageBeanList = new ArrayList<>();
        initChooseImageAdapter();
        mRvImg.setLayoutManager(new GridLayoutManager(this, COLUMN_NUMBER));
        mRvImg.setAdapter(mLingLabImageAdapter);
        mEtText.setFilters(new InputFilter[]{new InputLengthLimitFilter(6000, true, getString(R.string.post_detail_input_reply_max_length, 6000))});
        mPostCardItemDao = GreenDaoManager.getInstance().getDaoSession().getPostCardItemDao();
        mIntentExtra = (IntentExtra) getIntentExtra(null);

        if (mIntentExtra == null) {
            String json = getIntent().getStringExtra("p");
            if (!TextUtils.isEmpty(json)) {
                mIntentExtra = new Gson().fromJson(json, IntentExtra.class);
            }
        }

        ArrayList<LingLabImageBean> list = getIntent().getParcelableArrayListExtra("imgList");
        mTopic = (Topic) getIntent().getSerializableExtra(EXTRA_TOPIC);
        if (mTopic != null) {
            mIsFromTopic = true;
            initFromTopicUI();
        }
        if (list != null) {
            mLingLabImageBeanList.addAll(list);
        }
        mLingLabImageBeanList.add(null);
        mPostType = PostCard.PostType.LING_SENSE;
        mLingLabImageAdapter.notifyDataSetChanged();
        mLlLocation.setVisibility(View.VISIBLE);
        setLeftTitle(getString(R.string.title_post_ling_lab));
        initRecyclerView();
        if (mIntentExtra == null) {
            return;
        }
        mOpenFrom = mIntentExtra.getOpenFrom();
//        expression_view_pager.setEditText(mEtText);
        mPostType = mIntentExtra.getPostType();

        if (mPostType == PostCard.PostType.EDIT_LING_SENSE) {
            //编辑时候不显示位置信息
            mLlBottom.setVisibility(View.GONE);
            //不展示存草稿
            mIvSaveDraft.setVisibility(View.GONE);
            setLeftTitle(getString(R.string.title_post_edit_ling_lab));
            mPostCard = mIntentExtra.getPostCard();
            mLingLabImageBeanList.clear();
            for (int i = 0; i < mPostCard.getImgTexts().size() - 1; i++) {
                mLingLabImageBeanList.add(new LingLabImageBean(mPostCard.getImgTexts().get(i).getImg(), i));
                List<PostCommodity> postCommodityList = mPostCard.getImgTexts().get(i).getPostCommodityList();
                if (postCommodityList == null || postCommodityList.isEmpty()) {
                    continue;
                }
                for (PostCommodity postCommodity : postCommodityList) {
                    postCommodity.setCanMove(true);
                }
                PreferenceUtil.putPreference(this, mPostCard.getImgTexts().get(i).getImg() + "_" + User.shareInstance().getUserIdStr() + "_" + i, new Gson().toJson(postCommodityList));
                LogUtils.e("测试标签存储值" + mPostCard.getImgTexts().get(i).getImg() + ",,,," + new Gson().toJson(postCommodityList));
            }
            mLingLabImageBeanList.add(null);
            mLingLabImageAdapter.notifyDataSetChanged();
            mEtText.setText(mPostCard.getContentString());
            mEtPostTitle.setText(mPostCard.getPostTitle());
            if (!TextUtils.isEmpty(mPostCard.getTopicName())) {
                mTVTopicName.setVisibility(View.VISIBLE);
                mTVTopicName.setText(TopicUtils.getNameWithHashtag(mPostCard.getTopicName()));
            }
        } else {
            mDraftId = mIntentExtra.getDraftId();
            if (mDraftId != null && mDraftId > 0) {
                showDraft();
            }
            mLlBottom.setVisibility(View.VISIBLE);
            setLeftTitle(getString(R.string.title_post_ling_lab));
        }
        initRecyclerView();
    }

    /**
     * 展示话题的UI
     */
    private void initFromTopicUI() {
        initTopic();
        mTvTopic.setVisibility(View.GONE);
    }

    @Override
    protected boolean needSaveDraft() {
        //灵感贴不主动保存草稿
        return false;
    }

    //打开编辑帖子
    public static void startActivity(Context context, PostCard postCard, int jumpAfterSuccess, int openFrom) {
        IntentExtra intentExtra = new IntentExtra(PostCard.PostType.EDIT_LING_SENSE, "communityId", postCard.getPostId());
        intentExtra.setJumpAfterSuccess(jumpAfterSuccess);
        intentExtra.setOpenFrom(openFrom);
        intentExtra.setPostCard(postCard);
        IntentUtils.startActivity(context, CommunityDoPostLingLabActivity.class, intentExtra);
    }

    /**
     * 进入发菱感帖页面方法
     */
    public static void startActivity(Context context, int postType, String communityId, int jumpAfterSuccess, int openFrom) {
        startActivity(context, postType, communityId, jumpAfterSuccess, openFrom, null);
    }

    /**
     * 进入发菱感帖页面方法,带着图片进入
     */
    public static void startActivity(Context context, ArrayList<LingLabImageBean> lingLabImageBeanList, Topic topic) {
        Intent intent = new Intent(context, CommunityDoPostLingLabActivity.class);
        intent.putParcelableArrayListExtra("imgList", lingLabImageBeanList);
        intent.putExtra(EXTRA_TOPIC, topic);
        context.startActivity(intent);
    }

    /**
     * 从草稿进入
     */
    public static void startActivity(Context context, int postType, String communityId, int jumpAfterSuccess, int openFrom, Long draftId) {
        IntentExtra intentExtra = new IntentExtra(postType, communityId, null);
        intentExtra.setJumpAfterSuccess(jumpAfterSuccess);
        intentExtra.setOpenFrom(openFrom);
        intentExtra.setDraftId(draftId);
        IntentUtils.startActivity(context, CommunityDoPostLingLabActivity.class, intentExtra);
    }

    /**
     * 初始化图片适配器
     */
    private void initChooseImageAdapter() {
        if (mLingLabImageAdapter == null) {
            mLingLabImageAdapter = new LingLabImageAdapter(mRvImg, mLingLabImageBeanList);
            mLingLabImageAdapter.setActivity(CommunityDoPostLingLabActivity.this);
        }
        int screenWidth = DeviceUtil.getScreenWidth();
        int contentWidth = screenWidth - mRvImg.getPaddingLeft() - mRvImg.getPaddingRight();
        mImageSize = (int) (contentWidth / COLUMN_NUMBER - (getResources().getDimension(R.dimen.normal_8) * 3));
        mLingLabImageAdapter.setImageSize(mImageSize);

        mLingLabImageAdapter.setOnImageDeleteListener(new LingLabImageAdapter.OnImageDeleteListener() {
            @Override
            public void removeImage(int index) {
                String url = mLingLabImageAdapter.getData().get(index).getImgPath();
                //删除图片的下标
                int position = mLingLabImageAdapter.getData().get(index).getTag();
                if (url.startsWith("http")) {//删除的时候同时删除imgText中的数据
                    for (int i = 0; i < mPostCard.getImgTexts().size(); i++) {
                        if (url.equals(mPostCard.getImgTexts().get(i).getImg())) {
                            mPostCard.getImgTexts().remove(i);
                            //mChooseImageController.setEditPicCount(mPostCard.getImgTexts().size() - 1);
                            break;
                        }
                    }
                }
                mLingLabImageBeanList.remove(index);
                //删除图片的时候同时删除添加的标记
                PreferenceUtil.removePreference(CommunityDoPostLingLabActivity.this, url + "_" + User.shareInstance().getUserIdStr() + "_" + position);
                mLingLabImageAdapter.notifyDataSetChanged();
                initRecyclerView();
            }
        });
    }

    /**
     * 点击保存草稿
     */
    @Override
    protected void onClickSaveDraft() {
        super.onClickSaveDraft();
        if (hasNoContent()) {
            ToastUtil.showMessage(this, "未输入内容不可保存草稿哦~");
            return;
        }
        showSaveDraftDialog(false);
    }

    /**
     * 是否有没有内容
     * @return
     */
    private boolean hasNoContent() {
        return (mLingLabImageBeanList == null || mLingLabImageBeanList.size() <= 1) && mEtText.getText().toString().trim().length() <= 0 && mEtPostTitle.getText().toString().trim().length() <= 0;
    }

    protected void showSaveDraftDialog(boolean isFinish) {
        super.onClickSaveDraft();
        String message;
        String btnOkText;
        String btnCancelText;
        //如果是编辑菱感贴
        if (isFinish && mPostType == PostCard.PostType.EDIT_LING_SENSE) {
            message = getString(R.string.community_do_post_dialog_exit_confirm);
            btnCancelText = getString(R.string.community_do_post_dialog_exit_yes_exit);
            btnOkText = getString(R.string.community_do_post_dialog_exit_no_continue);
        } else {
            message = getString(R.string.community_do_post_dialog_save_confirm_new);
            btnOkText = getString(R.string.community_do_post_dialog_save_draft_yes);
            btnCancelText = getString(R.string.community_do_post_dialog_save_draft_cancel);
        }
        Dialog dialog = new CommonAlertDialog3(this, message, btnOkText, btnCancelText, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
//                if (needSaveDraft()) {
////                    saveImageAndText();
////                }
////                onExit();
////                finish();
                //如果不是编辑模式的话，保存草稿
                if( mPostType != PostCard.PostType.EDIT_LING_SENSE){
                    saveImageAndText(isFinish);
                }
                dialog.dismiss();
            }
        }, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                //选择直接退出
//                if (needSaveDraft()) {
//                    deleteCachePost();
//                    finish();
//                } else {
//                    dialog.dismiss();
//                }
                if (isFinish) {
                    finish();
                }
                dialog.dismiss();
            }
        });
        dialog.show();

    }

    private void initRecyclerView() {
        int recyclerViewHeight = mImageSize * ((mLingLabImageBeanList.size() - 1) / COLUMN_NUMBER + 1);
        ViewGroup.LayoutParams layoutParams = mRvImg.getLayoutParams();
        layoutParams.height = recyclerViewHeight;
        mRvImg.setLayoutParams(layoutParams);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
//        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == REQUEST_CODE_SELECT_LOCATION && resultCode == RESULT_OK) {
            poiInfo = (PoiInfo) IntentUtils.getExtra(data, null);
            showLocation();
            if (mLlLocation != null) {
                mLlLocation.getTvLeft().setText(poiInfo == null ? getResources().getString(R.string.community_do_post_select_location) : poiInfo.getName());
            }
        } else if (requestCode == REQUEST_CODE_SELECT_USER && resultCode == RESULT_OK) {
            setUserNotify(data);
        } else if (requestCode == REQUEST_CODE_SELECT_TOPIC && resultCode == RESULT_OK) {
            mTopic = (Topic) IntentUtils.getExtra(data, null);
            initTopic();
        } else {
            if (data != null) {
                List<String> images = data.getStringArrayListExtra(ImageSelector.SELECT_RESULT);
                if (images != null) {
                    mLingLabImageBeanList.remove(null);
                    for (int i = 0; i < images.size(); i++) {
                        LingLabImageBean lingLabImageBean = new LingLabImageBean(images.get(i), mLingLabImageBeanList.size());
                        mLingLabImageBeanList.add(lingLabImageBean);
                    }
                    mLingLabImageBeanList.add(null);
                    mLingLabImageAdapter.notifyDataSetChanged();
                    initRecyclerView();
                }
            }
        }
    }

    private void initTopic() {
        if (mTopic != null) {
            mTVTopicName.setVisibility(View.VISIBLE);
            mTVTopicName.setText(TopicUtils.getNameWithHashtag(mTopic.getTopicName()));
        } else {
            mTopic = null;
            mTVTopicName.setVisibility(View.GONE);
        }
    }

    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {
    }

    @Override
    protected void hideKeyboard() {
        if (mEtText != null) {
            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            if (imm != null) {
                imm.hideSoftInputFromWindow(mEtText.getWindowToken(), 0);
            }
        }
    }

    @Override
    protected void showKeyBoard() {
        if (mEtText != null) {
            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            if (imm != null) {
                imm.showSoftInput(mEtText, InputMethodManager.SHOW_FORCED);
            }
        }
    }

    //发布，不继承父类
    @Override
    protected void onClickMenu() {
        if (!AppUtil.checkLogin(this)) {
            return;
        }
        SensorsUtils.sensorsClickBtn("点击发布图文-提交", " 图文发布页", " 图文发布页");
        doPost();
    }

    @Override
    protected void doPost() {
        if (!isCommit) {
            if (mLingLabImageBeanList == null || mLingLabImageBeanList.size() <= 1) {
                ToastUtil.showMessage(this, "请至少选择1张图片~");
                return;
            }
            if (mEtText.getText().toString().length() < 10) {
                ToastUtil.showMessage(this, getString(R.string.car_buying_do_post_input_more_word_toast, 10));
                return;
            }
            postContent = mEtText.getText().toString().trim();
            postTitle = mEtPostTitle.getText().toString().trim();
            //检查网络是否可用
            if (NetworkUtil.isNetworkAvailable(this)) {
                isCommit = true;
                if (mPostType == PostCard.PostType.EDIT_LING_SENSE) {//编辑
                    mPostCard.setPostTitle(postTitle);
                    mPostCard.getImgTexts().get(mPostCard.getImgTexts().size() - 1).setText(postContent);
                    if (mLingLabImageBeanList != null && mLingLabImageBeanList.size() > 1) {
                        ArrayList<String> preCompress = new ArrayList<>();
                        for (int i = 0; i < mLingLabImageBeanList.size() - 1; i++) {
                            if (null == mLingLabImageBeanList.get(i)) {
                                continue;
                            }
                            if (isExistFIle(mLingLabImageBeanList.get(i).getImgPath())) {
                                preCompress.add(mLingLabImageBeanList.get(i).getImgPath());
                            }
                        }
                        if (preCompress.size() > 0) {
                            compressPic(preCompress);
                        } else {//没有新增图片，但是此处需要注意处理图片排序的问题
                            //因为mLingLabBeanList多一个null，ImgTexts多一项文本内容，古size还是一样的
                            List<PostCardItem> sortPostCardItem = new ArrayList<>();
                            if (mLingLabImageBeanList.size() == mPostCard.getImgTexts().size()) {
                                for (int i = 0; i < mLingLabImageBeanList.size() - 1; i++) {
                                    if (null == mLingLabImageBeanList.get(i)) {
                                        continue;
                                    }
                                    String imgUrl = mLingLabImageBeanList.get(i).getImgPath();
                                    if (TextUtils.isEmpty(imgUrl)) {
                                        continue;
                                    }
                                    for (PostCardItem postCardItem1 : mPostCard.getImgTexts()) {
                                        if (postCardItem1.getImg().equals(imgUrl)) {
                                            postCardItem1.setPostCommodityList(getPostCommodityList(imgUrl, String.valueOf(mLingLabImageBeanList.get(i).getTag())));
                                            sortPostCardItem.add(postCardItem1);
                                            //移除之后少遍历一次
                                            mPostCard.getImgTexts().remove(postCardItem1);
                                            break;
                                        }
                                    }
                                }
                            }
                            mPostCard.getImgTexts().removeAll(sortPostCardItem);
                            mPostCard.getImgTexts().addAll(0, sortPostCardItem);
                            commit(mPostCard);
                        }

                    } else {//没有修改图片
                        commit(mPostCard);
                    }
                } else {//发帖
                    ArrayList<String> preCompress = new ArrayList<>();
                    for (int i = 0; i < mLingLabImageBeanList.size() - 1; i++) {
                        if (null == mLingLabImageBeanList.get(i)) {
                            continue;
                        }
                        if (isExistFIle(mLingLabImageBeanList.get(i).getImgPath())) {
                            preCompress.add(mLingLabImageBeanList.get(i).getImgPath());
                        }
                    }
                    compressPic(preCompress);
                }

            } else {
                ToastUtil.showMessage(this, getString(R.string.common_network_unavailable));
            }
        }

    }

    @Override
    public void onBackPressed() {
        if (hasNoContent()) {
            finish();
            return;
        }
        showSaveDraftDialog(true);
    }

    private void onClickBack() {
        String message = null;
        String btnOkText = null;
        String btnCancelText = null;
        //如果是编辑菱感贴
        if (mPostType == PostCard.PostType.EDIT_LING_SENSE) {
            message = getString(R.string.community_do_post_dialog_exit_confirm);
            btnCancelText = getString(R.string.community_do_post_dialog_exit_yes_exit);
            btnOkText = getString(R.string.community_do_post_dialog_exit_no_continue);
        } else {
            if ((mLingLabImageBeanList != null && mLingLabImageBeanList.size() > 1) || !TextUtils.isEmpty(mEtText.getText().toString().trim()) || !TextUtils.isEmpty(mEtPostTitle.getText().toString().trim())) {
                message = getString(R.string.community_do_post_dialog_save_confirm);
                btnOkText = getString(community_do_post_dialog_save_yes);
                btnCancelText = getString(community_do_post_dialog_save_no_exit);
            } else {
                onExit();
                finish();
                return;
            }
        }

        Dialog dialog = new CommonAlertDialog(this, message, btnOkText, btnCancelText, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (mPostType == PostCard.PostType.EDIT_LING_SENSE) {
                    dialog.dismiss();
                } else {
                    saveImageAndText();
                    onExit();
                    finish();
                }

            }
        }, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                //直接退出的时候也清除标签
                deleteCacheTagList();
                //选择直接退出
                deleteCachePost();
                finish();
            }
        });
        dialog.show();
    }

    @Override
    protected void saveImageAndText() {
        saveImageAndText(false);
    }

    protected void saveImageAndText(boolean isFinish) {

        //获取草稿列表
        long count = mPostCardDraftDao.queryBuilder()
                .where(PostCardDraftDao.Properties.UserIdStr.eq(User.shareInstance().getUserIdStr()))
                .count();
        if ((mDraftId == null || mDraftId <= 0) && count >= AppConstants.DRAFT_MAX_COUNT) {
            ToastUtil.showMessage(this, "草稿箱已满，清理一下吧~");
            return;
        }
        PostCardDraft postCardDraft = new PostCardDraft();
        String postTitle = et_post_title.getText().toString();
        String postContent = mEtText.getText().toString();
        postCardDraft.setTitle(postTitle);
        postCardDraft.setContent(postContent);
        if (poiInfo != null) {
            postCardDraft.setPoiInfoStr(new Gson().toJson(poiInfo));
        }
        if (mTopic != null) {
            postCardDraft.setTopicInfoStr(new Gson().toJson(mTopic));
        }
        if (groupInfo != null) {
            postCardDraft.setGroupInfoStr(new Gson().toJson(groupInfo));
        }

        new Thread(new Runnable() {
            @Override
            public void run() {
                Long time = AppUtil.getServerCurrentTime();
                List<PostCardItem> itemList = new ArrayList<>();
                for (int i = 0; i < mLingLabImageBeanList.size() - 1; i++) {
                    if (null == mLingLabImageBeanList.get(i)) {
                        continue;
                    }
                    if (isExistFIle(mLingLabImageBeanList.get(i).getImgPath())) {
                        if (postCardDraft != null && TextUtils.isEmpty(postCardDraft.getCoverImage())) {
                            postCardDraft.setCoverImage(mLingLabImageBeanList.get(i).getImgPath());
                        }
                        PostCardItem postCardItem = new PostCardItem();
                        postCardItem.setType(mPostType);
                        postCardItem.setIndex(i);
                        postCardItem.setTag(mLingLabImageBeanList.get(i).getTag());
                        postCardItem.setImg(mLingLabImageBeanList.get(i).getImgPath());
                        if (mDraftId == null || mDraftId == 0) {
                            postCardItem.setDraftId(time);
                        } else {
                            postCardItem.setDraftId(mDraftId);
                        }
                        itemList.add(postCardItem);
                    }
                }
                if (!TextUtils.isEmpty(mEtText.getText().toString().trim())) {
                    PostCardItem postCardItem = new PostCardItem();
                    postCardItem.setType(mPostType);
                    postCardItem.setIndex(itemList.size());
                    postCardItem.setText(mEtText.getText().toString().trim());
                    if (mDraftId == null || mDraftId == 0) {
                        postCardItem.setDraftId(time);
                    } else {
                        postCardItem.setDraftId(mDraftId);
                    }

                    itemList.add(postCardItem);
                }

                postCardDraft.setType(mPostType);
                //设置id和毫秒值
                if (mDraftId == null || mDraftId == 0) {
                    postCardDraft.setId(time);
                    mDraftId = time;
                } else {
                    postCardDraft.setId(mDraftId);
                }

                postCardDraft.setPublishDate(time);
                postCardDraft.setUserIdStr(User.shareInstance().getUserIdStr());

//                if (itemList.size() > 0) {
                    //有id重复等问题，加synchronized尝试
                    synchronized (CommunityDoPostImageTextActivity.class) {
                        //先删除所有的同类型帖子，再添加
//                        deleteByType(mPostType);

                        if (saveDraft(postCardDraft, itemList)) {
                            //保存成功
                            runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    ToastUtil.showMessage(CommunityDoPostLingLabActivity.this, "草稿保存成功");
                                    if (isFinish) {
                                        CommunityDoPostLingLabActivity.this.finish();
                                    }
                                }
                            });

                        } else {
                            //保存失败
                            runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    ToastUtil.showMessage(CommunityDoPostLingLabActivity.this, "草稿保存失败");
                                }
                            });
                        }

                    }
//                }
            }
        }).start();
    }

    /**
     * 保存草稿具体操作
     *
     * @param postCardDraft
     * @param itemList
     * @return
     */
    public boolean saveDraft(PostCardDraft postCardDraft, List<PostCardItem> itemList) {
        try {
            // 放在一个事务中执行
            // 还有一个runInTx的方法, 跟callInTx的区别是没返回值. 根据实际情况自由选择就行.
            return GreenDaoManager.getInstance().getDaoSession().callInTx(new Callable<Boolean>() {
                @Override
                public Boolean call() {

                    if (mDraftId != null && mDraftId > 0) {
                        //先删除所有的同类型帖子，再添加
                        mPostCardItemDao.deleteInTx(
                                mPostCardItemDao.queryBuilder()
                                        .where(PostCardItemDao.Properties.DraftId.eq(mDraftId))
                                        .list()
                        );
                        //删除草稿箱
                        mPostCardDraftDao.deleteInTx(
                                mPostCardDraftDao.queryBuilder()
                                        .where(PostCardDraftDao.Properties.Id.eq(mDraftId))
                                        .list()
                        );
                    }
                    //先保存草稿列表
                    mPostCardDraftDao.insert(postCardDraft);
                    //再保存草稿
                    mPostCardItemDao.insertInTx(itemList);
                    return true;
                }
            });
        } catch (Exception e) {
            Log.e("存草稿", e.getMessage());
            return false;
        }
    }

    @Override
    protected void doImageSort(PostCard postCard, ArrayList<String> imageList, List<ImageModel> imageModels) {
        super.doImageSort(postCard, imageList, imageModels);
        final List<LingLabImageBean> chooseImgList = mLingLabImageBeanList;

        //排序后的数组
        final List<PostCardItem> data = new ArrayList<>();
        final List<PostCardItem> postCardItems = postCard.getImgTexts();
        int imgTemp = 0;
        for (int i = 0; i < chooseImgList.size(); i++) {
            if (chooseImgList.get(i) == null) {
                continue;
            }
            String img = chooseImgList.get(i).getImgPath();
            if (TextUtils.isEmpty(img)) {
                continue;
            }
            if (img.startsWith("http")) {
                for (PostCardItem postCardItem : postCardItems) {
                    if (img.equals(postCardItem.getImg())) {
                        postCardItem.setPostCommodityList(getPostCommodityList(img, String.valueOf(chooseImgList.get(i).getTag())));
                        data.add(postCardItem);
                        postCardItems.remove(postCardItem);
                        break;
                    }
                }
            } else {
                ImageModel imageModel = imageModels.get(imgTemp);
                PostCardItem postCardItem = new PostCardItem();
                postCardItem.setImg(imageList.get(imgTemp));
                postCardItem.setHeight(String.valueOf(imageModel.getHeight()));
                postCardItem.setWidth(String.valueOf(imageModel.getWidth()));
                postCardItem.setPostCommodityList(getPostCommodityList(img, String.valueOf(chooseImgList.get(i).getTag())));
                data.add(postCardItem);
                imgTemp++;
            }
        }
        postCard.getImgTexts().removeAll(data);
        postCard.getImgTexts().addAll(0, data);
    }

    @Override
    protected void showCar() {
        if (groupInfo != null) {
            mTVSelectCar.getTvLeft().setText(groupInfo.getContentGroupName());
        }
    }

    @Override
    protected void showLocation() {
        if (poiInfo != null && mLlLocation != null) {
            mLlLocation.setVisibility(View.VISIBLE);
            mLlLocation.getTvLeft().setText(poiInfo.getName());
        }
    }

    /**
     * 展示草稿内容
     */
    @Override
    protected void showDraft() {
        List<PostCardDraft> postCardDrafts = mPostCardDraftDao.queryBuilder()
                .where(PostCardDraftDao.Properties.Id.eq(mDraftId))
                .list();
        if (postCardDrafts != null && postCardDrafts.size() > 0) {
            PostCardDraft postCardDraft = postCardDrafts.get(0);
            if (!TextUtils.isEmpty(postCardDraft.getTopicInfoStr())) {
                String topicJson = postCardDraft.getTopicInfoStr();
                mTopic = new Gson().fromJson(topicJson, Topic.class);
                if (mTopic != null) {
                    mTVTopicName.setVisibility(View.VISIBLE);
                    mTVTopicName.setText(TopicUtils.getNameWithHashtag(mTopic.getTopicName()));
                }
            }
            if (!TextUtils.isEmpty(postCardDraft.getPoiInfoStr())) {
                String positionJson = postCardDraft.getPoiInfoStr();
                poiInfo = new Gson().fromJson(positionJson, PoiInfo.class);
                showLocation();
            }
            if (!TextUtils.isEmpty(postCardDraft.getGroupInfoStr())) {
                String contentGroupJson = postCardDraft.getGroupInfoStr();
                groupInfo = new Gson().fromJson(contentGroupJson, ContentGroupInfo.class);
                showCar();
            }
            if (!TextUtils.isEmpty(postCardDraft.getTitle())) {
                et_post_title.setText(postCardDraft.getTitle());
            }
        }

        List<PostCardItem> postCardItems = mPostCardItemDao.queryBuilder()
                .where(PostCardItemDao.Properties.DraftId.eq(mDraftId))
                .orderAsc(PostCardItemDao.Properties.Index)
                .list();
        //先清除图片
        mLingLabImageBeanList.clear();
        if (postCardItems != null && postCardItems.size() > 0) {
            if (!TextUtils.isEmpty(postCardItems.get(postCardItems.size() - 1).getText())) {
                mEtText.setText(postCardItems.get(postCardItems.size() - 1).getText());
                for (int i = 0; i < postCardItems.size() - 1; i++) {

                    if (AppUtil.isExistFIle(postCardItems.get(i).getImg())) {
                        mLingLabImageBeanList.add(new LingLabImageBean(postCardItems.get(i).getImg(), postCardItems.get(i).getTag()));
                    }
                }
            } else {
                for (int i = 0; i < postCardItems.size(); i++) {
                    if(AppUtil.isExistFIle(postCardItems.get(i).getImg())){
                        mLingLabImageBeanList.add(new LingLabImageBean(postCardItems.get(i).getImg(), postCardItems.get(i).getTag()));
                    }
                }
            }
        }
        //mChooseImageController.setEditPicCount(mLingLabImageBeanList.size());
        mLingLabImageBeanList.add(null);
        mLingLabImageAdapter.notifyDataSetChanged();
    }

    @Override
    protected void doLingLabPost(PostCard postCard, ArrayList<String> ImageList, List<ImageModel> imageModels) {
        super.doLingLabPost(postCard, ImageList, imageModels);
        List<PostCardItem> lingLabItemList = new ArrayList<>();
        postCard.setPostTitle(postTitle);
        postCard.setPostText(postContent);
        if (mPostType == PostCard.PostType.EDIT_LING_SENSE) {//编辑帖子时需要对图片进行排序
            //图片排序
            doImageSort(mPostCard, ImageList, imageModels);

            commit(mPostCard);
            return;
        } else {//发布帖子
            for (int i = 0; i < ImageList.size(); i++) {
                PostCardItem postCardItem = new PostCardItem();
                postCardItem.setImg(ImageList.get(i));
                postCardItem.setHeight(String.valueOf(imageModels.get(i).getHeight()));
                postCardItem.setWidth(String.valueOf(imageModels.get(i).getWidth()));
                postCardItem.setPostCommodityList(getPostCommodityList(mLingLabImageBeanList.get(i).getImgPath(), String.valueOf(mLingLabImageBeanList.get(i).getTag())));
                lingLabItemList.add(postCardItem);
            }

        }
        PostCardItem postCardItem = new PostCardItem();
        postCardItem.setText(postContent);
        lingLabItemList.add(postCardItem);
        postCard.setImgTexts(lingLabItemList);
        if (mTopic != null) {
            postCard.setTopicId(mTopic.getTopicId());
            postCard.setTopicName(mTopic.getTopicName());
        }
        if (groupInfo != null) {
            postCard.setContentGroupId(String.valueOf(groupInfo.getContentGroupIdOrZero()));
            postCard.setSeries(2);
        }
        commit(postCard);
    }

    @OnClick(R.id.ll_location)
    void clickPosition() {
        SensorsUtils.sensorsClickBtn("点击图文发布页-位置", "图文发布页", "图文发布页");
        IntentUtils.startActivityForResult(this, SelectLocationActivity.class, REQUEST_CODE_SELECT_LOCATION);
    }

    @OnClick(R.id.tv_topic)
    void clickTopic() {
        SensorsUtils.sensorsClickBtn("点击图文发布页-话题", "图文发布页", "图文发布页");
        IntentUtils.startActivityForResult(this, HotTopicListActivity.class, REQUEST_CODE_SELECT_TOPIC);
    }

    @OnClick(R.id.tv_select_car)
    void clickSelectCar() {
        SensorsUtils.sensorsClickBtn("点击图文发布页-选择车型", "图文发布页", "图文发布页");
        getLabelList();
    }

    public List<PostCommodity> getPostCommodityList(String img, String position) {
        List<PostCommodity> tagInfoBeans = null;
        String s = PreferenceUtil.getStringPreference(this, img + "_" + User.shareInstance().getUserIdStr() + "_" + position, "");
        if (!TextUtils.isEmpty(s)) {
            tagInfoBeans = new Gson().fromJson(s, new TypeToken<ArrayList<PostCommodity>>() {}.getType());
        }
        return tagInfoBeans;
    }

    @Override
    protected void deleteCacheTagList() {
        super.deleteCacheTagList();
        if (mLingLabImageBeanList != null) {
            for (int i = 0; i < mLingLabImageBeanList.size(); i++) {
                if (null == mLingLabImageBeanList.get(i)) {
                    continue;
                }
                String s = mLingLabImageBeanList.get(i).getImgPath();
                int position = mLingLabImageBeanList.get(i).getTag();
                PreferenceUtil.removePreference(this, s + "_" + User.shareInstance().getUserIdStr() + "_" + position);
            }

        }

        //清除草稿
        deletePostByDraftId();
        //清除有草稿的标记
        PreferenceUtil.removePreference(CommunityDoPostLingLabActivity.this, LingLabDoPostUtils.KEY_HAVE_DRAFT);
    }

    /**
     * 选择车型
     */
    @Override
    protected void getLabelList() {
        L00bangRequestManager2.getServiceInstance()
                .getContentGroupInfo(2)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<ColumnInfo>(this) {
                    @Override
                    public void onSuccess(final ColumnInfo columnInfo) {
                        super.onSuccess(columnInfo);
                        if (columnInfo == null) {
                            return;
                        }
                        List<ContentGroupInfo> list;
                        if ((list = columnInfo.getContentGroupList()) == null || list.isEmpty()) {
                            return;
                        }
                        List<String> strings = new ArrayList<>(list.size());
                        for (ContentGroupInfo contentGroupInfo : list) {
                            strings.add(contentGroupInfo.getContentGroupName());
                        }
                        CommonListDialog dialog = new CommonSelectListDialog(CommunityDoPostLingLabActivity.this, strings, new BaseListDialog.OnChoiceClickListener() {
                            @Override
                            public boolean onChoiceClick(int chosenIndex, String chosenText) {
                                if (chosenIndex == -1) {
                                    ToastUtil.showMessage(getApplicationContext(), R.string.report_person_reason_no_choice);
                                    return true;
                                }
                                groupInfo = columnInfo.getContentGroupList().get(chosenIndex);
                                showCar();
                                //mIntentExtra.setGroupInfo(groupInfo);
                                return false;

                            }
                        });
                        dialog.show();

                    }
                });
    }

    /**
     * 关闭
     */
    @OnClick(R.id.tv_close)
    protected void onClickClose() {
        onBackPressed();
    }

    /**
     * 存草稿
     */
    @OnClick(R.id.iv_save_draft)
    protected void onSaveDraftClick() {
        onClickSaveDraft();
    }

    /**
     * 发布
     */
    @OnClick(R.id.iv_publish)
    protected void onClickPublish() {
        onClickMenu();
    }

    /**
     * 本来是根据类型删除帖子，因为之前没中类型的帖子只保存一个草稿，现在重写该方法，
     * 因为灵感帖有草稿箱的概念，需要先删除草稿箱的帖子，再根据草稿id删除帖子
     *
     * @param type
     */
    @Override
    protected void deleteByType(int type) {
        deletePostByDraftId();
    }

    protected void deletePostByDraftId() {
        if (mDraftId != null && mDraftId > 0) {
            //先删除所有的同类型帖子，再添加
            mPostCardItemDao.deleteInTx(
                    mPostCardItemDao.queryBuilder()
                            .where(PostCardItemDao.Properties.DraftId.eq(mDraftId))
                            .list()
            );
            //删除之前的草稿列表
            mPostCardDraftDao.deleteInTx(
                    mPostCardDraftDao.queryBuilder()
                            .where(PostCardDraftDao.Properties.Id.eq(mDraftId))
                            .list()
            );
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == LingLabDoPostUtils.REQUEST_PHOTO_CODE) {
            //如果是灵感贴
            if (mLingLabImageAdapter != null) {

                if (grantResults == null || grantResults.length == 0) {
                    return;
                }
                boolean isGranted = true;
                for (int result : grantResults) {
                    if (result == PackageManager.PERMISSION_DENIED) {
                        isGranted = false;
                    }
                }
                mLingLabImageAdapter.onPermissionResult(isGranted, requestCode);
            }

        }

    }
}
