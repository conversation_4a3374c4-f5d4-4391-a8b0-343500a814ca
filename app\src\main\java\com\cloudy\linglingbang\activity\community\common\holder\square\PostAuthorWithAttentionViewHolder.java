package com.cloudy.linglingbang.activity.community.common.holder.square;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.community.common.holder.PostAuthorViewHolder;
import com.cloudy.linglingbang.activity.community.post.PostDetailActivity;
import com.cloudy.linglingbang.adapter.recommend_big_v.BigVRecommendAdapter;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.util.ViewHolderUtils;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.user.Author;
import com.cloudy.linglingbang.model.user.User;

import java.util.List;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 带关注的作者
 *
 * <AUTHOR>
 * @date 2018/8/19
 */
public class PostAuthorWithAttentionViewHolder extends PostAuthorViewHolder {
    private OnAttentionListener mOnAttentionListener;
    private ImageView mIvAttention;

    /**
     * 推荐的内容
     */
    @Nullable
    private RelativeLayout mRlRecommend;
    @Nullable
    private ImageView mIvCloseRecommend;
    @Nullable
    private RecyclerView mRvRecommend;

    public PostAuthorWithAttentionViewHolder(View itemView) {
        super(itemView);
    }

    public PostAuthorWithAttentionViewHolder setOnAttentionListener(OnAttentionListener onAttentionListener) {
        mOnAttentionListener = onAttentionListener;
        return this;
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        mIvAttention = itemView.findViewById(R.id.iv_attention);
        mRlRecommend = itemView.findViewById(R.id.rl_recommend);
        mIvCloseRecommend = itemView.findViewById(R.id.iv_close);
        mRvRecommend = itemView.findViewById(R.id.rv_recommend);
        //点击关注
        ViewHolderUtils.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (AppUtil.getActivity(v.getContext()) instanceof PostDetailActivity){
                    SensorsUtils.sensorsClickBtn("点击(关注)", "帖子详情页", "帖子详情页");
                }
                onClickIvAttention(v);
            }
        }, mIvAttention);
        //关闭推荐好友
        ViewHolderUtils.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onClickClose(v);
            }
        }, mIvCloseRecommend);
    }

    private void onClickClose(View view) {
        if (mRlRecommend != null) {
            mRlRecommend.setVisibility(View.GONE);
        }
    }

    @Override
    public void bindTo(PostCard postCard) {
        super.bindTo(postCard);
        if (mRlRecommend != null) {
            mRlRecommend.setVisibility(View.GONE);
        }
        //关注
        boolean showAttention = showAttention(postCard.getAuthor());
        ViewHolderUtils.setVisibility(showAttention, mIvAttention);
        if (showAttention && mIvAttention != null) {
            mIvAttention.setVisibility(View.VISIBLE);
            //如果是已关注则不显示，只能去个人主页取消关注
            if (UserUtils.hasAttentionOther(postCard.getAuthor())) {
                //mIvAttention.setImageResource(R.drawable.ic_post_detail_attentioned);
                mIvAttention.setVisibility(View.GONE);
            } else {
                mIvAttention.setVisibility(View.VISIBLE);
                mIvAttention.setImageResource(R.drawable.ic_add_attention);
            }
        }
    }

    /**
     * 是否需要展示关注按钮
     */
    private boolean showAttention(Author author) {
        //不是自己且未关注
        return !UserUtils.isSelf(author) && author.getAttentionDisplayStatus() == 0 && !UserUtils.hasAttentionOther(author);
    }

    /**
     * 点击关注
     */
    private void onClickIvAttention(View v) {
        if (mPostCard == null) {
            return;
        }
        Author author = mPostCard.getAuthor();
        if (author == null) {
            return;
        }
        if (!AppUtil.checkLogin(v.getContext(), AppUtil.RegisterChannel.CHANNEL_POST)) {
            //未登录
            return;
        }
        if (UserUtils.isSelf(author)) {
            ToastUtil.showMessage(v.getContext(), R.string.user_homepage_can_not_attention_self);
            return;
        }
        if (!UserUtils.hasAttentionOther(author)) {
            //如果没有关注，则加关注
            addAttention(v.getContext(), author);
        } else {
            //取消关注
            deleteAttention(v.getContext(), author);
        }

    }

    /**
     * 获取大V推荐
     */
    private void getRecommendList(Context context, String userStr) {
        L00bangRequestManager2.getServiceInstance()
                .getRecommendBigVList(userStr)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<List<User>>(context) {
                    @Override
                    public void onSuccess(List<User> userList) {
                        super.onSuccess(userList);
                        if (userList != null && userList.size() > 0) {
                            if (mRlRecommend != null) {
                                mRlRecommend.setPadding(0, 0, 0, 0);
                                mRlRecommend.setBackgroundColor(mRlRecommend.getContext().getResources().getColor(R.color.color_f9f9f9));
                                mRlRecommend.setVisibility(View.VISIBLE);

                            }
                            if (mRvRecommend != null) {
                                LinearLayoutManager linearLayoutManager = new LinearLayoutManager(context);
                                linearLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
                                mRvRecommend.setLayoutManager(linearLayoutManager);
                                mRvRecommend.setAdapter(new BigVRecommendAdapter(context, userList));
                            }
                        }

                    }
                });
    }

    /**
     * 关注
     */
    private void addAttention(Context context, final User user) {
        if (user != null) {
            L00bangRequestManager2.getServiceInstance()
                    .addFriend(user.getUserIdStr())
                    .compose(L00bangRequestManager2.setSchedulers())
                    .subscribe(new ProgressSubscriber<String>(context) {
                        @Override
                        public void onSuccess(String s) {
                            super.onSuccess(s);
                            ToastUtil.showMessage(context, R.string.user_homepage_attention_success);
                            updateAttention(true);
                            //获取好友推荐
                            getRecommendList(context, user.getUserIdStr());
                        }
                    });
        }
    }

    /**
     * 取消关注
     */
    private void deleteAttention(Context context, User user) {
        if (user != null) {
            L00bangRequestManager2.getServiceInstance()
                    .deleteFriend(user.getUserIdStr())
                    .compose(L00bangRequestManager2.setSchedulers())
                    .subscribe(new ProgressSubscriber<String>(context) {
                        @Override
                        public void onSuccess(String s) {
                            super.onSuccess(s);
                            ToastUtil.showMessage(context, R.string.user_homepage_cancel_attention_success);
                            updateAttention(false);
                        }
                    });
        }
    }

    /**
     * 更新关注
     */
    private void updateAttention(boolean attention) {
        ViewHolderUtils.setVisibility(!attention, mIvAttention);
        if (mOnAttentionListener != null) {
            if (mPostCard != null) {
                Author author = mPostCard.getAuthor();
                if (author != null) {
                    mOnAttentionListener.onAttention(author.getUserIdStr(), attention);
                }
            }
        } else {
            if (mPostCard != null) {
                Author author = mPostCard.getAuthor();
                UserUtils.updateUserAttention(author, attention);
            }
            bindTo(mPostCard);
        }
    }

    public interface OnAttentionListener {
        void onAttention(String userIdStr, boolean attention);
    }
}
