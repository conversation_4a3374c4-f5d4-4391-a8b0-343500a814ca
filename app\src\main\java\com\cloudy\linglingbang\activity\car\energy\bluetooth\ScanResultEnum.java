package com.cloudy.linglingbang.activity.car.energy.bluetooth;

/**
 * 蓝牙扫描结果枚举
 */
public enum ScanResultEnum {

    NO_BLE(-1, "未搜索到蓝牙信号"),
    NO_COMPLETE(0, "未完成蓝牙搜索"),
    HAVE_BLE(1, "搜索到当前蓝牙");

    /**
     * @param code  蓝牙扫描状态码
     * @param desc  蓝牙扫描状态描述
     */
    ScanResultEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private final int code;
    private final String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
