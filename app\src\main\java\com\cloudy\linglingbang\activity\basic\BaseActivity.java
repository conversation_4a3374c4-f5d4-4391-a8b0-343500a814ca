package com.cloudy.linglingbang.activity.basic;

import android.Manifest;
import android.annotation.TargetApi;
import android.app.Dialog;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import android.os.Bundle;
import android.os.SystemClock;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.CallSuper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;

import com.blankj.utilcode.util.SPUtils;
import com.cloudy.linglingbang.ApplicationLLB;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.HomeActivity;
import com.cloudy.linglingbang.activity.car.connected.CarControlController2;
import com.cloudy.linglingbang.activity.welcome.AgreementDialogUtils;
import com.cloudy.linglingbang.app.log.LogUtils;
import com.cloudy.linglingbang.app.receiver.LLBNetworkStateReceiver;
import com.cloudy.linglingbang.app.util.ActivityController;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.InvitedUtils;
import com.cloudy.linglingbang.app.util.LocationHelper;
import com.cloudy.linglingbang.app.util.NetworkUtil;
import com.cloudy.linglingbang.app.util.PermissionUtils;
import com.cloudy.linglingbang.app.util.PreferenceUtil;
import com.cloudy.linglingbang.app.util.StatusBarUtil;
import com.cloudy.linglingbang.app.util.SystemBarTintManager;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.jpush.JPushPage;
import com.cloudy.linglingbang.app.widget.dialog.ActivateCarOwnerDialog;
import com.cloudy.linglingbang.app.widget.dialog.TechnicianBirthdayDialogUtil;
import com.cloudy.linglingbang.constants.AppConstants;
import com.cloudy.linglingbang.constants.ConfigConstant;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BackgroundSubscriber;
import com.sensorsdata.analytics.android.sdk.SensorsDataAPI;
import com.umeng.analytics.MobclickAgent;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.util.List;

import butterknife.ButterKnife;

/**
 * Created by qiurong on 15/4/22
 * 基类activity
 * 1.友盟统计 子类不需要再次实现
 */
public abstract class BaseActivity extends AppCompatActivity implements LLBNetworkStateReceiver.NetChangeListener {
    /**
     * 提供一个请求code，用于只有一个startActivityForResult的情况
     */
    public static final int REQUEST_CODE_COMMON = 1;
    /**
     * 公共导航(可能为空)
     */
    protected androidx.appcompat.widget.Toolbar mToolbar;

    /**
     * 左边title文字(可能为空)
     */
    protected TextView toolbar_title_left;

    /**
     * 中间title文字(可能为空)
     */
    protected TextView mToolbarTitle;


    /**
     * 友盟统计页面的name
     */
    protected String activityName = null;

    private boolean isFromNotify;//是否是从通知栏进入,如果从通知栏进入，则返回时打开首页

    public Window window;

    private long mLastOnclickTime;

    /**
     * 是否来自登录成功，用于在onNewIntent中判断
     */
    public static final String INTENT_EXTRA_FROM_LOGIN_SUCCESS = "from_login_success";

    /**
     * 权限工具类
     */
    protected PermissionUtils mPermissionUtils;

    protected boolean isCurrentRunningForeground = true;

    private final int mLocalPermissionRequestCode = 1000;

    /** preference的key（上次传递定位的时间） */
    public static final String KEY_LOCATION_TIME = "lastLocationTime";

    /** preference的key（上次绑定车控推送的时间） */
    public static final String KEY_BIND_PUSH_TIME = "lastBindPushTime";

    /** 融云顶号提示，此处设置是发现小米9机型 */
    private Dialog mBaseLoginConflictDialog;
    private LLBNetworkStateReceiver llbNetworkStateReceiver;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ApplicationLLB.currentActivity = this;
        ActivityController.addActivity(this);
        loadViewLayout();
//        setStatBarColor();//设置状态栏颜色，沉浸状态栏
        //设置深色字体
        int result = StatusBarUtil.StatusBarLightMode(this);
        //设置标题栏是白色
        StatusBarUtil.setStatusBarColor(this, R.color.tool_bar_bg);
        ButterKnife.bind(this);
        //是否是从通知栏进入,如果从通知栏进入，则返回时打开首页
        isFromNotify = getIntent().getBooleanExtra(ConfigConstant.IS_FROM_NOTIFY, false);
        /**
         * 增加公共Toolbar
         */
        mToolbar = findViewById(R.id.common_toolbar);
        if (mToolbar != null) {
            super.setSupportActionBar(mToolbar);
            ActionBar actionBar;
            if ((actionBar = super.getSupportActionBar()) != null) {
                actionBar.setDisplayShowTitleEnabled(false);
            }
            mToolbar.setTitleTextAppearance(this, R.style.nav_text_color);
            mToolbar.setTitleTextColor(getResources().getColor(R.color.white));
            mToolbar.setSubtitleTextAppearance(this, R.style.nav_text_color);
            mToolbar.setNavigationIcon(R.drawable.ic_back); //此属性标志返回或自定义Icon
            toolbar_title_left = mToolbar.findViewById(R.id.toolbar_title_left);
            mToolbarTitle = mToolbar.findViewById(R.id.toolbar_title);
            mToolbar.getBackground().setAlpha(255);//设置背景不透明，防止其他动画干扰
            /**
             * 增加Navigation Icon点击事件
             */
            mToolbar.setNavigationOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    //点击左上角返回时判断是否是从通知栏进入,如果从通知栏进入，则返回时打开首页
                    if (isFromNotify) {
                        startHomeActivity();
                    }
                    onBack();
                }
            });
        }

        initialize();
        llbNetworkStateReceiver = new LLBNetworkStateReceiver();
        llbNetworkStateReceiver.setNetChangeListener(this);
        //实例化IntentFilter对象
        IntentFilter filter = new IntentFilter();
        filter.addAction("android.net.conn.CONNECTIVITY_CHANGE");
        filter.addAction("android.Net.wifi.WIFI_STATE_CHANGED");
        filter.addAction("android.net.wifi.STATE_CHANGE");
        //注册广播接收
        registerReceiver(llbNetworkStateReceiver, filter);
    }

    public void startHomeActivity() {
        Intent intent = new Intent();
        intent.setClass(BaseActivity.this, HomeActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        startActivity(intent);
    }

    /**
     * 设置状态栏的颜色，重写该方法能设置其他颜色
     */
    protected void setStatBarColor() {
        this.settingStatusBar(R.color.toolbar);
    }

    /**
     * 加载布局
     */
    protected abstract void loadViewLayout();

    /**
     * 初始化操作
     */
    protected abstract void initialize();

    /**
     * 设置中间文字
     *
     * @param title
     */
    public void setMiddleTitle(String title) {
        super.setTitle(title);
    }

    /**
     * 设置左边文字
     *
     * @param title
     */
    public void setLeftTitle(String title) {
        super.setTitle(title);
    }

    /**
     * 定义Toolbar背景颜色
     *
     * @param resId
     */
    public void setToolbarBackground(int resId) {
        if (mToolbar != null) {
            mToolbar.setBackgroundColor(getResources().getColor(resId));
        }
    }

    /**
     * 定义Toolbar 导航图标
     *
     * @param resId
     */
    public void setToolbarLeftIcon(int resId) {
        if (mToolbar != null) {
            mToolbar.setNavigationIcon(resId); //此属性标志返回或自定义Icon
        }
    }

    /**
     * 设置toolbar是否现实返回按钮
     *
     * @param isShow
     */
    public void setDisplayHomeAsUpEnabled(boolean isShow) {
        ActionBar bar = getSupportActionBar();
        if (bar != null) {
            bar.setDisplayHomeAsUpEnabled(isShow);
        }
    }

    /**
     * 设置某个位置Menu显示与隐藏
     *
     * @param position
     * @param isShow
     */
    public void setVisible(int position, boolean isShow) {
        if (mToolbar != null && mToolbar.getMenu() != null) {
            if (position < mToolbar.getMenu().size()) {
                MenuItem menuItem = mToolbar.getMenu().getItem(position);
                if (menuItem != null) {
                    menuItem.setVisible(isShow);
                }
            }
        }
    }

    @Override
    protected void onResume() {
        LogUtils.i(this.getClass().getSimpleName() + " = onResume");
        super.onResume();
        //友盟统计
        MobclickAgent.onPageStart(this.getClass().getSimpleName());
        MobclickAgent.onResume(this);
        JPushPage.onResume(this);
//        ChatHelper.getNotifier().reset();
        ApplicationLLB.currentActivity = this;
        if (mToolbar != null) {
            mToolbar.getBackground().setAlpha(255);//设置背景不透明，防止其他动画干扰
        }
        //如果登录框是弹出状态，则先dismiss，然后再次show一次（因为某些机型「目前测试小米9」App退到后台，在切换到前台会导致dialog没有弹出，但是却有dialog背景的问题）
        if (mBaseLoginConflictDialog != null && mBaseLoginConflictDialog.isShowing()) {
            mBaseLoginConflictDialog.dismiss();
            mBaseLoginConflictDialog.show();
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        if (!isCurrentRunningForeground) {
            onSwitchForeground();
        }
    }

    /**
     * app从后台切换到前台的操作
     */
    private void onSwitchForeground() {
        //获取定位信息并请求
        requestLocationInfo();
        //重新绑定车控推送
        reBindCarControlPush();
    }

    /**
     * 重新绑定车控
     */
    private void reBindCarControlPush() {
        //如果规定时间之前请求过，则不再请求
        Long lastTime = PreferenceUtil.getLongPreference(this, KEY_BIND_PUSH_TIME, 0);
        if (ApplicationLLB.getServerCurrentTime() - lastTime < ConfigConstant.BIND_CAR_CONTROL_PUSH_INTERVAL) {
            return;
        }
        CarControlController2.reBindCarControlPush(this);
    }

    /**
     * 获取定位
     */
    protected void requestLocationInfo() {
        //如果规定时间之前请求过，则不再请求
        Long lastTime = PreferenceUtil.getLongPreference(this, KEY_LOCATION_TIME, 0);
        if (!AgreementDialogUtils.isAgreement(this) || (ApplicationLLB.getServerCurrentTime() - lastTime < ConfigConstant.OPEN_APP_LOCATION_INTERVAL)) {
            return;
        }
        PreferenceUtil.putPreference(this, KEY_LOCATION_TIME, AppUtil.getServerCurrentTime());
        //如果有权限，则定位
        if (PermissionUtils.checkPermission(this, Manifest.permission.ACCESS_COARSE_LOCATION)) {
            getLocation();
        } else {
            putOpenAppLog(null);
        }
    }

    /**
     * 获取经纬度,之后再刷新一次数据
     */
    private void getLocation() {
        LocationHelper.getInstance().requestLocation(this, mLocalPermissionRequestCode, new LocationHelper.LocCallBack() {
            @Override
            public void onSuccess(LocationHelper.LocationEntity entity) {
                SensorsDataAPI.sharedInstance().setGPSLocation(entity.latitude, entity.longitude, entity.countryName, entity.provinceName, entity.cityName);
                putOpenAppLog(entity);
            }

            @Override
            public void onError(String errMsg) {
                putOpenAppLog(null);
            }
        });
    }

    /**
     * 定位成功，请求服务器
     *
     * @param entity
     */
    private void putOpenAppLog(LocationHelper.LocationEntity entity) {
        double longitude = 0;
        double latitude = 0;
        String cityName = null;
        if (entity != null) {
            latitude = entity.latitude;
            longitude = entity.longitude;
            if (entity.cityName != null) {
                try {
                    cityName = URLEncoder.encode(entity.cityName, "UTF-8");
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
            }
        }
        L00bangRequestManager2
                .getServiceInstance()
                .putOpenAppLog(longitude, latitude, cityName)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new BackgroundSubscriber<Object>(this) {
                    @Override
                    public void onSuccess(Object o) {
                        super.onSuccess(o);
                        //储存本次定位的时间
                        PreferenceUtil.putPreference(BaseActivity.this, KEY_LOCATION_TIME, AppUtil.getServerCurrentTime());
                    }
                });
    }

    @Override
    protected void onPause() {
        LogUtils.i(this.getClass().getSimpleName() + " = onPause");
        super.onPause();
        //友盟统计
        MobclickAgent.onPageEnd(this.getClass().getSimpleName());
        MobclickAgent.onPause(this);
        JPushPage.onPause(this);
        if (mPermissionUtils != null) {
            mPermissionUtils.dismissDialog();
        }

    }

    @Override
    protected void onStop() {
        isCurrentRunningForeground = AppUtil.isForeground(this);
        super.onStop();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        LogUtils.i(this.getClass().getSimpleName() + " = onDestroy");
//        ApplicationLLB.currentActivity = null;
        ActivityController.deleActvitity(this);
        if (llbNetworkStateReceiver != null) {
            unregisterReceiver(llbNetworkStateReceiver);
            llbNetworkStateReceiver.setNetChangeListener(null);
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        MenuInflater inflater = getMenuInflater();
        /*LayoutInflater layoutInflater = getLayoutInflater();
        if(layoutInflater.getFactory()!=null){
            layoutInflater = layoutInflater.cloneInContext(this);
        }
        layoutInflater.setFactory(new LayoutInflater.Factory() {
            @Override
            public View onCreateView(String name, Context context, AttributeSet attrs) {
                LogUtils.d("name: " + name);
                try {
                    LayoutInflater f = getLayoutInflater();
                    View view = f.createView(name, null, attrs);
                    view.setBackgroundResource(R.drawable.register_bg);
//                    if (view instanceof TextView) {
//                        ((TextView) view).setTextColor(getResources().getColor(R.color.red));
//                        ((TextView) view).setTextAppearance(BaseActivity.this,R.style.nav_text_color);
//                    }
                    return  view;
                } catch (ClassNotFoundException e) {
                    e.printStackTrace();
                } catch (InflateException e) {
                    e.printStackTrace();
                }
                return null;
            }
        });*/

        /**
         * 增加统一的Menu
         */
        inflater.inflate(R.menu.menu_common, menu);
        /**
         * 默认隐藏所有MenuItem，用到时重写此方法，设置setVisible（int position,boolean isShow）
         */
        try {
            this.setVisible(0, false);
            this.setVisible(1, false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    @Override
    protected void onPostCreate(@Nullable Bundle savedInstanceState) {
        super.onPostCreate(savedInstanceState);
        if (!super.isChild()) {
            this.onTitleChanged(getTitle(), getTitleColor());
        }
    }

    @Override
    protected void onTitleChanged(CharSequence title, int color) {
        super.onTitleChanged(title, color);
        if (mToolbarTitle != null) {
            mToolbarTitle.setText(title);
        }
    }

    /**
     * *显示溢出菜单图标
     **/
    @Override
    public boolean onMenuOpened(int featureId, Menu menu) {
        if (featureId == Window.FEATURE_ACTION_BAR && menu != null) {
            if (menu.getClass().getSimpleName().equals("MenuBuilder")) {
                try {
                    Method m = menu.getClass().getDeclaredMethod(
                            "setOptionalIconsVisible", Boolean.TYPE);
                    m.setAccessible(true);
                    m.invoke(menu, true);
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return super.onMenuOpened(featureId, menu);
    }

    public void initHttpDialog() {
//        L00bangRequestManager.dialogHttpProgress = new DialogHttpProgress(BaseActivity.this,R.style.Dialog);
    }

    /**
     * @param intent 使用intent，需要从intent中取值时，要判断是否能取到再进行刷新操作，比如可能是从登录返回，而不是传了新的intent
     */
    @Override
    protected void onNewIntent(Intent intent) {
        LogUtils.i(this.getClass().getSimpleName() + " = onNewIntent");
        super.onNewIntent(intent);
        ApplicationLLB.currentActivity = this;
        if (intent != null) {
            boolean fromLoginSuccess = intent.getBooleanExtra(INTENT_EXTRA_FROM_LOGIN_SUCCESS, false);
            if (fromLoginSuccess) {
                onLoginSuccess();
            }
        }
    }

    /**
     * 登录成功时回调，指某项操作需要登录，登录成功后返回，从onNewIntent中调用此处
     * 转给activity后，可再转给fragment
     */
    @CallSuper
    public void onLoginSuccess() {
        //调用技师生日祝福对话框
        TechnicianBirthdayDialogUtil birthdayDialogUtil = new TechnicianBirthdayDialogUtil(this);
        birthdayDialogUtil.showDialog();
        //调用去激活管家会员弹窗
        ActivateCarOwnerDialog.ActivateCarOwnerDialogUtil activateCarOwnerDialogUtil =
                new ActivateCarOwnerDialog.ActivateCarOwnerDialogUtil(this);
        activateCarOwnerDialogUtil.showDialog();
        //请求邀请码
//        requestInviteCode();
    }

    /**
     * 请求邀请码，因为homeActivity在onResume中处理，所以，需要重写该方法，不在调用这个，否则将执行两边
     */
    protected void requestInviteCode() {
        //登录成功如果有邀请码，则请求并弹窗
        if (!InvitedUtils.isHaveShow(this)) {
            InvitedUtils.requestAndShowDialog(BaseActivity.this);
        }
    }

    //沉浸式状态颜色设置
    protected void settingStatusBar(int color) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            window = this.getWindow();
            // 透明状态栏
            window.setFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS,
                    WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);// 透明导航栏
            setTranslucentStatus(true);
            SystemBarTintManager tintManager = new SystemBarTintManager(this);
            tintManager.setStatusBarTintEnabled(true);
            tintManager.setStatusBarTintResource(color);//通知栏所需颜色
        }
    }

    @TargetApi(Build.VERSION_CODES.KITKAT)
    private void setTranslucentStatus(boolean on) {
        WindowManager.LayoutParams winParams = window.getAttributes();
        final int bits = WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS;
        if (on) {
            winParams.flags |= bits;
        } else {
            winParams.flags &= ~bits;
        }
        window.setAttributes(winParams);
    }

    /**
     * 返回键返回主页
     */
    @Override
    public void onBackPressed() {
        //点击左上角返回时判断是否是从通知栏进入,如果从通知栏进入，则返回时打开首页
        if (!isWindowLocked()) {
            if (isFromNotify) {
                startHomeActivity();
            }
            onBack();
        }
    }

    /**
     * 点击返回时执行的操作，子类可以重写该方法
     */
    protected void onBack() {
        finish();
    }

    protected boolean isWindowLocked() {
        long current = SystemClock.elapsedRealtime();
        if (current - mLastOnclickTime > 500) {
            mLastOnclickTime = current;
            return false;
        }
        return true;
    }

    /**
     * 获取通用方法传递的String参数
     */
    protected String getIntentStringExtra() {
        return IntentUtils.getStringExtra(getIntent().getExtras());
    }

    protected long getOrParseIntentLongExtra() {
        return getOrParseIntentLongExtra(IntentUtils.INTENT_EXTRA_COMMON);
    }

    protected long getOrParseIntentLongExtra(String key) {
        return IntentUtils.getOrParseLongExtra(getIntent().getExtras(), key);
    }

    /**
     * 获取通用方法传递的对象
     */
    protected Object getIntentExtra(Object defaultObject) {
        return IntentUtils.getExtra(getIntent().getExtras(), defaultObject);
    }

    /**
     * 当intent传递的参数不正确时进行操作，默认为提示并finish()
     */
    protected void onIntentExtraError() {
        ToastUtil.showMessage(this, getString(R.string.common_intent_extra_error));
        finish();
    }

    /**
     * 设置默认字体大小，防止手机字体调大之后，布局发生错乱
     */
    @Override
    public Resources getResources() {
        Resources res = super.getResources();
        Configuration config = new Configuration();
        config.setToDefaults();
        res.updateConfiguration(config, res.getDisplayMetrics());
        return res;
    }

    /**
     * 检查权限的方法
     *
     * @param requestCode 请求码
     * @param explainText 检查权限是弹窗显示的文字
     * @param settingExplainText 如果用户点击不再提醒，弹出去设置弹窗的提示文字（传null表示不弹出去设置窗口）
     * @param permissions 需要检查的权限
     */
    public void checkPermissions(final int requestCode, String explainText, final String settingExplainText, final String... permissions) {
        if (mPermissionUtils == null) {
            mPermissionUtils = new PermissionUtils(this);
        }
        mPermissionUtils.setPermissionResultListener(new PermissionUtils.OnPermissionListener() {
            @Override
            public void permissionsGranted(int requestCode, List<String> perms) {
                if (perms.size() == permissions.length) {
                    onPermissionResult(true, requestCode);
                }
            }

            @Override
            public void permissionsDenied(int requestCodeResult, List<String> perms) {
                if (requestCodeResult == requestCode) {//判断是为了防止从fragment请求的权限也会走该方法
                    onPermissionResult(false, requestCode);
                    PermissionUtils.showSettingDialog(BaseActivity.this, settingExplainText, perms);
                }
            }

            @Override
            public void permissionsDeniedSelfDialog(int requestCodeResult, List<String> perms) {
                if (requestCodeResult == requestCode) {//判断是为了防止从fragment请求的权限也会走该方法
                    onPermissionResult(false, requestCode);
                }
            }
        });
        mPermissionUtils.checkPermissions(requestCode, explainText, settingExplainText, permissions);
    }

    /**
     * 权限申请结果回调，子类重写该方法，执行某些操作
     *
     * @param isGranted true，有权限，false表示没有
     * @param requestCode 请求code
     */
    protected void onPermissionResult(boolean isGranted, int requestCode) {
        if (requestCode == mLocalPermissionRequestCode) {
            LocationHelper.getInstance().onPermissionResult(isGranted, requestCode);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (mPermissionUtils != null) {//如果为空，则说明mPermissionUtils没有在该activity中初始化，可能在fragment中初始化
            mPermissionUtils.onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
    }

    public void setBaseLoginConflictDialog(Dialog baseLoginConflictDialog) {
        mBaseLoginConflictDialog = baseLoginConflictDialog;
    }

    /**
     * 网络类型
     */
    private int netType;

    @Override
    public void onChangeListener(int status) {
        this.netType = status;
        if (isNetConnect()) {
            SPUtils.getInstance().put(AppConstants.NETWORK_STATE, true);
            netConnect();
        } else {
            SPUtils.getInstance().put(AppConstants.NETWORK_STATE, false);
            netDisConnect();
        }
    }

    protected void netConnect() {

    }

    protected void netDisConnect() {

    }

    /**
     * 判断有无网络 。
     *
     * @return true 有网, false 没有网络.
     */
    public boolean isNetConnect() {
        if (netType == NetworkUtil.TYPE_MOBILE) {
            return true;
        } else if (netType == NetworkUtil.TYPE_WIFI) {
            return true;
        } else if (netType == NetworkUtil.TYPE_NONE) {
            return false;
        }
        return false;
    }

    //    @Override
//    public void onRationaleDenied(int requestCode) {
//        onPermissionResult(false, requestCode);
//    }
//
//    @Override
//    public void onRationaleAccepted(int requestCode) {}
}
