package com.cloudy.linglingbang.activity.service.newenergy;

import android.content.Context;
import android.content.DialogInterface;
import android.view.View;
import android.widget.CheckBox;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.span.SpanUtils;
import com.cloudy.linglingbang.app.widget.dialog.alert.BaseAlertDialog;
import com.cloudy.linglingbang.constants.WebUrlConfigConstant;

/**
 * 打开自动补电的弹窗
 *
 * <AUTHOR>
 * @date 2020/12/23
 */
public class OpenAutomaticSupplementElectricityDialog extends BaseAlertDialog {
    private TextView mTvAgreement;
    private CheckBox mCbCheck;
    private ButtonPostListener mButtonPostListener;

    public void setButtonPostListener(ButtonPostListener buttonPostListener) {
        mButtonPostListener = buttonPostListener;
    }

    public OpenAutomaticSupplementElectricityDialog(Context context) {
        super(context);
        getAlertController().setButton(DialogInterface.BUTTON_POSITIVE, "立即开启", null);
        getAlertController().setButton(DialogInterface.BUTTON_NEGATIVE, "暂不开启", null);
        getAlertController().setTitle("12V蓄电池自动补电");
        getAlertController().setMessage(getContext().getResources().getString(R.string.msg_automatic_supplement_electricity));

    }

    /*public OpenAutomaticSupplementElectricityDialog(Context context, String title, String message, String btnOkText, String btnCancelText, OnClickListener btnOkOnClickListener, OnClickListener btnCancelOnClickListener) {
        super(context, title, message, btnOkText, btnCancelText, btnOkOnClickListener, btnCancelOnClickListener);
    }*/

    public boolean getCbIsChecked() {
        if (mCbCheck != null) {
            return mCbCheck.isChecked();
        }
        return false;
    }

    @Override
    protected int getDefaultLayoutResId() {
        return R.layout.dialog_open_automatic_supplemet_electricity;
    }

    @Override
    protected void initView() {
        super.initView();
        mTvAgreement = findViewById(R.id.tv_agreement);
        mCbCheck = findViewById(R.id.cb_check);
        String txt = "我已阅读并同意《12V蓄电池自动补电免责声明》";
        SpanUtils.setPartSpanText(mTvAgreement, txt, mContext.getResources().getColor(R.color.color_4a90e2), mContext.getResources().getDimension(R.dimen.activity_set_text_26), txt.indexOf("《"), txt.length());

        getAlertController().setButton(DialogInterface.BUTTON_POSITIVE, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (mButtonPostListener != null && mButtonPostListener.onToPositive()) {
                    return;
                }
            }
        });
        mTvAgreement.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                JumpPageUtil.goCommonWeb(mContext, WebUrlConfigConstant.RULE_AUTOMATIC);
            }
        });

    }

    @Override
    public void show() {
        super.show();
        getAlertController().getButton(DialogInterface.BUTTON_POSITIVE)
                .setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (mButtonPostListener != null && mButtonPostListener.onToPositive()) {
                            return;
                        }
                    }
                });
    }

    public interface ButtonPostListener {
        /** DialogInterface.BUTTON_POSITIVE 点击相应 */
        boolean onToPositive();

    }
}
