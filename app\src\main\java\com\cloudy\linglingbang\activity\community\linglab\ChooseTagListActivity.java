package com.cloudy.linglingbang.activity.community.linglab;

import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseScrollTabViewPagerActivity;
import com.cloudy.linglingbang.app.widget.ClearEditText;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.tag.TabLabels;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.viewpager.widget.PagerAdapter;
import butterknife.BindView;

/**
 * 选择标签的列表
 *
 * <AUTHOR>
 * @date 2021/11/11
 */
public class ChooseTagListActivity extends BaseScrollTabViewPagerActivity<Fragment> implements TextWatcher {
    @BindView(R.id.et_search_content)
    ClearEditText mEtSearchContent;
    @BindView(R.id.tv_cancel)
    TextView mTvCancel;
    private List<Fragment> fragmentList = new ArrayList<>();

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_choose_tag_list);
    }

    @Override
    protected void initialize() {

        super.initialize();
        queryLabel();
        mEtSearchContent.setImeOptions(EditorInfo.IME_ACTION_SEARCH);
        watchSearch();
        mTvCancel.setOnClickListener(view -> finish());

        mEtSearchContent.addTextChangedListener(this);

        mEtSearchContent.setOnClearCallback(() -> ((ChooseTagListFragment) getCurrentItem()).setCommoditySearchName(mEtSearchContent.getText().toString().trim()));
    }

    private void queryLabel() {
        L00bangRequestManager2.getServiceInstance()
                .getPostCommodityTabs()
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<List<TabLabels>>(this) {
                    @Override
                    public void onSuccess(List<TabLabels> tabLabels) {
                        super.onSuccess(tabLabels);
                        List<String> titles = new ArrayList<>(tabLabels.size());
                        for (TabLabels tabLabel : tabLabels) {
                            titles.add(tabLabel.getClassifyName());
                            fragmentList.add(ChooseTagListFragment.newInstance(tabLabel.getFrontEndClassifyId()));
                        }
                        tabs.notifyDataSetChanged(titles);
                        mAdapter.notifyDataSetChanged();
                    }
                });
    }

    @Override
    protected PagerAdapter createViewPagerAdapter(List<Fragment> data, String[] titles) {
        return new FragmentStatePagerAdapter(getSupportFragmentManager()) {
            @NonNull
            @Override
            public Fragment getItem(int position) {
                return data.get(position);
            }

            @Override
            public int getCount() {
                return data.size();
            }

            @Nullable
            @Override
            public CharSequence getPageTitle(int position) {
                return titles[position];
            }
        };
    }

    @Override
    protected List<Fragment> createAdapterData() {
        return fragmentList;
    }

    @Override
    protected void onItemSelected(int position) {
        super.onItemSelected(position);
        ((ChooseTagListFragment) getCurrentItem()).setCommoditySearchName(mEtSearchContent.getText().toString().trim());
    }

    @Override
    protected String[] getTitles() {
        return new String[]{""};
    }

    /**
     * @方法说明:监控软键盘的的搜索按钮
     * @方法名称:watchSearch
     * @返回值:void
     */
    private void watchSearch() {
        mEtSearchContent.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                // 先隐藏键盘
                hideKeyboard();
                // 搜索，进行自己要的操作...
                ((ChooseTagListFragment) getCurrentItem()).setCommoditySearchName(mEtSearchContent.getText().toString().trim());
                return true;
            }
            return false;
        });
    }

    protected void hideKeyboard() {
        if (mEtSearchContent != null) {
            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            if (imm != null) {
                imm.hideSoftInputFromWindow(mEtSearchContent.getWindowToken(), 0);
            }
        }
    }

    @Override
    public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

    }

    @Override
    public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

    }

    @Override
    public void afterTextChanged(Editable editable) {
        if (TextUtils.isEmpty(mEtSearchContent.getText().toString())) {
            ((ChooseTagListFragment) getCurrentItem()).setCommoditySearchName(mEtSearchContent.getText().toString().trim());
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mEtSearchContent.removeTextChangedListener(this);
    }
}
