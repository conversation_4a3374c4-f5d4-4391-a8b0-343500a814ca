package com.cloudy.linglingbang.activity.fragment.store.home.adapter;

import android.view.View;

import com.cloudy.linglingbang.BaseInstrumentedTest;
import com.cloudy.linglingbang.activity.fragment.store.home.model.StoreHomeElementWrapper;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.model.store.home.StoreElementCommodity;
import com.cloudy.linglingbang.model.store.home.StoreLayoutElement;

import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/10/19
 */
public class StoreHomeElement_1_2_ViewHolderTest extends BaseInstrumentedTest {

    @Test
    public void shouldShowNeighborTitle() {
        StoreHomeElement_1_2_ViewHolder holder = new StoreHomeElement_1_2_ViewHolder(new View(getContext()));
        StoreLayoutElement showTitleElement = new StoreLayoutElement();
        showTitleElement.setTitle("1");
        StoreHomeElementWrapper show = new StoreHomeElementWrapper(showTitleElement);
        StoreHomeElementWrapper hidden = new StoreHomeElementWrapper(new StoreLayoutElement());
        List<Object> data = new ArrayList<>();
        data.add(show);
        data.add(hidden);
        data.add(show);

        StoreHomeAdapter adapter = new StoreHomeAdapter(getContext(), data);
        holder.setAdapter(adapter);

        //第 0 个，下一个隐藏，应该隐藏
        show.setIndexInComponent(0);
        hidden.setIndexInComponent(1);
        // false
        Assert.assertFalse(holder.shouldShowNeighborTitle(show, 0));

        //第 1 个，前一个展示了，应该展示
        //true
        Assert.assertTrue(holder.shouldShowNeighborTitle(hidden, 1));

        //index 不正确，false
        Assert.assertFalse(holder.shouldShowNeighborTitle(show, 1));

        //没有下一个，false
        Assert.assertFalse(holder.shouldShowNeighborTitle(show, 2));
    }

    @Test
    public void shouldShowTitle() {
        StoreHomeElement_1_2_ViewHolder holder = new StoreHomeElement_1_2_ViewHolder(new View(getContext()));
        Assert.assertFalse(holder.shouldShowTitle(null));

        //无标题 false
        StoreLayoutElement element = new StoreLayoutElement();
        Assert.assertFalse(holder.shouldShowTitle(element));

        //有标题 true
        element.setTitle("1");
        Assert.assertTrue(holder.shouldShowTitle(element));

        //无标题，但是有商品 false
        element.setTitle(null);
        StoreElementCommodity commodity = new StoreElementCommodity();
        element.setProductVo(commodity);
        Assert.assertFalse(holder.shouldShowTitle(element));

        long now = AppUtil.getServerCurrentTime();
        commodity.setActivityStartTime(now);
        //没有结束时间 false
        Assert.assertFalse(holder.shouldShowTitle(element));

        //有结束时间 true
        commodity.setActivityEndTime(now + 1000 * 60);
        Assert.assertTrue(holder.shouldShowTitle(element));
    }
}