package com.cloudy.linglingbang.activity.community.common.holder;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.ViewHolderUtils;
import com.cloudy.linglingbang.app.widget.PraiseCountTextView;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.NormalSubscriber;

/**
 * 底部信息
 *
 * <AUTHOR>
 * @date 2018/6/26
 */
public class PostBottomInfoViewHolder extends BasePostChildViewHolder {
    private TextView mTvPostType;
    protected TextView mTvTime;

    private TextView mTvReadCount;

    private TextView mTvReplyCount;

    private LinearLayout mLlPraise;
    private ImageView mIvPraise;
    private PraiseCountTextView mTvPraiseCount;

    public PostBottomInfoViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        mTvPostType = itemView.findViewById(R.id.tv_post_type);
        mTvTime = itemView.findViewById(R.id.tv_time);

        mTvReadCount = itemView.findViewById(R.id.tv_read_count);

        mTvReplyCount = itemView.findViewById(R.id.tv_reply_count);

        mLlPraise = itemView.findViewById(R.id.ll_praise);
        mIvPraise = itemView.findViewById(R.id.iv_praise);
        mTvPraiseCount = itemView.findViewById(R.id.tv_praise_count);

        setPraiseViewClip(mIvPraise);

        ViewHolderUtils.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onClickLlPraise(v);
            }
        }, mLlPraise);
    }

    private void setPraiseViewClip(View praiseView) {
        if (praiseView == null) {
            return;
        }
        //为了能展示点赞的放大效果，点赞 view 的 parent 不截切
        ViewParent parent = praiseView.getParent();
        if (parent == null) {
            return;
        }
        if (parent instanceof ViewGroup) {
            ((ViewGroup) parent).setClipChildren(false);
        }

        //再 parent，即底部信息布局
        parent = parent.getParent();
        if (parent == null) {
            return;
        }
        if (parent instanceof ViewGroup) {
            ((ViewGroup) parent).setClipChildren(false);
        }

        //再 parent ，即底部信息的 parent，因为底部信息高度是 wrap_content，所以其 parent 也要设置不裁切
        parent = parent.getParent();
        if (parent == null) {
            return;
        }
        if (parent instanceof ViewGroup) {
            ((ViewGroup) parent).setClipChildren(false);
        }
    }

    @Override
    public void bindTo(PostCard postCard) {
        super.bindTo(postCard);
        if (needShowPostType()) {
            showPostType(postCard);
        }

        //时间
        if (mTvTime != null) {
            Long creationDate = mPostCard.getCreationDate();
            if (creationDate != null && creationDate != 0) {
                mTvTime.setText(AppUtil.checkMessTimeNew(creationDate));
            } else {
                mTvTime.setText("");
            }
        }

        if (mTvReadCount != null) {
            //围观数
            if (mPostCard.getPostShowCount() == 0) {
                mTvReadCount.setText(R.string.item_post_bottom_info_tv_read);
            } else {
                mTvReadCount.setText(AppUtil.getCommentDesc(mPostCard.getPostShowCount()));
            }
        }

        if (mTvReplyCount != null) {
            //回复数
            if (mPostCard.getPostCommentCount() == 0) {
                mTvReplyCount.setText(R.string.item_post_bottom_info_tv_replay);
            } else {
                mTvReplyCount.setText(AppUtil.getCommentDesc(mPostCard.getPostCommentCount()));
            }
        }

        //设置点赞的颜色
        updatePraiseView(mPostCard.getIsPraise() == 1);
    }

    /**
     * 是否显示帖子类型
     * 默认展示，详情不展示，回帖不展示
     */
    protected boolean needShowPostType() {
        return true;
    }

    private void showPostType(PostCard postCard) {
        if (mTvPostType == null) {
            return;
        }
        //帖子类型，暂时只展示图文和话题
        int typeStringResId;
        switch (postCard.getPostTypeIdOrNegative()) {
            case PostCard.PostType.IMAGE_TEXT:
                typeStringResId = R.string.post_type_image_text;
                break;
            case PostCard.PostType.TOPIC:
                typeStringResId = R.string.post_type_topic;
                break;
            case PostCard.PostType.SHORT_VIDEO:
            case PostCard.PostType.VIDEO_POST:
                typeStringResId = R.string.post_type_video;
                break;
            case PostCard.PostType.VOTE:
                typeStringResId = R.string.post_type_vote;
                break;
            case PostCard.PostType.QUESTIONNAIRE:
                typeStringResId = R.string.post_type_questionnaire;
                break;
            default:
                typeStringResId = 0;
                break;
        }
        if (typeStringResId > 0) {
            mTvPostType.setVisibility(View.VISIBLE);
            mTvPostType.setText(typeStringResId);
        } else {
            mTvPostType.setVisibility(View.GONE);
        }
    }

    /**
     * 更新点赞 view
     */
    private void updatePraiseView(boolean praised) {
        if (mIvPraise != null) {
            if (praised) {
                mIvPraise.setImageResource(R.drawable.ic_home_post_like_checked);
            } else {
                mIvPraise.setImageResource(R.drawable.ic_home_post_like_normal);
            }
        }
        if (mTvPraiseCount != null) {
            //显示点赞数
            if (mPostCard.getPostPraiseCount() == 0) {
                mTvPraiseCount.setText(R.string.item_post_bottom_info_tv_praise);
            } else {
                mTvPraiseCount.setText(AppUtil.getCommentDesc(mPostCard.getPostPraiseCount()));
            }
        }
    }

    /**
     * 点赞
     */
    private void onClickLlPraise(View v) {
        Context context = v.getContext();
        if (AppUtil.checkLogin(context)) {
            if (mPostCard.getIsPraise() == 0) {
                //提前设置
                updatePraiseView(true);
                //点赞动画
                mIvPraise.startAnimation(AnimationUtils.loadAnimation(context, R.anim.post_praise_anim));
                mTvPraiseCount.addCount(mPostCard.getPostPraiseCount());
                praisedPostCard();
            } else {
                ToastUtil.showMessage(context, context.getString(R.string.item_post_bottom_info_toast_praised));
            }
        }
    }

    /**
     * 赞帖子
     */
    private void praisedPostCard() {
        mLlPraise.setEnabled(false);
        L00bangRequestManager2
                .setSchedulers(L00bangRequestManager2.getInstance().getService().praisedPostCard(Long.valueOf(mPostCard.getPostId())))
                .subscribe(new NormalSubscriber<String>(mLlPraise.getContext()) {
                    @Override
                    public void onSuccess(String s) {
                        super.onSuccess(s);
                        mLlPraise.setEnabled(true);
                        mPostCard.setIsPraise(1);
                        mPostCard.setPostPraiseCount(mPostCard.getPostPraiseCount() + 1);
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                        mLlPraise.setEnabled(true);
                        //取消回去
                        updatePraiseView(false);
                    }
                });
    }

}
