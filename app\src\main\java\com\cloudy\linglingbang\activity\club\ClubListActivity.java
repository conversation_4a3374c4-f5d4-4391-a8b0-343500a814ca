package com.cloudy.linglingbang.activity.club;

import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.activity.club.list.CarClubListFragment;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.constants.WebUrlConfigConstant;

import androidx.core.view.MenuItemCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

/**
 * 车友会列表的activity页面
 *
 * <AUTHOR>
 * @date 2018/6/28
 */
public class ClubListActivity extends BaseActivity {
    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_club_list);
    }

    @Override
    protected void initialize() {
        FragmentManager fm = this.getSupportFragmentManager();
        FragmentTransaction ft = fm.beginTransaction();
        Fragment fragment = new CarClubListFragment();
        ft.replace(R.id.fl_root, fragment);
        ft.commit();

    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(R.menu.menu_more, menu);
        MenuItem item = menu.findItem(R.id.it_message);
        MenuItemCompat.setActionProvider(item, new androidx.core.view.ActionProvider(this) {
            @Override
            public View onCreateActionView() {
                View view = View.inflate(ClubListActivity.this, R.layout.layout_menu_car_club, null);
                view.setOnClickListener(v -> {
                    JumpPageUtil.goCommonWeb(ClubListActivity.this, WebUrlConfigConstant.BALANCE_CAR_CLUB_RANK_LIST);
                });
                return view;
            }
        });
        return true;
    }
}
