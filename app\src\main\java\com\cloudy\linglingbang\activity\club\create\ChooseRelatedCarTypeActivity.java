package com.cloudy.linglingbang.activity.club.create;

import android.content.Intent;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.widget.CompoundButton;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.widget.FlowLayout;
import com.cloudy.linglingbang.app.widget.textview.PressEffectiveCompoundButton;
import com.cloudy.linglingbang.model.CarType;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.user.User;

import java.util.ArrayList;
import java.util.List;

/**
 * 选择涉及车型
 *
 * <AUTHOR>
 * @date 2017/11/16
 */
public class ChooseRelatedCarTypeActivity extends BaseActivity {
    private List<CarType> mChosenCarTypeList;
    private FlowLayout mFlowLayout;
    private long[] mIntentCarTypeIdArray;
    private long mUserCarTypeId;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_choose_related_car_type);
    }

    @Override
    protected void initialize() {
        mUserCarTypeId = User.getsUserInstance().getApproveCarTypeId();
        //解析传过来的 id ，在循环时根据 id 选中并添加选中的车型，可以解决传过来 id 但没有对应车型的情况
        mIntentCarTypeIdArray = getIntent().getLongArrayExtra(IntentUtils.INTENT_EXTRA_COMMON);
        mChosenCarTypeList = new ArrayList<>();
        mFlowLayout = (FlowLayout) findViewById(R.id.flow_layout);
        //获取数据
        L00bangRequestManager2.getServiceInstance()
                .getCarType()
                .compose(L00bangRequestManager2.<ArrayList<CarType>>setSchedulers())
                .subscribe(new ProgressSubscriber<ArrayList<CarType>>(this) {
                    @Override
                    public void onSuccess(final ArrayList<CarType> carTypes) {
                        super.onSuccess(carTypes);
                        onGetCarTypeResult(carTypes);
                    }
                });
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        super.onCreateOptionsMenu(menu);
        if (mToolbar != null && mToolbar.getMenu() != null) {
            MenuItem item = mToolbar.getMenu().findItem(R.id.action_right_text);
            if (item != null) {
                item.setTitle(getString(R.string.create_car_club_step_2_complete));
                item.setVisible(true);
            }
        }
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.action_right_text:
                onClickFinish();
                return true;
            default:
                return super.onOptionsItemSelected(item);
        }
    }

    private void onGetCarTypeResult(ArrayList<CarType> carTypes) {
        if (carTypes != null) {
            LayoutInflater inflater = LayoutInflater.from(this);
            for (CarType carType : carTypes) {
                PressEffectiveCompoundButton compoundButton = (PressEffectiveCompoundButton) inflater.inflate(R.layout.item_car_buying_label, mFlowLayout, false);
                compoundButton.setTag(R.id.tag_position, carType);
                compoundButton.setText(carType.getFullName());
                //选中
                if (mUserCarTypeId == carType.getCarTypeId() || inArray(mIntentCarTypeIdArray, carType.getCarTypeId())) {
                    compoundButton.setChecked(true);
                    if (!mChosenCarTypeList.contains(carType)) {
                        mChosenCarTypeList.add(carType);
                    }
                }
                compoundButton.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                    @Override
                    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                        CarType chosenCarType = (CarType) buttonView.getTag(R.id.tag_position);
                        if (isChecked) {
                            if (mChosenCarTypeList.size() >= 3) {
                                ToastUtil.showMessage(ChooseRelatedCarTypeActivity.this, getString(R.string.choose_related_car_type_prompt));
                                buttonView.setChecked(false);
                            } else {
                                mChosenCarTypeList.add(chosenCarType);
                            }
                        } else {
                            if (mUserCarTypeId == chosenCarType.getCarTypeId()) {
                                buttonView.setChecked(true);
                                ToastUtil.showMessage(ChooseRelatedCarTypeActivity.this, getString(R.string.choose_related_car_type_user_car_type_prompt));
                            } else {
                                mChosenCarTypeList.remove(chosenCarType);
                            }
                        }
                    }
                });
                mFlowLayout.addView(compoundButton);
            }
        }
    }

    private boolean inArray(long[] array, long element) {
        if (array == null) {
            return false;
        }
        for (long a : array) {
            if (a == element) {
                return true;
            }
        }
        return false;
    }

    private void onClickFinish() {
        //保存选中结果
        long[] intentCarTypeIdArray = new long[mChosenCarTypeList.size()];
        for (int i = 0; i < mChosenCarTypeList.size(); i++) {
            intentCarTypeIdArray[i] = mChosenCarTypeList.get(i).getCarTypeId();
        }
        Intent intent = new Intent();
        intent.putExtra(IntentUtils.INTENT_EXTRA_COMMON, intentCarTypeIdArray);
        StringBuilder name = new StringBuilder();
        for (int i = 0; i < mChosenCarTypeList.size(); i++) {
            if (i > 0) {
                name.append("、");
            }
            name.append(mChosenCarTypeList.get(i).getFullName());
        }
        intent.putExtra(IntentUtils.INTENT_EXTRA_FROM, name.toString());
        //因为要显示，在这里拼好传过去
        setResult(RESULT_OK, intent);
        finish();
    }
}
