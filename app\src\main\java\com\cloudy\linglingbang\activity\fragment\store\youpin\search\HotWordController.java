package com.cloudy.linglingbang.activity.fragment.store.youpin.search;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.search.history.ISearchHistory;
import com.cloudy.linglingbang.app.widget.FlowLayout;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.store.home.EcCommoditySearchKeyWord;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.rxjava3.functions.Function;

/**
 * 热词搜索控制器
 *
 * <AUTHOR>
 * @date 2020/5/6
 */
public class HotWordController implements ISearchHistory<String> {

    /**
     * 搜索相关布局，用于展示或隐藏
     */
    private View mSearchHotWordView;
    private Context mContext;
    private OnSearchListener mOnSearchListener;

    public HotWordController(Context context, OnSearchListener onSearchListener) {
        mContext = context;
        mOnSearchListener = onSearchListener;
    }

    @Override
    public void show() {
        if (mSearchHotWordView == null) {
            //为空，加载
            init();
        }
        if (mSearchHotWordView != null && mSearchHotWordView.getVisibility() != View.VISIBLE) {
            mSearchHotWordView.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 首次显示时初始化
     */
    private void init() {
        mSearchHotWordView = LayoutInflater.from(mContext).inflate(R.layout.layout_search_hot_word, null);
        FlowLayout flowLayout = mSearchHotWordView.findViewById(R.id.flow_layout);
        flowLayout.removeAllViews();
    }

    public void queryHotWord() {
        L00bangRequestManager2.getServiceInstance()
                .getSearchStoreKeyword()
                .map(new Function<BaseResponse<List<EcCommoditySearchKeyWord>>, BaseResponse<List<String>>>() {
                    @Override
                    public BaseResponse<List<String>> apply(BaseResponse<List<EcCommoditySearchKeyWord>> baseResponse) {
                        List<String> list = new ArrayList<>();
                        if (baseResponse.getData() != null && baseResponse.getData().size() > 0) {
                            for (EcCommoditySearchKeyWord word : baseResponse.getData()) {
                                list.add(word.getKeyword());
                            }
                        }
                        return baseResponse.cloneWithData(list);
                    }
                }).compose(L00bangRequestManager2.<List<String>>setSchedulers())
                .subscribe(new ProgressSubscriber<List<String>>(mContext) {
                    @Override
                    public void onSuccess(List<String> strings) {
                        super.onSuccess(strings);
                        if (mOnSearchListener != null) {
                            mOnSearchListener.showInputMethod();
                        }
                        FlowLayout flowLayout = mSearchHotWordView.findViewById(R.id.flow_layout);
                        flowLayout.removeAllViews();
                        if (strings == null || strings.isEmpty()) {
                            hide();
                            return;
                        }
                        show();
                        for (String string : strings) {
                            flowLayout.addView(createView(string));
                        }
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                        hide();
                        if (mOnSearchListener != null) {
                            mOnSearchListener.showInputMethod();
                        }
                    }
                });
    }

    private View createView(final String label) {
        TextView textView = new TextView(mContext);
        textView.setText(label);
        textView.setTextColor(mContext.getResources().getColor(R.color.color_384967));
        textView.setBackgroundColor(mContext.getResources().getColor(R.color.color_ebecef));
        textView.setTextSize(13);
        textView.setPadding(mContext.getResources().getDimensionPixelSize(R.dimen.normal_32), mContext.getResources().getDimensionPixelSize(R.dimen.normal_11), mContext.getResources().getDimensionPixelSize(R.dimen.normal_32), mContext.getResources().getDimensionPixelSize(R.dimen.normal_11));
        textView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnSearchListener != null) {
                    mOnSearchListener.search(label);
                }
            }
        });
        return textView;
    }

    @Override
    public void hide() {
        if (mSearchHotWordView != null) {
            mSearchHotWordView.setVisibility(View.GONE);
        }
    }

    @Override
    public void click(String item) {

    }

    @Override
    public void add(String item) {

    }

    @Override
    public void delete(String item) {

    }

    @Override
    public void clear() {

    }

    public View getSearchHotWordView() {
        return mSearchHotWordView;
    }

    public interface OnSearchListener {
        void search(String text);

        void showInputMethod();
    }
}
