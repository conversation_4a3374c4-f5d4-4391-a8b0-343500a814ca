package com.cloudy.linglingbang.activity.basic;

import android.content.Context;

import java.util.List;

import androidx.viewpager.widget.PagerAdapter;

/**
 * 滚动tab的context
 *
 * <AUTHOR>
 * @date 2017/5/16.
 */
public interface IViewPagerTabContext<T> {
    /**
     * 获取context
     */
    Context getContext();

    /**
     * radioButton显示的文字数组
     */
    String[] getRadioButtonTextArray();

    /**
     * radioButton的布局资源Id，方便子类继承，如果返回0，controller中也有默认的布局
     */
    int getRadioButtonResourceId(int index);

    /**
     * 创建viewpader的adapter
     */
    PagerAdapter createViewPagerAdapter(List<T> data);

    /**
     * 创建adapter的数据
     */
    List<T> createAdapterData();

    /**
     * 当选中item时
     */
    void onPageSelected(int position);

    /**
     * 当点击某个标签时
     *
     * @param position
     */
    void onTabClick(int position);
}
