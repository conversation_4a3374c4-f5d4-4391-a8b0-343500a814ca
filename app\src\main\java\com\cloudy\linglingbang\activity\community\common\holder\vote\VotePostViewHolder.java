package com.cloudy.linglingbang.activity.community.common.holder.vote;

import android.view.View;

import com.cloudy.linglingbang.activity.community.common.holder.BasePostViewHolder;

import java.util.ArrayList;

/**
 * 投票类帖子
 *
 * <AUTHOR>
 * @date 2018/8/18
 */
public class VotePostViewHolder extends BasePostViewHolder {
    public VotePostViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    protected void initChildViewHolder(View itemView) {
        mChildViewHolderList = new ArrayList<>();
        mChildViewHolderList.add(new VotePostImageViewHolder(itemView));
        mChildViewHolderList.add(new VotePostBottomInfoViewHolder(itemView));
    }
}
