package com.cloudy.linglingbang.activity.service.newenergy.util;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.view.View;
import android.view.animation.LinearInterpolator;

import com.cloudy.linglingbang.app.util.DeviceUtil;

/**
 * <AUTHOR>
 * @describe
 * @date 2022/10/27
 */
public class UnifyTireCheckAnimation {
    private static final long mTranslationDuration = 1000;
    private static final long mRotateDuration = 250;

    private View mTarget;
    private AnimatorSet mAnimatorSet;

    public UnifyTireCheckAnimation(View target){
        mTarget = target;
    }

    public void start() {
        if (mTarget == null) {
            return;
        }
        mTarget.post(new Runnable() {
            @Override
            public void run() {
                startTranslationAnimation(true, true);
            }
        });
    }

    /**
     * 开始移动动画
     */
    private void startTranslationAnimation(boolean start, final boolean left) {
        if (mTarget == null) {
            return;
        }
        int targetHeight = mTarget.getMeasuredHeight();
        //设置相机距离
        mTarget.setCameraDistance(mTarget.getContext().getResources().getDisplayMetrics().density * targetHeight * 100);

        //平移
        int translationHeight = DeviceUtil.getScreenHeight() - targetHeight;
        long duration;
        ObjectAnimator translationAnimator;
        if (start) {
            //开始时从屏幕外移入
            duration = (long) (mTranslationDuration * 1.0 / translationHeight * (targetHeight + translationHeight));
            translationAnimator = ObjectAnimator.ofFloat(mTarget, "translationY", -targetHeight, translationHeight);
        } else {
            duration = mTranslationDuration;
            if (left) {
                translationAnimator = ObjectAnimator.ofFloat(mTarget, "translationY", 0, translationHeight);
            } else {
                translationAnimator = ObjectAnimator.ofFloat(mTarget, "translationY", translationHeight, 0);
            }
        }
        translationAnimator.setDuration(duration);
        translationAnimator.setInterpolator(new LinearInterpolator());

        //旋转
        ObjectAnimator rotateAnimator;
        if (left) {
            //从下向上翻，顺时针，对于 rotationY 需要减小
            rotateAnimator = ObjectAnimator.ofFloat(mTarget, "rotationX", 0, -180);
        } else {
            rotateAnimator = ObjectAnimator.ofFloat(mTarget, "rotationX", -180, 0);
        }
        rotateAnimator.setDuration(mRotateDuration);
        rotateAnimator.setInterpolator(new LinearInterpolator());
        //如果没有提前量，也可以不设 delay 使用 playSequentially()
        rotateAnimator.setStartDelay(duration);

        mAnimatorSet = new AnimatorSet();
        mAnimatorSet.playTogether(translationAnimator, rotateAnimator);
        mAnimatorSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                //重播，无法使用 repeat 因为旋转重复时不再有 startDelay
                startTranslationAnimation(false, !left);
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        mAnimatorSet.start();
    }

    public void stop() {
        if (mTarget != null) {
            mTarget.setTranslationX(0);
            mTarget.setRotationX(0);
        }
        if (mAnimatorSet != null) {
            mAnimatorSet.removeAllListeners();
            mAnimatorSet.cancel();
            mAnimatorSet = null;
        }
    }
}
