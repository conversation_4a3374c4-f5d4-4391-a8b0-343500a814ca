package com.cloudy.linglingbang.activity.community.common.holder;

import android.text.Editable;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.LinkMovementMethod;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.util.TopicUtils;
import com.cloudy.linglingbang.app.widget.dialog.DialogUtil;
import com.cloudy.linglingbang.model.postcard.PostCard;

import androidx.annotation.CheckResult;

/**
 * 帖子内容
 * <p>
 * flags 由 BasePostAdapter 设置
 * createViewHolder 时设置给 BasePostViewHolder
 * bindTo 时再设置给 PostContentViewHolder
 *
 * <AUTHOR>
 * @date 2018/6/25
 */
public class PostContentViewHolder extends BasePostChildViewHolder {
    private TextView mTvContent;
    private int mIconFlags;
    private static final int MAX_LINE = 1;

    public PostContentViewHolder(View itemView) {
        super(itemView);
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        mIconFlags = PostIconEnum.addDefaultShowIconInfo(mIconFlags);
        mTvContent = itemView.findViewById(R.id.tv_content);
        mTvContent.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                onLongClickContent(v);
                return false;
            }
        });
        // spannable 使 ellipsize 失效
        mTvContent.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                setContentViewEllipsis(s);
            }
        });
    }

    /**
     * 省略
     */
    private void setContentViewEllipsis(CharSequence content) {
        if (TextUtils.isEmpty(content)) {
            return;
        }
        if (mTvContent.getLineCount() > MAX_LINE) {
            int endOfLastLine = mTvContent.getLayout().getLineEnd(MAX_LINE - 1);
            CharSequence charSequence = content.subSequence(0, endOfLastLine - 1);
            SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder(charSequence);
            spannableStringBuilder.append("…");
            mTvContent.setText(spannableStringBuilder);
            //使用 TextUtils.ellipsize 无法计算空行的宽度
        }
    }

    public void setIconFlags(int iconFlags) {
        mIconFlags = iconFlags;
    }

    /**
     * 点击内容
     * 即要使话题可点击，内容可占击，使用 CustomLinkMovementMethod
     * 又要使没话题也可以点击（可能因为 ImageSpan 或其他，待测试），所以使用点击事件
     */
    public void onClickContent(View v) {
        ViewParent parent = v.getParent();
        if (parent instanceof ViewGroup) {
            ((ViewGroup) parent).performClick();
        }
    }

    /**
     * 长按复制内容
     */
    private void onLongClickContent(View v) {
        if (mPostCard != null) {
            CharSequence content = getTitleOrContent(mPostCard);
            if (!TextUtils.isEmpty(content)) {
                DialogUtil.showCopyTextDialog(v.getContext(), content);
            }
        }
    }

    @Override
    public void bindTo(PostCard postCard) {
        super.bindTo(postCard);
        setContent(postCard, mTvContent);
    }

    /**
     * /**
     * 提出来，供子类重写
     *
     * @param textView 部分 span 可能需要操作 TextView ，所以传过来
     */
    protected void setContent(PostCard postCard, TextView textView) {
        CharSequence content = getTitleOrContent(postCard);
        content = addTopicInfo(postCard, content, textView, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onClickContent(v);
            }
        });
        content = addIconInfo(postCard, content, mIconFlags, textView);
        if (!TextUtils.isEmpty(content)) {
            textView.setVisibility(View.VISIBLE);
            textView.setText(content);
        } else {
            textView.setVisibility(View.GONE);
        }
    }

    /**
     * 获取标题或内容
     */
    protected CharSequence getTitleOrContent(PostCard postCard) {
        CharSequence content = postCard.getPostTitle();
        if (TextUtils.isEmpty(content)) {
            content = generateContent(postCard);
        }
        return content;
    }

    /**
     * 拼接帖子内容
     */
    private CharSequence generateContent(PostCard postCard) {
        String[] texts = postCard.getTexts();
        StringBuilder stringBuilder = new StringBuilder();
        if (texts != null && texts.length > 0) {
            for (int i = 0; i < texts.length; i++) {
                stringBuilder.append(texts[i]);
                if (i < texts.length - 1) {
                    stringBuilder.append('\n');
                }
            }
        }
        return stringBuilder;
    }

    /**
     * 添加话题信息
     */
    @CheckResult
    public static CharSequence addTopicInfo(PostCard postCard, CharSequence content, TextView textView, View.OnClickListener defaultOnClickListener) {
        String topicName = postCard.getTopicName();
        if (TextUtils.isEmpty(topicName)) {
            if (defaultOnClickListener != null) {
                textView.setOnClickListener(defaultOnClickListener);
            }
            return content;
        } else {
            //使 ClickableSpan 生效
            textView.setMovementMethod(CustomLinkMovementMethod.getInstance());
            CharSequence topicNameString = TopicUtils.getNameWithHashtagSpannableString(topicName, 0, postCard.getTopicIdOrZero());
            if (!TextUtils.isEmpty(topicNameString)) {
                SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder();
                spannableStringBuilder.append(topicNameString);
                if (content != null) {
                    spannableStringBuilder.append(content);
                }
                return spannableStringBuilder;
            } else {
                return content;
            }
        }
    }

    /**
     * 添加图标信息
     */
    @CheckResult
    public static CharSequence addIconInfo(PostCard postCard, CharSequence content, int flags, TextView textView) {
        CharSequence iconInfo = PostIconEnum.getIconInfo(textView.getContext(), postCard, flags, textView);
        if (!TextUtils.isEmpty(iconInfo)) {
            SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder();
            spannableStringBuilder.append(iconInfo);
            if (TextUtils.isEmpty(content)) {
                //最后拼图标，如果内容为空，要拼上一个空格，否则图标不显示
                spannableStringBuilder.append(" ");
            } else {
                spannableStringBuilder.append(content);
            }
            return spannableStringBuilder;
        } else {
            return content;
        }

    }

    /**
     * https://blog.csdn.net/it_talk/article/details/51098613
     */
    public static class CustomLinkMovementMethod extends LinkMovementMethod {

        private static CustomLinkMovementMethod sInstance;

        @Override
        public boolean onTouchEvent(TextView widget, Spannable buffer, MotionEvent event) {
            boolean b = super.onTouchEvent(widget, buffer, event);
            //解决点击事件冲突问题
            if (!b && event.getAction() == MotionEvent.ACTION_UP) {
                //处理widget的父控件点击事件
                ViewParent parent = widget.getParent();
                if (parent instanceof ViewGroup) {
                    return ((ViewGroup) parent).performClick();
                }
            }
            return b;
        }

        public static CustomLinkMovementMethod getInstance() {
            if (sInstance == null) {
                sInstance = new CustomLinkMovementMethod();
            }
            return sInstance;
        }

    }

}
