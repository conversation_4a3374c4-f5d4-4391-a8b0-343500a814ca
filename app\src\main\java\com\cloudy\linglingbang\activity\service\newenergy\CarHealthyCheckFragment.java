package com.cloudy.linglingbang.activity.service.newenergy;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshFragment;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.service.newenergy.util.CarHealthyCheckAnimation;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.widget.dialog.SmartChargingDialog;
import com.cloudy.linglingbang.app.widget.dialog.SmartChargingIntroDialog;
import com.cloudy.linglingbang.app.widget.image.ImageLoadUtils;
import com.cloudy.linglingbang.app.widget.recycler.header.HeaderAndFooterWrapperAdapter;
import com.cloudy.linglingbang.app.widget.textview.PressEffectiveButton;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.server.CarHealthyCheckInfo;

import java.util.List;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 车辆检查
 *
 * <AUTHOR>
 * @date 2019-08-05
 */
public class CarHealthyCheckFragment extends BaseRecyclerViewRefreshFragment<CarHealthyCheckInfo> {


    private ImageView mIvCarImg;
    /** 执行帧动画控件 */
    ImageView mIvCheckAnim;

    /**
     * 检查失败或成功的根布局
     */
    ConstraintLayout mConstraintLayout;

    /**
     * 如果检查失败重试的按钮
     */
    PressEffectiveButton pefRetry;
    /**
     * 检查中或者失败提示的图标
     */
    ImageView mIvCarCheckStatus;
    /**
     * 检查中或者检查失败显示的文字
     */
    TextView mTvCarCheckText;

    private String mVin;
    private String mCarImageUrl;
    private Handler mHandler;

    private final Runnable mRunnable = new Runnable() {
        @Override
        public void run() {
            requestResult();
        }
    };
    /** 请求时的时间 */
    private long mRequestTime;
    private CarHealthyCheckAnimation mTranslateAndRotateAnimation;
    private CarHealthyCheckAdapter mCarHealthyCheckAdapter;
    private HeaderAndFooterWrapperAdapter mHeaderAndFooterWrapperAdapter;

    private boolean isRequesting;

    public static Fragment newInstance(String vin, String carImageUrl) {
        Bundle bundle = new Bundle();
        bundle.putString("vin", vin);
        bundle.putString("url", carImageUrl);
        CarHealthyCheckFragment carHealthyCheckFragment = new CarHealthyCheckFragment();
        carHealthyCheckFragment.setArguments(bundle);
        return carHealthyCheckFragment;
    }

    @Override
    protected void initViews() {
        super.initViews();
        mVin = getArguments().getString("vin");
        mCarImageUrl = getArguments().getString("url");
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_car_healthy_check;
    }

    /**
     * 请求失败
     */
    private void requestFailure() {
        mConstraintLayout.setVisibility(View.VISIBLE);
        mIvCarCheckStatus.setImageDrawable(getResources().getDrawable(R.drawable.ic_car_healthy_check_failure));
        mTvCarCheckText.setText(getString(R.string.txt_check_car_healthy_failure));
        pefRetry.setVisibility(View.VISIBLE);
        stopAnim();
        mIvCheckAnim.setVisibility(View.GONE);
    }

    /**
     * 请求成功,更改状态,显示请求后的列表
     */
    private void updateViews() {
        mIvCheckAnim.setVisibility(View.GONE);
        mConstraintLayout.setVisibility(View.GONE);
        if (mCarHealthyCheckAdapter != null) {
            addFootView();
            /*List<CarHealthyCheckInfo> data = mCarHealthyCheckAdapter.getData();
            if (data != null && !data.isEmpty()) {
                for (CarHealthyCheckInfo info : data) {
                    if (info.getItemList() != null && !info.getItemList().isEmpty()) {
                        for (CarHealthyCheckInfo.ItemListBean itemListBean : info.getItemList()) {
                            if (-1 == itemListBean.getItemStatus()) {
                                addFootView();
                            }
                        }
                    }
                }
            }*/
            getRefreshController().getAdapter().notifyDataSetChanged();
            getRefreshController().getSwipeToLoadLayout().setRefreshing(false);
            getRefreshController().getSwipeToLoadLayout().setLoadingMore(false, true);
            stopAnim();
        }

    }

    /**
     * 添加尾部布局
     */
    private void addFootView() {
        if (getActivity() == null) {
            return;
        }
        View footView = LayoutInflater.from(getActivity()).inflate(R.layout.foot_car_healthy_status, getRefreshController().getRecyclerView(), false);
        if (mHeaderAndFooterWrapperAdapter != null) {
            if (mHeaderAndFooterWrapperAdapter.getFootersCount() == 0) {
                mHeaderAndFooterWrapperAdapter.addFooterView(footView);
                ServiceCarHealthyCheckFootLoader serviceCarHealthyCheckFootLoader = new ServiceCarHealthyCheckFootLoader(footView);
                serviceCarHealthyCheckFootLoader.initData();
            }
        }
    }

    /**
     * 请求服务器结果
     */
    private void requestResult() {
        if (mCarHealthyCheckAdapter != null) {
            if (mCarHealthyCheckAdapter.getData() != null && !mCarHealthyCheckAdapter.getData().isEmpty()) {
                updateViews();
            } else {
                requestFailure();
            }
        }

    }



    @Override
    public void onDestroyView() {
        super.onDestroyView();
        stopAnim();
        if (mHandler != null) {
            mHandler.removeCallbacks(mRunnable);
        }
    }

    /**
     * 停止执行动画
     */
    private void stopAnim() {
        mTranslateAndRotateAnimation.stop();
    }

    @Override
    public HeaderAndFooterWrapperAdapter createAdapter(List<CarHealthyCheckInfo> list) {
        mCarHealthyCheckAdapter = new CarHealthyCheckAdapter(getContext(), list);
        mHeaderAndFooterWrapperAdapter = new HeaderAndFooterWrapperAdapter(mCarHealthyCheckAdapter);
        View headerView = LayoutInflater.from(getActivity()).inflate(R.layout.head_car_healthy_status, getRefreshController().getRecyclerView(), false);
        initHeaderView(headerView);
        mHeaderAndFooterWrapperAdapter.addHeaderView(headerView);
        mCarHealthyCheckAdapter.setOnChildItemClickedListener(new CarHealthyCheckAdapter.OnChildItemClickListener() {
            @Override
            public void onChildItemClickListener(View view) {
                if (view.getId() == R.id.peb_smart_charging_status) {
                    //调用去补电接口
                    chargeApply();
                } else if (view.getId() == R.id.iv_car_healthy_check_smart_charging) {
                    //智能补电说明
                    showSmartChargingIntroDialog();
                }
            }
        });
        return mHeaderAndFooterWrapperAdapter;
    }

    /**
     * 申请补电
     */
    private void chargeApply() {
        if (getContext() == null) {
            return;
        }
        if (!isRequesting) {
            isRequesting = true;
            L00bangRequestManager2.getServiceInstance()
                    .chargeApply(mVin)
                    .compose(L00bangRequestManager2.setSchedulers())
                    .subscribe(new ProgressSubscriber<Object>(getContext()) {
                        @Override
                        public void onSuccess(Object o) {
                            super.onSuccess(o);
                            isRequesting = false;
                            //成功的时候提示补电中
                            showCharging();

                        }

                        @Override
                        public void onFailure(Throwable e) {
                            super.onFailure(e);
                            isRequesting = false;
                            //失败提示补电失败的弹窗
                            showSmartChargingDialog();
                            ToastUtil.showMessage(getContext(), e.getMessage());
                        }
                    });
        }

    }

    /**
     * 充电中提示文案,修改适配器中数据
     */
    private void showCharging() {
        ToastUtil.showMessage(getContext(), getString(R.string.tips_car_charging));
        List<CarHealthyCheckInfo> data = mCarHealthyCheckAdapter.getData();
        if (data != null && !data.isEmpty()) {
            for (CarHealthyCheckInfo carHealthyCheckInfo : data) {
                if ("3".equals(carHealthyCheckInfo.getType())) {
                    List<CarHealthyCheckInfo.ItemListBean> itemList = carHealthyCheckInfo.getItemList();
                    for (CarHealthyCheckInfo.ItemListBean itemListBean : itemList) {
                        itemListBean.setItemStatus(0);
                        itemListBean.setItemStatusStr(getString(R.string.tips_car_charging));
                    }
                }
            }
            getRefreshController().getAdapter().notifyDataSetChanged();
        }
    }

    /**
     * 显示充电失败的框
     */
    private void showSmartChargingDialog() {
        if (getContext() == null) {
            return;
        }
        SmartChargingDialog smartChargingDialog = new SmartChargingDialog(getContext());
        smartChargingDialog.setIcon(R.drawable.smart_charging_failure);
        smartChargingDialog.setMessage(getActivity().getResources().getString(R.string.txt_smart_charging_failure));
        smartChargingDialog.setMessageColor(getContext().getResources().getColor(R.color.red));
        smartChargingDialog.setRightText(getContext().getResources().getString(R.string.txt_to_smart_charging));
        smartChargingDialog.setOnClickDialogListener(new SmartChargingDialog.OnClickDialogListener() {
            @Override
            public void onClickDialog() {
                chargeApply();
            }
        });
        if (!smartChargingDialog.isShowing()) {
            smartChargingDialog.show();
        }
    }

    /**
     * 显示充电说明框
     */
    private void showSmartChargingIntroDialog() {
        SmartChargingIntroDialog smartChargingIntroDialog = new SmartChargingIntroDialog(getContext());
        if (!smartChargingIntroDialog.isShowing()) {
            smartChargingIntroDialog.show();
        }
    }

    /**
     * 初始化headView
     */
    private void initHeaderView(View headerView) {
        //车辆图片
        mIvCarImg = headerView.findViewById(R.id.iv_car_img);
        new ImageLoad(getActivity(), mIvCarImg, ImageLoadUtils.processImageUrl(mCarImageUrl), ImageLoad.LoadMode.URL)
                .setPlaceholderAndError(R.drawable.ic_common_place_holder_corner_10)
                .load();
        mIvCheckAnim = headerView.findViewById(R.id.iv_car_check_anim);
        mConstraintLayout = headerView.findViewById(R.id.stub_car_check_status);
        pefRetry = headerView.findViewById(R.id.tv_check_car_healthy_retry);
        mIvCarCheckStatus = headerView.findViewById(R.id.iv_car_healthy_check_status);
        mTvCarCheckText = headerView.findViewById(R.id.tv_check_car_healthy_failure);

        mIvCarCheckStatus.setImageDrawable(getResources().getDrawable(R.drawable.ic_car_healthy_checking));

        mTranslateAndRotateAnimation = new CarHealthyCheckAnimation(mIvCheckAnim);

        pefRetry.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!AppUtil.checkLogin(getContext())) {
                    return;
                }
                getRefreshController().onRefresh();
            }
        });
    }

    @Override
    public Observable<BaseResponse<List<CarHealthyCheckInfo>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return service2.healthInfo(mVin);
    }

    /**
     * 初始化刷新控制器
     */
    @Override
    public RefreshController<CarHealthyCheckInfo> createRefreshController() {

        final RefreshController<CarHealthyCheckInfo> refreshController = new RefreshController<CarHealthyCheckInfo>(this) {
            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return null;
            }

            @Override
            public void onRefresh() {
                super.onRefresh();
                mRequestTime = System.currentTimeMillis();
                mIvCheckAnim.setVisibility(View.VISIBLE);
                mTranslateAndRotateAnimation.start();
                mIvCarCheckStatus.setImageDrawable(getResources().getDrawable(R.drawable.ic_car_healthy_checking));
                mIvCarCheckStatus.setVisibility(View.VISIBLE);
                mTvCarCheckText.setText(getString(R.string.txt_watch_car_healthy_checking));
                pefRetry.setVisibility(View.GONE);
            }

            @Override
            protected boolean showRefreshingWhenLoadDataAfterInitViews() {
                return false;
            }

            @Override
            protected boolean isRefreshEnable() {
                return false;
            }

            @Override
            protected void onRefreshComplete() {
                super.onRefreshComplete();
                setRefreshEnable(false);
                setLoadMoreEnable(false);
            }

            @Override
            protected void onLoadSuccess(int loadPage, List<CarHealthyCheckInfo> list, int loadType) {
                if (getRecyclerView() == null || list == null) {
                    return;
                }
//                Iterator<CarHealthyCheckInfo> iterator = list.iterator();
//                while (iterator.hasNext()) {
//                    if ("3".equals(iterator.next().getType())) {
//                        iterator.remove();
//                    }
//                }
                mData.clear();
                mData.addAll(list);
                setLoadMoreEnable(false);
                requestResult();
            }

            @Override
            public void onLoadFail(int loadPage, Throwable e) {
                super.onLoadFail(loadPage, e);
                requestResult();
            }
        };

        return refreshController;
    }
}
