package com.cloudy.linglingbang.activity.community.post;

import android.content.Context;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.Comment;

import java.util.List;

/**
 * 帖子详情，webview作为头部
 *
 * <AUTHOR> create at 2016/10/18 17:33
 */
class PostDetailWebHeaderAdapter extends BaseRecyclerViewAdapter<Comment> {

    private long mPostId;

    public PostDetailWebHeaderAdapter(Context context, List<Comment> data, long postId) {
        super(context, data);
        mPostId = postId;
    }

    @Override
    protected BaseRecyclerViewHolder<Comment> createViewHolder(View itemView) {
        return null;
    }

    @Override
    protected BaseRecyclerViewHolder<Comment> createViewHolderWithViewType(View itemView, int viewType) {
        if (viewType == 0) {
            return new PostContentWebViewHolder(itemView, mPostId);
        } else {
            return new PostDetailCommentViewHolder(this, itemView);
        }
    }

    @Override
    protected int getItemLayoutRes(int viewType) {
        if (viewType == 0) {
            return R.layout.item_post_detail_web_header;
        } else {
            return R.layout.item_comment;
        }
    }

    @Override
    public int getItemViewType(int position) {
        return position == 0 ? 0 : 1;
    }
}
