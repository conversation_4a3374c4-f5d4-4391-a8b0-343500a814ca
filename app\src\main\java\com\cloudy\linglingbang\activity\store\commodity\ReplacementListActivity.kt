package com.cloudy.linglingbang.activity.store.commodity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import butterknife.BindView
import butterknife.OnClick
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity
import com.cloudy.linglingbang.activity.basic.IntentUtils
import com.cloudy.linglingbang.activity.basic.RefreshController
import com.cloudy.linglingbang.activity.store.commodity.adapter.ReplacementAdapter
import com.cloudy.linglingbang.activity.store.commodity.dialog.ActivityDescDialog
import com.cloudy.linglingbang.activity.store.commodity.dialog.SkuSelectorDialog
import com.cloudy.linglingbang.app.util.AppUtil
import com.cloudy.linglingbang.app.util.ModelUtils
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils
import com.cloudy.linglingbang.app.util.span.ForegroundColorAndAbsoluteSizeSpan
import com.cloudy.linglingbang.app.widget.banner.AdRoundImageView
import com.cloudy.linglingbang.app.widget.recycler.header.HeaderAndFooterWrapperAdapter
import com.cloudy.linglingbang.constants.AppConstants
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber
import com.cloudy.linglingbang.model.store.commodity.Compact
import com.cloudy.linglingbang.model.store.commodity.Replacement
import io.reactivex.rxjava3.core.Observable
import java.math.BigDecimal

/**
 *  商品 换购/凑单
 *
 * <AUTHOR>
 * @date 2022/10/13
 */
open class ReplacementListActivity : BaseRecyclerViewRefreshActivity<Any>() {
    var cartId = 0L
    var commodityId = 0L
    var activityId = 0L
    var commoditySkuId = 0L

    var mHeaderView: View? = null
    var mReplacement: Any? = null

    /**
     * 0:换购 1:凑单
     */
    var type: Int = 0

    private var mIvCommodityImage: AdRoundImageView? = null
    private var mTvCommodityName: TextView? = null
    private var mTvCommodityCount: TextView? = null
    private var mTvCommodityPrice: TextView? = null
    private var mTvCommodityActivityContent: TextView? = null
    private var mTvCommodityActivityDate: TextView? = null
    private var mIvCommodityActivityTip: ImageView? = null

    var adapter: ReplacementAdapter? = null

    @JvmField
    @BindView(R.id.tv_total_price)
    var mTvTotalPrice: TextView? = null

    @JvmField
    @BindView(R.id.tv_total_count)
    var mTvTotalCount: TextView? = null

    @JvmField
    @BindView(R.id.btn_commit)
    var mBtnCommit: TextView? = null


    companion object {
        /**
         * 去凑单页面
         */
        fun startActivity(
            context: Context?,
            activityId: Long,
        ) {
            startActivity(context, null, null, null, activityId, 1)
        }

        private fun startActivity(
            context: Context?,
            cartId: Long?,
            commodityId: Long?,
            commoditySkuId: Long?,
            activityId: Long,
            type: Int
        ) {
            val intent = Intent(context, ReplacementListActivity::class.java)
            cartId?.let {
                intent.putExtra("cartId", it)
            }
            commoditySkuId?.let {
                intent.putExtra("commoditySkuId", it)
            }
            commodityId?.let {
                intent.putExtra("commodityId", it)
            }
            intent.putExtra("type", type)
            intent.putExtra("activityId", activityId)
            context?.startActivity(intent)
        }

        /**
         * 去换购页面
         */
        fun toReplacementPage(
            context: Context?,
            cartId: Long,
            commodityId: Long,
            commoditySkuId: Long,
            activityId: Long,
        ) {
            startActivity(context, cartId, commodityId, commoditySkuId, activityId, 0)
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (type == 1) {
            getCompactList()
        } else {
            getGoodsList()
        }
    }

    private fun getCompactList() {
        val map = HashMap<String, String>(2)
        map["activityId"] = activityId.toString()
        map["channelSourceId"] = AppConstants.CHANNEL_SOURCE_ID.toString()
        L00bangRequestManager2.getServiceInstance()
            .getActivityCommodity(map)
            .compose(L00bangRequestManager2.setSchedulers())
            .subscribe(object : ProgressSubscriber<Compact>(this) {
                override fun onSuccess(t: Compact?) {
                    super.onSuccess(t)
                    mReplacement = t
                    setHeaderView()
                }
            })

    }

    override fun loadViewLayout() {
        setContentView(R.layout.activity_replacement)
    }

    override fun initialize() {
        super.initialize()
        cartId = intent?.getLongExtra("cartId", 0L) ?: 0L
        type = intent?.getIntExtra("type", type) ?: type
        commodityId = intent?.getLongExtra("commodityId", 0L) ?: 0L
        activityId = intent?.getLongExtra("activityId", 0L) ?: 0L
        commoditySkuId = intent?.getLongExtra("commoditySkuId", 0L) ?: 0L
        if (type == 1) {
            setMiddleTitle(getString(R.string.commodity_compact_activity_list_title))
            mBtnCommit?.setText(R.string.commodity_btn_go_shopping_cart)
        } else {
            setMiddleTitle(getString(R.string.commodity_replace_activity_list_title))
            mBtnCommit?.text = resources.getString(R.string.common_ok_queren)
        }
        setPriceData()
    }

    private fun setPriceData() {
        val sp = SpannableStringBuilder()
        if (type == 0) {
            var p = BigDecimal.ZERO
            mTvTotalCount?.text =
                resources.getString(R.string.commodity_have_replace_count, "-", "-")
            if (mReplacement is Replacement) {
                var count = 0
                (mReplacement as Replacement).apply {
                    if (exchangeCommodityList?.isNotEmpty() == true) {
                        for (commodityInfo in exchangeCommodityList) {
                            if (commodityInfo.isCheck == 1 && commodityInfo.expiredReason == 0) {
                                count++
                                p = p.add(
                                    toBigDecimal(commodityInfo.sellPrice).multiply(commodityInfo.limitBuyCountMin.toBigDecimal())
                                )
                            }
                        }
                        mTvTotalCount?.text = resources.getString(
                            R.string.commodity_have_replace_count,
                            count.toString(),
                            exchangeCommodityList.size.toString()
                        )
                    }
                }
            }
            sp.append("总计： ")
            sp.append(ModelUtils.getRmbOrEmptyString(p))
        } else {
            var p = BigDecimal.ZERO
            mTvTotalCount?.setText(R.string.commodity_compact_activity_tip)
            if (mReplacement is Compact) {
                (mReplacement as Compact).apply {
                    if (!TextUtils.isEmpty(activityInfo?.promotionActivityTips)) {
                        mTvTotalCount?.text = activityInfo?.promotionActivityTips
                    }
                    p = p.add(toBigDecimal(payAmount))
                }
            }
            sp.append("小计： ")
            sp.append(ModelUtils.getRmbOrEmptyString(p, true))
        }
        sp.setSpan(
            ForegroundColorAndAbsoluteSizeSpan(
                resources.getColor(R.color.color_ea0029),
                resources.getDimensionPixelSize(R.dimen.common_text_size_44).toFloat()
            ),
            sp.indexOf("¥"),
            sp.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        mTvTotalPrice?.text = sp
    }

    override fun createAdapter(list: MutableList<Any>): RecyclerView.Adapter<out RecyclerView.ViewHolder> {
        val headerAndFooterWrapperAdapter = HeaderAndFooterWrapperAdapter<Any>()
        mHeaderView = LayoutInflater.from(this)
            .inflate(R.layout.layout_replacement_header, refreshController?.recyclerView, false)
        headerAndFooterWrapperAdapter.addHeaderView(mHeaderView)
        adapter = ReplacementAdapter(this, list, type)
        initHeaderView()
        headerAndFooterWrapperAdapter.innerAdapter = adapter
        return headerAndFooterWrapperAdapter
    }

    override fun getListDataFormNet(
        service2: L00bangService2?,
        pageNo: Int,
        pageSize: Int
    ): Observable<BaseResponse<MutableList<Any>>>? {
        return null
    }

    override fun createRefreshController(): RefreshController<Any> {
        return object : RefreshController<Any>(this) {
            override fun initViews(rootView: View?) {
                super.initViews(rootView)
                swipeToLoadLayout.isRefreshEnabled = false
                swipeToLoadLayout.isLoadMoreEnabled = false
            }

            override fun loadDataAfterInitViews(): Boolean {
                return false
            }

            override fun createItemDecoration(context: Context?): RecyclerView.ItemDecoration? {
                return null
            }
        }
    }

    /**
     * 获取换购
     */
    private fun getGoodsList() {
        val map = HashMap<String, String>(5)
        map["cartId"] = cartId.toString()
        map["commodityId"] = commodityId.toString()
        map["activityId"] = activityId.toString()
        map["commoditySkuId"] = commoditySkuId.toString()
        map["channelSourceId"] = AppConstants.CHANNEL_SOURCE_ID.toString()
        L00bangRequestManager2.getServiceInstance()
            .getActivityExchangeCommodity(map)
            .compose(L00bangRequestManager2.setSchedulers())
            .subscribe(object : ProgressSubscriber<Replacement>(this) {
                override fun onSuccess(t: Replacement?) {
                    super.onSuccess(t)
                    mReplacement = t
                    setHeaderView()
                }
            })
    }

    private fun initHeaderView() {
        mHeaderView?.apply {

            findViewById<View>(R.id.rl_content)?.visibility =
                if (type == 1) View.GONE else View.VISIBLE

            mIvCommodityImage = findViewById(R.id.iv_image)
            mTvCommodityName = findViewById(R.id.tv_name)
            mTvCommodityCount = findViewById(R.id.tv_count)
            mTvCommodityPrice = findViewById(R.id.tv_price)

            mTvCommodityActivityContent = findViewById(R.id.tv_content)
            mTvCommodityActivityDate = findViewById(R.id.tv_date)
            mIvCommodityActivityTip = findViewById(R.id.iv_tip)
            mIvCommodityActivityTip?.visibility = View.GONE
            mIvCommodityActivityTip?.setOnClickListener {
                if (mReplacement is Replacement) {
                    (mReplacement as Replacement).activityInfo?.apply {
                        val dialog =
                            ActivityDescDialog(this@ReplacementListActivity, promotionActivityDesc)
                        dialog.show()
                    }
                } else if (mReplacement is Compact) {
                    (mReplacement as Compact).activityInfo?.apply {
                        val dialog =
                            ActivityDescDialog(
                                this@ReplacementListActivity,
                                promotionActivityDesc
                            )
                        dialog.show()
                    }
                }

            }
        }
    }

    private fun setHeaderView() {
        mIvCommodityActivityTip?.visibility = View.GONE
        if (mReplacement is Replacement) {
            (mReplacement as Replacement).apply {
                setActivityView(activityInfo)
                commodityInfo?.apply {
                    mIvCommodityImage?.createImageLoad(skuImage)?.load()
                    mTvCommodityName?.text = commodityName
                    mTvCommodityCount?.text = "x$quantity"
                    mTvCommodityPrice?.text = "¥$sellPrice"
                }
                refreshController.data.clear()
                if (exchangeCommodityList != null) {
                    refreshController.data.addAll(exchangeCommodityList)
                }
                refreshController.adapter.notifyDataSetChanged()
            }

        } else if (mReplacement is Compact) {
            (mReplacement as Compact).apply {
                refreshController.data.clear()
                if (commodityList != null) {
                    refreshController.data.addAll(commodityList)
                }
                refreshController.adapter.notifyDataSetChanged()

                setActivityView(activityInfo)
            }
        }

        setPriceData()
    }

    private fun setActivityView(activityInfo: Replacement.ActivityInfo?) {
        activityInfo?.apply {
            mIvCommodityActivityTip?.visibility =
                if (TextUtils.isEmpty(promotionActivityDesc)) View.GONE else View.VISIBLE
            mTvCommodityActivityContent?.text =
                resources.getString(R.string.commodity_compact_activity_sales, promotionName)
            mTvCommodityActivityDate?.text = resources.getString(
                R.string.commodity_compact_activity_date,
                AppUtil.formatDate(promotionStartTime, "yyyy.MM.dd HH:mm"),
                AppUtil.formatDate(promotionEndTime, "yyyy.MM.dd HH:mm")
            )
        }

        if (activityInfo is Compact.ActivityInfo) {
            mTvTotalCount?.setText(R.string.commodity_compact_activity_tip)
            if (activityInfo.isApply) {
                mTvTotalCount?.setText(R.string.commodity_compact_activity_satisfy)
            }
        }
    }

    @OnClick(R.id.btn_commit)
    fun onClick(view: View) {
        if (view.id == R.id.btn_commit) {
            if (type == 1) {
                SensorsUtils.sensorsClickBtn("点击去购物车", "凑单活动页", "去购物车按钮")
                IntentUtils.startActivity(this, ShoppingCartActivity::class.java)
                finish()
            } else {
                //保存换购商品
                saveExchangeToCart()
            }
        }
    }

    /**
     * 保存换购商品
     */
    private fun saveExchangeToCart() {
        val map = HashMap<String, Any>(5)
        map["cartId"] = cartId.toString()
        map["activityId"] = activityId.toString()
        map["channelSourceId"] = AppConstants.CHANNEL_SOURCE_ID.toString()

        val list = arrayListOf<Map<String, Any>>()
        adapter?.apply {
            data.forEach {
                if (it is Replacement.CommodityInfo) {
                    val commodityInfo = it
                    if (commodityInfo.isCheck == 1) {
                        val temp = HashMap<String, Any>(2)
                        temp["commodityId"] = commodityInfo.commodityId
                        temp["skuId"] = commodityInfo.skuId
                        list.add(temp)
                    }
                }
            }
        }
        map["lmShoppingCartExchanges"] = list
        L00bangRequestManager2.getServiceInstance()
            .saveExchangeToCart(map)
            .compose(L00bangRequestManager2.setSchedulers())
            .subscribe(object : ProgressSubscriber<Boolean>(this) {
                override fun onSuccess(t: Boolean?) {
                    super.onSuccess(t)
                    finish()
                }
            })
    }

    private var skuSelectorDialog: SkuSelectorDialog? = null
    private var oldCommodityId: Long? = null
    fun openSkuDialog(commodityId: Long?) {
        if (commodityId == null) {
            return
        }
        if (commodityId == oldCommodityId) {
            oldCommodityId = commodityId
        } else {
            oldCommodityId = commodityId
            skuSelectorDialog?.dismiss()
            skuSelectorDialog = null
            crateSkuDialog()
        }
        if (skuSelectorDialog == null) {
            crateSkuDialog()
        }
        skuSelectorDialog?.commodityId = oldCommodityId
        skuSelectorDialog?.openType = CommodityOpenDialog.SHOPPING_CAR
        if (skuSelectorDialog?.isShowing == false) {
            skuSelectorDialog?.showDialog()
        }
    }

    private fun crateSkuDialog() {
        if (skuSelectorDialog == null) {
            skuSelectorDialog = object : SkuSelectorDialog(this) {
                override fun doBtnOkSensors() {
                    SensorsUtils.sensorsClickBtn("点击去购物车", "凑单活动页", "去购物车按钮")
                }
            }
            skuSelectorDialog?.setOnSkuSelectListener(object :
                SkuSelectorDialog.OnSkuSelectListener {
                override fun onSkuSelect(attributes: String?, skuId: Long?) {
                }

                override fun addCarSuccess(count: Int) {
                    if (type == 1) {
                        getCompactList()
                    }
                }
            })
        }
    }

    fun refreshPrice() {
        setPriceData()
    }

    private fun toBigDecimal(price: String?): BigDecimal {
        if (TextUtils.isEmpty(price)) {
            return BigDecimal.ZERO
        }
        return try {
            BigDecimal(price)
        } catch (e: Exception) {
            BigDecimal.ZERO
        }
    }
}