package com.cloudy.linglingbang.activity.community.post.shortVideo;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.SurfaceView;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.IRefreshContext;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.community.post.CommunityUtils;
import com.cloudy.linglingbang.activity.community.post.ReplyInputLayout;
import com.cloudy.linglingbang.activity.community.post.TopCommentsLoader;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.DividerItemDecoration;
import com.cloudy.linglingbang.model.Comment;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.user.User;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import io.reactivex.rxjava3.core.Observable;

/**
 * 短视频帖子底部弹出评论的dialog
 *
 * <AUTHOR>
 * @date 2018/11/19
 */
public class ShortVideoCommentDialog extends DialogFragment implements IRefreshContext<Comment> {
    private Context mContext;
    private RefreshController<Comment> mRefreshController;
    private View mRootView;
    private boolean mIsInitialized;
    private long mPostId;
    private TopCommentsLoader mTopCommentsLoader;
    private PostCard mPostCard;

    /**
     * 回复框
     */
    @BindView(R.id.reply_input_layout)
    ReplyInputLayout mReplyInputLayout;

    /**
     * 回复文本框
     */
    @BindView(R.id.tv_reply)
    TextView mTvReply;

    public static DialogFragment newInstance(PostCard postCard) {
        DialogFragment dialogFragment = new ShortVideoCommentDialog();
        Bundle bundle = new Bundle();
        bundle.putSerializable("postCard", postCard);
//        bundle.putLong("postCard", postCard);
        dialogFragment.setArguments(bundle);
        return dialogFragment;
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        if (getArguments() != null) {
            mPostCard = (PostCard) getArguments().getSerializable("postCard");
            if (mPostCard != null) {
                try {
                    mPostId = Long.valueOf(mPostCard.getPostId());
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                }
            }
        }
        if (mRefreshController == null) {
            mRefreshController = createRefreshController();
        }
        mContext = getContext();

    }

    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        //取消标题栏
        getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);
        final Window window = getDialog().getWindow();
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        window.getDecorView().setPadding(0, 0, 0, 0);
        WindowManager.LayoutParams wlp = window.getAttributes();
        wlp.gravity = Gravity.BOTTOM;
        wlp.width = WindowManager.LayoutParams.MATCH_PARENT;
        wlp.height = WindowManager.LayoutParams.MATCH_PARENT;
        wlp.windowAnimations = R.style.Animation_Bottom_Dialog;
        window.setAttributes(wlp);
        mRootView = inflater.inflate(R.layout.dialog_short_video_comment, container, false);
        ButterKnife.bind(this, mRootView);
        initInputView();
        return mRootView;
    }

    /**
     * 加载输入框
     */
    private void initInputView() {
        //添加一个0dp的SurfaceView，防止第一次动态添加时闪黑屏问题
        SurfaceView surfaceView = new SurfaceView(mContext);
        surfaceView.setLayoutParams(new ViewGroup.LayoutParams(0,
                0));
        mReplyInputLayout.setReplyPostHint(getString(R.string.post_detail_input_reply_hint));
        mReplyInputLayout.setReplyPostCommentHint(getString(R.string.post_comment_input_reply_hint));
        mReplyInputLayout.addView(surfaceView);
        mReplyInputLayout.setShowQuickReply(false);
        mReplyInputLayout.setShowExpressAndPic(false);
        //end
        mReplyInputLayout.setReplyInputCallBack(new ReplyInputLayout.ReplyInputCallBack() {
            @Override
            public void onCancel() {
                showReplyLayout();
            }

            @Override
            public void onReplyResult(Comment resultComment, int commentPosition) {
                if (commentPosition == -1) {
                    //回-1时，表示是主题
                    //获取置顶评论的条数，插入新的回复
                    int topCommentsSize = mTopCommentsLoader.getTopComments() == null ? 0 : mTopCommentsLoader.getTopComments().size();
                    getRefreshController().getData().add(topCommentsSize, resultComment);
                    getRefreshController().getAdapter().notifyItemInserted(topCommentsSize);
                    //滚动到回帖处
                    getRefreshController().getRecyclerView().scrollToPosition(topCommentsSize);
                } else {
                    //表示回复楼层
                    onReplayCommentSuccess(resultComment, commentPosition);
                }
                //回复楼主和回复楼层都数量都加1/**/
                /*mPostCard.setPostCommentCount(mPostCard.getPostCommentCount() + 1);
                onGetPostDetail(mPostCard, commentPosition);*/
            }

            /**
             * 回复评论成功
             */
            private void onReplayCommentSuccess(Comment resultComment, int position) {
                List<Comment> commentList = getRefreshController().getData();
                if (commentList != null && position < commentList.size()) {
                    //取出回复了哪一条评论
                    Comment anchorComment = commentList.get(position);
                    if (anchorComment != null) {
                        //有了 comment id 遍历获取
                        Long anchorCommentId = anchorComment.getCommentId();
                        if (anchorCommentId != null && anchorCommentId != 0) {
                            for (int i = 0; i < commentList.size(); i++) {
                                Comment comment = commentList.get(i);
                                if (comment != null) {
                                    Long commentId = comment.getCommentId();
                                    if (commentId != null && commentId.equals(anchorCommentId)) {
                                        //添加子回复
                                        List<Comment> subCommentList = comment.getSubWebComments();
                                        if (subCommentList == null) {
                                            subCommentList = new ArrayList<>();
                                        }
                                        subCommentList.add(resultComment);
                                        comment.setSubWebComments(subCommentList);
                                        getRefreshController().getAdapter().notifyItemChanged(i);
                                    }
                                }
                            }
                        }
                    }
                }
            }

        });

    }

    /**
     * 显示回复主题的布局
     */
    private void showReplyLayout() {
        mTvReply.setVisibility(View.VISIBLE);
        if (CommunityUtils.PostUtils.canReply(mPostCard)) {
            mTvReply.setText(R.string.post_detail_input_reply);
            mTvReply.setClickable(true);
            mTvReply.setBackgroundResource(R.drawable.bg_rectangle_circle_solid_ffffff);
        } else {
            //不可回复时设置背景。
            //不用处理点击事件，点击后在showReplyPost中判断了
            mTvReply.setText(R.string.post_detail_input_can_not_reply);
            mTvReply.setClickable(false);
            mTvReply.setBackgroundResource(R.drawable.bg_rectangle_circle_solid_ffffff);
        }
    }

    public void showReplyLayout(Comment comment, int position) {
        if (!CommunityUtils.PostUtils.canReply(mPostCard)) {
            return;
        }
        if (comment != null && comment.getUser() != null) {
            if (User.getsUserInstance() != null) {
                if (comment.getUser().getUserIdStr().equals(User.getsUserInstance().getUserIdStr())) {
                    ToastUtil.showMessage(mContext, "您不能回复自己的回复");
                    return;
                }
            }
            String commentParentId = comment.getCommentParentId();
            if (TextUtils.isEmpty(commentParentId) || commentParentId.equals("0")) {
                //有的时候，是回复楼中楼
                //没有的时候，就是回复楼层，没有commentParentId
                commentParentId = String.valueOf(comment.getCommentId());
            }
            mReplyInputLayout.showInput(mPostId, position, comment.getCommentId(), commentParentId, comment.getUser());
            mTvReply.setVisibility(View.GONE);
        }
    }



    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if (!mIsInitialized) {
            //懒加载不初始化，加载实际布局后会重新调用 onViewCreated
            mIsInitialized = true;
            mRefreshController.initViews(mRootView);
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        //设置全屏
        Window window = getDialog().getWindow();
        DisplayMetrics dm = new DisplayMetrics();
        getActivity().getWindowManager().getDefaultDisplay().getMetrics(dm);
        window.setLayout(dm.widthPixels, getDialog().getWindow().getAttributes().height);
        //设置透明
        WindowManager.LayoutParams windowParams = window.getAttributes();
        windowParams.dimAmount = 0.0f;
        window.setAttributes(windowParams);

    }

    /**
     * 点击关闭时取消
     */
    @OnClick(R.id.iv_close)
    protected void onCloseClick(View view) {
        dismiss();
    }

    /**
     * 点击回复楼主文本框
     */
    @OnClick(R.id.tv_reply)
    protected void onReplyClick(View view) {
        showReplyPost();
    }

    /**
     * 显示回复主题，在点击下方回复布局和点击你牛你先说时调用
     */
    public void showReplyPost() {
        if (CommunityUtils.PostUtils.canReply(mPostCard)) {
            mReplyInputLayout.showInput(mPostId);
            mTvReply.setVisibility(View.GONE);
        }
    }


    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<Comment> list) {
        ShortVideoPostCommentAdapter shortVideoPostViewHolder = new ShortVideoPostCommentAdapter(getActivity(), list);
        shortVideoPostViewHolder.setOnReplyClickListener(new ShortVideoCommentReplyAdapter.OnReplyClickListener() {
            @Override
            public void onReplyClick(Comment comment, int position) {
                showReplyLayout(comment, position);
            }
        });
        return shortVideoPostViewHolder;
    }

    @Override
    public Observable<BaseResponse<List<Comment>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return service2.getComments(mPostId, pageNo, pageSize);
    }

    @Override
    public RefreshController<Comment> createRefreshController() {
        return new RefreshController<Comment>(this) {
            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return new DividerItemDecoration(context, DividerItemDecoration.VERTICAL_LIST, R.drawable.item_divider_video_comment);
            }

            @Override
            public void onRefresh() {
                super.onRefresh();
                mTopCommentsLoader.load();
            }

            @Override
            protected void onLoadSuccess(int loadPage, List<Comment> list, int loadType) {
                if (loadPage == 1) {
                    mTopCommentsLoader.addTopComments(list);
                }
                super.onLoadSuccess(loadPage, list, loadType);
            }

            @Override
            public void initViews(View rootView) {
                super.initViews(rootView);
                mTopCommentsLoader = new TopCommentsLoader(getActivity(), mPostId, (BaseRecyclerViewAdapter) getRefreshController().getAdapter(), false);
            }

        }.setEmptyBackgroundColorId(R.color.color_232323)
                .setEmptyImageResId(R.drawable.ic_message_empty_work_hard)
                .setEmptyStringResId(R.string.post_detail_no_comment_you_can_reply_first);
    }

    @Override
    public RefreshController<Comment> getRefreshController() {
        return mRefreshController;
    }

    /**
     * 点击空白处关闭
     */
    @OnClick(R.id.view_click_dismiss)
    protected void onDismissClick(View view) {
        dismiss();
    }

}
