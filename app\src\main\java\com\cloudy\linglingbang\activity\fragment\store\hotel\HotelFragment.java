package com.cloudy.linglingbang.activity.fragment.store.hotel;

import android.content.Context;
import android.graphics.Color;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshFragment;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.fragment.store.hotel.order.MyOrderActivity;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.LocationHelper;
import com.cloudy.linglingbang.app.util.baiduMap.CoordinateTransform;
import com.cloudy.linglingbang.app.widget.dialog.hotel.ChooseHotelCityDialog;
import com.cloudy.linglingbang.app.widget.dialog.hotel.HotelSelectDateDialog;
import com.cloudy.linglingbang.app.widget.image.ImageLoadUtils;
import com.cloudy.linglingbang.app.widget.recycler.header.HeaderAndFooterWrapperAdapter;
import com.cloudy.linglingbang.model.hotel.HotelList;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.OnClick;
import io.reactivex.rxjava3.core.Observable;

import static android.text.Spanned.SPAN_INCLUSIVE_INCLUSIVE;

/**
 * 锦江之星
 *
 * <AUTHOR>
 * @date 2022/3/14
 */
public class HotelFragment extends BaseRecyclerViewRefreshFragment<HotelList> {

    private TextView mTvAddress;
    private TextView mTvDate;
    private ChooseHotelCityDialog.ChooseCityUtil mChooseCityUtil;
    //    private final static String BEIJING_CODE = "AR06513";
    /**
     * 城市code
     */
    private String cityCode = null;
    /**
     * 入住时间
     */
    private long startDate;
    /**
     * 离店时间
     */
    private long endDate;

    public static Fragment newInstance() {
        return new HotelFragment();
    }

    /**
     * 跳转搜索
     */
    void checkAndSearch(View view) {
        HolderSearchResultActivity.startActivity(view.getContext(), startDate, endDate, cityCode);
    }

    /**
     * 选择入住和离店时间
     */
    void onClickDate(View view) {
        HotelSelectDateDialog datePop = new HotelSelectDateDialog(getContext());
        datePop.setOnDateSelected((startDate, endDate) -> {
            setDate(startDate, endDate);
            refresh();
        });
        datePop.setStartDate(startDate);
        datePop.setEndDate(endDate);
        datePop.show();
    }

    /**
     * 选择城市
     */
    void onClickAddress(View view) {
        if (mChooseCityUtil == null) {
            mChooseCityUtil = new ChooseHotelCityDialog.ChooseCityUtil(getContext(), (chosenProvinceModel, chosenCityModel) -> {
                mTvAddress.setText(chosenCityModel.getCityName());
                cityCode = chosenCityModel.getCityCode();
                refresh();
                return false;
            });
        }
        mChooseCityUtil.showDialog();
    }

    @OnClick(R.id.btn_order_now)
    void onClickOrder(View view) {
        if (AppUtil.checkLogin(getActivity())) {
            IntentUtils.startActivity(getActivity(), MyOrderActivity.class);
        }
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_hotel;
    }

    /**
     * 设置入住和离店时间
     */
    private void setDate(long startDate, long endDate) {
        if (startDate == endDate) {
            return;
        }
        this.startDate = startDate;
        this.endDate = endDate;
        String data = getResources().getString(R.string.txt_hotel_out_in, AppUtil.formatDate(startDate, "MM-dd")
                , AppUtil.formatDate(endDate, "MM-dd"));
        SpannableString dateSp = new SpannableString(data);
        int index = data.indexOf(" ") + 1;
        dateSp.setSpan(new ForegroundColorSpan(Color.parseColor("#2C7BFC")), index, index + 5, SPAN_INCLUSIVE_INCLUSIVE);
        index = data.lastIndexOf(" ") + 1;
        dateSp.setSpan(new ForegroundColorSpan(Color.parseColor("#2C7BFC")), index, index + 5, SPAN_INCLUSIVE_INCLUSIVE);
        mTvDate.setText(dateSp);
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<HotelList> list) {
        HeaderAndFooterWrapperAdapter adapter = new HeaderAndFooterWrapperAdapter();
        View headerView = View.inflate(getActivity(), R.layout.layout_hotel_header, null);
        headerView.setLayoutParams(new ViewGroup.LayoutParams(-1, -2));
        initHeaderView(headerView);
        adapter.addHeaderView(headerView);
        HotelAdapter hotelAdapter = new HotelAdapter(getActivity(), list);
        adapter.setInnerAdapter(hotelAdapter);
        hotelAdapter.setOnItemClickListener((itemView, position) -> HotelDetailActivity.startActivity(itemView.getContext(), list.get(position).getInnId(), startDate, endDate));
        return adapter;
    }

    /**
     * 头部视图初始化
     */
    private void initHeaderView(View headerView) {
        ImageView imageView = headerView.findViewById(R.id.image);
        ImageLoadUtils.createImageLoad(imageView, "")
                .setPlaceholderAndError(R.drawable.ic_hotel_top_place)
                .load();
        mTvAddress = headerView.findViewById(R.id.tv_address);
        mTvDate = headerView.findViewById(R.id.tv_date);
        mTvDate.setOnClickListener(this::onClickDate);
        headerView.findViewById(R.id.ed_content)
                .setOnClickListener(this::checkAndSearch);
        headerView.findViewById(R.id.rl_address)
                .setOnClickListener(this::onClickAddress);

        mTvAddress.setText(LocationHelper.DEFAULT_CITY_LIUZHOU);
        setDate(AppUtil.getServerCurrentTime(), AppUtil.getServerCurrentTime() + AppUtil.ONEDAY);
        LocationHelper.LocationEntity locationEntity = LocationHelper.getInstance().getLastLocation();
        if (locationEntity != null && !TextUtils.isEmpty(locationEntity.cityName)) {
            mTvAddress.setText(locationEntity.cityName);
            refresh();
        } else {
            mTvAddress.postDelayed(mRunnable, 1000);
            LocationHelper.getInstance().requestLocation(this, new LocationHelper.LocCallBack() {
                @Override
                public void onSuccess(LocationHelper.LocationEntity entity) {
                    if (!TextUtils.isEmpty(entity.cityName)) {
                        mTvAddress.setText(entity.cityName);
                        mTvAddress.removeCallbacks(mRunnable);
                        mTvAddress.post(mRunnable);
                    }
                }

                @Override
                public void onError(String errMsg) {
//                mTvAddress.setText(LocationHelper.DEFAULT_CITY);
                }
            });
        }
    }

    @Override
    public Observable<BaseResponse<List<HotelList>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        Map<String, String> map = new HashMap<>();
        map.put("pageNum", String.valueOf(pageNo));
        map.put("pageSize", String.valueOf(pageSize));
        if (!TextUtils.isEmpty(cityCode)) {
            map.put("cityCode", cityCode);
        }
        map.put("mapType", "0");//地图类型 0 百度 1 google 2 腾讯 3 高德
        LocationHelper.LocationEntity locationEntity = LocationHelper.getInstance().getLastLocation();
        if (!TextUtils.isEmpty(locationEntity.cityName)) {
            double[] ls = CoordinateTransform.transformGCJ02ToBD09(locationEntity.longitude, locationEntity.latitude);
            map.put("lng", String.valueOf(ls[0]));
            map.put("lag", String.valueOf(ls[1]));
        }

        map.put("dtArrorig", AppUtil.formatDate(startDate, "yyyy-MM-dd"));
        map.put("dtDeporig", AppUtil.formatDate(endDate, "yyyy-MM-dd"));
        return service2.hotelPage(map);
    }

    @Override
    public RefreshController<HotelList> createRefreshController() {
        return new RefreshController<HotelList>(this) {
            @Override
            public void initViews(View rootView) {
                super.initViews(rootView);
                swipeToLoadLayout.setBackgroundColor(Color.TRANSPARENT);
            }

            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return null;
            }

            @Override
            protected void onRefreshComplete() {
                if (mData.isEmpty()) {
                    mData.add(HotelList.EMPTY);
                }
                super.onRefreshComplete();
                if (TextUtils.isEmpty(cityCode)) {
                    LocationHelper.LocationEntity locationEntity = LocationHelper.getInstance().getLastLocation();
                    if (locationEntity != null && !TextUtils.isEmpty(locationEntity.cityName)) {
                        mTvAddress.setText(locationEntity.cityName);
                    }
                }
            }

            @Override
            protected boolean loadDataAfterInitViews() {
                return false;
            }
        };
    }

    Runnable mRunnable = this::refresh;

    @Override
    public void refresh() {
        super.refresh();
        if (getRefreshController() != null && getRefreshController().getSwipeToLoadLayout() != null) {
            getRefreshController().getSwipeToLoadLayout().manualRefresh();
        }
    }

    @Override
    public void onPermissionResult(boolean isGranted, int requestCode) {
        super.onPermissionResult(isGranted, requestCode);
        LocationHelper.getInstance().onPermissionResult(isGranted, requestCode);
    }
}
