package com.cloudy.linglingbang.activity.car.energy.ble;

/**
 * <AUTHOR>
 * @describe
 * @date 2021/12/24
 */
public enum  BleControlCode {
    OPEN_DOOR("开门","0101F2000000","39D6","0001"),
    OPEN_TAILGATE("开尾门","01010A000000","39D6","0001"),
    CLOSE_DOOR("关门","0102F2000000","39D6","0001"),
    POWER_OFF("下电","030900000000","40E5","0003"),
    MODE_PARK_IN("遥控模式泊入","030100000000","40E5","0001"),
    MODE_PARK_OUT("遥控模式泊出","030300000000","40E5","0001"),
    MODE_PARK_MANUAL("手动泊出泊入","030200000000","40E5","0001"),
    START_PARK("开始泊车","030400000000","40E5","0002"),
    PAUSE_PARK("暂停泊车","030500000000","40E5","0002"),
    STOP_PARK("终止泊车","030600000000","40E5","0002"),
    PARK_MODE_LEFT_V("泊出方式_左_垂直","030A01010000","40E5","0004"),
    PARK_MODE_LEFT_H("泊出方式_左_水平","030A01020000","40E5","0004"),
    PARK_MODE_RIGHT_V("泊出方式_右_垂直","030A02010000","40E5","0004"),
    PARK_MODE_RIGHT_H("泊出方式_右_水平","030A02020000","40E5","0004"),
    PARK_MODE_FORWARD_V("泊出方式_前_垂直","030A03010000","40E5","0004"),
    PARK_MODE_FORWARD_H("泊出方式_后_水平","030A03020000","40E5","0004"),
    PARK_MODE_FORWARD("前进","030700000000","40E5","0002"),
    PARK_MODE_BACKWARD("后退","030800000000","40E5","0002"),
    PARK_MODE_LEFTWARD("向左","030B00000000","40E5","0002"),
    PARK_MODE_RIGHTWARD("向右","030C00000000","40E5","0002"),
    PARK_MODE_UP_LEFT("左前","030D00000000","40E5","0002"),
    PARK_MODE_BACK_LEFT("左后","030E00000000","40E5","0002"),
    PARK_MODE_UP_RIGHT("右前","030F00000000","40E5","0002"),
    PARK_MODE_BACK_RIGHT("右后","031000000000","40E5","0002"),
    PARK_MODE_RETURN("回正","031100000000","40E5","0002"),

    MODE_PARK_OUT_2("一键泊出","031200000000","40E5","0001"),
    START_OUT_2("开始泊出","031300000000","40E5","0001"),
    PAUSE_OUT_2("暂停泊出","031400000000","40E5","0001"),
    STOP_OUT_2("终止泊出","031500000000","40E5","0001"),
    START_PARK_2("开始泊车","031600000000","40E5","0001"),
    PAUSE_PARK_2("暂停泊车","031700000000","40E5","0001"),
    STOP_PARK_2("终止泊车","031800000000","40E5","0001"),
    SET_FRONT_2("设置向前泊出","031901000000","40E5","0001"),
    SET_AFTER_2("设置向后泊出","031902000000","40E5","0001"),
    SET_LEFT_2("设置向左泊出","031903000000","40E5","0001"),
    SET_RIGHT_2("设置向右泊出","031904000000","40E5","0001"),
    STRAIGHT_FORWARD_2("直线前进","030700000000","40E5","0001"),
    STRAIGHT_BACK_2("直线后退","030800000000","40E5","0001"),
    ;
            ;

    //service data 长度
    public static final String PAYLOAD_LENGTH = "06";

    private String name;
    private String code;
    private String serviceID;
    private String subFunction;

    BleControlCode(String name, String code, String serviceID, String subFunction) {
        this.name = name;
        this.code = code;
        this.serviceID = serviceID;
        this.subFunction = subFunction;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public String getServiceID() {
        return serviceID;
    }

    public String getSubFunction() {
        return subFunction;
    }

    @Override
    public String toString() {
        return "E300BlueControl{" +
                "name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", serviceID='" + serviceID + '\'' +
                ", subFunction='" + subFunction + '\'' +
                '}';
    }
}
