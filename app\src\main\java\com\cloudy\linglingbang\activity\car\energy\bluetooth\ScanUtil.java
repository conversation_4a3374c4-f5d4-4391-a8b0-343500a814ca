package com.cloudy.linglingbang.activity.car.energy.bluetooth;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.le.BluetoothLeScanner;
import android.bluetooth.le.ScanCallback;
import android.bluetooth.le.ScanFilter;
import android.bluetooth.le.ScanResult;
import android.bluetooth.le.ScanSettings;
import android.content.Context;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.ParcelUuid;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.RequiresApi;

import com.cloudy.linglingbang.activity.car.energy.ConnectHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by LiYeWen on 2025/04/30
 * 蓝牙扫描工具类
 */
public class ScanUtil {

    private static ScanUtil instance;
    private String mMac;
    //蓝牙扫描
    private BluetoothLeScanner mBluetoothLeScanner;
    //是否是正在扫描蓝牙
    private boolean isScanning = false;
    //蓝牙扫描回调监听
    private BleScanListener mScanListener;
    //扫描重试最大次数
    private final static int NUMBER_RETRY = 0;
//        private final static int NUMBER_RETRY = 5;
    //当前扫描失败重试次数
    private int mNumberRetry = 0;
    private Context mContext;
    //蓝牙扫描失败
    private final static int SCAN_FAILURE = 101;
    //扫描超时
    private final static int SCAN_TIME_OUT = 102;
    //蓝牙扫描成功
    private final static int SCAN_SUCCESS = 103;
    //重新扫描
    private final static int SCAN_RETRY = 104;

    private ScanUtil(){}

    /**
     * 获取单例对象
     * @return
     */
    public static ScanUtil getInstance() {
        if (instance == null) {
            synchronized (ScanUtil.class) {
                instance = new ScanUtil();
            }
        }
        return instance;
    }

    /**
     * 开始扫描蓝牙
     * @param context
     * @param mac       当前车辆的mac
     * @param listener  蓝牙扫描回调监听
     */
    public void startScan(Context context, String mac, BleScanListener listener){
        mNumberRetry = 0;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            scan(context, mac, listener);
        }else {
            Log.e(ConnectHelper.TAG, "蓝牙扫描阶段 startScan() 手机系统版本过低, mac=" + mac + ", SDK_INT = " + Build.VERSION.SDK_INT);
        }
    }

    /**
     * 开始扫码蓝牙（内部方法，扫描重试直接调用此方法）
     * @param context
     * @param mac       当前车辆的mac
     * @param listener  蓝牙扫描回调监听
     */
    @RequiresApi(api = Build.VERSION_CODES.M)
    private void scan(Context context, String mac, BleScanListener listener){

        if(context == null){
            Log.e(ConnectHelper.TAG, "蓝牙扫描阶段 startScan(), context == null, mac=" + mac);
            mHandler.sendMessage(mHandler.obtainMessage(SCAN_FAILURE));
            return;
        }

        //正在扫描蓝牙
        if(isScanning){
            if(mac != null && !mac.equals(mMac)){
                Log.e(ConnectHelper.TAG, "蓝牙扫描阶段 startScan(), mac发生了变化,即将重新扫描蓝牙, isScanning = true, mac=" + mac + ",mMac="+mMac);
                //停止上一次的蓝牙扫描
                stopScan();
            }else {
                Log.e(ConnectHelper.TAG, "蓝牙扫描阶段 startScan(), 正在扫描蓝牙, 不再重复扫描, isScanning = true, mac=" + mac);
                //由于这次扫描的mac和上一次是一样的，所以只需等待上一次的扫描结果即可
                return;
            }
        }

        mContext = context;
        mMac = mac;
        mScanListener = listener;
        Object bluetoothManager = context.getSystemService(Context.BLUETOOTH_SERVICE);
        if(bluetoothManager == null){
            Log.e(ConnectHelper.TAG, "蓝牙扫描阶段 startScan(), bluetoothManager == null, mac=" + mac);
            mHandler.sendMessage(mHandler.obtainMessage(SCAN_FAILURE));
            return;
        }

        if(ConnectUtil.getInstance().getBluetoothAdapter() == null){
            Log.e(ConnectHelper.TAG, "蓝牙扫描阶段 startScan(), bluetoothAdapter == null, mac=" + mac);
            mHandler.sendMessage(mHandler.obtainMessage(SCAN_FAILURE));
            return;
        }

        mBluetoothLeScanner = ConnectUtil.getInstance().getBluetoothAdapter().getBluetoothLeScanner();
        if(mBluetoothLeScanner == null){
            Log.e(ConnectHelper.TAG, "蓝牙扫描阶段 startScan(), mBluetoothLeScanner == null, mac=" + mac);
            mHandler.sendMessage(mHandler.obtainMessage(SCAN_FAILURE));
            return;
        }

        //配置扫描参数
        ScanSettings settings = new ScanSettings.Builder()
                // 扫描模式，低延迟模式，以更高功耗换取快速设备发现
                .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY)
                //回调类型，所有匹配，每次扫描到匹配设备均触发回调
                .setCallbackType(ScanSettings.CALLBACK_TYPE_ALL_MATCHES)
                //匹配模式，激进模式，主动发送探测请求以确认设备存在性
                .setMatchMode(ScanSettings.MATCH_MODE_AGGRESSIVE)
                //匹配数量，设置蓝牙LE扫描过滤器硬件匹配的匹配数量
                .setNumOfMatches(ScanSettings.MATCH_NUM_ONE_ADVERTISEMENT)
                //报告延迟，设置扫描结果批量上报的时间间隔
                .setReportDelay(0)
                .build();

        List<ScanFilter> filters = null;
        if(!TextUtils.isEmpty(mac)){
            //创建扫描过滤器（可选，这里过滤指定mac）
            filters = new ArrayList<>();
            filters.add(new ScanFilter.Builder()
                    //通过mac过滤
//                    .setDeviceAddress(mac)
                    //通过UUID过滤
                    .setServiceUuid(new ParcelUuid(ConnectUtil.UUID_SERVICE_AUTH))
                    .build());
        }
        cancelTimeout();

        if (!ConnectUtil.getInstance().bluetoothIsEnabled()) {
            Log.e(ConnectHelper.TAG, "蓝牙扫描阶段 startScan() 手机系统蓝牙已关闭 isNoEnabled, mac=" + mac);
            mHandler.sendMessage(mHandler.obtainMessage(SCAN_FAILURE));
            return;
        }

        //蓝牙扫描超时定时器，15秒扫描超时
        mHandler.sendEmptyMessageDelayed(SCAN_TIME_OUT, 15000);
        isScanning = true;
        Log.e(ConnectHelper.TAG, "蓝牙扫描阶段 startScan(), 开始扫描蓝牙, mac=" + mac + ", mNumberRetry = " + mNumberRetry + ", SDK_INT = " + Build.VERSION.SDK_INT);
        //开始扫描蓝牙
        mBluetoothLeScanner.startScan(filters, settings, mScanCallback);
    }

    /**
     * 蓝牙扫描回调
     */
    @SuppressLint("NewApi")
    private ScanCallback mScanCallback = new ScanCallback() {

        @Override
        public void onScanResult(int callbackType, ScanResult result) {
            super.onScanResult(callbackType, result);

            if(TextUtils.isEmpty(mMac)){
                Log.e(ConnectHelper.TAG, "蓝牙扫描阶段，扫描回调 onScanResult(), mMac == null");
                return;
            }

            if(result == null){
                Log.e(ConnectHelper.TAG, "蓝牙扫描阶段，扫描回调 onScanResult(), result == null");
                return;
            }

            BluetoothDevice device = result.getDevice();
            if(device == null){
                Log.e(ConnectHelper.TAG, "蓝牙扫描阶段，扫描回调 onScanResult(), device == null");
                return;
            }

            if (device.getAddress() == null) {
                Log.e(ConnectHelper.TAG, "蓝牙扫描阶段，扫描回调 onScanResult(), device.getAddress() == null");
                return;
            }
//            Log.e(ConnectHelper.TAG, "蓝牙扫描阶段，扫描回调 onScanResult() , mac=" + device.getAddress());

            //比较MAC地址（不区分大小写）
            if (device.getAddress().equalsIgnoreCase(mMac)) {
                Log.e(ConnectHelper.TAG, "蓝牙扫描阶段，扫描回调 onScanResult(), 扫描到当前蓝牙mac=" + device.getAddress() + ", isScanning = " + isScanning);
                if(isScanning){
                    mHandler.sendMessage(mHandler.obtainMessage(SCAN_SUCCESS));
                }
//                //停止蓝牙扫描
//                stopScan();
            }
        }

        @Override
        public void onScanFailed(int errorCode) {
            super.onScanFailed(errorCode);
            Log.e(ConnectHelper.TAG, "蓝牙扫描阶段，扫描回调 onScanFailed(), 蓝牙扫描失败 errorCode = " + errorCode);
            mHandler.sendEmptyMessageDelayed(SCAN_FAILURE, 100);
//            mHandler.sendEmptyMessageDelayed(SCAN_FAILURE, 1000);
        }
    };

    /**
     * 停止蓝牙扫描
     */
    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public void stopScan() {
        //取消蓝牙扫描定时器
        cancelTimeout();
        if(mBluetoothLeScanner == null){
            Log.e(ConnectHelper.TAG, "蓝牙扫描阶段 stopScan(), mBluetoothLeScanner == null");
            return;
        }
        if(ConnectUtil.getInstance().bluetoothIsEnabled()){
            mBluetoothLeScanner.stopScan(mScanCallback);
        }
    }

    /**
     * 取消蓝牙扫描定时器
     */
    private void cancelTimeout(){
        isScanning = false;
        if (mHandler != null) {
            //清理所有待处理任务
            mHandler.removeCallbacksAndMessages(null);
        }
    }
    /**
     * 蓝牙扫描失败，扫描重试方法
     */
    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    private void scanFailure(){
        stopScan();
        if(mNumberRetry < NUMBER_RETRY){
            Log.e(ConnectHelper.TAG, "蓝牙扫描阶段 scanFailure(), mNumberRetry = " + mNumberRetry+ ", 1秒后扫描重试");
            mNumberRetry++;
            //1秒后重新扫描
            mHandler.sendEmptyMessageDelayed(SCAN_RETRY, 1000);
        }else {
            if(mScanListener != null){
                //蓝牙扫描失败回调分发
                mScanListener.onFailure(mMac);
            }else {
                Log.e(ConnectHelper.TAG, "蓝牙扫描阶段 scanFailure(), mScanListener == null, mNumberRetry = " + mNumberRetry);
            }
        }
    }

    private Handler mHandler = new Handler(Looper.getMainLooper()) {

        @RequiresApi(api = Build.VERSION_CODES.M)
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                //蓝牙扫描失败
                case SCAN_FAILURE:
                    scanFailure();
                    break;

                //扫描超时
                case SCAN_TIME_OUT:
                    Log.e(ConnectHelper.TAG, "蓝牙扫描阶段 Runnable(), Timeout 蓝牙扫描超时, mNumberRetry = " + mNumberRetry);
                    scanFailure();
                    break;

                //蓝牙扫描成功
                case SCAN_SUCCESS:
                    if(mScanListener != null){
                        Log.e(ConnectHelper.TAG, "蓝牙扫描阶段 SCAN_SUCCESS(), mMac = " + mMac);
                        //蓝牙扫描成功回调分发
                        mScanListener.onSuccess(mMac);
                    }else {
                        Log.e(ConnectHelper.TAG, "蓝牙扫描阶段 SCAN_SUCCESS(), mScanListener == null");
                    }
                    //停止蓝牙扫描
                    stopScan();
                    break;

                //重新扫描
                case SCAN_RETRY:
                    scan(mContext, mMac, mScanListener);
                    break;

            }
        }
    };

}
