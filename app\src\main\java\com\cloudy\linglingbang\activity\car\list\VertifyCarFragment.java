package com.cloudy.linglingbang.activity.car.list;

import android.view.View;

import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.model.user.User;
import com.cloudy.linglingbang.web.BaseX5WebViewFragment;

/**
 * 认证车辆的web页面
 *
 * <AUTHOR>
 * @date 2020-02-27
 */
public class VertifyCarFragment extends BaseX5WebViewFragment {
    public static VertifyCarFragment newInstance() {
        return new VertifyCarFragment();
    }

    @Override
    protected void initialize(View view) {
        super.initialize(view);
    }

    @Override
    protected String setUrl() {
        int vehicleApproveStyle = User.getsUserInstance().getVehicleApproveStyle();
        int checkStatus = User.getsUserInstance().getCheckStatus();
        StringBuilder sb = JumpPageUtil.getAuthUrlStringBuilder(checkStatus, vehicleApproveStyle, false);
        sb.append("&fromApp=1");
        return sb.toString();
    }

    @Override
    protected boolean isShowBar() {
        return false;
    }

}
