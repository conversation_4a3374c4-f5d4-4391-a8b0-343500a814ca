package com.cloudy.linglingbang.activity.community.post.shortVideo;

import android.content.Context;
import android.content.DialogInterface;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.community.CommunityDoPostImageTextActivity;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.StatusBarUtil;
import com.cloudy.linglingbang.app.widget.banner.AdRoundImageView;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.constants.WebUrlConfigConstant;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.user.User;

import java.util.List;

import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import butterknife.ButterKnife;
import io.reactivex.rxjava3.core.Observable;

import static com.cloudy.linglingbang.activity.fragment.homePage.ColumnListActivity.INTENT_EXTRA_COLUMN_NAME;

/**
 * 视频帖帖子列表的activity
 *
 * <AUTHOR>
 * @date 2018/11/21
 */
public class ShortVideoPostListActivity extends BaseRecyclerViewRefreshActivity<PostCard> {
    private long mColumnId;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_short_video_refresh);
    }

    @Override
    public boolean onPrepareOptionsMenu(Menu menu) {
        menu.clear();
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(R.menu.menu_publish_video, menu);
        MenuItem item = menu.findItem(R.id.iv_publish);
        mToolbar.setOnMenuItemClickListener(onMenuItemClick);
        return true;
    }

    /**
     * 增加Toolbar监听
     */
    protected Toolbar.OnMenuItemClickListener onMenuItemClick = new Toolbar.OnMenuItemClickListener() {
        @Override
        public boolean onMenuItemClick(MenuItem menuItem) {
            switch (menuItem.getItemId()) {
                case R.id.iv_publish:
                    if (AppUtil.checkLogin(ShortVideoPostListActivity.this)) {
                        if (User.getsUserInstance().getAudit() == 0) {
                            new CommonAlertDialog(ShortVideoPostListActivity.this, R.string.dialog_message_author, R.string.btn_ok_author, new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialog, int which) {
                                    JumpPageUtil.goCommonWeb(ShortVideoPostListActivity.this, WebUrlConfigConstant.APPLY_AUTHOR);
                                    dialog.dismiss();
                                }
                            }).show();
                        } else {
                            JumpPageUtil.goShortVideoPostPage(getContext(), PostCard.PostType.SHORT_VIDEO, null, 0, CommunityDoPostImageTextActivity.OPEN_FROM_SQUARE);
                        }
                    }
                    break;
            }
            return true;
        }
    };

    @Override
    protected void initialize() {
        super.initialize();
        //id
        try {
            mColumnId = Long.parseLong(getIntentStringExtra());
        } catch (Exception e) {
            e.printStackTrace();
        }
        //标题
        String columnName = (String) IntentUtils.getExtra(getIntent().getExtras(), INTENT_EXTRA_COLUMN_NAME, "");
        setLeftTitle(columnName);
        mToolbar.setBackgroundColor(getResources().getColor(R.color.black));
        setMiddleTitle(getResources().getString(R.string.video_post_list));
        mToolbar.setNavigationIcon(R.drawable.ic_back_white);
        StatusBarUtil.StatusBarDarkMode(this, StatusBarUtil.StatusBarLightMode(this));
        StatusBarUtil.setStatusBarColor(this, R.color.black);
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(final List list) {
        RecyclerView.Adapter<? extends RecyclerView.ViewHolder> adapter = new VideoPostAdapter(this, list);
        ((VideoPostAdapter) adapter).setOnItemClickListener(new BaseRecyclerViewHolder.OnItemClickListener() {
            @Override
            public void onItemClick(View itemView, int position) {
                ShortVideoSlideListActivity.startActivity(ShortVideoPostListActivity.this, list, position, mColumnId);
            }
        });
        return adapter;
    }

    @Override
    public RefreshController createRefreshController() {
        RefreshController refreshController = new RefreshController(this) {
            @Override
            protected RecyclerView.LayoutManager createLayoutManager(Context context) {
                return new GridLayoutManager(ShortVideoPostListActivity.this, 2);
            }

            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return null;
            }

            @Override
            public void initViews(View rootView) {
                super.initViews(rootView);
                getSwipeToLoadLayout().setBackgroundColor(getResources().getColor(R.color.black));
            }

        }.setEmptyBackgroundColorId(R.color.black)
                .setEmptyImageResId(R.drawable.ic_message_empty_work_hard);
        refreshController.setPageSize(20);
        return refreshController;
    }

    @Override
    public Observable<BaseResponse<List<PostCard>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return service2.getPostColumns(mColumnId, pageNo, pageSize);
    }

    class VideoPostAdapter extends BaseRecyclerViewAdapter<PostCard> {

        public VideoPostAdapter(Context context, List<PostCard> data) {
            super(context, data);
        }

        @Override
        protected BaseRecyclerViewHolder<PostCard> createViewHolder(View itemView) {
            return new VideoPostViewHolder(itemView);
        }

        @Override
        protected int getItemLayoutRes(int viewType) {
            return R.layout.item_short_video_post;
        }
    }

    class VideoPostViewHolder extends BaseRecyclerViewHolder<PostCard> {

        /**
         * 封面图
         */
        @BindView(R.id.iv_video_cover)
        AdRoundImageView mIvVideoCover;

        /**
         * 头像
         */
        @BindView(R.id.iv_header)
        ImageView mIvHeader;

        /**
         * 昵称
         */
        @BindView(R.id.tv_nickname)
        TextView mTvNickname;

        /**
         * 阅读数
         */
        @BindView(R.id.tv_read_count)
        TextView mTvReadCount;
        /**
         * 点赞数
         */
        @BindView(R.id.tv_praise_count)
        TextView mTvPraiseCount;

        /**
         * 标题
         */
        @BindView(R.id.tv_title)
        TextView mTvTitle;

        public VideoPostViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initItemView(View itemView) {
            super.initItemView(itemView);
            ButterKnife.bind(this, itemView);
        }

        @Override
        public void bindTo(PostCard postCard, int position) {
            super.bindTo(postCard, position);
            //加载封面
            mIvVideoCover.createImageLoad(postCard.getCoverImage())
                    .setDoNotAnimate()
                    .load();
            mTvReadCount.setText(AppUtil.getCommentDesc(postCard.getPostShowCount()));
            mTvPraiseCount.setText(AppUtil.getCommentDesc(postCard.getPostPraiseCount()));
            mTvTitle.setText(postCard.getPostTitle());
            //作者相关
            if (postCard.getAuthor() != null) {
                //加载头像
                String url = AppUtil.getImageUrlBySize(postCard.getAuthor().getPhoto(), AppUtil._120X120);
                new ImageLoad(getContext(), mIvHeader, url, ImageLoad.LoadMode.URL)
                        .setPlaceholder(R.drawable.user_head_default_120x120)
                        .setErrorImageId(R.drawable.user_head_default_120x120)
                        .setCircle(true)
                        .load();
                mTvNickname.setText(postCard.getAuthor().getNickname());
            }
        }
    }
}
