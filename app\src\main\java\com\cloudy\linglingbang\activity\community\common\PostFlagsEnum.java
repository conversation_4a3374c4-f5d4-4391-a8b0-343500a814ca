package com.cloudy.linglingbang.activity.community.common;

import androidx.annotation.CheckResult;

/**
 * 帖子的额外设置
 * 帖子展示的位置有
 * <p>
 * 首页-推荐
 * 首页-问答
 * 首页-问答-热点问题
 * 社区-广场
 * 社区-本地
 * 社区-本地-车友会
 * 社区-车型
 * 我的-关注
 * 我的-收藏
 * 我的-个人主页
 * 社区-他人主页
 *
 * <AUTHOR>
 * @date 2018/8/24
 */
public enum PostFlagsEnum {
    /**
     * 用户等级，目前默认展示，如有需要可以更改
     */
    SHOW_USER_LEVEL,
    /**
     * 显示楼主，目前帖子详情的评论列表展示
     */
    SHOW_FLOOR_HOST,
    /**
     * 展示关注
     * 用于广场、本地、车型
     */
    SHOW_ATTENTION,
    /**
     * 忽略头像点击，目前用于用户主页，防止循环跳转
     */
    IGNORE_AVATAR_CLICK,
    /**
     * 隐藏用户信息
     */
    GONE_AUTHOR_INFO,

    /**
     * 社区中 帖子 是否显示金币 广场帖子显示
     */
    SHOW_GOLD_COIN,
    //结束
    ;

    /**
     * 默认值
     */
    @CheckResult
    public static int addDefaultFlags(int flags) {
        flags = PostFlagsEnum.SHOW_USER_LEVEL.addFlags(flags);
        return flags;
    }

    /**
     * 显示图标，即 addFlags
     */
    @CheckResult
    public int addFlags(int flags) {
        return addFlags(flags, this);
    }

    /**
     * 根据 flags 判断是否需要显示某图标
     */
    public boolean checkFlags(int flags) {
        return checkFlags(flags, this);
    }

    /**
     * 添加 flag
     */
    @CheckResult
    public static int addFlags(int flags, Enum anEnum) {
        return flags | (1 << anEnum.ordinal());
    }

    //判断 flag
    public static boolean checkFlags(int flags, Enum anEnum) {
        return (flags & (1 << anEnum.ordinal())) != 0;
    }
}
