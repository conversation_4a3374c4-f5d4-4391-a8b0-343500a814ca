package com.cloudy.linglingbang.activity.fragment.store.home;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.fragment.homePage.today.TodayFragment;
import com.cloudy.linglingbang.activity.fragment.store.home.adapter.DiscussionCommunityAdapter;
import com.cloudy.linglingbang.app.receiver.UserInfoChangeReceiver;
import com.cloudy.linglingbang.app.util.UserStatusChangeManager;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.constants.FinalSensors;

import org.jetbrains.annotations.NotNull;

/**
 * 讨论社区
 *
 * <AUTHOR>
 * @date 2021-10-21
 */
public class DiscussionCommunityFragment extends TodayFragment {

    @Override
    protected void setViewPadding() {
    }

    @NotNull
    @Override
    protected UserStatusChangeManager getUserStatusChangeManager() {
        return new UserStatusChangeManager(getContext(), new UserInfoChangeReceiver() {
            @Override
            protected void onRequestUser() {
                super.onRequestUser();
                onUserChange();
            }

            @Override
            protected void onUpdateUserStatus(int type, String userIdStr, int status) {
                super.onUpdateUserStatus(type, userIdStr, status);
                if (type == UserInfoChangeReceiver.USER_INFO_ATTENTION) {
                    if (mTodayRefreshController != null) {
                        mTodayRefreshController.onUpdateUseAttention(userIdStr, status);
                    }
                }
            }

            @Override
            protected void onClearUser() {
                super.onClearUser();
                onUserChange();
            }

            @Override
            protected void onUpdateUser() {
                super.onUpdateUser();
                onUserChange();
            }
        });
    }

    @Override
    public RefreshController<Object> createRefreshController() {
        mTodayRefreshController = new DiscussionCommunityAdapter(this);
        mTodayRefreshController.setEmptyImageResId(R.drawable.ic_hot_empty);
        mTodayRefreshController.setEmptyStringResId(R.string.empty_today);
        return mTodayRefreshController;
    }

    @Override
    public void onStart() {
        super.onStart();
        SensorsUtils.sensorsViewStart(FinalSensors.BROWSE_LIFE_INFORMATION);
    }

    @Override
    public void onStop() {
        super.onStop();
        SensorsUtils.sensorsViewEndNew("改装-讨论社区", FinalSensors.BROWSE_LIFE_INFORMATION, "浏览改装-讨论社区");
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (hidden) {
            SensorsUtils.sensorsViewEndNew("改装-讨论社区", FinalSensors.BROWSE_LIFE_INFORMATION, "浏览改装-讨论社区");
        } else {
            SensorsUtils.sensorsViewStart(FinalSensors.BROWSE_LIFE_INFORMATION);
        }
    }

}
