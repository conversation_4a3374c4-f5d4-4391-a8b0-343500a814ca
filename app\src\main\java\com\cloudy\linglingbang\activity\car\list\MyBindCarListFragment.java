package com.cloudy.linglingbang.activity.car.list;

import android.content.Context;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshFragment;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.app.receiver.UserInfoChangedHelper;
import com.cloudy.linglingbang.model.car.list.BindCarInfo;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;

import java.util.List;

import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 我的爱车
 *
 * <AUTHOR>
 * @date 2020-02-27
 */
public class MyBindCarListFragment extends BaseRecyclerViewRefreshFragment<BindCarInfo> implements BindCarListAdapter.SwitchCarListener {
    public boolean isNeedRefresh = false;

    @Override
    protected int getLayoutRes() {
        return R.layout.activity_bind_car_list;
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<BindCarInfo> list) {
        BindCarListAdapter adapter = new BindCarListAdapter(getActivity(), list);
        adapter.setSwitchCarListener(this);
        return adapter;
    }

    @Override
    public Observable<BaseResponse<List<BindCarInfo>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return service2.favoriteCarList(pageNo, pageSize);
    }

    @Override
    public RefreshController<BindCarInfo> createRefreshController() {
        final RefreshController<BindCarInfo> refreshController = new RefreshController<BindCarInfo>(this) {
            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return null;
            }

        };

        return refreshController.setEmptyStringResId(R.string.empty_car_list)
                .setEmptyImageResId(R.drawable.ic_empty_bind_car_list);
    }

    @Override
    public void switchSuccess() {
        onBindCarChanged();
    }

    public void onBindCarChanged() {
        getRefreshController().onRefresh();
        //切换爱车成功后发送广播，刷新车控页面
        UserInfoChangedHelper.sendControlCarChangedBroadcast();
    }

    /**
     * 编辑车辆回来后需要刷新数据
     * 切换爱车后也需要刷新数据
     */
    @Override
    public void onResume() {
        super.onResume();
        if (isNeedRefresh) {
            getRefreshController().onRefresh();
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        isNeedRefresh = true;
    }
}
