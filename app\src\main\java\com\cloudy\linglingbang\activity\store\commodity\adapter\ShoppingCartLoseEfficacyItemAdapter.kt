package com.cloudy.linglingbang.activity.store.commodity.adapter

import android.app.Activity
import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import butterknife.BindView
import butterknife.ButterKnife
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.activity.store.commodity.CommodityDetailActivity
import com.cloudy.linglingbang.activity.store.commodity.adapter.holder.OnChangeCommodityListener
import com.cloudy.linglingbang.app.util.DeviceUtil
import com.cloudy.linglingbang.app.widget.SlidingButtonView
import com.cloudy.linglingbang.app.widget.banner.AdRoundImageView
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder
import com.cloudy.linglingbang.model.SourceModel
import com.cloudy.linglingbang.model.store.commodity.CartCommodity
import com.cloudy.linglingbang.model.store.commodity.CartInfo

/**
 * 失效商品的adapter
 *
 * <AUTHOR>
 * @date 2022/10/18
 */
class ShoppingCartLoseEfficacyItemAdapter(context: Context?, data: List<CartCommodity>?) :
    BaseRecyclerViewAdapter<CartCommodity>(context, data) {

    var mOnChangeCommodityListener: OnChangeCommodityListener? = null

    override fun createViewHolder(itemView: View?): BaseRecyclerViewHolder<CartCommodity> {
        return ShoppingCartLoseEfficacyItemViewHolder(itemView)
    }

    override fun getItemLayoutRes(viewType: Int): Int {
        return R.layout.item_commodity_shopping_cart_lose_efficacy
    }


    inner class ShoppingCartLoseEfficacyItemViewHolder(itemView: View?) :
        BaseRecyclerViewHolder<CartCommodity>(itemView) {

        @JvmField
        @BindView(R.id.cb_check_sku)
        var cbCheckSku: CheckBox? = null

        @JvmField
        @BindView(R.id.iv_sku_pic)
        var ivSkuPic: AdRoundImageView? = null

        @JvmField
        @BindView(R.id.tv_name)
        var tvName: TextView? = null

        @JvmField
        @BindView(R.id.tv_efficacy_type)
        var tvEfficacyType: TextView? = null


        /** 内容 */
        @JvmField
        @BindView(R.id.layout_content)
        var layoutContent: LinearLayout? = null

        @JvmField
        @BindView(R.id.slv_parent)
        var slvParent: SlidingButtonView? = null

        /** 删除 */
        @JvmField
        @BindView(R.id.tv_delete)
        var tvDelete: TextView? = null


        override fun initItemView(itemView: View) {
            super.initItemView(itemView)
            ButterKnife.bind(this, itemView)
        }

        override fun bindTo(bean: CartCommodity?, position: Int) {
            super.bindTo(bean, position)
            //设置内容布局的宽为屏幕宽度
            layoutContent?.layoutParams?.width =
                DeviceUtil.getScreenWidth(layoutContent?.context as Activity?)
            //动态设置删除按钮的高度
            val w = View.MeasureSpec.makeMeasureSpec(
                0,
                View.MeasureSpec.UNSPECIFIED
            )
            val h = View.MeasureSpec.makeMeasureSpec(
                0,
                View.MeasureSpec.UNSPECIFIED
            )
            layoutContent?.measure(w, h)
            val height: Int = layoutContent!!.measuredHeight
            val params: ViewGroup.LayoutParams = tvDelete!!.layoutParams
            params.height = height
            layoutContent?.setOnClickListener {
                bean?.commodityId?.let { it1 -> closeMenuAndToDetail(it1) }
            }
            tvDelete?.layoutParams = params
            bean?.apply {
                ivSkuPic?.createImageLoad(skuImage)?.load()
                tvName?.text = commodityName
                tvEfficacyType?.text = expiredReasonStr
                tvDelete?.setOnClickListener {
                    mOnChangeCommodityListener?.onDeleteSingle(bean.cartId)
                }
                if (!isEditModel) {
                    cbCheckSku?.isEnabled = false
                } else {
                    cbCheckSku?.isEnabled = true
                    cbCheckSku?.isChecked = isCheckForEdit == 1
                }
                cbCheckSku?.setOnCheckedChangeListener { _, isChecked ->
                    if (isEditModel) {
                        isCheckForEdit = if (isChecked) 1 else 0
                    }
                    mOnChangeCommodityListener?.onChangeSingleSelect(
                        cartId,
                        if (isChecked) 1 else 0
                    )
                }

            }
            slvParent?.setSlidingButtonListener(object : SlidingButtonView.OnSlidingButtonListener {
                override fun onMenuIsOpen(view: View?) {
                    mOnChangeCommodityListener?.onMenuIsOpen(view)
                }

                override fun onDownOrMove(slidingButtonView: SlidingButtonView?) {
                    mOnChangeCommodityListener?.onDownOrMove(slidingButtonView)
                }

            })


        }

        private fun closeMenuAndToDetail(commodityId: Long) {
            //判断是否有删除菜单打开
            if (slvParent?.open == true) {
                slvParent?.closeMenu()
            } else {
                CommodityDetailActivity.startActivity(
                    itemView.context,
                    commodityId,
                    SourceModel(
                        SourceModel.POSITION_TYPE.SHOPPING_CAR_TYPE,
                        SourceModel.POSITION_TYPE.SHOPPING_CAR_VALUE
                    )
                )
            }

        }


    }
}