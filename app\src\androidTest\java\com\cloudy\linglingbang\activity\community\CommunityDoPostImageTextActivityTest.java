package com.cloudy.linglingbang.activity.community;

import com.cloudy.linglingbang.BaseInstrumentedTest;
import com.cloudy.linglingbang.activity.shortvideo.CommunityDoPostShortVideoActivity;
import com.cloudy.linglingbang.model.postcard.PostCard;

/**
 * 发帖
 *
 * <AUTHOR>
 * @date 2019-11-26
 */
public class CommunityDoPostImageTextActivityTest extends BaseInstrumentedTest {

    @Override
    public void test() {
        super.test();
        CommunityDoPostImageTextActivity.IntentExtra intentExtra;
        int index = 0;
        switch (index) {
            case 0:
                //好货互通
                //图文贴
                intentExtra
                        = new CommunityDoPostImageTextActivity.IntentExtra(PostCard.PostType.IMAGE_TEXT, "", null);
                intentExtra.setJumpAfterSuccess(0);
                intentExtra.setOpenFrom(CommunityDoPostImageTextActivity.OPEN_FROM_GOODS);
                // TODO: [liu<PERSON><PERSON><PERSON> create at 2019-12-02] 标签
//                intentExtra.setGroupInfo("测试标签");
                CommunityDoPostImageTextActivity.startActivity(getActivity(), intentExtra);
                break;
            case 1:
                //好货互通
                //视频贴
                intentExtra
                        = new CommunityDoPostImageTextActivity.IntentExtra(PostCard.PostType.SHORT_VIDEO, "", null);
                intentExtra.setJumpAfterSuccess(0);
                intentExtra.setOpenFrom(CommunityDoPostImageTextActivity.OPEN_FROM_GOODS);

                CommunityDoPostShortVideoActivity.startActivity(getContext(), intentExtra);
                break;
            case 2:
                //车型论坛
                //图文贴
                intentExtra
                        = new CommunityDoPostImageTextActivity.IntentExtra(PostCard.PostType.IMAGE_TEXT, "", null);
                intentExtra.setJumpAfterSuccess(0);
                intentExtra.setOpenFrom(CommunityDoPostImageTextActivity.OPEN_FROM_CAR_TYPE);

                //车型
//                intentExtra.setGroupInfo("车型");
                //城市
//                intentExtra.setChannelName();
//                intentExtra.setChannelId();
                //城市--就是渠道
                CommunityDoPostImageTextActivity.startActivity(getActivity(), intentExtra);
                break;
            case 3:
                //车型论坛
                //视频贴
                //车型
//                intentExtra.setGroupInfo("车型");
                //城市
//                intentExtra.setChannelName();
//                intentExtra.setChannelId();
                //城市--就是渠道
                CommunityDoPostShortVideoActivity.startActivity(getContext(), PostCard.PostType.SHORT_VIDEO, null, 0, CommunityDoPostImageTextActivity.OPEN_FROM_CAR_TYPE);

//                JumpPageUtil.goShortVideoPostPage(getContext(), PostCard.PostType.SHORT_VIDEO, null, 0, CommunityDoPostImageTextActivity.OPEN_FROM_CAR_TYPE);
                break;
            case 4:
                //人车生活
                //图文贴
                intentExtra
                        = new CommunityDoPostImageTextActivity.IntentExtra(PostCard.PostType.IMAGE_TEXT, "", null);
                intentExtra.setJumpAfterSuccess(0);
                intentExtra.setOpenFrom(CommunityDoPostImageTextActivity.OPEN_FROM_PEOPLE_CAR_LIFT);

                // intentExtra.setGroupInfo("测试标签");
                CommunityDoPostImageTextActivity.startActivity(getActivity(), intentExtra);
                break;
            case 5:
                //人车生活
                //视频贴
                // intentExtra.setGroupInfo("测试标签");
                CommunityDoPostShortVideoActivity.startActivity(getContext(), PostCard.PostType.SHORT_VIDEO, null, "测试标签", 0, CommunityDoPostImageTextActivity.OPEN_FROM_PEOPLE_CAR_LIFT);
                break;
        }
    }
}