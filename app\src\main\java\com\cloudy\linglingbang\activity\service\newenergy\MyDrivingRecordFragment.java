package com.cloudy.linglingbang.activity.service.newenergy;

import android.content.Context;
import android.os.Bundle;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshFragment;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.app.widget.hoverview.HoverGroupLayout;
import com.cloudy.linglingbang.app.widget.hoverview.HoverRecyclerView;
import com.cloudy.linglingbang.app.widget.hoverview.OnChildScrollListener;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.server.DailyMileageBean;
import com.cloudy.linglingbang.model.server.MyDrivingRecord;

import java.util.List;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 我的行驶记录
 *
 * <AUTHOR>
 * @date 2019-07-16
 */
public class MyDrivingRecordFragment extends BaseRecyclerViewRefreshFragment<MyDrivingRecord> implements OnChildScrollListener {

    private static HoverGroupLayout mHoverGroupLayout;
    private static String mVin;
    private DailyMileageBean dailyMileageBean;

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_base_refresh_rank;
    }

    @Override
    protected void initViews() {
        super.initViews();
        if (dailyMileageBean == null) {
            dailyMileageBean = new DailyMileageBean();
        }
        dailyMileageBean.setVin(mVin);
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        HoverRecyclerView recyclerView = (HoverRecyclerView) getRefreshController().getRecyclerView();
        recyclerView.setOnChildScrollListener(this);

    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<MyDrivingRecord> list) {
        return new MyDrivingAdapter(getActivity(), list);
    }

    @Override
    public Observable<BaseResponse<List<MyDrivingRecord>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        if (dailyMileageBean == null) {
            dailyMileageBean = new DailyMileageBean();
        }
        dailyMileageBean.setVin(mVin);
        dailyMileageBean.setPageNo(pageNo);
        dailyMileageBean.setPageSize(pageSize);
        return service2.getDailyMileage(dailyMileageBean);
    }

    public static MyDrivingRecordFragment newInstance(HoverGroupLayout hoverGroupLayout, String vin) {
        mHoverGroupLayout = hoverGroupLayout;
        mVin = vin;
        MyDrivingRecordFragment myCommentPostListFragment = new MyDrivingRecordFragment();
        return myCommentPostListFragment;
    }

    @Override
    public RefreshController<MyDrivingRecord> createRefreshController() {
        final RefreshController<MyDrivingRecord> refreshController = new RefreshController<MyDrivingRecord>(this) {
            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return null;
            }

            //不展示刷新样式
            @Override
            protected boolean showRefreshingWhenLoadDataAfterInitViews() {
                return false;
            }
        };
        return refreshController;
    }

    /**
     * 供外部调用刷新页面
     */
    public void onRefresh() {
        if (getRefreshController() != null) {
            getRefreshController().onRefresh();
        }
    }

    @Override
    public boolean isChildScroll() {
        return mHoverGroupLayout != null && mHoverGroupLayout.isScrollToEnd();
    }

}
