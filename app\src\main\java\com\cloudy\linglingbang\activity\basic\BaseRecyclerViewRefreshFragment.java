package com.cloudy.linglingbang.activity.basic;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;

import java.util.List;

import androidx.annotation.Nullable;

/**
 * 用recyclerView的下拉刷新Fragment
 * 继承{@linkplain IRefreshContext}，调用{@linkplain RefreshController#initViews(View)}
 * <br/>子类实现{@linkplain IRefreshContext#createAdapter(List)}
 * 和{@linkplain IRefreshContext#getListDataFormNet(L00bangService2, int, int)}
 * <br/>要修改{@linkplain RefreshController}，可以重写{@linkplain #createRefreshController()}
 * <br/>添加常用的{@linkplain #getRefreshController()},{@linkplain #getPageSize()},{@linkplain #getData()}
 *
 * <AUTHOR> create at 2016/10/9 15:22
 */
public abstract class BaseRecyclerViewRefreshFragment<T> extends LazyFragment implements IRefreshContext<T> {
    private Context mContext;
    private RefreshController<T> mRefreshController;
    /**
     * 是否已经初始化
     */
    private boolean mIsInitialized;

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        mContext = activity;
        if (mRefreshController == null) {
            mRefreshController = createRefreshController();
        }
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_base_refresh;
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        //initViews之后，为了方便在其中获取接口所需的参数
        // 否则如果调用在之前，可以会没获取到参数
        /**
         * 这里重新做记录
         * 初始化，应该在initViews()之后就完成，因有可能需要在initViews()中获取一些参数，比如帖子id之类，然后才能初始化。
         * （当然，也可以把获取参数放到onCreate等方法中，此时显示出将initData和initViews分开的好处了）
         * 因此需要在initViews()中，先执行获取参数等操作，再调用super.initViews()，这是相当不协调的。
         * 因此将其单独提出来，放到onViewCreate()中，因为该方法肯定在initViews()之后调用。
         * 但是又带来了问题，就是这个方法会执行多次（一开始没注意到），比如在ViewPager中，隐藏再显示，如果setOffscreenPageLimit默认为1，
         * 会在destroyItem中被执行mCurTransaction.detach((Fragment)object);然后再显示的时候又onCreateView同时调用该方法onViewCreated，
         * 因此加了判断，只初始化一次
         */
        if (!mIsInitialized && isRealViewLoaded()) {
            //懒加载不初始化，加载实际布局后会重新调用 onViewCreated
            mIsInitialized = true;
            mRefreshController.initViews(mRootView);
        }
    }

    @Override
    public Context getContext() {
        return mContext;
    }

    @Override
    public RefreshController<T> createRefreshController() {
        return new RefreshController<>(this);
    }

    @Override
    public RefreshController<T> getRefreshController() {
        return mRefreshController;
    }

    public int getPageSize() {
        return getRefreshController().getPageSize();
    }

    public List<T> getData() {
        return getRefreshController().getData();
    }
}
