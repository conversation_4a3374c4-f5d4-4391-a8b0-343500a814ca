package com.cloudy.linglingbang.activity.shortvideo;

import android.content.Context;
import android.content.pm.ActivityInfo;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;
import android.view.Window;

import com.aliyun.player.source.VidSts;
import com.cloudy.aliyunshortvideo.widget.AliyunVodPlayerView;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.app.util.FileUtil;
import com.cloudy.linglingbang.app.util.PermissionUtils;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.model.shortvideo.AliSTSTokenInfo;

import java.io.File;
import java.io.Serializable;

import androidx.annotation.NonNull;
import butterknife.BindView;

/**
 * 短视频播放页面
 *
 * <AUTHOR>
 * @date 2017/12/27
 */

public class ShortVideoActivity extends BaseActivity {

    /** 读写权限请求code */
    private static final int REQUEST_WRITE_PERMISSION = 1001;

    /** 阿里云播放器 */
    @BindView(R.id.sv_short_video)
    AliyunVodPlayerView mAliyunVodPlayerView;

    @Override
    protected void loadViewLayout() {
        //隐藏标题栏
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.activity_short_video);
        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
    }

    @Override
    protected void initialize() {
        //检查权限
        PermissionUtils.checkStoragePermissions(null,
                this,
                REQUEST_WRITE_PERMISSION,
                "五菱汽车即将申请读写权限，点击确认以继续！",
                "该功能需要读写权限，否则无法正常使用，是否打开设置？");
        mAliyunVodPlayerView.setOnNormalScreenBackClickListener(new AliyunVodPlayerView.OnNormalScreenBackClickListener() {
            @Override
            public void onBackClick() {
                mAliyunVodPlayerView.release();
                finish();
            }
        });
        //不允许全屏
        mAliyunVodPlayerView.setCanFullScreen(false);
    }

    @Override
    protected void onPermissionResult(boolean isGranted, int requestCode) {
        super.onPermissionResult(isGranted, requestCode);
        if (requestCode == REQUEST_WRITE_PERMISSION) {
            if (isGranted) {
                IntentExtra intentExtra = (IntentExtra) getIntentExtra(null);
                if (intentExtra == null) {
                    onIntentExtraError();
                    return;
                }
                /*
                 设置缓存目录路径,用于短视频重复播放场景,在调用prepare方法之前设置
                 maxSize设置100M时缓存文件超过100M后会优先覆盖最早缓存的文件,
                 maxDuration设置为300秒时表示超过300秒的视频不会启用缓存功能.
                */
                int videoType = intentExtra.getVideoType();
                if (videoType == IntentExtra.TYPE_ALIYUN) {
                    String videoId = intentExtra.getVideoId();
                    String coverUrl = intentExtra.getCoverUrl();
                    AliSTSTokenInfo aliSTSTokenInfo = intentExtra.getAliSTSTokenInfo();
                    if (TextUtils.isEmpty(videoId) || aliSTSTokenInfo == null) {
                        onIntentExtraError();
                        return;
                    }
                    //使用vid+STS方式播放
                    VidSts vidSts = new VidSts();
                    vidSts.setVid(videoId);//视频vid
                    vidSts.setAccessKeyId(aliSTSTokenInfo.getAccessKeyId());//播放凭证id
                    vidSts.setSecurityToken(aliSTSTokenInfo.getAccessKeySecret());//播放凭证secret
                    vidSts.setSecurityToken(aliSTSTokenInfo.getSecurityToken());//播放凭证token
                    vidSts.setTitle(" ");//隐藏视频标题
                    mAliyunVodPlayerView.setVideoSource(vidSts, coverUrl, "");
                } else {
                    if (!FileUtil.isHaveFile(intentExtra.getVideoPath())) {
                        ToastUtil.showMessage(this, R.string.short_video_local_file_not_exist);
                        return;
                    }
                    //使用URL播放
                    mAliyunVodPlayerView.setVideoSource(Uri.fromFile(new File(intentExtra.getVideoPath())).toString());
                }
            } else {
                finish();
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (mAliyunVodPlayerView != null) {
            mAliyunVodPlayerView.start();
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        if (mAliyunVodPlayerView != null) {
            mAliyunVodPlayerView.pause();
        }
    }

    @Override
    protected void onDestroy() {
        if (mAliyunVodPlayerView != null) {
            mAliyunVodPlayerView.release();
        }
        super.onDestroy();
    }

    @Override
    public void finish() {
        super.finish();
        //注释掉activity本身的过渡动画,避免退出时向右滑出
        overridePendingTransition(0, R.anim.umeng_socialize_fade_out);
    }

    /** 跳转播放aliyun视频 */
    public static void playAliyunVideo(@NonNull final Context context, final String videoId, final String coverUrl) {
        //使用vid+STS方式播放
        ShortVideoUtil.getAliSTSToken(context, new ShortVideoUtil.STSTokenCallback() {
            @Override
            public void onSuccess(AliSTSTokenInfo tokenInfo) {
                //加载视频信息
                if (tokenInfo != null) {
                    IntentExtra intentExtra = new IntentExtra(videoId, coverUrl, tokenInfo);
                    startActivity(context, intentExtra);
                }
            }

            @Override
            public void onFailure(Throwable e) {
                ToastUtil.showMessage(context, "获取播放凭证失败");
            }
        });
    }

    /** 播放本地视频 */
    public static void playLocalVideo(@NonNull Context context, String videoPath) {
        IntentExtra intentExtra = new IntentExtra(videoPath);
        startActivity(context, intentExtra);
    }

    /** 判断api版本,阿里云播放器不支持小于16的手机 */
    private static void startActivity(Context context, IntentExtra intentExtra) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.JELLY_BEAN) {
            ToastUtil.showMessage(context, R.string.short_video_can_not_use);
            return;
        }
        IntentUtils.startActivity(context, ShortVideoActivity.class, intentExtra);
    }

    private static class IntentExtra implements Serializable {
        /** 视频来源 */
        private int videoType;
        /** 本地视频-类型 */
        private static final int TYPE_LOCAL = 0;
        /** aliyun短视频-类型 */
        private static final int TYPE_ALIYUN = 1;
        /** aliyun视频vid */
        private String videoId;
        /** aliyun视频封面url */
        private String coverUrl;
        /** aliyun播放凭证 */
        private AliSTSTokenInfo aliSTSTokenInfo;
        /** 本地视频路径 */
        private String videoPath;

        public IntentExtra(String videoId, String coverUrl, AliSTSTokenInfo aliSTSTokenInfo) {
            this.videoId = videoId;
            this.coverUrl = coverUrl;
            this.aliSTSTokenInfo = aliSTSTokenInfo;
            this.videoType = TYPE_ALIYUN;
        }

        public IntentExtra(String videoPath) {
            this.videoPath = videoPath;
            this.videoType = TYPE_LOCAL;
        }

        private AliSTSTokenInfo getAliSTSTokenInfo() {
            return aliSTSTokenInfo;
        }

        private void setAliSTSTokenInfo(AliSTSTokenInfo aliSTSTokenInfo) {
            this.aliSTSTokenInfo = aliSTSTokenInfo;
        }

        public int getVideoType() {
            return videoType;
        }

        public void setVideoType(int videoType) {
            this.videoType = videoType;
        }

        public String getVideoPath() {
            return videoPath;
        }

        public void setVideoPath(String videoPath) {
            this.videoPath = videoPath;
        }

        public String getVideoId() {
            return videoId;
        }

        public void setVideoId(String videoId) {
            this.videoId = videoId;
        }

        public String getCoverUrl() {
            return coverUrl;
        }

        public void setCoverUrl(String coverUrl) {
            this.coverUrl = coverUrl;
        }
    }
}
