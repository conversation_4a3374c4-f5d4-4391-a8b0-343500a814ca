apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

}
repositories {
    flatDir {
        dirs 'libs'
    }
}

dependencies {
    implementation name: 'NoCaptchaSDK-external-release-5.4.20', ext: 'aar'
    implementation name: 'SecurityBodySDK-external-release-5.4.40', ext: 'aar'
    implementation name: 'SecurityGuardSDK-external-release-5.4.56', ext: 'aar'
    api name: 'verificationsdklib-1.3.2.3417747-3ND-SMS-NC', ext: 'aar'
}
