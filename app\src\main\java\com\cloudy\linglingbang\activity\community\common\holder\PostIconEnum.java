package com.cloudy.linglingbang.activity.community.common.holder;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.drawable.Drawable;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.style.ImageSpan;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.model.postcard.PostCard;

import androidx.annotation.CheckResult;

/**
 * 图标枚举
 * 枚举顺序即代表显示的优先级，然后各自控制是否显示
 * <p>
 * 默认展示 荐>精>技|提
 * 本地和车型，增加顶
 * 问答专区，增加纳
 *
 * <AUTHOR>
 * @date 2018/6/25
 */
public enum PostIconEnum {
    /**
     * 顶
     */
    TOP(R.drawable.ic_post_label_top),
    /**
     * 热
     */
    HOT(R.drawable.ic_post_label_top),
    /**
     * 纳自己
     */
    MY_ADOPT(R.drawable.ic_post_label_my_adopt),
    /**
     * 纳别人
     */
    OTHER_ADOPT(R.drawable.ic_post_label_other_adopt),
    /**
     * 荐
     */
    RECOMMEND(R.drawable.ic_post_label_recommend),
    /**
     * 精
     */
    ELITE(R.drawable.ic_post_label_elite),
    /**
     * 技
     */
    TECHNICIAN(R.drawable.ic_post_label_technician),
    /**
     * 提，提车作业帖
     */
    EXPERIENCE(R.drawable.ic_post_label_experience),
    /**
     * 问
     */
    QUESTION(R.drawable.ic_post_label_question),
    /**
     * 司
     */
    SOPHISTICATED_DRIVER(R.drawable.ic_post_label_sophisticated_driver),

    ;
    private final int iconResId;

    PostIconEnum(int iconResId) {
        this.iconResId = iconResId;
    }

    public int getIconResId() {
        return iconResId;
    }

    /**
     * 显示图标，即 addFlags
     */
    @CheckResult
    public int showPostIcon(int flags) {
        return addFlags(flags, this);
    }

    /**
     * 根据 flags 判断是否需要显示某图标
     */
    public boolean needShowByFlags(int flags) {
        return checkFlags(flags, this);
    }

    /**
     * 添加 flag
     */
    @CheckResult
    public static int addFlags(int flags, Enum anEnum) {
        return flags | (1 << anEnum.ordinal());
    }

    //判断 flag
    public static boolean checkFlags(int flags, Enum anEnum) {
        return (flags & (1 << anEnum.ordinal())) != 0;
    }

    /**
     * 添加默认要展示的图标
     * <p>
     * 默认的荐>精>技>提
     */
    @CheckResult
    public static int addDefaultShowIconInfo(int flags) {
        flags = PostIconEnum.RECOMMEND.showPostIcon(flags);
        flags = PostIconEnum.ELITE.showPostIcon(flags);
        flags = PostIconEnum.TECHNICIAN.showPostIcon(flags);
        flags = PostIconEnum.EXPERIENCE.showPostIcon(flags);
        return flags;
    }

    /**
     * 添加默认要展示的图标
     * 新的帖子列表的图标
     * <p>
     * 默认的荐>精>技
     */
    @CheckResult
    public static int addNewPostListDefaultShowIconInfo(int flags) {
        flags = PostIconEnum.RECOMMEND.showPostIcon(flags);
        flags = PostIconEnum.ELITE.showPostIcon(flags);
        flags = PostIconEnum.TECHNICIAN.showPostIcon(flags);
        return flags;
    }

    /**
     * 根据帖子判断是否需要展示图标
     */
    public boolean needShowByPostCard(PostCard postCard) {
        switch (this) {
            case TOP:
            case HOT:
                //顶
                return postCard.getIsTop() == 1;
            case MY_ADOPT:
                //纳自己
                return postCard.getIsAccept() == 1 && postCard.getiAdopt() == 1;
            case OTHER_ADOPT:
                //纳别人
                return postCard.getIsAccept() == 1 && postCard.getiAdopt() != 1;
            case RECOMMEND:
                //荐
                return postCard.getIsRecommend() == 1;
            case ELITE:
                //精
                return postCard.getIsElite() == 1;
            case TECHNICIAN:
                //技
                return postCard.getPostTypeIdOrNegative() == PostCard.PostType.ASK_TECHNICIAN;
            case EXPERIENCE:
                //提，提车作业帖
                return postCard.getPostTypeIdOrNegative() == PostCard.PostType.CAR_BUYING_EXPERIENCE;
            case QUESTION:
                //问
                return postCard.getPostTypeIdOrNegative() == PostCard.PostType.QUESTION;
            case SOPHISTICATED_DRIVER:
                //司
                return postCard.getPostTypeIdOrNegative() == PostCard.PostType.ASK_SOPHISTICATED_DRIVER;
            default:
                break;
        }
        return false;
    }

    /**
     * 根据帖子获取需要展示的图标
     */
    public static CharSequence getIconInfo(Context context, PostCard postCard, int flags, TextView textView) {
        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder();
        for (PostIconEnum postIconEnum : values()) {
            if (postIconEnum.needShowByFlags(flags)) {
                if (postIconEnum.needShowByPostCard(postCard)) {
                    postIconEnum.addIconInfo(context, spannableStringBuilder, textView);
                }
            }
        }
        return spannableStringBuilder;
    }

    /**
     * 添加图标展示
     */
    private void addIconInfo(Context context, SpannableStringBuilder spannableStringBuilder, TextView textView) {

        //不包含字体间距
        textView.setIncludeFontPadding(false);
        float textSize = textView.getTextSize();
        int size = (int) Math.ceil(textSize) + 1;
        Drawable d = context.getResources().getDrawable(getIconResId());
        d.setBounds(0, 0, size, size);
        ImageSpan imageSpan = new PostIconImageSpan(d, context.getResources().getDimensionPixelOffset(R.dimen.normal_10));

        int start = spannableStringBuilder.length();
        spannableStringBuilder.append(name());
        int end = spannableStringBuilder.length();
        spannableStringBuilder.setSpan(imageSpan, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
    }

    /**
     * 帖子图标
     * 只添加了 paddingRight，drawable 已经提前设置了 bounds ，绘制不偏移正常
     * <p>
     * 如果设置 android:includeFontPadding="false"
     * 只需 canvas.translate(x, top 即可);
     * {@link com.cloudy.linglingbang.app.util.chat.ExpressionUtil.CenteredImageSpan}
     */
    public static class PostIconImageSpan extends ImageSpan {
        private final int mPaddingRight;

        public PostIconImageSpan(Drawable d, int paddingRight) {
            super(d);
            mPaddingRight = paddingRight;
        }

        /**
         * 返回宽度
         */
        @Override
        public int getSize(Paint paint, CharSequence text, int start, int end,
                Paint.FontMetricsInt fontMetricsInt) {
            return getDrawable().getBounds().right + mPaddingRight;
        }

        /**
         * see detail message in android.text.TextLine
         *
         * @param canvas the canvas, can be null if not rendering
         * @param text   the text to be draw
         * @param start  the text start position
         * @param end    the text end position
         * @param x      the edge of the replacement closest to the leading margin
         * @param top    the top of the line
         * @param y      the baseline
         * @param bottom the bottom of the line
         * @param paint  the work paint
         */
        @Override
        public void draw(Canvas canvas, CharSequence text, int start, int end,
                float x, int top, int y, int bottom, Paint paint) {
            canvas.save();
            //设置transY，让图片和文字对齐
            Drawable d = getDrawable();
            int transY = 0;
            transY = ((bottom - top) - d.getBounds().bottom) / 2 + top;
            canvas.translate(x, transY);
            getDrawable().draw(canvas);
            canvas.restore();
        }
    }

}
