package com.cloudy.linglingbang.activity.community.common.holder;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.community.common.PostFlagsEnum;
import com.cloudy.linglingbang.activity.community.post.CommunityUtils;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.ViewHolderUtils;
import com.cloudy.linglingbang.app.widget.PraiseCountTextView;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.NormalSubscriber;
import com.cloudy.linglingbang.model.user.Author;

/**
 * 作者
 *
 * <AUTHOR>
 * @date 201911/26
 */
public class PostAuthorStaggeredGridViewHolder extends BasePostChildViewHolder {
    private ImageView mIvUserHead;
    private TextView mTvUserName;
    private ImageView mIvGoldCoin;
    private int mFlags;
    private CommunityUtils.PostOperateUtils.OperateListener mOperateListener;
    private boolean mFinishAfterDelete;
    private LinearLayout mLlPraise;
    private ImageView mIvPraise;
    private PraiseCountTextView mTvPraiseCount;

    private Author mAuthor;

    public PostAuthorStaggeredGridViewHolder(View itemView) {
        super(itemView);
        addFlags(PostFlagsEnum.SHOW_GOLD_COIN);
        //如果直接创建，就需要设默认值
//        mFlags = PostFlagsEnum.addDefaultFlags(mFlags);
    }

    public void setFlags(int flags) {
        mFlags = flags;
    }

    public PostAuthorStaggeredGridViewHolder addFlags(PostFlagsEnum flagsEnum) {
        mFlags = flagsEnum.addFlags(mFlags);
        return this;
    }

    public void setOperateListener(CommunityUtils.PostOperateUtils.OperateListener operateListener) {
        mOperateListener = operateListener;
    }

    public PostAuthorStaggeredGridViewHolder setFinishAfterDelete(boolean finishAfterDelete) {
        mFinishAfterDelete = finishAfterDelete;
        return this;
    }

    @Override
    protected void initItemView(View itemView) {
        super.initItemView(itemView);
        mIvUserHead = itemView.findViewById(R.id.iv_user_head);
        mTvUserName = itemView.findViewById(R.id.tv_user_name);
        mIvGoldCoin = itemView.findViewById(R.id.iv_award);//金币
        mLlPraise = itemView.findViewById(R.id.ll_praise);
        mIvPraise = itemView.findViewById(R.id.iv_praise);
        mTvPraiseCount = itemView.findViewById(R.id.tv_praise_count);

        setPraiseViewClip(mIvPraise);

        ViewHolderUtils.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onClickLlPraise(v);
            }
        }, mLlPraise);

        //点击用户头像、性别、昵称
        ViewHolderUtils.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onClickUser(v);
            }
        }, mIvUserHead, mTvUserName);

    }

    @Override
    public void bindTo(PostCard postCard) {
        super.bindTo(postCard);
        bindTo(postCard.getAuthor());
//        if (mIvGoldCoin != null) {
//            if (isShowReward(postCard.getPostShareAward())) {
//                mIvGoldCoin.setVisibility(View.VISIBLE);
//            } else {
//                mIvGoldCoin.setVisibility(View.GONE);
//            }
//        }

        //设置点赞的颜色
        updatePraiseView(mPostCard.getIsPraise() == 1);

    }

    /**
     * 是否显示金币
     *
     * @param showReward
     * @return
     */
    private boolean isShowReward(PostCard.PostShareAward showReward) {
        return showReward != null && PostFlagsEnum.SHOW_GOLD_COIN.checkFlags(mFlags) && showReward.getIsAward() == 1;
    }

    public void bindTo(Author author) {
        //如果没有返回 author 可能会复用

        mAuthor = author;
        if (author != null) {
            //头像，如果没头像显示默认
            if (mIvUserHead != null) {
                String url = AppUtil.getImageUrlBySize(author.getPhoto(), AppUtil._120X120);
                ImageLoad.LoadUtils.loadAvatar(mIvUserHead.getContext(), mIvUserHead, url);
            }
            ViewHolderUtils.setText(author.getNickname(), mTvUserName);
//            UserIdentitySpanUtils.setUserNameAndIdentity(mTvUserName, author, mFlags, true);

        } else {
            //头像
            if (mIvUserHead != null) {
                ImageLoad.LoadUtils.loadAvatar(mIvUserHead.getContext(), mIvUserHead, "");
            }
            //用户昵称、地址、车友会
            ViewHolderUtils.setText("", mTvUserName);
        }

    }

    /**
     * 需要显示操作
     */
    protected boolean showOperation() {
        //设置了回调才显示，默认不显示
        //如果是副邦主，只要加精和置顶不同时满足，则显示
        if (mPostCard.getChiefType() == 1) {
            return true;
        } else {
            return mPostCard.getChiefType() == 3 && (mPostCard.getIsElite() == 0 || mPostCard.getIsTop() == 0);
        }
    }

    /**
     * 点击用户
     */
    private void onClickUser(View v) {
        if (PostFlagsEnum.IGNORE_AVATAR_CLICK.checkFlags(mFlags)) {
            //个人主页不再跳转
            return;
        }
        if (mAuthor != null) {
            JumpPageUtil.goToPersonPage(v.getContext(), mAuthor);
        }
    }

    /**
     * 点击车友会或经销商
     */
    private void onClickCarClub(View v) {
        JumpPageUtil.goClubOrDealer(v.getContext(), mAuthor);
    }

    /**
     * 点击标签
     */
    private void onClickTvLabel(View v) {
        if (mPostCard != null) {
            JumpPageUtil.goLabelPostList(v.getContext(), mPostCard.getLabelName(), mPostCard.getLabelIdOrZero());
        }
    }

    /**
     * 点击操作按钮
     */
    private void onClickIvOperation(View v) {
        //除了默认的操作，额外的操作
        CommunityUtils.PostUtils postUtils = new CommunityUtils.PostUtils(v.getContext(), mPostCard, v, null, mOperateListener)
                .setFinishAfterDelete(mFinishAfterDelete);
        postUtils.showOperatePopup();
    }

    private void setPraiseViewClip(View praiseView) {
        if (praiseView == null) {
            return;
        }
        //为了能展示点赞的放大效果，点赞 view 的 parent 不截切
        ViewParent parent = praiseView.getParent();
        if (parent == null) {
            return;
        }
        if (parent instanceof ViewGroup) {
            ((ViewGroup) parent).setClipChildren(false);
        }

        //再 parent，即底部信息布局
        parent = parent.getParent();
        if (parent == null) {
            return;
        }
        if (parent instanceof ViewGroup) {
            ((ViewGroup) parent).setClipChildren(false);
        }

        //再 parent ，即底部信息的 parent，因为底部信息高度是 wrap_content，所以其 parent 也要设置不裁切
        parent = parent.getParent();
        if (parent == null) {
            return;
        }
        if (parent instanceof ViewGroup) {
            ((ViewGroup) parent).setClipChildren(false);
        }

        parent = parent.getParent();
        if (parent == null) {
            return;
        }
        if (parent instanceof ViewGroup) {
            ((ViewGroup) parent).setClipChildren(false);
        }

    }

    /**
     * 点赞
     */
    private void onClickLlPraise(View v) {
        Context context = v.getContext();
        if (AppUtil.checkLogin(context)) {
            if (mPostCard.getIsPraise() == 0) {
                //提前设置
                updatePraiseView(true);
                //点赞动画
                mIvPraise.startAnimation(AnimationUtils.loadAnimation(context, R.anim.post_praise_anim));
                mTvPraiseCount.addCount(mPostCard.getPostPraiseCount());
                praisedPostCard();
            } else {
                ToastUtil.showMessage(context, context.getString(R.string.item_post_bottom_info_toast_praised));
            }
        }
    }

    /**
     * 更新点赞 view
     */
    private void updatePraiseView(boolean praised) {
        if (mIvPraise != null) {
            if (praised) {
                mIvPraise.setImageDrawable(AppUtil.getTintDrawable(R.drawable.ic_post_praaise_press_new, R.color.color_384967, mIvPraise.getContext()));
            } else {
                mIvPraise.setImageResource(R.drawable.ic_post_praise_new);
            }
        }
        if (mTvPraiseCount != null) {
            if (praised) {
                mTvPraiseCount.setTextColor(mTvPraiseCount.getContext().getResources().getColor(R.color.common_blue_384967));
            } else {
                mTvPraiseCount.setTextColor(mTvPraiseCount.getContext().getResources().getColor(R.color.text_light_color_929292));
            }
            //显示点赞数
            if (mPostCard.getPostPraiseCount() == 0) {
                mTvPraiseCount.setText("");
            } else {
                mTvPraiseCount.setText(AppUtil.getCommentDesc(mPostCard.getPostPraiseCount()));
            }
        }
    }

    /**
     * 赞帖子
     */
    private void praisedPostCard() {
        mLlPraise.setEnabled(false);
        L00bangRequestManager2
                .setSchedulers(L00bangRequestManager2.getInstance().getService().praisedPostCard(Long.valueOf(mPostCard.getPostId())))
                .subscribe(new NormalSubscriber<String>(mLlPraise.getContext()) {
                    @Override
                    public void onSuccess(String s) {
                        super.onSuccess(s);
                        mLlPraise.setEnabled(true);
                        mPostCard.setIsPraise(1);
                        mPostCard.setPostPraiseCount(mPostCard.getPostPraiseCount() + 1);
                    }

                    @Override
                    public void onFailure(Throwable e) {
                        super.onFailure(e);
                        mLlPraise.setEnabled(true);
                        //取消回去
                        updatePraiseView(false);
                    }
                });
    }



}
