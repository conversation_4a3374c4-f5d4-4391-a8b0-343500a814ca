package com.cloudy.linglingbang.activity.fragment.store.youpin.search;

import android.content.Context;
import android.graphics.Rect;
import android.view.View;
import android.view.ViewGroup;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.fragment.store.home.StoreHomeRefreshController;
import com.cloudy.linglingbang.activity.fragment.store.home.adapter.StoreHomeAdapter;
import com.cloudy.linglingbang.activity.fragment.store.youpin.ElementUtils;
import com.cloudy.linglingbang.activity.search.controller.BaseSearchController;
import com.cloudy.linglingbang.activity.store.commodity.CommodityListPageCode;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BaseSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.server.Ad.Ad2;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 优品：搜索商品
 *
 * <AUTHOR>
 * @date 2020/5/10
 */
public class CommoditySearchController extends BaseSearchController {
    public CommoditySearchController(Context context, ViewGroup container) {
        super(context, container);
    }

    private SensorsUtils.StoreHomeAnchor mStoreHomeAnchor;

    public CommoditySearchController setStoreHomeAnchor(SensorsUtils.StoreHomeAnchor storeHomeAnchor) {
        mStoreHomeAnchor = storeHomeAnchor;
        return this;
    }

    @Override
    public int getSearchType() {
        return 0;
    }

    @Override
    public RefreshController createRefreshController() {
        return new StoreHomeRefreshController(this, "") {
            @Override
            protected boolean loadDataAfterInitViews() {
                //不主动加载
                return false;
            }

            @Override
            protected boolean isRefreshEnable() {
                //不可下拉刷新
                return false;
            }

            @Override
            protected boolean isLoadMoreEnable() {
                return true;
            }

            @Override
            protected Observable<BaseResponse<List<Object>>> getListDataFromNet(L00bangService2 service2, int pageNo, int pageSize) {
                Map<String, Object> map = new HashMap<>(5);
                map.put("pageNo", String.valueOf(pageNo));
                map.put("pageSize", String.valueOf(pageSize));
                map.put("searchType", "7");
                map.put("pageCode", CommodityListPageCode.PAGE_CODE_STORE_SEARCH_COMMODITIES);
                map.put("keyWord", mLastSearchText);
                return service2.findEcCommodityAppList(map)
                        .map(ElementUtils.getStoreElementToObjectFun(Ad2.LINK_TYPE.TYPE_NEW_COMMODITY_DETAIL, 0));
            }

            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return new StoreHomeElementItemDecoration(context) {
                    @Override
                    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                        super.getItemOffsets(outRect, view, parent, state);
                        outRect.bottom = 0;
                        outRect.top = px20 + px20;
                    }
                };
            }

            @Override
            public String getEmptyString() {
                return getContext().getString(R.string.store_home_search_result_empty);
            }

            @Override
            protected BaseSubscriber<List<Object>> createNormalSubscriber(Context context, final int loadPage) {
                if (loadPage <= 1) {
                    //不可下拉刷新，搜索时，显示加载框
                    return new ProgressSubscriber<List<Object>>(context) {
                        @Override
                        public void onSuccess(List<Object> list) {
                            super.onSuccess(list);
                            onLoadSuccess(loadPage, list, LOAD_TYPE_NET);
                            show();
                        }

                        @Override
                        public void onFailure(Throwable e) {
                            super.onFailure(e);
                            onLoadFail(loadPage, e);
                            show();
                        }
                    };
                } else {
                    return super.createNormalSubscriber(context, loadPage);
                }
            }
        }.setEmptyStringResId(R.string.store_home_search_result_empty)
                .setEmptyImageResId(R.drawable.ic_search_result_empty);
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List list) {
        return new StoreHomeAdapter(getContext(), list)
                .setCountDownTag(getContext())
                .setStoreHomeAnchor(mStoreHomeAnchor);
    }

    @Override
    public Observable<BaseResponse<List<Object>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        return null;
    }
}
