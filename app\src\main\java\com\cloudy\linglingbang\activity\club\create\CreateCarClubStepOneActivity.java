package com.cloudy.linglingbang.activity.club.create;

import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.ChooseImageController;
import com.cloudy.linglingbang.app.util.ImageLoad;
import com.cloudy.linglingbang.app.util.ValidatorUtils;
import com.cloudy.linglingbang.app.widget.dialog.ChooseCityDialog;
import com.cloudy.linglingbang.app.widget.item.CommonItem;
import com.cloudy.linglingbang.model.CityModel;
import com.cloudy.linglingbang.model.ProvinceModel;
import com.cloudy.linglingbang.model.club.CreateCarClubBean;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.umeng.analytics.MobclickAgent;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 创建车友会
 *
 * <AUTHOR>
 * @date 2017/11/15
 */
public class CreateCarClubStepOneActivity extends BaseActivity {
    private static final int REQUEST_CODE_CHOOSE_CAR_TYPE = 21;
    private static final int REQUEST_CODE_CHOOSE_NEXT_STEP = 22;
    private CommonItem mItemName;
    private CommonItem mItemLocation;
    private CommonItem mItemCarType;
    private CommonItem mItemQQGroup;
    private CommonItem mItemIcon;
    private ChooseCityDialog.ChooseCityUtil mChooseCityUtil;
    private Long mChosenCityId;
    private long[] mChosenCarTypeIdArray;
    private String mCarClubIconPath;
    private List<ValidatorUtils.Validator> mValidatorList;
    private ChooseImageController mChooseImageController;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_create_car_club_step_1);
    }

    @Override
    protected void initialize() {
        mChooseImageController = new ChooseImageController(this, new ChooseImageController.OnChooseImageListener() {
            @Override
            public void onAddImage(String path) {
                mCarClubIconPath = path;
                mItemIcon.getIvRight().setImageURI(null);
                //用glide加载圆形图，直接加载uri是方形的
                //因为上传之后都是显示为方形
                new ImageLoad(CreateCarClubStepOneActivity.this, mItemIcon.getIvRight(), Uri.fromFile(new File(path)))
                        .setUserMemoryCache(false)//设置不使用缓存
                        .setErrorImageId(R.drawable.ic_create_car_club_choose_image)
                        .load();
            }
        })
                .setCropWidthAndHeight(150);
        mItemName = (CommonItem) findViewById(R.id.item_name);
        mItemLocation = (CommonItem) findViewById(R.id.item_location);
        mItemCarType = (CommonItem) findViewById(R.id.item_car_type);
        mItemQQGroup = (CommonItem) findViewById(R.id.item_qq_group);
        mItemIcon = (CommonItem) findViewById(R.id.item_icon);

        //点击事件
        mItemLocation.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                chooseCity();
            }
        });
        mItemCarType.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                chooseCarType();
            }
        });
        mItemIcon.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mChooseImageController.showChooseTypeDialog();
            }
        });

        //校验
        mValidatorList = new ArrayList<>();
        mValidatorList.add(new ValidatorUtils.ItemEmptyValidator(mItemName));
        mValidatorList.add(new ValidatorUtils.TextViewLengthValidator(mItemName.getTvRight(), 7, 13)
                .setToast(getString(R.string.create_car_club_step_1_name_prompt)));
        mValidatorList.add(new ValidatorUtils.ItemEmptyValidator(mItemLocation) {
            @Override
            public void onValidateFail() {
                super.onValidateFail();
                chooseCity();
            }
        });
        mValidatorList.add(new ValidatorUtils.ItemEmptyValidator(mItemCarType) {
            @Override
            public boolean isValidInner() {
                return mChosenCarTypeIdArray != null && mChosenCarTypeIdArray.length > 0 && mChosenCarTypeIdArray.length <= 3;
            }
        });
        mValidatorList.add(new ValidatorUtils.ItemEmptyValidator(mItemQQGroup));
        mValidatorList.add(new ValidatorUtils.TextViewLengthValidator(mItemQQGroup.getTvRight(), 4, 15)
                .setToast(getString(R.string.create_car_club_step_1_qq_group_prompt)));
        mValidatorList.add(new ValidatorUtils.ItemEmptyValidator(mItemIcon) {
            @Override
            public boolean isValidInner() {
                return !TextUtils.isEmpty(mCarClubIconPath);
            }

            @Override
            public void onValidateFail() {
                super.onValidateFail();
                mChooseImageController.showChooseTypeDialog();
            }
        });
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        super.onCreateOptionsMenu(menu);
        if (mToolbar != null && mToolbar.getMenu() != null) {
            MenuItem item = mToolbar.getMenu().findItem(R.id.action_right_text);
            if (item != null) {
                item.setTitle(getString(R.string.next_step));
                item.setVisible(true);
            }
        }
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.action_right_text:
                onClickNextStep();
                return true;
            default:
                return super.onOptionsItemSelected(item);
        }
    }

    private void chooseCity() {
        if (mChooseCityUtil == null) {
            mChooseCityUtil = new ChooseCityDialog.ChooseCityUtil(this, new ChooseCityDialog.OnChooseCityListener() {
                @Override
                public boolean onChoseCity(ProvinceModel chosenProvinceModel, CityModel chosenCityModel) {
                    mChosenCityId = chosenCityModel.getCityId();
                    String location = AppUtil.getProvinceAndCity(chosenProvinceModel.getProvinceName(), chosenCityModel.getCityName());
                    mItemLocation.getTvRight().setText(location);
                    return false;
                }
            });
        }
        mChooseCityUtil.showDialog();
    }

    /**
     * 选择车型，将当前的也传过去
     */
    private void chooseCarType() {
        Intent intent = new Intent(this, ChooseRelatedCarTypeActivity.class);
        intent.putExtra(IntentUtils.INTENT_EXTRA_COMMON, mChosenCarTypeIdArray);
        startActivityForResult(intent, REQUEST_CODE_CHOOSE_CAR_TYPE);
    }

    private void onClickNextStep() {
        for (ValidatorUtils.Validator validator : mValidatorList) {
            if (!validator.isValid()) {
                return;
            }
        }
        validateClubName();
    }

    /**
     * 校验车友会名字是否重复
     */
    private void validateClubName() {
        L00bangRequestManager2.getServiceInstance()
                .validateCreateCarClubParam(mItemName.getTvRight().getText().toString())
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<Object>(this) {
                    @Override
                    public void onSuccess(Object o) {
                        gotoStepTwo();
                    }
                });
    }

    /**
     * 转到下一步
     */
    private void gotoStepTwo() {
        //添加友盟统计
        MobclickAgent.onEvent(this, "305");
        CreateCarClubBean createCarClubBean = new CreateCarClubBean();
        createCarClubBean.setChannelName(mItemName.getTvRight().getText().toString());
        createCarClubBean.setCityId(mChosenCityId);
        createCarClubBean.setCarTypeIds(mChosenCarTypeIdArray);
        createCarClubBean.setGroupNum(mItemQQGroup.getTvRight().getText().toString());
        createCarClubBean.setChannelFaviconPath(mCarClubIconPath);
        IntentUtils.startActivityForResult(this, CreateCarClubStepTwoActivity.class, REQUEST_CODE_CHOOSE_NEXT_STEP, createCarClubBean);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE_CHOOSE_CAR_TYPE) {
            if (resultCode == RESULT_OK) {
                if (data != null) {
                    //可以将 null 赋给它
                    mChosenCarTypeIdArray = data.getLongArrayExtra(IntentUtils.INTENT_EXTRA_COMMON);
                    mItemCarType.getTvRight().setText(data.getStringExtra(IntentUtils.INTENT_EXTRA_FROM));
                } else {
                    mItemCarType.getTvRight().setText(null);
                }
            }
        } else if (requestCode == REQUEST_CODE_CHOOSE_NEXT_STEP) {
            if (resultCode == RESULT_OK) {
                finish();
                return;
            }
        }
        if (mChooseImageController != null) {
            mChooseImageController.onActivityResult(requestCode, resultCode, data);
        }

    }

    @Override
    protected void onPermissionResult(boolean isGranted, int requestCode) {
        super.onPermissionResult(isGranted, requestCode);
        if (mChooseImageController != null) {
            mChooseImageController.onPermissionResult(isGranted, requestCode);
        }
    }
}
