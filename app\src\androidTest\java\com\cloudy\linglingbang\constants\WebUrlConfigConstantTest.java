package com.cloudy.linglingbang.constants;

import android.content.Intent;
import android.net.Uri;

import com.cloudy.linglingbang.BaseInstrumentedTest;

/**
 * 跳转测试
 *
 * <AUTHOR>
 * @date 2018/11/14
 */
public class WebUrlConfigConstantTest extends BaseInstrumentedTest {
    @Override
    public void test() {
        super.test();
        testOpenAppFromUrl();
    }

    /**
     * 测试通过地址打开 app
     * 需要提供给 h5 地址跳转时，可以先用此方法测试一下
     */
    private void testOpenAppFromUrl() {
        String url = "llb://web.CommonWeb/rotaryLottery";
        Intent intent = new Intent()
                .setAction(Intent.ACTION_VIEW)
                .setData(Uri.parse(url));
        getActivity().startActivity(intent);
    }
}