package com.cloudy.linglingbang.activity.club.create;

import android.content.DialogInterface;
import android.text.TextUtils;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.RadioGroup;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseActivity;
import com.cloudy.linglingbang.app.util.GetVerificationCodeController;
import com.cloudy.linglingbang.app.util.UploadImageController;
import com.cloudy.linglingbang.app.util.ValidatorUtils;
import com.cloudy.linglingbang.app.widget.dialog.alert.CommonAlertDialog;
import com.cloudy.linglingbang.app.widget.item.CommonItem;
import com.cloudy.linglingbang.model.SmsTypeEnum;
import com.cloudy.linglingbang.model.club.CreateCarClubBean;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.user.User;
import com.umeng.analytics.MobclickAgent;

import java.util.ArrayList;
import java.util.List;

/**
 * 创建车友会
 *
 * <AUTHOR>
 * @date 2017/11/15
 */
public class CreateCarClubStepTwoActivity extends BaseActivity {
    private CreateCarClubBean mCreateCarClubBean;
    private CommonItem mItemName;
    private CommonItem mItemSex;
    private CommonItem mItemTel;
    private CommonItem mItemVerifyCode;
    private CommonItem mItemWechat;
    private CommonItem mItemQQ;
    private CommonItem mItemCarType;
    private List<ValidatorUtils.Validator> mValidatorList;
    private RadioGroup mSexRadioGroup;
    private GetVerificationCodeController mGetVerificationCodeController;
    private UploadImageController mUploadImageController;

    @Override
    protected void loadViewLayout() {
        setContentView(R.layout.activity_create_car_club_step_2);
    }

    @Override
    protected void initialize() {
        mCreateCarClubBean = (CreateCarClubBean) getIntentExtra(null);
        if (mCreateCarClubBean == null) {
            onIntentExtraError();
        }

        //view
        mItemName = (CommonItem) findViewById(R.id.item_name);
        mItemSex = (CommonItem) findViewById(R.id.item_sex);
        mItemTel = (CommonItem) findViewById(R.id.item_tel);
        mItemVerifyCode = (CommonItem) findViewById(R.id.item_validate_code);
        mItemWechat = (CommonItem) findViewById(R.id.item_weichat);
        mItemQQ = (CommonItem) findViewById(R.id.item_qq);
        mItemCarType = (CommonItem) findViewById(R.id.item_car_type);
        mSexRadioGroup = (RadioGroup) mItemSex.findViewById(R.id.rg_sex_group);

        //初始化
        mItemName.requestFocus();
        mItemCarType.getTvRight().setText(User.getsUserInstance().getApproveCarTypeName());
        mItemCarType.getIvRight().setVisibility(View.INVISIBLE);
        mGetVerificationCodeController = new GetVerificationCodeController(this, SmsTypeEnum.CREATE_CAR_CLUB.getId(), (Button) mItemVerifyCode.findViewById(R.id.btn_get), (EditText) mItemTel.getTvRight(), (EditText) mItemVerifyCode.getTvRight());

        //校验
        mValidatorList = new ArrayList<>();
        mValidatorList.add(new ValidatorUtils.ItemEmptyValidator(mItemName)
                .setToast(getString(R.string.create_car_club_step_2_name_prompt)));
        mValidatorList.add(new ValidatorUtils.TextViewLengthValidator(mItemName.getTvRight(), 2, 15)
                .setToast(getString(R.string.create_car_club_step_2_name_length_prompt)));
        mValidatorList.add(new ValidatorUtils.ItemEmptyValidator(mItemSex) {
            @Override
            public boolean isValidInner() {
                return mSexRadioGroup.getCheckedRadioButtonId() != -1;
            }
        }
                .setToast(getString(R.string.create_car_club_step_2_sex_prompt)));
        mValidatorList.add(new ValidatorUtils.ItemEmptyValidator(mItemTel)
                .setToast(getString(R.string.create_car_club_step_2_tel_prompt)));
        mValidatorList.add(new ValidatorUtils.TextViewEmptyValidator(mItemTel.getTvRight()) {
            @Override
            public boolean isValidInner() {
                return ValidatorUtils.Regex.isPhone(getTrimValidateContent().toString());
            }
        }.setToast(getString(R.string.error_mobile_format)));
        mValidatorList.add(new ValidatorUtils.ItemEmptyValidator(mItemVerifyCode));
        mValidatorList.add(new ValidatorUtils.ToastValidator(this, getString(R.string.create_car_club_step_2_qq_or_wechat_prompt)) {
            @Override
            public boolean isValidInner() {
                //有一个不为空即可
                return !TextUtils.isEmpty(mItemWechat.getTvRight().getText().toString().trim()) ||
                        !TextUtils.isEmpty(mItemQQ.getTvRight().getText().toString().trim());
            }
        });
        mValidatorList.add(new ValidatorUtils.TextViewLengthValidator(mItemWechat.getTvRight(), 4, 15) {
            @Override
            public boolean isValidInner() {
                //如果为空则不校验
                return TextUtils.isEmpty(getTrimValidateContent())
                        || super.isValidInner();
            }
        }.setToast(getString(R.string.create_car_club_step_2_wechat_prompt)));
        mValidatorList.add(new ValidatorUtils.TextViewLengthValidator(mItemQQ.getTvRight(), 4, 15) {
            @Override
            public boolean isValidInner() {
                //如果为空则不校验
                return TextUtils.isEmpty(getTrimValidateContent())
                        || super.isValidInner();
            }
        }.setToast(getString(R.string.create_car_club_step_2_qq_prompt)));
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        super.onCreateOptionsMenu(menu);
        if (mToolbar != null && mToolbar.getMenu() != null) {
            MenuItem item = mToolbar.getMenu().findItem(R.id.action_right_text);
            if (item != null) {
                item.setTitle(getString(R.string.create_car_club_step_2_complete));
                item.setVisible(true);
            }
        }
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.action_right_text:
                onClickComplete();
                return true;
            default:
                return super.onOptionsItemSelected(item);
        }
    }

    private void onClickComplete() {
        for (ValidatorUtils.Validator validator : mValidatorList) {
            if (!validator.isValid()) {
                return;
            }
        }
        if (TextUtils.isEmpty(mCreateCarClubBean.getChannelFavicon())) {
            uploadImage();
            return;
        }
        //添加友盟统计
        MobclickAgent.onEvent(this, "306");

        mCreateCarClubBean.setApplyUserName(mItemName.getTvRight().getText().toString());
        mCreateCarClubBean.setApplyUserSex(mSexRadioGroup.getCheckedRadioButtonId() == R.id.rb_sex_man ? 1 : 2);
        mCreateCarClubBean.setChannelApplyMobile(mItemTel.getTvRight().getText().toString());
        mCreateCarClubBean.setValidateCode(mItemVerifyCode.getTvRight().getText().toString());
        mCreateCarClubBean.setWxNum(mItemWechat.getTvRight().getText().toString());
        mCreateCarClubBean.setQqNum(mItemQQ.getTvRight().getText().toString());

        L00bangRequestManager2.getServiceInstance()
                .applyCreateCarClub(mCreateCarClubBean)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new ProgressSubscriber<Object>(this) {
                    @Override
                    public void onSuccess(Object o) {
                        super.onSuccess(o);
                        showSuccessDialog();
                    }
                });
    }

    private void showSuccessDialog() {
        String title = getString(R.string.dialog_apply_for_moderator_success_title);
        String message = getString(R.string.dialog_apply_for_moderator_success_message);
        String buttonText = getString(R.string.common_ok_haode);
        CommonAlertDialog dialog = new CommonAlertDialog(this, message, buttonText, null, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                setResult(RESULT_OK);
                CreateCarClubStepTwoActivity.this.finish();
            }
        }, null);
        dialog.getAlertController().setTitle(title);
        dialog.setCancelable(false);
        dialog.setCanceledOnTouchOutside(false);
        dialog.show();
    }

    /**
     * 上传图片，这里的图片可能为空或者已经被删除，有可能会产生问题
     */
    private void uploadImage() {
        if (mUploadImageController == null) {
            mUploadImageController = new UploadImageController(this)
                    .setNeedCompress(true)
                    .setOnUploadSuccessListener(new UploadImageController.OnUploadImageSuccessListener() {
                        @Override
                        public void onUploadSuccess(String result) {
                            //上传完成，赋值图片，同时将图片路径清空
                            mCreateCarClubBean.setChannelFavicon(result);
                            mCreateCarClubBean.setChannelFaviconPath(null);
                            onClickComplete();
                        }
                    });
        }
        mUploadImageController.upload(mCreateCarClubBean.getChannelFaviconPath());
    }
}

