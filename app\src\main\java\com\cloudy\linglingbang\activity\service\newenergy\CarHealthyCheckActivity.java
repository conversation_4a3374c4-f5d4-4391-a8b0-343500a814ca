package com.cloudy.linglingbang.activity.service.newenergy;

import android.content.Context;
import android.content.Intent;

import com.cloudy.linglingbang.activity.basic.SingleFragmentActivity;

import androidx.fragment.app.Fragment;

/**
 * 新能源车辆检查状态
 *
 * <AUTHOR>
 * @date 2019-08-05
 */
public class CarHealthyCheckActivity extends SingleFragmentActivity {

    private String vin, mCarImageUrl;
    @Override
    protected Fragment createFragment() {
        return CarHealthyCheckFragment.newInstance(vin, mCarImageUrl);
    }

    public static void startActivity(Context context, String vin, String carImageUrl) {
        Intent intent = new Intent(context, CarHealthyCheckActivity.class);
        intent.putExtra("vin", vin);
        intent.putExtra("url", carImageUrl);
        context.startActivity(intent);
    }

    @Override
    public void initialize() {
        vin = getIntent().getStringExtra("vin");
        mCarImageUrl = getIntent().getStringExtra("url");
        super.initialize();
        setTitle("车辆体检");
    }
}
