package com.cloudy.linglingbang.activity.community.post;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.ArrayMap;
import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.SurfaceView;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.aliyun.player.IPlayer;
import com.cloudy.aliyunshortvideo.widget.AliyunVodPlayerView;
import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshActivity;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.activity.community.post.detail.PostDetailHelper;
import com.cloudy.linglingbang.activity.live.widget.LiveVideoPrise;
import com.cloudy.linglingbang.app.util.AppUtil;
import com.cloudy.linglingbang.app.util.JumpPageUtil;
import com.cloudy.linglingbang.app.util.ShareUtil;
import com.cloudy.linglingbang.app.util.StatusBarUtils;
import com.cloudy.linglingbang.app.util.ToastUtil;
import com.cloudy.linglingbang.app.util.UserUtils;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.recycler.EmptySupportedRecyclerView;
import com.cloudy.linglingbang.constants.FinalSensors;
import com.cloudy.linglingbang.constants.WebUrlConfigConstant;
import com.cloudy.linglingbang.model.Comment;
import com.cloudy.linglingbang.model.postcard.CommentTitle;
import com.cloudy.linglingbang.model.postcard.PostCard;
import com.cloudy.linglingbang.model.postcard.PostCardItem;
import com.cloudy.linglingbang.model.postcard.PostCommodity;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangRequestManager2;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BackgroundSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.BaseSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.NormalSubscriber;
import com.cloudy.linglingbang.model.request.retrofit2.subscribers.ProgressSubscriber;
import com.cloudy.linglingbang.model.user.User;
import com.umeng.analytics.MobclickAgent;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Random;

import butterknife.BindView;
import butterknife.OnClick;
import io.reactivex.rxjava3.core.Observable;

/**
 * 帖子详情
 *
 * <AUTHOR> create at 2016/10/18 10:30
 */
public class PostDetailActivity extends BaseRecyclerViewRefreshActivity<Comment> {
    private PostCard mPostCard;
    private PostDetailItemDecoration mPostDetailItemDecoration;
    private ShareUtil mShareUtil;
    /**
     * 有顶部封图的title
     */
    @BindView(R.id.toolbar_layout)
    RelativeLayout mToolBarLayout;

    //输入布局
    @BindView(R.id.reply_input_layout)
    ReplyInputLayout reply_input_layout;

    //底部的回复、赞、收藏等
    @BindView(R.id.ll_reply_post)
    LinearLayout ll_reply_post;
    //回复楼主及多少条回复，用于不可回复时设置背景
    @BindView(R.id.rl_reply_post)
    RelativeLayout rl_reply_post;

    @BindView(R.id.tv_reply)
    TextView tv_reply;
    @BindView(R.id.tv_reply_count)
    TextView tv_reply_count;

    @BindView(R.id.iv_favorite)
    ImageView iv_favorite;
    @BindView(R.id.tv_favorite_num)
    TextView mTvFavoriteNum;

    //赞
    @BindView(R.id.iv_praise)
    ImageView iv_praise;
    @BindView(R.id.tv_praise_num)
    TextView mTvPraiseNum;

    @BindView(R.id.iv_comment)
    ImageView iv_comment;
    @BindView(R.id.tv_comment_num)
    TextView mTvCommentNum;

    //返回顶部
    @BindView(R.id.iv_back_to_top)
    ImageView iv_back_to_top;

    /**
     * 采纳技师提示
     */
    @BindView(R.id.tv_remind_user_adopt_answer)
    TextView tv_remind_user_adopt_answer;

    private List<PostCard> mRecommendPostList;

    private long mPostId;
    /**
     * 回复的id，用于只显示指定回复
     */
    private long mRelationId;
    //是否加载全部回复，默认为true，当传了relationId时置为false，当点击加载全部评论按钮后置为true
    private boolean mLoadAllComments = true;

    /**
     * 打开方式，0为纯web view，不会调用这个。1为原生，2为上面web view，评论为原生
     */
    private int mOpenType;

    private int shareVisible;

    private TopCommentsLoader mTopCommentsLoader;

    /**
     * 只看楼主
     */
    private boolean mSeeAuthorOnlyStatus;
    //发现 → (发现、搜索结果页、个人主页、linglab等等..)
    private String openFrom;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        String postIdString = getIntent().getStringExtra("postId");
        shareVisible = getIntent().getIntExtra("shareVisible", 0);
        openFrom = getIntent().getStringExtra("openFrom");
        try {
            mPostId = Long.parseLong(postIdString.trim());
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        mRelationId = getIntent().getLongExtra("relationId", 0);
        if (mRelationId > 0) {
            mLoadAllComments = false;
            //此处还没有创建adapter，由其自己创建时设置
        }
        mOpenType = (int) IntentUtils.getExtra(getIntent().getExtras(), IntentUtils.INTENT_EXTRA_FROM, JumpPageUtil.OPEN_TYPE.APP);
        super.onCreate(savedInstanceState);
        //在initialize中还是null
        if (mOpenType == JumpPageUtil.OPEN_TYPE.WEB_VIEW_AND_APP || mOpenType == JumpPageUtil.OPEN_TYPE.APP) {
            getRefreshController().getRecyclerView().addOnScrollListener(new RecyclerView.OnScrollListener() {
                @Override
                public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                    super.onScrolled(recyclerView, dx, dy);
                    iv_back_to_top.setVisibility(recyclerView.computeVerticalScrollOffset() > 200 ? View.VISIBLE : View.GONE);
                }

                @Override
                public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                    super.onScrollStateChanged(recyclerView, newState);
                }
            });
        }
        getRefreshController().getRecyclerView().addOnChildAttachStateChangeListener(new RecyclerView.OnChildAttachStateChangeListener() {
            @Override
            public void onChildViewAttachedToWindow(View view) {
                if (view != null) {
                    RecyclerView.ViewHolder childViewHolder = getRefreshController().getRecyclerView()
                            .getChildViewHolder(view);
                    if (childViewHolder instanceof PostDetailNativeAdapter.ContentSenseViewHolder) {
                        ((PostDetailNativeAdapter.ContentSenseViewHolder) childViewHolder).changeAdTurning(false);
                    }
                }
            }

            @Override
            public void onChildViewDetachedFromWindow(View view) {
                if (view != null) {
                    RecyclerView.ViewHolder childViewHolder = getRefreshController().getRecyclerView()
                            .getChildViewHolder(view);
                    if (childViewHolder instanceof PostDetailNativeAdapter.PostDetailBaseContentViewHolder) {
                        PostDetailNativeAdapter.PostDetailBaseContentViewHolder postDetailBaseContentViewHolder =
                                (PostDetailNativeAdapter.PostDetailBaseContentViewHolder) childViewHolder;
                        if (postDetailBaseContentViewHolder.playerView != null
                                && !postDetailBaseContentViewHolder.playerView.isSetFullScreen()
                                && postDetailBaseContentViewHolder.playerView.getPlayerState() == IPlayer.started
                        ) {
                            postDetailBaseContentViewHolder.playerView.stop();
                            postDetailBaseContentViewHolder.playerView.release();
                            postDetailBaseContentViewHolder.rlContainer.removeView(postDetailBaseContentViewHolder.playerView);
                            postDetailBaseContentViewHolder.playerView = null;
                        }
                    } else if (childViewHolder instanceof PostDetailNativeAdapter.ContentSenseViewHolder) {
                        ((PostDetailNativeAdapter.ContentSenseViewHolder) childViewHolder).changeAdTurning(false);
                    }
                }
            }
        });
        ((PostDetailNativeAdapter) getRefreshController().getAdapter()).setOnVideoClickListener
                (new PostDetailNativeAdapter.OnVideoClickListener() {
                    @Override
                    public void onVideoClick() {
                        stopPostVideo();
                    }
                });
        mTopCommentsLoader = new TopCommentsLoader(this, mPostId, (PostDetailNativeAdapter) getRefreshController().getAdapter());
    }

    @Override
    protected void onStart() {
        super.onStart();
        //神策埋点
        SensorsUtils.sensorsViewStart(FinalSensors.POST_DETAIL);
    }

    @Override
    protected void loadViewLayout() {
        this.setContentView(R.layout.activity_post_detail);
    }

    private void doAnimation(MotionEvent event) {
        float[] num = {-30, -20, 0, 20, 30};//随机心形图片角度
        RelativeLayout mRlContent = findViewById(R.id.rl_content);
        final ImageView imageView = new ImageView(this);
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(300, 300);
        params.leftMargin = (int) event.getX() - 150;
        params.topMargin = (int) event.getY() - 300;
        imageView.setImageDrawable(getResources().getDrawable(com.cloudy.aliyunshortvideo.R.drawable.ic_video_double_praise));
        imageView.setLayoutParams(params);
        mRlContent.addView(imageView);
        AnimatorSet animatorSet = new AnimatorSet();

        animatorSet.play(LiveVideoPrise.scale(imageView, "scaleX", 2f, 0.9f, 100, 0))
                .with(LiveVideoPrise.scale(imageView, "scaleY", 2f, 0.9f, 100, 0))
                .with(LiveVideoPrise.rotation(imageView, 0, 0, num[new Random().nextInt(4)]))
                .with(LiveVideoPrise.alpha(imageView, 0, 1, 100, 0))
                .with(LiveVideoPrise.scale(imageView, "scaleX", 0.9f, 1, 50, 150))
                .with(LiveVideoPrise.scale(imageView, "scaleY", 0.9f, 1, 50, 150))
                .with(LiveVideoPrise.translationY(imageView, 0, -600, 800, 400))
                .with(LiveVideoPrise.alpha(imageView, 1, 0, 300, 400))
                .with(LiveVideoPrise.scale(imageView, "scaleX", 1, 3f, 700, 400))
                .with(LiveVideoPrise.scale(imageView, "scaleY", 1, 3f, 700, 400));
        animatorSet.start();
        animatorSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                mRlContent.removeViewInLayout(imageView);
            }
        });
    }

    private DoubleClick doubleClick;

    //当需要使用双击事件监听，调用此方法即可。
    protected void enableDClickReturn() {
        doubleClick = new DoubleClick(new DoubleClick.OnDoubleClickListener() {
            @Override
            public void onDoubleClick(MotionEvent event) {
                doAnimation(event);
                if (mPostCard != null && mPostCard.getIsPraise() != 1) {
                    doPraise();
                }

            }
        }, findViewById(R.id.recycler_view));
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        if (doubleClick == null) {
            return super.dispatchTouchEvent(event);
        } else {
            doubleClick.dispatchTouchEvent(event);
        }
        return super.dispatchTouchEvent(event);
    }

    @Override
    protected void initialize() {
        super.initialize();
        //添加一个0dp的SurfaceView，防止第一次动态添加时闪黑屏问题
        SurfaceView surfaceView = new SurfaceView(this);
        surfaceView.setLayoutParams(new ViewGroup.LayoutParams(0,
                0));

        reply_input_layout.addView(surfaceView);
        reply_input_layout.setReplyPostHint(getString(R.string.post_detail_input_reply_hint));
        reply_input_layout.setReplyPostCommentHint(getString(R.string.post_comment_input_reply_hint));
        reply_input_layout.setEventPage("发现");
        //end
        reply_input_layout.setReplyInputCallBack(new ReplyInputLayout.ReplyInputCallBack() {
            @Override
            public void onCancel() {
                showReplyLayout();
            }

            @Override
            public void onReplyResult(Comment resultComment, int commentPosition) {
                if (commentPosition == -1) {
                    //回-1时，表示是主题
                    int firstCommentItemPosition = getFirstCommentItemPosition();
                    if (getRefreshController().getData().size() > firstCommentItemPosition) {
                        Comment comment = getRefreshController().getData().get(firstCommentItemPosition);
                        if (comment != null && comment.getAccept() == 1) {
                            //已采纳的，总在第1位
                            getRefreshController().getData().add(firstCommentItemPosition + 1, resultComment);
                            getRefreshController().getAdapter().notifyItemInserted(firstCommentItemPosition + 1);
                        } else {
                            getRefreshController().getData().add(firstCommentItemPosition, resultComment);
                            getRefreshController().getAdapter().notifyItemInserted(firstCommentItemPosition);
                        }
                    } else {
                        getRefreshController().getData().add(firstCommentItemPosition, resultComment);
                        getRefreshController().getAdapter().notifyItemInserted(firstCommentItemPosition);
                    }
                    //滚动到回帖处
                    getRefreshController().getRecyclerView().scrollToPosition(firstCommentItemPosition);
                } else {
                    //表示回复楼层
                    onReplayCommentSuccess(resultComment, commentPosition);
                }
                //回复楼主和回复楼层都数量都加1
                mPostCard.setPostCommentCount(mPostCard.getPostCommentCount() + 1);
                onGetPostDetail(mPostCard, commentPosition);
            }

            /**
             * 回复评论成功
             */
            private void onReplayCommentSuccess(Comment resultComment, int position) {
                List<Comment> commentList = getRefreshController().getData();
                if (commentList != null && position < commentList.size()) {
                    //取出回复了哪一条评论
                    Comment anchorComment = commentList.get(position);
                    if (anchorComment != null) {
                        //有了 comment id 遍历获取
                        Long anchorCommentId = anchorComment.getCommentId();
                        if (anchorCommentId != null && anchorCommentId != 0) {
                            for (int i = 0; i < commentList.size(); i++) {
                                Comment comment = commentList.get(i);
                                if (comment != null) {
                                    Long commentId = comment.getCommentId();
                                    if (commentId != null && commentId.equals(anchorCommentId)) {
                                        //添加子回复
                                        List<Comment> subCommentList = comment.getSubWebComments();
                                        if (subCommentList == null) {
                                            subCommentList = new ArrayList<>();
                                        }
                                        subCommentList.add(resultComment);
                                        comment.setSubWebComments(subCommentList);
                                        getRefreshController().getAdapter().notifyItemChanged(i);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });
        if (mOpenType == JumpPageUtil.OPEN_TYPE.APP) {
            setLeftTitle("帖子详情");
            reply_input_layout.postDelayed(() -> {
                getPostDetail(true);
                loadRecommendPost();
            }, getIntent().getBooleanExtra("isFromEdit", false) ? 200 : 0);
        }

        enableDClickReturn();
    }

    /**
     * 删除评论之后刷新评论数
     *
     * @param deleteCount 需要减少的楼层数
     */
    public void onDeleteComment(int deleteCount) {
        //回复楼主和回复楼层都数量都加1
        mPostCard.setPostCommentCount(mPostCard.getPostCommentCount() - deleteCount);
        getRefreshController().getAdapter().notifyDataSetChanged();
    }

    /**
     * 加载帖子详情，用于刷新时先加载
     */
    private void getPostDetail(boolean showProgress) {
        //如果是原生的，加载帖子详情
        BaseSubscriber<PostCard> subscriber;
        if (showProgress) {
            subscriber = new ProgressSubscriber<PostCard>(this) {
                @Override
                public void onSuccess(PostCard postCard) {
                    super.onSuccess(postCard);
                    if (postCard != null) {
                        onGetPostDetail(postCard, -2);
                        //获取到结果后，开始加载评论
                        startLoadComment();
                    }
                }
            };
        } else {
            subscriber = new NormalSubscriber<PostCard>(this) {
                @Override
                public void onSuccess(PostCard postCard) {
                    super.onSuccess(postCard);
                    if (postCard != null) {
                        onGetPostDetail(postCard, -2);
                        //获取到结果后，开始加载评论
                        startLoadComment();
                    }
                }
            };
        }
        L00bangRequestManager2
                .getServiceInstance()
                .getPostDetails(mPostId)
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(subscriber);
    }

    /**
     * 开始加载评论
     */
    private void startLoadComment() {
        if (mPostCard != null) {
            if (mPostCard.getPostCommentCount() > 0) {
                getRefreshController().getListData(0);
                if (mLoadAllComments) {
                    mTopCommentsLoader.load();
                }
            } else {
                //评论为0，不需要加载
                //删除加载过的评论，防止刚刚有，刷新之后又被删除了
                if (getRefreshController() != null && getRefreshController().getData() != null && getRefreshController().getData().size() > 0) {
                    for (int i = 0; i < getRefreshController().getData().size(); i++) {
                        if (getRefreshController().getData().get(i) != null) {
                            getRefreshController().getData().remove(i);
                            i--;
                        }
                    }
                }
                //添加标题
                addAllCommentsTitle(getRefreshController().getData());
                //下面这条和ll_reply_post是createRefreshController()中的onLoadSuccess
                ((PostDetailNativeAdapter) getRefreshController().getAdapter()).setLoadingComment(false);
                //以下两条是RefreshController中的onRefreshComplete
                getRefreshController().getAdapter().notifyDataSetChanged();
                getRefreshController().getSwipeToLoadLayout().setRefreshing(false);
                showReplyLayout();
            }
        }
    }

    private void addAllCommentsTitle(List<Comment> data) {
        if (data != null && !data.isEmpty()) {
            Comment lastComment = data.get(data.size() - 1);
            if (lastComment != null) {
                if (lastComment instanceof CommentTitle && !lastComment.isFromTop()) {
                    //已经有全部
                    return;
                }
            }
            //没有全部标题，添加
            data.add(new CommentTitle("", false));
        }
    }

    /**
     * 获取到post
     *
     * @param postCard        the post
     * @param commentPosition 评论的楼层，-2为网络加载，-1为回复帖子，其他为回复楼层
     */
    private void onGetPostDetail(PostCard postCard, int commentPosition) {
        //调整布局
        PostDetailHelper.adjustLayoutByPostcard(this, postCard);
        if (postCard != null) {
            mPostCard = postCard;
            showCoverImgView();
            //回复数量
            tv_reply_count.setText(getString(R.string.post_detail_input_reply_count, mPostCard.getPostCommentCount()));
            //显示采纳技师答案提示栏
            if (String.valueOf(PostCard.PostType.ASK_TECHNICIAN).equals(mPostCard.getPostTypeId())
                    && mPostCard.getPostCommentCount() >= 1 && mPostCard.getIsAccept() == 0) {
                //是技师帖 有回复 无采纳
//                User author = mPostCard.getAuthor();
//                if (author != null && !TextUtils.isEmpty(author.getUserIdStr())) {
//                    User user = User.shareInstance();
//                    if (user != null && !TextUtils.isEmpty(user.getUserIdStr())) {
//                        if (user.getUserIdStr().equals(author.getUserIdStr())) {
//                            //是自己的技师提问帖
//                            //设置返还金币部分提示文字的颜色  注意：此处若在xml中使用<font>标签设置颜色，可能导致部分机型无法显示出文字
//                            //技师 100 工程师 150
//                            int coin = mPostCard.getTechType() == 1 ? 100 : 150;
//                            String text = String.format(Locale.getDefault(), "若已解决您的问题请<font color=\"#ff7000\">采纳</font>，采纳后返还您<font color=\"#ff7000\">%d金币</font>", coin);
//                            tv_remind_user_adopt_answer.setText(Html.fromHtml(text));
//                            setRemindUserAdoptTipsVisible(View.VISIBLE);
//                        }
//                    }
//                }
            }
            //收藏
            iv_favorite.setImageResource(mPostCard.getIsFavorite() == 1 ? R.drawable.ic_post_details_bottom_favorite_s : R.drawable.ic_post_details_bottom_favorite_n);
            if (mPostCard.getPostCollectCount() == null) {
                mTvFavoriteNum.setVisibility(View.INVISIBLE);
            } else {
                mTvFavoriteNum.setVisibility(View.VISIBLE);
                mTvFavoriteNum.setText(AppUtil.getCommentDesc(mPostCard.getPostCollectCount()));
            }
            //赞
            iv_praise.setImageResource(mPostCard.getIsPraise() == 1 ? R.drawable.ic_post_detail_bottom_like_checked : R.drawable.ic_post_detail_bottom_like_normal);
            mTvPraiseNum.setText(AppUtil.getCommentDesc(mPostCard.getPostPraiseCount()));
            //评论
            iv_comment.setImageResource(R.drawable.ic_post_detail_comment);
            mTvCommentNum.setText(AppUtil.getCommentDesc(mPostCard.getPostCommentCount()));
            if (mOpenType == JumpPageUtil.OPEN_TYPE.APP) {
                ((PostDetailNativeAdapter) getRefreshController().getAdapter()).setPostCard(mPostCard);
            }
            ((PostDetailNativeAdapter) getRefreshController().getAdapter()).setRecommendPostList(mRecommendPostList);

            List<PostCardItem> imageTextList = mPostCard.getImgTexts();
            if (imageTextList != null) {
                PostCardItem firstPostCardItem = imageTextList.get(0);
                if (TextUtils.isEmpty(firstPostCardItem.getImg()) && TextUtils.isEmpty(firstPostCardItem.getText())) {
                    //如果服务器返回的第1个，图文都为空，则将其删除
                    imageTextList.remove(0);
                    mPostCard.setImgTexts(imageTextList);
                }
            }
            //添加一个以供显示帖子
            if (getRefreshController().getData() != null && getRefreshController().getData().size() == 0) {
                if (mOpenType == JumpPageUtil.OPEN_TYPE.WEB_VIEW_AND_APP) {
                    getRefreshController().getData().add(0, null);
                } else if (mOpenType == JumpPageUtil.OPEN_TYPE.APP) {
                    int size = getHeaderCount();
                    //有1条，应该加3个
                    for (int i = 0; i < size; i++) {
                        getRefreshController().getData().add(0, null);
                    }
                }
            }
            //设置
            if (mPostDetailItemDecoration != null && mPostCard.getImgTexts() != null) {
                mPostDetailItemDecoration.setHeaderCount(getHeaderCount());
            }
            getRefreshController().getAdapter().notifyDataSetChanged();
            if (commentPosition == -2) {
                if (!mLoadAllComments) {
                    //如果是只加载一条回复，跳转到指定的回复，由于正在加载，先跳到-1处
                    getRefreshController().getRecyclerView().scrollToPosition(getFirstCommentItemPosition() - 1);
                }
            }
        }
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<Comment> list) {
        if (mOpenType == JumpPageUtil.OPEN_TYPE.WEB_VIEW_AND_APP) {
            return new PostDetailWebHeaderAdapter(this, list, mPostId);
        } else {
            PostDetailNativeAdapter postDetailNativeAdapter = new PostDetailNativeAdapter(this, list, mPostCard);
            postDetailNativeAdapter.setLoadAllComments(mLoadAllComments);
            return postDetailNativeAdapter;
        }
    }

    @Override
    public Observable<BaseResponse<List<Comment>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        if (mLoadAllComments) {
            if (isSeeAuthorOnlyStatus()) {
                //加载只看楼主
                return service2.getUserComments(mPostId, pageNo, pageSize);
            } else {
                return service2.getComments(mPostId, pageNo, pageSize);
            }
        } else {
            //只加载一条回复
            return service2.getCommentById(mRelationId);
        }
    }

    @Override
    public RefreshController<Comment> createRefreshController() {
        RefreshController<Comment> refreshController = new RefreshController<Comment>(this) {
            @Override
            public void getListData(int page) {
                if (page <= 1) {
                    ((PostDetailNativeAdapter) getAdapter()).setLoadingComment(true);
                }
                super.getListData(page);
            }

            @Override
            protected void onLoadSuccess(int loadPage, List<Comment> list, int loadType) {
                ((PostDetailNativeAdapter) getAdapter()).setLoadingComment(false);
                if (loadPage == 1) {
                    showReplyLayout();
                    if (list == null) {
                        list = new ArrayList<>();
                    }
                    setAuthorOnlyCommentCount(list.size());
                    //加载第一页，插入标题
                    list.add(0, new CommentTitle("", false));
                    if (mLoadAllComments) {
                        //提前取出值，手动再处理
                        int size = list.size();
                        boolean more = size >= getPageSize();
                        mTopCommentsLoader.addTopComments(list);
                        super.onLoadSuccess(loadPage, list, loadType);
                        if (size == 0) {
                            showEmptyInfo();
                        }
                        setLoadMoreEnable(more);
                            /*
                            直接 return 不用再走下面的逻辑
                            因为后面是 mLoadAllComments==false 的
                             */
                        return;
                    }
                }
                super.onLoadSuccess(loadPage, list, loadType);
                if (!mLoadAllComments) {
                    //如果是只加载一条回复，跳转到指定的回复
                    getRecyclerView().scrollToPosition(getFirstCommentItemPosition());
                    //不可以刷新，不可以加载更多
                    getRefreshController().setRefreshEnable(false);
                    getRefreshController().setLoadMoreEnable(false);
                }
            }

            /**
             * 设置作者回复的数量
             */
            private void setAuthorOnlyCommentCount(int size) {
                //这个状态判断实际是不准确的，可能在请求过程中点击了切换
                if (isSeeAuthorOnlyStatus()) {
                    if (mPostCard != null) {
                        mPostCard.setPostAuthorCommentCount(size);
                    }
                }
            }

            @Override
            protected int getFirstPageSizeOffset() {
                //加上一个空评论
                if (mOpenType == JumpPageUtil.OPEN_TYPE.WEB_VIEW_AND_APP) {
                    return 1;
                } else {
                    //有1条，应该加3个
                    return getHeaderCount();
                }
            }

            @Override
            protected boolean loadDataAfterInitViews() {
                return mOpenType == JumpPageUtil.OPEN_TYPE.WEB_VIEW_AND_APP;
            }

            @Override
            public void onRefresh() {
//                super.onRefresh();
                //刷新时，也先加载帖子
                getPostDetail(false);
                loadRecommendPost();
            }

            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return null;
            }
        };
        if (!mLoadAllComments) {
            //只加载一条回复，不提示到底了
            refreshController.setShowBottomToast(false);
        }
        return refreshController;
    }

    /**
     * 获取第一个评论的位置，包括置顶评论
     */
    public int getFirstCommentItemPosition() {
        // topCommentsSize 是包括了置顶评论的
        int topCommentsSize = mTopCommentsLoader.getTopComments() == null ? 0 : mTopCommentsLoader.getTopComments().size();
        //再多加 1 因为后面又加了全部评论标题
        topCommentsSize += 1;
        return getHeaderCount() + topCommentsSize;
    }

    /**
     * 获取前方 header 的数量，不包括 置顶评论
     */
    public int getHeaderCount() {
        //前面的数量，再加上 1 ，得到包含底部信息的数量
        return getPraiseItemPosition() + 1;
    }

    /**
     * 获取点赞数item的位置，用于点赞后刷新显示
     */
    private int getPraiseItemPosition() {
        if (mPostCard == null || mPostCard.getImgTexts() == null) {
            return 0;
        } else {
            //图文数量，加上作者的 1，得到前面的数量，即底部信息 item 的位置
            int result = mPostCard.getImgTexts().size() + 1;
            if (mPostCard.getPostTypeIdOrNegative() == PostCard.PostType.LING_SENSE) {
                int imgCount = CommunityUtils.getLingSensePostImageCount(mPostCard, null);
                List<PostCardItem> list = mPostCard.getImgTexts();
                if (imgCount > 0) {
                    //全部是图片时减一，用来展示标题和内容
                    if (imgCount == list.size()) {
                        imgCount -= 1;
                    }
                    result -= imgCount;
                    //菱感贴banner
                    result += 1;
                }
            }
            //加上封面的 1
            result += !TextUtils.isEmpty(mPostCard.getCoverImage()) ? 1 : 0;
            //加上相关推荐 1
            result += (mRecommendPostList != null && !mRecommendPostList.isEmpty() ? 1 : 0);
            return result;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE_COMMON && resultCode == Activity.RESULT_OK) {
            //帖子举报成功后返回设置状态
            //若不为空则直接设置举报状态，否则重新获取
            if (mPostCard != null) {
                mPostCard.setProsecutionUncheck(1);
                getRefreshController().getAdapter().notifyDataSetChanged();
//                onGetPostDetail(mPostCard, -2);
            } else {
                getRefreshController().onRefresh();
            }
        } else {
            reply_input_layout.onActivityResult(requestCode, resultCode, data);
            if (mShareUtil != null) {
                mShareUtil.onActivityResult(requestCode, resultCode, data);
            }
        }

    }

    @Override
    protected void onPermissionResult(boolean isGranted, int requestCode) {
        super.onPermissionResult(isGranted, requestCode);
        reply_input_layout.onPermissionResult(isGranted, requestCode);
    }

    @OnClick({R.id.iv_back_to_top, R.id.rl_reply_post, R.id.fl_favorite, R.id.fl_praise, R.id.fl_comment})
    public void onClick(View view) {
        String op = null;
        switch (view.getId()) {
            case R.id.iv_back_to_top:
                getRefreshController().getRecyclerView().scrollToPosition(0);
                break;
            case R.id.rl_reply_post:
                //SensorsUtils.sensorsClickBtn("回复","帖子");
                op = "回复框";
                showReplyPost();
                break;
            case R.id.fl_favorite:
                op = "收藏";
                if (mPostCard.getIsFavorite() == 1) {
                    //取消收藏
                    if (AppUtil.checkLogin(this, AppUtil.RegisterChannel.CHANNEL_POST)) {
                        //先成功，失败后再复原
                        mPostCard.setIsFavorite(0);
                        iv_favorite.setImageResource(R.drawable.ic_post_details_bottom_favorite_n);
                        L00bangRequestManager2
                                .getServiceInstance()
                                .unCollectPost(mPostId)
                                .compose(L00bangRequestManager2.setSchedulers())
                                .subscribe(new NormalSubscriber<Object>(this) {
                                    @Override
                                    public void onSuccess(Object o) {
                                        super.onSuccess(o);
                                        if (mPostCard.getPostCollectCount() == null) {
                                            mTvFavoriteNum.setVisibility(View.INVISIBLE);
                                        } else {
                                            mTvFavoriteNum.setVisibility(View.VISIBLE);
                                            int collectionNum = mPostCard.getPostCollectCount() - 1 < 0 ? 0 : mPostCard.getPostCollectCount() - 1;
                                            mPostCard.setPostCollectCount(collectionNum);
                                            mTvFavoriteNum.setText(AppUtil.getCommentDesc(mPostCard.getPostCollectCount()));

                                        }
                                    }

                                    @Override
                                    public void onFailure(Throwable e) {
                                        super.onFailure(e);
                                        mPostCard.setIsFavorite(1);
                                        iv_favorite.setImageResource(R.drawable.ic_post_details_bottom_favorite_s);
                                    }
                                });
                    }
                } else {
                    //收藏
                    if (AppUtil.checkLogin(this, AppUtil.RegisterChannel.CHANNEL_POST)) {
                        //先成功，失败后再复原
                        mPostCard.setIsFavorite(1);
                        iv_favorite.setImageResource(R.drawable.ic_post_details_bottom_favorite_s);
                        L00bangRequestManager2
                                .getServiceInstance()
                                .collectPost(mPostId)
                                .compose(L00bangRequestManager2.setSchedulers())
                                .subscribe(new NormalSubscriber<Object>(this) {
                                    @Override
                                    public void onSuccess(Object o) {
                                        super.onSuccess(o);
                                        if (mPostCard.getPostCollectCount() == null) {
                                            mTvFavoriteNum.setVisibility(View.INVISIBLE);
                                        } else {
                                            mTvFavoriteNum.setVisibility(View.VISIBLE);
                                            int collectionNum = mPostCard.getPostCollectCount() + 1 < 0 ? 0 : mPostCard.getPostCollectCount() + 1;
                                            mPostCard.setPostCollectCount(collectionNum);
                                            mTvFavoriteNum.setText(AppUtil.getCommentDesc(mPostCard.getPostCollectCount()));

                                        }
                                    }

                                    @Override
                                    public void onFailure(Throwable e) {
                                        super.onFailure(e);
                                        mPostCard.setIsFavorite(0);
                                        iv_favorite.setImageResource(R.drawable.ic_post_details_bottom_favorite_n);
                                    }
                                });
                    }
                }
                break;
            case R.id.fl_praise:
                op = "点赞";
                if (mPostCard.getIsPraise() == 1) {
                    ToastUtil.showMessage(this, "您已经赞过了");
                } else {
                    doPraise();
                }
                break;
            case R.id.fl_comment:
                EmptySupportedRecyclerView recyclerView = getRefreshController().getRecyclerView();
                PostDetailNativeAdapter postDetailNativeAdapter = (PostDetailNativeAdapter) recyclerView.getAdapter();

                for (int i = 0; i < postDetailNativeAdapter.getItemCount(); i++) {
                    if (postDetailNativeAdapter.getItemViewType(i) == 6 || postDetailNativeAdapter.getItemViewType(i) == 7) {
                        recyclerView.scrollToPosition(i);
                        return;
                    }

                    /*View childAt = layoutManager.getChildAt(i);
                    if (childAt != null) {
                        RecyclerView.ViewHolder childViewHolder = recyclerView.getChildViewHolder(childAt);
                        if (childViewHolder instanceof PostDetailCommentViewHolder) {
                            position = i;
                            recyclerView.scrollToPosition(position);
                            return;
                        }
                    }*/
                }

                break;
            default:
                break;
        }
        if (!TextUtils.isEmpty(op)) {
            SensorsUtils.sensorsClickBtn("点击(" + op + ")", "帖子详情页", "帖子详情页");
        }

    }

    private void doPraise() {
        if (AppUtil.checkLogin(this, AppUtil.RegisterChannel.CHANNEL_POST)) {
            //SensorsUtils.sensorsClickBtn("点赞","帖子");
            //先成功，失败后复原
            mPostCard.setIsPraise(1);
            iv_praise.setImageResource(R.drawable.ic_post_detail_bottom_like_checked);
            //更新点赞数
            int praiseCount = mPostCard.getPostPraiseCount() + 1;
            mPostCard.setPostPraiseCount(praiseCount);
            getRefreshController().getAdapter().notifyItemChanged(getPraiseItemPosition());
            L00bangRequestManager2
                    .getServiceInstance()
                    .praisedPostCard(mPostId)
                    .compose(L00bangRequestManager2.setSchedulers())
                    .subscribe(new NormalSubscriber<String>(this) {
                        @Override
                        public void onSuccess(String s) {
                            super.onSuccess(s);
                            mTvPraiseNum.setText(AppUtil.getCommentDesc(mPostCard.getPostPraiseCount()));
                        }

                        @Override
                        public void onFailure(Throwable e) {
                            super.onFailure(e);
                            mPostCard.setIsPraise(0);
                            iv_praise.setImageResource(R.drawable.ic_post_detail_bottom_like_normal);
                            //更新点赞数
                            int praiseCount = mPostCard.getPostPraiseCount() - 1;
                            mPostCard.setPostPraiseCount(praiseCount);
                            mTvPraiseNum.setText(AppUtil.getCommentDesc(mPostCard.getPostPraiseCount()));
                            getRefreshController().getAdapter().notifyItemChanged(getPraiseItemPosition());
                        }
                    });
        }
    }

    /**
     * 从adapter中调用，
     */
    public void onClickLoadAllComments() {
        mLoadAllComments = true;
        //adapter中隐藏加载全部回复的按钮
        //在加载完成后也会恢复下拉刷新和加载更多
        getRefreshController().setRefreshEnable(true);
        getRefreshController().setLoadMoreEnable(true);
        //恢复到底了提示
        getRefreshController().setShowBottomToast(true);
        /*
          这里有③种方式
          ①直接加载评论，调用startLoadComment();
          ②显示下拉刷动画，调用getRefreshController().getSwipeToLoadLayout().setRefreshing(true);
          ③不显示下拉刷新动画，调用getPostDetail(false);
         */
        //①加载评论
//        startLoadComment();
        //滚动到顶部
//        getRefreshController().getRecyclerView().scrollToPosition(0);
        //开始刷新
//        ②getRefreshController().getSwipeToLoadLayout().setRefreshing(true);
        getPostDetail(false);
    }

    /**
     * 显示回复主题的布局
     */
    private void showReplyLayout() {
        ll_reply_post.setVisibility(View.VISIBLE);
        if (CommunityUtils.PostUtils.canReply(mPostCard)) {
            tv_reply.setText(R.string.post_detail_input_reply_new);
            Drawable drawableLeft = getResources().getDrawable(
                    R.drawable.ic_post_detail_edit);
            tv_reply.setCompoundDrawablesWithIntrinsicBounds(drawableLeft,
                    null, null, null);
            tv_reply.setCompoundDrawablePadding(getResources().getDimensionPixelOffset(R.dimen.normal_6));

            rl_reply_post.setClickable(true);
            rl_reply_post.setBackgroundResource(R.drawable.bg_corner_stroke_eaeaea);
        } else {
            //不可回复时设置背景。
            //不用处理点击事件，点击后在showReplyPost中判断了
            tv_reply.setText(R.string.post_detail_input_can_not_reply);
            rl_reply_post.setClickable(false);
            rl_reply_post.setBackgroundResource(R.drawable.bg_corner_stroke_eaeaea_solid_f1f1f1);
        }
    }

    /**
     * 显示回复主题，在点击下方回复布局和点击你牛你先说时调用
     */
    public void showReplyPost() {
        if (CommunityUtils.PostUtils.canReply(mPostCard)) {
            MobclickAgent.onEvent(this, "63");
            reply_input_layout.setShowQuickReply(true);
            reply_input_layout.showInput(mPostId);
            ll_reply_post.setVisibility(View.GONE);
        }
    }

    public void showReplyLayout(Comment comment, int position) {
        if (!CommunityUtils.PostUtils.canReply(mPostCard)) {
            return;
        }
        if (comment != null && comment.getUser() != null) {
            if (User.getsUserInstance() != null) {
                if (comment.getUser().getUserIdStr().equals(User.getsUserInstance().getUserIdStr())) {
                    ToastUtil.showMessage(this, "您不能回复自己的回复");
                    return;
                }
            }
            String commentParentId = comment.getCommentParentId();
            if (TextUtils.isEmpty(commentParentId) || commentParentId.equals("0")) {
                //有的时候，是回复楼中楼
                //没有的时候，就是回复楼层，没有commentParentId
                commentParentId = String.valueOf(comment.getCommentId());
            }
            reply_input_layout.setShowQuickReply(comment.getCommentId() <= 0);
            reply_input_layout.showInput(mPostId, position, comment.getCommentId(), commentParentId, comment.getUser());
            ll_reply_post.setVisibility(View.GONE);
        }
    }

    @OnClick(R.id.iv_back)
    void clickBack() {
        finish();
    }

    /**
     * 有头图的分享
     */
    @OnClick(R.id.iv_share)
    void clickShare() {
        //SensorsUtils.sensorsClickBtn("分享","帖子");
        if (mPostCard != null) {
            String postContent = "";
            postContent = mPostCard.getContentString();
            String shareUrl;
            if (mPostCard.getPostTypeId() != null && mPostCard.getPostTypeId().equals(String.valueOf(PostCard.PostType.SHORT_VIDEO))) {
                shareUrl = String.format(Locale.getDefault(), WebUrlConfigConstant.POST_DETAIL_SHORT_VIDEO_SHARE_PATTERN, mPostCard.getPostId());
            } else {
                shareUrl = String.format(Locale.getDefault(), WebUrlConfigConstant.POST_DETAIL_SHARE_PATTERN, mPostCard.getPostId());
            }
            boolean isChangeTitle = TextUtils.isEmpty(mPostCard.getPostTitle());
            //如果标题是空，咋替换标题为内容
            String shareTitle = TextUtils.isEmpty(mPostCard.getPostTitle()) ? getString(R.string.share_post_default_title) : mPostCard.getPostTitle();
            sharePost(mPostCard.getImages(), postContent, shareUrl, shareTitle, isChangeTitle);
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        super.onCreateOptionsMenu(menu);
        if (mToolbar != null && mToolbar.getMenu() != null) {
            MenuItem item = mToolbar.getMenu().findItem(R.id.action_right_text);
            if (item != null) {
                item.setIcon(R.drawable.community_share_post_icon);
                item.setVisible(shareVisible != 1);
                item.setTitle("");
            }
        }
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.action_right_text:
                if (mPostCard != null) {
                    String postContent = "";
                    postContent = mPostCard.getContentString();
                    String shareUrl;
                    if (mPostCard.getPostTypeId() != null && mPostCard.getPostTypeId().equals(String.valueOf(PostCard.PostType.SHORT_VIDEO))) {
                        shareUrl = String.format(Locale.getDefault(), WebUrlConfigConstant.POST_DETAIL_SHORT_VIDEO_SHARE_PATTERN, mPostCard.getPostId());
                    } else {
                        shareUrl = String.format(Locale.getDefault(), WebUrlConfigConstant.POST_DETAIL_SHARE_PATTERN, mPostCard.getPostId());
                    }
                    boolean isChangeTitle = TextUtils.isEmpty(mPostCard.getPostTitle());
                    //如果标题是空，咋替换标题为内容
                    String shareTitle = TextUtils.isEmpty(mPostCard.getPostTitle()) ? getString(R.string.share_post_default_title) : mPostCard.getPostTitle();
                    sharePost(mPostCard.getImages(), postContent, shareUrl, shareTitle, isChangeTitle);
                    SensorsUtils.sensorsClickBtn("点击分享" + mPostCard.getPostTitle() + mPostCard.getPostId(), "帖子详情", "分享");
                }
                return true;
            default:
                return super.onOptionsItemSelected(item);
        }
    }

    private void sharePost(String[] images, String postContent, String shareUrl, String shareTitle, boolean isChangeTitle) {
        MobclickAgent.onEvent(this, "62");
        if (shareUrl == null) {
            shareUrl = WebUrlConfigConstant.SHARE_LLB;
        } else {
            User user = User.getsUserInstance();
            if (UserUtils.hasLogin(user)) {
                shareUrl = String.format(ShareUtil.POST_SHARE_PARAM, shareUrl, User.getsUserInstance().getUserIdStr());
            }
        }
        if (postContent == null) {
            postContent = "";
        }
        if (postContent.length() > 50) {
            postContent = postContent.substring(0, 50);
        }
        mShareUtil = ShareUtil.newInstance();
        mShareUtil.setShareType(ShareUtil.ShareType.SHARE_TYPE_POST);
        mShareUtil.setShareActivityId(mPostId);
        if (mPostCard != null) {
            mShareUtil.setPostType(mPostCard.getPostTypeIdOrNegative());
            //话题贴，话题名称
            if (mPostCard.getPostTypeIdOrNegative() == PostCard.PostType.TOPIC) {
                mShareUtil.setTopicName(mPostCard.getTopicName());
            }
        }
        mShareUtil.share(this, shareUrl, postContent, (images != null && images.length > 0) ? Arrays.asList(images) : null, shareTitle, isChangeTitle);
    }

    public void setRemindUserAdoptTipsVisible(int visibility) {
        if (tv_remind_user_adopt_answer.getVisibility() != visibility) {
            tv_remind_user_adopt_answer.setVisibility(visibility);
        }
    }

    @Override
    protected void onPause() {
        stopPostVideo();
        super.onPause();
    }

    @Override
    public void onBackPressed() {
        boolean isPlayerFullScreen = false;
        EmptySupportedRecyclerView recyclerView = getRefreshController().getRecyclerView();
        LinearLayoutManager layoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
        for (int i = 0; i < layoutManager.getChildCount(); i++) {
            View childAt = layoutManager.getChildAt(i);
            if (childAt != null) {
                RecyclerView.ViewHolder childViewHolder = recyclerView.getChildViewHolder(childAt);
                if (childViewHolder instanceof PostDetailNativeAdapter.PostDetailBaseContentViewHolder) {
                    PostDetailNativeAdapter.PostDetailBaseContentViewHolder postDetailBaseContentViewHolder =
                            (PostDetailNativeAdapter.PostDetailBaseContentViewHolder) childViewHolder;
                    if (postDetailBaseContentViewHolder.playerView != null && postDetailBaseContentViewHolder.playerView.isFullScreen()) {
                        postDetailBaseContentViewHolder.playerView.setNormalScreen();
                        isPlayerFullScreen = true;
                    }
                }
            }
        }
        ViewGroup vp = findViewById(Window.ID_ANDROID_CONTENT);
        int count = vp == null ? 0 : vp.getChildCount();
        for (int i = 0; i < count; i++) {
            View childAt = vp.getChildAt(i);
            if (childAt instanceof AliyunVodPlayerView) {
                isPlayerFullScreen = true;
                ((AliyunVodPlayerView) childAt).setNormalScreen();
            }
        }
        if (!isPlayerFullScreen) {
            super.onBackPressed();
        }
    }

    /**
     * 关闭帖子里面的视频
     */
    private void stopPostVideo() {
        EmptySupportedRecyclerView recyclerView = getRefreshController().getRecyclerView();
        LinearLayoutManager layoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
        for (int i = 0; i < layoutManager.getChildCount(); i++) {
            View childAt = layoutManager.getChildAt(i);
            if (childAt != null) {
                RecyclerView.ViewHolder childViewHolder = recyclerView.getChildViewHolder(childAt);
                if (childViewHolder instanceof PostDetailNativeAdapter.PostDetailBaseContentViewHolder) {
                    PostDetailNativeAdapter.PostDetailBaseContentViewHolder postDetailBaseContentViewHolder =
                            (PostDetailNativeAdapter.PostDetailBaseContentViewHolder) childViewHolder;
                    if (postDetailBaseContentViewHolder.playerView != null) {
                        postDetailBaseContentViewHolder.playerView.stop();
                        postDetailBaseContentViewHolder.playerView.release();
                        postDetailBaseContentViewHolder.rlContainer.removeView(postDetailBaseContentViewHolder.playerView);
                        postDetailBaseContentViewHolder.playerView = null;
                    }
                } else if (childViewHolder instanceof PostDetailNativeAdapter.ContentSenseViewHolder) {
                    ((PostDetailNativeAdapter.ContentSenseViewHolder) childViewHolder).changeAdTurning(false);
                }
            }
        }
    }

    /**
     * 切换只看楼主，并加载数据
     */
    public void toggleSeeAuthorOnly() {
        mSeeAuthorOnlyStatus = !mSeeAuthorOnlyStatus;
        getRefreshController().getListData(1);
    }

    /**
     * 该方法判断是否加载了所有评论，帖子是否展示只看楼主，由 Adapter 创建 ViewHolder 时判断
     */
    public boolean isLoadAllComments() {
        return mLoadAllComments;
    }

    /**
     * 是否是只看楼主状态
     */
    public boolean isSeeAuthorOnlyStatus() {
        return mSeeAuthorOnlyStatus;
    }

    @Override
    protected void onStop() {
        super.onStop();
        if (mPostCard != null) {
            //神策埋点
            List<PostCommodity> postCommodityList = mPostCard.getPostCommodityList();
            String s = (postCommodityList == null || postCommodityList.size() <= 0) ? "不存在" : "存在";
            SensorsUtils.sensorsViewEndNew(openFrom, FinalSensors.POST_DETAIL, "浏览帖子详情" + mPostCard.getPostTitle() + mPostCard.getPostId() + s);
        }
    }

    public void showCoverImgView() {
        if (!TextUtils.isEmpty(mPostCard.getCoverImage())) {
            //如果有封面
            StatusBarUtils.setFullScreenTransparentStatusBarAndWhiteText(this);
            mToolBarLayout.setVisibility(View.VISIBLE);
            mToolbar.setVisibility(View.GONE);
        } else {
            mToolBarLayout.setVisibility(View.GONE);
            mToolbar.setVisibility(View.VISIBLE);
        }
    }

    private void loadRecommendPost() {
        L00bangRequestManager2.getServiceInstance()
                .recommendPost(String.valueOf(mPostId), new ArrayMap<>())
                .compose(L00bangRequestManager2.setSchedulers())
                .subscribe(new BackgroundSubscriber<List<PostCard>>(this) {
                    @Override
                    public void onSuccess(List<PostCard> postCards) {
                        super.onSuccess(postCards);
                        if (postCards == null || postCards.isEmpty()) {
                            return;
                        }
                        if (mPostCard != null) {
                            if (mRecommendPostList == null) {
                                //添加item占位
                                getRefreshController().getData().add(0, null);
                            }
                            mRecommendPostList = postCards;
                            onGetPostDetail(mPostCard, -2);
                        }
                    }
                });
    }

}
