package com.cloudy.linglingbang.activity.fragment.store.youpin;

import android.view.View;

import com.cloudy.linglingbang.activity.fragment.store.home.adapter.StoreHomeElement_nav_ViewHolder;
import com.cloudy.linglingbang.activity.fragment.store.home.model.CommonTitleWithMore;
import com.cloudy.linglingbang.activity.fragment.store.home.model.StoreHomeElementWrapper;
import com.cloudy.linglingbang.activity.fragment.store.home.model.StoreHomeNavElementWrapper;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter;
import com.cloudy.linglingbang.app.widget.recycler.header.HeaderAndFooterWrapperAdapter;
import com.cloudy.linglingbang.app.widget.recycler.wrapper.BaseWrapperItemAdapter;
import com.cloudy.linglingbang.model.store.home.StoreLayoutComponent;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 列表滑动导航栏管理
 *
 * <AUTHOR>
 * @date 2020/5/8
 */
public class StoreNavScrollListener extends RecyclerView.OnScrollListener implements StoreHomeElement_nav_ViewHolder.OnNavChangeListener {
    /**
     * 导航栏所在下标
     */
    private int mNavIndex;
    /**
     * 页面中导航栏高度
     */
    private int mNavHeight;
    private int offset;
    private final RecyclerView mRecyclerView;
    private int mScrollState;
    /*** 是否是nav主动切换 */
    private boolean navSelect;
    /**
     * 页面中导航栏ViewHolder
     */
    private StoreHomeElement_nav_ViewHolder mNavViewHolder;
    /**
     * 列表中导航栏ViewHolder
     */
    private StoreHomeElement_nav_ViewHolder mNavAdapterViewHolder;
    private final LinearLayoutManager mLayoutManager;
    private int firstVisibleItemPosition;
    private int lastVisibleItemPosition;

    public StoreNavScrollListener(RecyclerView recyclerView) {
        mRecyclerView = recyclerView;
        mLayoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
    }

    @Override
    public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
        super.onScrolled(recyclerView, dx, dy);
        if (dy != 0) {
            updateNav();
        }
    }

    private void updateNav() {
        //避免多次不必要刷新、减少刷新频率
        if (firstVisibleItemPosition == mLayoutManager.findFirstVisibleItemPosition()
                && lastVisibleItemPosition == mLayoutManager.findLastVisibleItemPosition()) {
            return;
        }
        firstVisibleItemPosition = mLayoutManager.findFirstVisibleItemPosition();
        lastVisibleItemPosition = mLayoutManager.findLastVisibleItemPosition();

        View itemView = mLayoutManager.findViewByPosition(mNavIndex);
        if (itemView != null) {
            RecyclerView.ViewHolder viewHolder = mRecyclerView.getChildViewHolder(itemView);
            if (viewHolder instanceof StoreHomeElement_nav_ViewHolder) {
                setNavAdapterViewHolder((StoreHomeElement_nav_ViewHolder) viewHolder);
            }
        }

        if (firstVisibleItemPosition >= mNavIndex) {
            long layoutComponentId = -1;
            int index = lastVisibleItemPosition - (lastVisibleItemPosition - firstVisibleItemPosition) / 3;
            if (index + 1 > mNavIndex) {
                Object object = getItemObj(index);
                layoutComponentId = getLayoutComponentId(object, layoutComponentId);
            }
            if (mNavViewHolder != null) {
                setFloatNavViewVisible();
                if (layoutComponentId != -1 && !navSelect) {
                    mNavViewHolder.setSelectByLayoutId(layoutComponentId);
                }
            }
            if (mNavAdapterViewHolder != null) {
                mNavAdapterViewHolder.setChangeListener(null);
            }
        } else {
            if (mNavViewHolder != null) {
                mNavViewHolder.itemView.setVisibility(View.GONE);
                mNavViewHolder.setChangeListener(null);
                mNavViewHolder.setSelect(0);
            }
            if (mNavAdapterViewHolder != null) {
                mNavAdapterViewHolder.setChangeListener(this);
                mNavAdapterViewHolder.setSelect(0);
            }
        }
    }

    @Override
    public void onNavTabChange(int index, StoreLayoutComponent component) {
        if (mScrollState != RecyclerView.SCROLL_STATE_IDLE) {
            return;
        }
        if (mRecyclerView == null) {
            return;
        }
        List data = getData();
        if (data == null || data.isEmpty()) {
            return;
        }
        Object obj;
        for (int i = 0; i < data.size(); i++) {
            obj = data.get(i);
            if (getLayoutComponentId(obj, -1) == component.getLayoutComponentId()) {
                if (i >= mNavIndex) {
                    setFloatNavViewVisible();
                } else {
                    mNavViewHolder.itemView.setVisibility(View.GONE);
                }
                scrollToPosition(i);
                break;
            }
        }
    }

    private void setFloatNavViewVisible() {
        if (mNavViewHolder == null) {
            return;
        }
        mNavViewHolder.setChangeListener(this);
        if (mNavViewHolder.itemView.getVisibility() != View.VISIBLE) {
            mNavViewHolder.itemView.setVisibility(View.VISIBLE);
            if (mNavAdapterViewHolder != null) {
                mNavViewHolder.setSelect(mNavAdapterViewHolder.getCurrentItem());
            }
            if (mNavHeight == 0) {
                int w = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
                int h = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
                mNavViewHolder.itemView.measure(w, h);
                mNavHeight = mNavViewHolder.itemView.getMeasuredHeight();
            }
        }
    }

    private void scrollToPosition(int index) {
        //若组件在导航栏上方则不滑动
        if (index <= mNavIndex) {
            return;
        }
        navSelect = true;
        if (mLayoutManager != null) {
            mLayoutManager.scrollToPositionWithOffset(index, mNavHeight + offset);
        } else {
            mRecyclerView.scrollToPosition(index);
        }
        navSelect = false;
    }

    @Override
    public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
        super.onScrollStateChanged(recyclerView, newState);
        mScrollState = newState;
    }

    @Nullable
    private Object getItemObj(int position) {
        List list = getData();
        if (list == null || list.size() <= position || position < 0) {
            return null;
        }
        return list.get(position);
    }

    @Nullable
    private List getData() {
        if (mRecyclerView == null) {
            return null;
        }
        if (mRecyclerView.getAdapter() == null) {
            return null;
        }
        RecyclerView.Adapter<?> adapter = mRecyclerView.getAdapter();
        if (adapter instanceof HeaderAndFooterWrapperAdapter) {
            adapter = ((HeaderAndFooterWrapperAdapter) adapter).getInnerAdapter();
        }
        if (adapter instanceof BaseWrapperItemAdapter) {
            return ((BaseWrapperItemAdapter) adapter).getData();
        }
        if (adapter instanceof BaseRecyclerViewAdapter) {
            return ((BaseRecyclerViewAdapter) adapter).getData();
        }
        return null;
    }

    public void setNavIndex(int navIndex) {
        mNavIndex = navIndex;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public void setNavAdapterViewHolder(StoreHomeElement_nav_ViewHolder navAdapterViewHolder) {
        mNavAdapterViewHolder = navAdapterViewHolder;
    }

    public void setNavViewHolder(View itemView, StoreHomeNavElementWrapper wrapper, int position) {
        setNavIndex(position);
        if (mNavViewHolder == null) {
            mNavViewHolder = new StoreHomeElement_nav_ViewHolder(itemView);
        }
        mNavViewHolder.bindTo(wrapper, position);
        //延时150毫秒，等待导航栏绘制
        itemView.postDelayed(this::updateNav, 150);
    }

    public long getLayoutComponentId(Object obj, long defaultId) {
        if (obj instanceof StoreHomeElementWrapper) {
            return ((StoreHomeElementWrapper) obj).getLayoutComponentId();
        } else if (obj instanceof CommonTitleWithMore) {
            return ((CommonTitleWithMore) obj).getLayoutComponentId();
        }
        return defaultId;
    }
}
