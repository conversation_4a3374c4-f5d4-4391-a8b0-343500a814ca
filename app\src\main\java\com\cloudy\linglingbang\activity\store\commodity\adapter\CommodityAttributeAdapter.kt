package com.cloudy.linglingbang.activity.store.commodity.adapter

import android.content.Context
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.cloudy.linglingbang.R
import com.cloudy.linglingbang.app.widget.FlowLayout
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewAdapter
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder
import com.cloudy.linglingbang.app.widget.recycler.TransparentGridItemDecoration
import com.cloudy.linglingbang.app.widget.textview.PressEffectiveCompoundButton
import com.cloudy.linglingbang.model.store.commodity.CommoditySku

/**
 * 商品属性adapter
 */
class CommodityAttributeAdapter(context: Context?, data: List<CommoditySku.AttributeValueAttr?>?) :
    BaseRecyclerViewAdapter<CommoditySku.AttributeValueAttr?>(context, data) {
    override fun createViewHolder(itemView: View): BaseRecyclerViewHolder<CommoditySku.AttributeValueAttr?> {
        return CommodityAttributeViewHolder(itemView, mCommodityAttributeListener)
    }

    private var mCommodityAttributeListener: CommodityAttributeListener? = null
    fun setCommodityAttributeListener(commodityAttributeListener: CommodityAttributeListener?) {
        mCommodityAttributeListener = commodityAttributeListener
    }

    override fun getItemLayoutRes(viewType: Int): Int {
        return R.layout.layout_sku_other_service
    }

    interface CommodityAttributeListener {
        fun onAttributeClickListener(button: PressEffectiveCompoundButton?, position: Int, i: Int)
        fun showTip(url: String?, name: String?)
    }

    class CommodityAttributeViewHolder(
        itemView: View?,
        private val mCommodityAttributeListener: CommodityAttributeListener?
    ) : BaseRecyclerViewHolder<CommoditySku.AttributeValueAttr?>(itemView) {

        var commodityAttribute: CommoditySku.AttributeValueAttr? = null

        /**
         * 属性标题
         */
        var tvAttributeTitle: TextView? = null

        /**
         * 属性值流式布局
         */
        var mFlowLayout: FlowLayout? = null
        var recyclerView: RecyclerView? = null
        var mIvTip: ImageView? = null
        override fun initItemView(itemView: View) {
            super.initItemView(itemView)
            tvAttributeTitle = itemView.findViewById(R.id.tv_attribute_title)
            mIvTip = itemView.findViewById(R.id.iv_tip)
            mFlowLayout = itemView.findViewById(R.id.flow_layout)
            recyclerView = itemView.findViewById(R.id.recycler_view_item)
            mIvTip?.setOnClickListener {
                mCommodityAttributeListener?.showTip(
                    commodityAttribute?.attrDesc,
                    commodityAttribute?.attributeName
                )
            }
            mFlowLayout?.setChildHorizontalSpace(itemView.resources.getDimensionPixelOffset(R.dimen.normal_40))
            mFlowLayout?.setChildVerticalSpace(itemView.resources.getDimensionPixelOffset(R.dimen.normal_40))
        }

        override fun bindTo(commodityAttribute: CommoditySku.AttributeValueAttr?, position: Int) {
            super.bindTo(commodityAttribute, position)
            itemView.setPadding(
                itemView.paddingLeft,
                itemView.resources.getDimensionPixelOffset(if (position == 0) R.dimen.normal_40 else R.dimen.normal_60),
                itemView.paddingRight,
                itemView.paddingBottom
            )
            this.commodityAttribute = commodityAttribute
            mIvTip?.visibility = View.GONE
            if (commodityAttribute != null) {
                if (!TextUtils.isEmpty(commodityAttribute.attrDesc)) {
                    mIvTip?.visibility = View.VISIBLE
                }

                tvAttributeTitle?.text = commodityAttribute.attributeName
                var hasImage = false
                for (attributeValue in commodityAttribute.lmAttributeValueList) {
                    if (!TextUtils.isEmpty(attributeValue.attributeValueImage)) {
                        hasImage = true
                        break
                    }
                }

                if (hasImage) {
                    mFlowLayout?.removeAllViews()
                    mFlowLayout?.visibility = View.GONE

                    recyclerView?.apply {
                        if (itemDecorationCount == 0) {
                            val space = resources.getDimensionPixelSize(R.dimen.normal_20)
                            addItemDecoration(TransparentGridItemDecoration(3, space, space))
                        }
                        visibility = View.VISIBLE
                        val adapter1 = AttributeValueWithImageAdapter(
                            context,
                            commodityAttribute.lmAttributeValueList
                        )
                        adapter1.setOnItemClickListener { _, p ->
                            mCommodityAttributeListener?.onAttributeClickListener(
                                null,
                                position,
                                p
                            )
                        }
                        adapter = adapter1
                    }

                } else {
                    recyclerView?.adapter = null
                    recyclerView?.visibility = View.GONE
                    mFlowLayout?.visibility = View.VISIBLE
                    mFlowLayout?.removeAllViews()
                    val inflater = LayoutInflater.from(itemView.context)
                    for (commodityAttributeValue in commodityAttribute.lmAttributeValueList) {
                        if (commodityAttributeValue != null) {
                            val compoundButton = inflater.inflate(
                                R.layout.item_commodity_attribute_value1,
                                mFlowLayout,
                                false
                            ) as PressEffectiveCompoundButton
                            compoundButton.text = commodityAttributeValue.attributeValueName
                            compoundButton.setOnClickListener {
                                mCommodityAttributeListener?.onAttributeClickListener(
                                    compoundButton,
                                    position,
                                    commodityAttribute.lmAttributeValueList.indexOf(
                                        commodityAttributeValue
                                    )
                                )
                            }
                            if (commodityAttributeValue.checkState == 2) {
                                compoundButton.isEnabled = false
                                compoundButton.visibility = View.GONE
                                compoundButton.setBackgroundColor(
                                    compoundButton.resources.getColor(
                                        R.color.color_f2f2f2
                                    )
                                )
                            } else {
                                compoundButton.isChecked = commodityAttributeValue.checkState == 1
                            }
                            mFlowLayout?.addView(compoundButton)
                        }
                    }
                }
            }
        }
    }
}