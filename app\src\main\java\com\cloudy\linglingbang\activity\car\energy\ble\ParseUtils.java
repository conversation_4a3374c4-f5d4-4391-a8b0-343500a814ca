package com.cloudy.linglingbang.activity.car.energy.ble;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;

/**
 * <AUTHOR>
 * @describe
 * @date 2021/12/28
 */
public class ParseUtils {
    /**
     * 序列化对象
     *
     * @param o
     * @return
     * @throws IOException
     */
    public static String serialize(Object o) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ObjectOutputStream objectOutputStream = new ObjectOutputStream(
                byteArrayOutputStream);
        objectOutputStream.writeObject(o);
        String serStr = byteArrayOutputStream.toString("ISO-8859-1");
        serStr = java.net.URLEncoder.encode(serStr, "UTF-8");
        objectOutputStream.close();
        byteArrayOutputStream.close();
        return serStr;
    }

    /**
     * 反序列化对象
     *
     * @param str
     * @return
     * @throws IOException
     * @throws ClassNotFoundException
     */
    public static Object deSerialization(String str) throws IOException,
            ClassNotFoundException {

        String redStr = java.net.URLDecoder.decode(str, "UTF-8");
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(
                redStr.getBytes("ISO-8859-1"));
        ObjectInputStream objectInputStream = new ObjectInputStream(
                byteArrayInputStream);
        Object o = objectInputStream.readObject();
        objectInputStream.close();
        byteArrayInputStream.close();

        return o;
    }

    /**
     * 左侧补0
     *
     * @param str
     * @return
     * @throws IOException
     * @throws ClassNotFoundException
     */

    public static String addZeroForLeft(String str, int formatLength) {
        int strLength = str.length();
        if (formatLength > strLength) {
            formatLength -= strLength;
            str = String.format("%0" + formatLength + 'd', 0) + str;
        }
        return str;
    }
}
