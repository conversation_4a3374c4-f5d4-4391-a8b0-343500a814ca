package com.cloudy.linglingbang.activity.community.linglab;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.cloudy.linglingbang.R;
import com.cloudy.linglingbang.activity.basic.BaseRecyclerViewRefreshFragment;
import com.cloudy.linglingbang.activity.basic.IntentUtils;
import com.cloudy.linglingbang.activity.basic.RefreshController;
import com.cloudy.linglingbang.app.util.sensors.SensorsUtils;
import com.cloudy.linglingbang.app.widget.recycler.BaseRecyclerViewHolder;
import com.cloudy.linglingbang.model.request.retrofit2.BaseResponse;
import com.cloudy.linglingbang.model.request.retrofit2.L00bangService2;
import com.cloudy.linglingbang.model.tag.ChooseTagBean;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.recyclerview.widget.RecyclerView;
import io.reactivex.rxjava3.core.Observable;

/**
 * 选择标签的推荐商品列表
 *
 * <AUTHOR>
 * @date 2021/11/11
 */
public class ChooseTagListFragment extends BaseRecyclerViewRefreshFragment<ChooseTagBean> {

    private long mClassifyId;
    private String mCommoditySearchName;

    public static ChooseTagListFragment newInstance(long classifyId) {
        Bundle args = new Bundle();
        args.putLong("classifyId", classifyId);
        ChooseTagListFragment fragment = new ChooseTagListFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected void initViews() {
        super.initViews();
        mClassifyId = getArguments().getLong("classifyId");
    }

    @Override
    public RecyclerView.Adapter<? extends RecyclerView.ViewHolder> createAdapter(List<ChooseTagBean> list) {
        ChooseTagCommodityAdapter chooseTagCommodityAdapter = new ChooseTagCommodityAdapter(getActivity(), list);
        chooseTagCommodityAdapter.setOnItemClickListener(new BaseRecyclerViewHolder.OnItemClickListener() {
            @Override
            public void onItemClick(View itemView, int position) {
                List<ChooseTagBean> d = chooseTagCommodityAdapter.getData();
                if (d == null || d.size() <= position) {
                    return;
                }
                SensorsUtils.sensorsClickBtn("选择商品" + d.get(position).getCommodityName(), "图文发布页-种草-选择商品页", "选择商品");
                Intent intent = new Intent();
                intent.putExtra(IntentUtils.INTENT_EXTRA_COMMON, d.get(position));
                getActivity().setResult(Activity.RESULT_OK, intent);
                getActivity().finish();
            }
        });
        return chooseTagCommodityAdapter;
    }

    private final Map<String, String> map = new HashMap<>(4);

    @Override
    public Observable<BaseResponse<List<ChooseTagBean>>> getListDataFormNet(L00bangService2 service2, int pageNo, int pageSize) {
        map.remove("commodityName");
        map.put("pageSize", String.valueOf(pageSize));
        map.put("pageNo", String.valueOf(pageNo));
        map.put("classifyId", String.valueOf(mClassifyId));
        if (!TextUtils.isEmpty(mCommoditySearchName)) {
            map.put("commodityName", mCommoditySearchName);
        }
        return service2.getPostCommoditiesList(map);
    }

    @Override
    public RefreshController<ChooseTagBean> createRefreshController() {
        final RefreshController<ChooseTagBean> refreshController = new RefreshController<ChooseTagBean>(this) {

            @Override
            public void initViews(View rootView) {
                super.initViews(rootView);
                getRecyclerView().setBackgroundColor(getResources().getColor(R.color.white));
            }

            @Override
            protected RecyclerView.ItemDecoration createItemDecoration(Context context) {
                return null;
            }

        };
        refreshController.setEmptyImageResId(R.drawable.ic_tag_list_empty);
        if (TextUtils.isEmpty(mCommoditySearchName)) {
            refreshController.setEmptyString("这里有点荒凉~");
        } else {
            refreshController.setEmptyString("没有你想要的结果");
        }
        return refreshController;
    }

    public void setCommoditySearchName(String searchName) {
        mCommoditySearchName = searchName;
        getRefreshController().onRefresh();
        if (TextUtils.isEmpty(mCommoditySearchName)) {
            getRefreshController().setEmptyString("这里有点荒凉~");
        } else {
            getRefreshController().setEmptyString("没有你想要的结果");
        }
    }
}
